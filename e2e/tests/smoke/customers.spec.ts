import { test } from "../../utils/fixtures";
import { expect } from "@playwright/test";
import { faker } from '@faker-js/faker';
require("dotenv").config();
const membershipId = faker.string.uuid();
const membershipIdMandatory = faker.string.uuid();
test.describe.configure({
  mode: "serial",
});

test("Check if creating of customer works correctly", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loyalty", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and fill form to create an user and save it", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.fillFormSimplestWay(membershipId);
  });
  await test.step("Check if newly created user is visible on list", async () => {
    await customersPage.pressOkButtonAfterAction();
    await customersPage.selectSpecificCustomer(membershipId);
  });
});
test("Check if editing customer works correctly", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerEditPage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and edit user then save it", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.enterEditSpecificCustomer(membershipId);
    await customerEditPage.editUser();
  });
  await test.step("Check if editing has proper effect", async () => {
    const result = await customersPage.getFirstNameAfterEditing(membershipId);
    expect(result).toBe("EDITED");
  });
});
test("Check if deleting customer works correctly", async ({ dashboardPage, loginPage, customersPage }) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.deleteSpecificCustomer(membershipId);
    await customersPage.page.reload();
  });
  await test.step("Check if deleting has proper effect", async () => {
    const result = await customersPage.checkIfElementIsVisible(membershipId);
    expect(result).toBe(false);
  });
});

test("Check if creating customer with only name is not possible", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.fillFormOnlyName();
  });
  await test.step("Check if creating is blocked", async () => {
    const emailRed = await customerCreatePage.emailError.isVisible();
    const loyaSchemeRed = await customerCreatePage.groupGuidError.isVisible();
    const membershipNoRed = await customerCreatePage.membershipNoError.isVisible();
    const lastNameRed = await customerCreatePage.lastNameError.isVisible();
    expect(emailRed).toBe(true);
    expect(loyaSchemeRed).toBe(true);
    expect(membershipNoRed).toBe(true);
    expect(lastNameRed).toBe(true);
  });
});

test("Check if creating customer with only email is not possible", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.fillFormOnlyEmail();
  });
  await test.step("Check if creating is blocked", async () => {
    const firstNameRed = await customerCreatePage.firstNameError.isVisible();
    const loyaSchemeRed = await customerCreatePage.groupGuidError.isVisible();
    const membershipNoRed = await customerCreatePage.membershipNoError.isVisible();
    const lastNameRed = await customerCreatePage.lastNameError.isVisible();
    expect(firstNameRed).toBe(true);
    expect(loyaSchemeRed).toBe(true);
    expect(membershipNoRed).toBe(true);
    expect(lastNameRed).toBe(true);
  });
});

test("Check if creating customer with only last name is not possible", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.fillFormOnlyLastName();
  });
  await test.step("Check if creating is blocked", async () => {
    const firstNameRed = await customerCreatePage.firstNameError.isVisible();
    const loyaSchemeRed = await customerCreatePage.groupGuidError.isVisible();
    const membershipNoRed = await customerCreatePage.membershipNoError.isVisible();
    const emailRed = await customerCreatePage.emailError.isVisible();
    expect(firstNameRed).toBe(true);
    expect(loyaSchemeRed).toBe(true);
    expect(membershipNoRed).toBe(true);
    expect(emailRed).toBe(true);
  });
});

test("Check if creating customer with blank form is not possible", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.saveForm.click();
  });
  await test.step("Check if creating is blocked", async () => {
    const firstNameRed = await customerCreatePage.firstNameError.isVisible();
    const loyaSchemeRed = await customerCreatePage.groupGuidError.isVisible();
    const membershipNoRed = await customerCreatePage.membershipNoError.isVisible();
    const emailRed = await customerCreatePage.emailError.isVisible();
    const lastNameRed = await customerCreatePage.lastNameError.isVisible();
    expect(firstNameRed).toBe(true);
    expect(loyaSchemeRed).toBe(true);
    expect(membershipNoRed).toBe(true);
    expect(emailRed).toBe(true);
    expect(lastNameRed).toBe(true);
  });
});

test("Check if creating customer with only membership number is not possible", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.fillFormOnlyMembership();
  });
  await test.step("Check if creating is blocked", async () => {
    const firstNameRed = await customerCreatePage.firstNameError.isVisible();
    const loyaSchemeRed = await customerCreatePage.groupGuidError.isVisible();
    const lastNameRed = await customerCreatePage.lastNameError.isVisible();
    const emailRed = await customerCreatePage.emailError.isVisible();
    expect(firstNameRed).toBe(true);
    expect(loyaSchemeRed).toBe(true);
    expect(emailRed).toBe(true);
    expect(lastNameRed).toBe(true);
  });
});

test("Check if creating customer with only loya scheme is not possible", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.fillFormOnlyLoyaScheme();
  });
  await test.step("Check if creating is blocked", async () => {
    const firstNameRed = await customerCreatePage.firstNameError.isVisible();
    const membershipRed = await customerCreatePage.membershipNoError.isVisible();
    const lastNameRed = await customerCreatePage.lastNameError.isVisible();
    const emailRed = await customerCreatePage.emailError.isVisible();
    expect(firstNameRed).toBe(true);
    expect(membershipRed).toBe(true);
    expect(emailRed).toBe(true);
    expect(lastNameRed).toBe(true);
  });
});

test("Check if creating of customer works correctly with mandatory fields only", async ({
  dashboardPage,
  loginPage,
  customersPage,
  customerCreatePage,
}) => {
  await test.step("Go to dashboard page and select customers and loya", async () => {
    await loginPage.logIn();
    await dashboardPage.pressCustomersAndLoyaButton();
  });
  await test.step("Go to customers tab and fill form to create an user and save it", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.pressNewCustomerButton();
    await customerCreatePage.fillFormMandatory(membershipIdMandatory);
  });
  await test.step("Check if newly created user is visible on list", async () => {
    await customersPage.pressOkButtonAfterAction();
    await customersPage.selectSpecificCustomer(membershipIdMandatory);
  });
  await test.step("Go to customers tab and delete user", async () => {
    await dashboardPage.pressCustomersButton();
    await customersPage.deleteSpecificCustomer(membershipIdMandatory);
    await customersPage.page.reload();
  });
  await test.step("Check if deleting has proper effect", async () => {
    const result = await customersPage.page.locator(`//td[normalize-space()='${membershipIdMandatory}']`).isVisible();
    expect(result).toBe(false);
  });
});
