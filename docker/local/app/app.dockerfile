FROM php:8.2-fpm

WORKDIR /var/www

RUN apt-get update && apt-get install -y --no-install-recommends curl gnupg2 ca-certificates lsb-release debian-archive-keyring && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt/* /tmp/* /var/tmp/*

RUN ln -snf /usr/share/zoneinfo/UTC /etc/localtime && echo UTC > /etc/timezone && \
    curl https://nginx.org/keys/nginx_signing.key | gpg --dearmor | tee /usr/share/keyrings/nginx-archive-keyring.gpg > /dev/null && \
    echo "deb [signed-by=/usr/share/keyrings/nginx-archive-keyring.gpg] http://nginx.org/packages/debian `lsb_release -cs` nginx" | tee /etc/apt/sources.list.d/nginx.list

RUN apt-get update && apt-get install -y --no-install-recommends \
    libxslt1-dev  \
    libmcrypt-dev  \
    build-essential \
    libcurl4-openssl-dev \
    curl \
    git \
    jpegoptim optipng pngquant gifsicle \
    libonig-dev \
    libxml2-dev \
    locales \
    unzip \
    zip \
    htop \
    nginx \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libzip-dev \
    supervisor \
    mariadb-client \
    nano && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt/* /tmp/* /var/tmp/*


# Install PHP extensions
RUN pecl install -o -f redis xdebug mcrypt excimer && \
    docker-php-ext-enable redis xdebug mcrypt excimer && \
    docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install -j$(nproc) gd && \
    docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath && \
    docker-php-ext-install xmlwriter curl opcache ftp && \
    docker-php-ext-install soap && \
    docker-php-ext-install intl && \
    docker-php-ext-install zip && \
    docker-php-ext-install xsl && \
    rm -rf /tmp/pear

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

COPY supervisord.conf /etc/supervisord.conf

ENTRYPOINT ["/usr/bin/supervisord"]
WORKDIR /var/www