FROM php:8.2-fpm

WORKDIR /var/www

RUN apt-get update && apt-get install -y --no-install-recommends \
    libxslt1-dev  \
    libmcrypt-dev  \
    build-essential \
    libcurl4-openssl-dev \
    curl \
    git \
    jpegoptim optipng pngquant gifsicle \
    libonig-dev \
    libxml2-dev \
    locales \
    unzip \
    zip \
    htop \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libzip-dev \
    supervisor \
    mariadb-client \
    nano && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt/* /tmp/* /var/tmp/*


# Install PHP extensions
RUN pecl install -o -f redis xdebug mcrypt excimer && \
    docker-php-ext-enable redis xdebug mcrypt excimer && \
    docker-php-ext-configure gd --with-freetype --with-jpeg && \
    docker-php-ext-install -j$(nproc) gd && \
    docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath && \
    docker-php-ext-install xmlwriter curl opcache ftp && \
    docker-php-ext-install soap && \
    docker-php-ext-install intl && \
    docker-php-ext-install zip && \
    docker-php-ext-install xsl && \
    rm -rf /tmp/pear

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install Cron
RUN apt-get update && apt-get install -y --no-install-recommends cron && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt/* /tmp/* /var/tmp/*

RUN echo "* * * * * root php /var/www/artisan schedule:run >> /var/log/cron.log 2>&1" >> /etc/crontab && \
    touch /var/log/cron.log

COPY supervisord.conf /etc/supervisord.conf

ENTRYPOINT ["/usr/bin/supervisord"]
WORKDIR /var/www