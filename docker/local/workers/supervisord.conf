[supervisord]
loglevel=info                ; log level; default info; others: debug,warn,trace
logfile=/var/www/storage/logs/supervisor.log
pidfile=/tmp/supervisord.pid ; supervisord pidfile; default supervisord.pid
nodaemon=true               ; start in foreground if true; default false
minfds=1024                  ; min. avail startup file descriptors; default 1024
minprocs=200                 ; min. avail process descriptors;default 200
[supervisorctl]
serverurl=unix:///tmp/supervisor.sock ; use a unix:// URL  for a unix socket
[unix_http_server]
file=/tmp/supervisor.sock   ; the path to the socket file
[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:app-cron]
process_name=%(program_name)s_%(process_num)02d
command=cron -f -l 8
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/cron.log

[program:app-worker]
priority=10
process_name=%(program_name)s
command=php /var/www/artisan horizon
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=root
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/horizon.log
stopwaitsecs=3600

[program:app-worker-stock]
priority=10
process_name=%(program_name)s
command=php /var/www/artisan horizon --environment=local-stock
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=root
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/horizon.log
stopwaitsecs=3600

[program:app-worker-high-memory]
priority=10
process_name=%(program_name)s
command=php /var/www/artisan horizon --environment=local-high-memory
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=root
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/horizon.log
stopwaitsecs=3600

[program:app-worker-high-memory-background]
priority=11
process_name=%(program_name)s
command=php /var/www/artisan horizon --environment=local-high-memory-background
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=root
numprocs=1
redirect_stderr=true
stdout_logfile=/var/www/storage/logs/horizon.log
stopwaitsecs=3600