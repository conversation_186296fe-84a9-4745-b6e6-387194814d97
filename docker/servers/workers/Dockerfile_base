FROM php:8.2-fpm as newbridge-web-workers

RUN apt-get update && apt-get install -y \
    build-essential curl gifsicle git \
    jpegoptim libmcrypt-dev  libfreetype6-dev libjpeg62-turbo-dev \
    libonig-dev libpng-dev libgmp-dev libxml2-dev libxslt1-dev  libzip-dev \
    locales optipng pngquant unzip zip \
    gnupg2 ca-certificates lsb-release debian-archive-keyring \
    cron supervisor python3 pip procps dnsutils gcc python3-dev \
    && docker-php-ext-install opcache \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd \
    && docker-php-ext-install bcmath exif intl mbstring pdo_mysql pcntl soap xmlwriter xsl zip ftp gmp \
    && pecl install mcrypt && docker-php-ext-enable mcrypt \
    && pecl install excimer && docker-php-ext-enable excimer \
    && pecl install -o -f redis \
    && rm -rf /tmp/pear \
    && docker-php-ext-enable redis \
    && apt purge build-essential gcc -y \
    && apt autoremove -y \
    && apt-get clean && rm -rf /var/lib/apt/lists/* /var/cache/apt/* /tmp/* /var/tmp/* \
    && ln -snf /usr/share/zoneinfo/UTC /etc/localtime && echo UTC > /etc/timezone

RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

RUN echo "* * * * * www-data php /var/www/artisan schedule:run >> /dev/null 2>&1" >> /etc/crontab

RUN pip3 install --no-cache-dir fastapi uvicorn azure-monitor-opentelemetry watchdog --break-system-packages

RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash

WORKDIR /var/www
