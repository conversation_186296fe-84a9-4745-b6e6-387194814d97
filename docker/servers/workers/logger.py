import logging
from logging import <PERSON><PERSON><PERSON>, getLogger, Filter
from azure.monitor.opentelemetry import configure_azure_monitor
import os
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

os.environ["OTEL_SERVICE_NAME"] = "nbworkers-log-scraper"
os.environ["OTEL_RESOURCE_ATTRIBUTES"] = ("service.instance.id=" + os.getenv("HOSTNAME"))
app_insights_connection_string = os.getenv("APPINSIGHTS_CONNECTIONSTRING")

configure_azure_monitor(
    logger_name="god_logger",
    connection_string=app_insights_connection_string
)
logger = getLogger("god_logger")
logger.setLevel(INFO)

logger.info('Logger initialized and ready to capture new log entries.')

log_files = [
    {"name": "CRON-ERROR", "path": "/var/www/storage/logs/cron-err.log"},
    {"name": "HORIZON-ERROR", "path": "/var/www/storage/logs/horizon-err.log"},
    {"name": "HEALTHPROBE-ERROR", "path": "/var/www/storage/logs/healthprobe-err.log"},
]

class LogFileHandler(FileSystemEventHandler):
    def __init__(self, log_file):
        self.log_file_path = log_file["path"]
        self.log_prefix = f"[{log_file['name']}]"
        self.last_position = os.path.getsize(self.log_file_path) if os.path.exists(self.log_file_path) else 0

    def on_modified(self, event):
        if event.src_path == self.log_file_path:
            self.read_new_lines()

    def on_deleted(self, event):
        if event.src_path == self.log_file_path:
            self.last_position = 0

    def on_created(self, event):
        if event.src_path == self.log_file_path:
            self.last_position = 0
            self.read_new_lines()

    def read_new_lines(self):
        try:
            with open(self.log_file_path, "r") as log_file:
                log_file.seek(self.last_position)
                new_data = log_file.read()
                if new_data:
                    self.last_position = log_file.tell()
                    stripped_data = new_data.strip()
                    if stripped_data and '"GET / HTTP/1.1" 200 OK' not in stripped_data:
                        logger.info(f"{self.log_prefix} {stripped_data}")
        except FileNotFoundError:
            pass
        except Exception as e:
            logger.error(f'Error reading file {self.log_file_path}: {e}')

observer = Observer()
for log_file in log_files:
    event_handler = LogFileHandler(log_file)
    observer.schedule(event_handler, path=os.path.dirname(log_file["path"]), recursive=False)

observer.start()

try:
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    observer.stop()
observer.join()