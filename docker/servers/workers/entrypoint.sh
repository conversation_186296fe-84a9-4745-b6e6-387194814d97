#!/usr/bin/env bash

az login --federated-token "$(cat $AZURE_FEDERATED_TOKEN_FILE)" --service-principal -u $AZURE_CLIENT_ID -t $AZURE_TENANT_ID --allow-no-subscriptions

secret_value=$(az keyvault secret show --vault-name $AZURE_VAULT_NAME --name $AZURE_VAULT_ENV_SECRET --query value -o tsv)
echo "" >> /var/www/.env
echo "$secret_value" >> /var/www/.env
echo "IS_WORKER=true" >> /var/www/.env

if [ "$IS_STOCK_WORKERS" == "true" ]; then
  echo "" >> /var/www/.env
  echo "IS_STOCK_WORKER=true" >> /var/www/.env
fi

chown www-data:www-data /var/www/.env

#Laravel prepare
/usr/local/bin/php /var/www/artisan config:cache

supervisorctl start all
