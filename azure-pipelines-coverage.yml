trigger: none

pool:
  vmImage: "ubuntu-latest"

variables:
  COMPOSER_CACHE_DIR: $(Pipeline.Workspace)/.composer

name: "Testing Types Coverage $(Date:yyyyMMdd).$(Rev:r)"

resources:
  containers:
    - container: builder
      image: 'sync667/awesome-laravel-base-builder:latest'
      command: 'tail -f /dev/null'
      options: '--name builder'

stages:
  - stage: Tests
    jobs:
      - job: Tests_Types_Coverage
        services:
          builder: builder
        steps:
          - task: Cache@2
            inputs:
              key: "composer | src/NewbridgeBackOffice/composer.lock"
              restoreKeys: "composer"
              path: $(COMPOSER_CACHE_DIR)
            displayName: Cache Composer Packages
          - script: cp .env-testing .env
            displayName: "Copy .env-testing to .env"
            workingDirectory: "src/NewbridgeBackOffice"

          - script: docker exec builder bash -c "cd /__w/1/s/src/NewbridgeBackOffice && composer install --prefer-dist --no-scripts"
            displayName: "Install dependencies"

          - script: XDEBUG_MODE=coverage php -d memory_limit=2048M ./vendor/bin/pest --type-coverage --min=10
            displayName: "Run types coverage tests"
            workingDirectory: "src/NewbridgeBackOffice"
