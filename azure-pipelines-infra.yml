trigger:
  branches:
    include:
      - '*'
  paths:
    include:
      - 'infra/*'
      - 'azure-pipelines-infra.yml'

name: 1.0.0$(Rev:.r)

pool:
  vmImage: ubuntu-latest

resources:
  repositories:
    - repository: templates
      type: git
      name: Newbridge/pipeline-templates

variables:
  projectName: newbridge-pos
  workingDirectory: $(System.DefaultWorkingDirectory)/infra
  release: '$(Build.DefinitionName)'
  release_url: '$(System.TeamFoundationCollectionUri)$(System.TeamProject)/_build?definitionId=$(System.DefinitionId)'

stages:
  - stage: DeployCI
    displayName: Deploy to CI
    pool:
      vmImage: ubuntu-latest

    variables:
      - group: newbridge alerts @ ci
      - group: owner principal @ ci
      - name: firewall_rules
        value: |
          [
            { "name" = "aks_ci1we_static_ip_1",  "ip"   = "*************" },
            { "name" = "aks_ci1we_static_ip_2", "ip"   = "************" },
            { "name" = "aks_ci1we_static_ip_3", "ip"   = "*************" },
            { "name" = "aks_ci1we_static_ip_4", "ip"   = "**************" },
            { "name" = "aks_ci2we_static_ip_1", "ip"   = "*************" },
            { "name" = "aks_ci2we_static_ip_2", "ip"   = "************" },
            { "name" = "aks_ci2we_static_ip_3", "ip"   = "************" },
            { "name" = "aks_ci2we_static_ip_4", "ip"   = "*************" },
            { "name" = "aks_ci3we_static_ip_1", "ip"   = "***************" },
            { "name" = "aks_ci3we_static_ip_2", "ip"   = "***************" },
            { "name" = "aks_ci3we_static_ip_3", "ip"   = "***************" },
            { "name" = "aks_ci3we_static_ip_4", "ip"   = "***************" }
          ]

    jobs:
      - template: terraform-deploy.yaml@templates
        parameters:
          workingDirectory: $(workingDirectory)
          environmentName: ci
          regionName: we
          projectName: $(projectName)
          backendInfraName: infra
          terraformVariables: |
            release="$(release)"
            release_url="$(release_url)"
            env_owner_principal_id="$(env_owner_principal_id)"
            env_azure_workload_identity="$(ci_env_azure_workload_identity)"
            cluster_nbweb_env_azure_workload_identity="$(ci_cluster_nbweb_env_azure_workload_identity)"
            cluster_nbworkers_env_azure_workload_identity="$(ci_cluster_nbworkers_env_azure_workload_identity)"
            cluster_nbstockworkers_env_azure_workload_identity="$(ci_cluster_nbstockworkers_env_azure_workload_identity)"
            cluster_nbhighmemoryworkers_env_azure_workload_identity="$(ci_cluster_nbhighmemoryworkers_env_azure_workload_identity)"
            cluster_nbhighmemorybackgroundworkers_env_azure_workload_identity="$(ci_cluster_nbhighmemorybackgroundworkers_env_azure_workload_identity)"
            cluster_nbsentryrelay_env_azure_workload_identity="$(ci_cluster_nbsentryrelay_env_azure_workload_identity)"
            cluster_newbridgekds_env_azure_workload_identity="$(ci_cluster_newbridgekds_env_azure_workload_identity)"
            cluster_our-menus_env_azure_workload_identity="$(ci_cluster_our-menus_env_azure_workload_identity)"
            cluster_stockmanager_env_azure_workload_identity="$(ci_cluster_stockmanager_env_azure_workload_identity)"
            firewall_rules=$(firewall_rules)
            db_sku="B_Standard_B2s"
            db_iops="0"
            db_high_availability="false"
            db_zone="3"
            db_standby_zone="2"
            mysql_password="$(ci_mysql_password)"
            redis_sku_family="C"
            redis_sku_name="Basic"
            redis_sku_capacity="0"
            slack_webhook_url="$(slack_webhook_url)"
            alerts_function_url="$(alerts_function_url)"
            web_events_topic_name="BackOfficeDev"

  - stage: DeployStage
    displayName: Deploy to Stage
    pool:
      vmImage: ubuntu-latest

    variables:
      - group: newbridge alerts @ stage
      - group: owner principal @ stage
      - name: firewall_rules
        value: |
          [
            {"name" = "aks_stage1we_static_ip_1", "ip" = "*************"},
            {"name" = "aks_stage1we_static_ip_2", "ip" = "*************"},
            {"name" = "aks_stage1we_static_ip_3", "ip" = "*************"},
            {"name" = "aks_stage1we_static_ip_4", "ip" = "************"},
            {"name" = "aks_stage2we_static_ip_1", "ip" = "***********"},
            {"name" = "aks_stage2we_static_ip_2", "ip" = "************"},
            {"name" = "aks_stage2we_static_ip_3", "ip" = "***********"},
            {"name" = "aks_stage2we_static_ip_4", "ip" = "************"},
            {"name" = "aks_stage3we_static_ip_1", "ip" = "***************"},
            {"name" = "aks_stage3we_static_ip_2", "ip" = "***************"},
            {"name" = "aks_stage3we_static_ip_3", "ip" = "**************"},
            {"name" = "aks_stage3we_static_ip_4", "ip" = "**************"}
          ]

    jobs:
      - template: terraform-deploy.yaml@templates
        parameters:
          workingDirectory: $(workingDirectory)
          environmentName: stage
          regionName: we
          projectName: $(projectName)
          backendInfraName: infra
          terraformVariables: |
            release="$(release)"
            release_url="$(release_url)"
            env_owner_principal_id="$(env_owner_principal_id)"
            env_azure_workload_identity="$(stage_env_azure_workload_identity)"
            cluster_nbweb_env_azure_workload_identity="$(stage_cluster_nbweb_env_azure_workload_identity)"
            cluster_nbworkers_env_azure_workload_identity="$(stage_cluster_nbworkers_env_azure_workload_identity)"
            cluster_nbstockworkers_env_azure_workload_identity="$(stage_cluster_nbstockworkers_env_azure_workload_identity)"
            cluster_nbhighmemoryworkers_env_azure_workload_identity="$(stage_cluster_nbhighmemoryworkers_env_azure_workload_identity)"
            cluster_nbhighmemorybackgroundworkers_env_azure_workload_identity="$(stage_cluster_nbhighmemorybackgroundworkers_env_azure_workload_identity)"
            cluster_nbsentryrelay_env_azure_workload_identity="$(stage_cluster_nbsentryrelay_env_azure_workload_identity)"
            cluster_newbridgekds_env_azure_workload_identity="$(stage_cluster_newbridgekds_env_azure_workload_identity)"
            cluster_our-menus_env_azure_workload_identity="$(stage_cluster_our-menus_env_azure_workload_identity)"
            cluster_stockmanager_env_azure_workload_identity="$(stage_cluster_stockmanager_env_azure_workload_identity)"
            firewall_rules=$(firewall_rules)
            db_sku="GP_Standard_D4ads_v5"
            db_iops="0"
            db_high_availability="false"
            db_zone="3"
            db_standby_zone="2"            
            mysql_password="$(stage_mysql_password)"
            redis_sku_family="C"
            redis_sku_name="Basic"
            redis_sku_capacity="0"
            slack_webhook_url="$(slack_webhook_url)"
            alerts_function_url="$(alerts_function_url)"
            web_events_topic_name="backofficestaging"

  - stage: DeployProduction
    displayName: Deploy to Production
    condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'master'))
    pool:
      vmImage: ubuntu-latest

    variables:
      - group: newbridge alerts @ prod
      - group: owner principal @ prod
      - name: firewall_rules
        value: |
          [
            {"name" = "aks_prod1we_static_ip_1", "ip" = "*************"},
            {"name" = "aks_prod1we_static_ip_2", "ip" = "**************"},
            {"name" = "aks_prod1we_static_ip_3", "ip" = "*************"},
            {"name" = "aks_prod1we_static_ip_4", "ip" = "*************"},
            {"name" = "aks_prod2we_static_ip_1", "ip" = "*************"},
            {"name" = "aks_prod2we_static_ip_2", "ip" = "************"},
            {"name" = "aks_prod2we_static_ip_3", "ip" = "*************"},
            {"name" = "aks_prod2we_static_ip_4", "ip" = "*************"},
            {"name" = "aks_prod3we_static_ip_1", "ip" = "*************"},
            {"name" = "aks_prod3we_static_ip_2", "ip" = "*************"},
            {"name" = "aks_prod3we_static_ip_3", "ip" = "*************"},
            {"name" = "aks_prod3we_static_ip_4", "ip" = "*************"}
          ]

    jobs:
      - template: terraform-deploy.yaml@templates
        parameters:
          workingDirectory: $(workingDirectory)
          environmentName: prod
          regionName: we
          projectName: $(projectName)
          backendInfraName: infra
          terraformVariables: |
            release="$(release)"
            release_url="$(release_url)"
            env_owner_principal_id="$(env_owner_principal_id)"
            env_azure_workload_identity="$(prod_env_azure_workload_identity)"
            cluster_nbweb_env_azure_workload_identity="$(prod_cluster_nbweb_env_azure_workload_identity)"
            cluster_nbworkers_env_azure_workload_identity="$(prod_cluster_nbworkers_env_azure_workload_identity)"
            cluster_nbstockworkers_env_azure_workload_identity="$(prod_cluster_nbstockworkers_env_azure_workload_identity)"
            cluster_nbhighmemoryworkers_env_azure_workload_identity="$(prod_cluster_nbhighmemoryworkers_env_azure_workload_identity)"
            cluster_nbhighmemorybackgroundworkers_env_azure_workload_identity="$(prod_cluster_nbhighmemorybackgroundworkers_env_azure_workload_identity)"
            cluster_nbsentryrelay_env_azure_workload_identity="$(prod_cluster_nbsentryrelay_env_azure_workload_identity)"
            cluster_newbridgekds_env_azure_workload_identity="$(prod_cluster_newbridgekds_env_azure_workload_identity)"
            cluster_our-menus_env_azure_workload_identity="$(prod_cluster_our-menus_env_azure_workload_identity)"
            cluster_stockmanager_env_azure_workload_identity="$(prod_cluster_stockmanager_env_azure_workload_identity)"
            firewall_rules=$(firewall_rules)
            db_sku="MO_Standard_E4ads_v5"
            db_iops="0"
            db_high_availability="true"
            db_zone="3"
            db_standby_zone="2"            
            mysql_password="$(prod_mysql_password)"
            redis_sku_family="P"
            redis_sku_name="Premium"
            redis_sku_capacity="1"
            storage_account_replication_type="GRS"
            slack_webhook_url="$(slack_webhook_url)"
            alerts_function_url="$(alerts_function_url)"
            web_events_topic_name="backoffice"
