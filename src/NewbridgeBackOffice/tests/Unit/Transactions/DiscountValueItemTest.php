<?php

use NewbridgeWeb\Http\Controllers\PosApi\v1\Transactions\Process\ProcessDetails;

it('calculates the correct discount value refund positive discount', function () {
    $processDetails = new ProcessDetails();

    $rrp = $processDetails->calculateGrossTotal(-22.5, 6);
    $salePrice = $processDetails->calculateNetTotal($rrp, 12.86, 6);
    $commandType = 6;
    $discountValue = $processDetails->calculateDiscountValue($rrp, $salePrice, $commandType);

    echo "RRP: $rrp\n";
    echo "Sale Price: $salePrice\n";
    echo "Discount Value: $discountValue\n";

    $netValueCalculation = $rrp - ($discountValue * -1);

    echo "Net Value Calculation: $netValueCalculation\n";

    expect($netValueCalculation)->toBe(12.86);
});

it('calculates the correct discount value for refund with a discount', function () {
    $processDetails = new ProcessDetails();

    $rrp = $processDetails->calculateGrossTotal(-22.5, 6);
    $salePrice = $processDetails->calculateNetTotal($rrp, -12.86, 6);
    $commandType = 6;
    $discountValue = $processDetails->calculateDiscountValue($rrp, $salePrice, $commandType);

    echo "RRP: $rrp\n";
    echo "Sale Price: $salePrice\n";
    echo "Discount Value: $discountValue\n";

    $netValueCalculation = $rrp - ($discountValue * -1);

    echo "Net Value Calculation: $netValueCalculation\n";

    expect($netValueCalculation)->toBe(-12.86);
});

it('calculates the correct discount value for a not discounted refund', function () {
    $processDetails = new ProcessDetails();

    // Test case 1: RRP is greater than Sale Price
    $rrp = $processDetails->calculateGrossTotal(-12.50, 0);
    $salePrice = $processDetails->calculateNetTotal($rrp, -12.50, 0);
    $commandType = 0;
    $discountValue = $processDetails->calculateDiscountValue($rrp, $salePrice, $commandType);

    echo "RRP: $rrp\n";
    echo "Sale Price: $salePrice\n";
    echo "Discount Value: $discountValue\n";

    $netValueCalculation = $rrp - ($discountValue * -1);

    echo "Net Value Calculation: $netValueCalculation\n";

    expect($netValueCalculation)->toBe(-12.5);
});

it('calculates the correct discount value normal discount transaction', function () {
    $processDetails = new ProcessDetails();

    $commandType = 0;
    $rrp = $processDetails->calculateGrossTotal(22.5, $commandType);
    $salePrice = $processDetails->calculateNetTotal($rrp, 12.86, $commandType);
    $discountValue = $processDetails->calculateDiscountValue($rrp, $salePrice, $commandType);

    expect($discountValue)->toBe(-9.64);
});
