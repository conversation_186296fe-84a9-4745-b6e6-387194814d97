<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use NewbridgeWeb\Http\Controllers\PosApi\App\Ordering\WebOrders;
use NewbridgeWeb\Jobs\WebOrders\ProcessWebOrder;
use NewbridgeWeb\Repositories\Sites;

it('tests the receive function of WebOrders', function () {
    // Arrange
    Queue::fake();

    $data = new stdClass();
    $data->products = [];
    $data->delivery = (object) ['method' => 'eat-in', 'table' => (object) ['guid' => '']];
    $data->order_number = '12345';
    $data->sale_total = '100.00';
    $data->payments = [];

    $request = Request::create('/receive', 'POST', [], [], [], [], json_encode($data));
    $request->headers->set('platform', 'newbridge');
    $request->merge(['company' => ['id' => 426]]);

    $webOrders = new WebOrders();
    $response = $webOrders->receive($request, 1);
    expect($response->getStatusCode())->toBe(200);
    Queue::assertPushed(ProcessWebOrder::class, function ($job) use ($data) {
        return $job->data == $data;
    });
});