<?php

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\PosApi\v1\EventSenderApi;
use Symfony\Component\HttpFoundation\Response;

it('Send to service bus successful', function () {
    $request = new Request();
    $request->replace([
        'Topic' => 'test',
        'Subject' => 'test',
        'EventType' => 'test',
        'Data' => ['test' => 'test']
    ]);

    $eventSenderApi = new EventSenderApi();
    $response = $eventSenderApi->sendEvent($request);

    expect($response->getStatusCode())->toBe(Response::HTTP_OK);
});

it('Send to service bus without topic should fail', function () {
    $request = new Request();
    $request->headers->set('Content-Type', 'application/json');
    $request->headers->set('Accept', 'application/json');
    $request->replace([
        // 'Topic' is intentionally omitted to test validation failure
        'Subject' => 'test',
        'EventType' => 'test',
        'Data' => ['test' => 'test']
    ]);

    $eventSenderApi = new EventSenderApi();
    try {
        $eventSenderApi->sendEvent($request);
    } catch (\Exception $e) {
        expect($e->getMessage())->toBe('The topic field is required.');
    }

});