<?php

use Illuminate\Support\Str;
use NewbridgeWeb\User;

use function Pest\Laravel\actingAs;

it('checks all GET routes for a valid HTTP response', function () {
    $allowedStatusCodes = [200, 301, 302, 401, 402];
    $skipRoutesContaining = ['{', 'horizon', 'clockwork', 'broadcasting/auth'];

    //TODO: this skip need to be removed once we validate this routes
    $skipToBeValidated = ['api', 'keyaccess', 'newbridge/search/analytics', 'newbridge/search/extensions',
        'newbridge/search/reindex',
        'reports/download' , 'stock', 'departments/get-json',
        'company/pms-links/data', 'guestline/migration/data', 'guestline/migration/create',
        'company/pms-configuration/guestline/key-form', 'exports/download',
        'products/mass-edit', 'products/barcodes/print', 'pages/get', 'pos-settings',
        'xero', 'terminal-report', 'newbridge/debug-data/download',
        'newbridge/debug-data/monitor', 'instant-run/draught', 'update-polling', 'company/update-pms-links',
        'companies/recalculate/df29j239id239d29b239u8bd9u23bd2'
    ];

    $routes = \Illuminate\Support\Facades\Route::getRoutes()->getRoutesByMethod()['GET'];

    $user = User::find(config('tests.testing_user_id'));
    $user->real_company_id = $user->real_company_id ?: $user->company_id;

    foreach ($routes as $route) {
        $uri = $route->uri;

        if (Str::contains($uri, array_merge($skipRoutesContaining, $skipToBeValidated))) {
            continue;
        }

        $response = actingAs($user)->get($uri);

        $this->assertTrue(
            in_array($response->getStatusCode(), $allowedStatusCodes, true),
            "Failed on route: $uri with status code: " . $response->getStatusCode()
        );
    }
});
