<?php

use NewbridgeWeb\User;

use function Pest\Laravel\actingAs;

it('returns the versions manager view', function () {
    $user = User::find(config('tests.testing_user_id'));
    $user->real_company_id = $user->real_company_id ?: $user->company_id;

    $this->actingAs($user);

    $response = $this->get(route('versions-manager.index'));

    $response->assertOk();
    $response->assertViewIs('modules.newbridge.versions-manager.table');
    $response->assertViewHas(['versions', 'resellers']);
});

it('fetches site data successfully', function () {
    $user = User::find(config('tests.testing_user_id'));
    $user->real_company_id = $user->real_company_id ?: $user->company_id;
    $this->actingAs($user);

    $response = $this->getJson(route('versions-manager.data'));

    $response->assertOk();
    $response->assertJsonStructure(['data']);
});

it('returns validation errors for update', function () {
    $user = User::find(config('tests.testing_user_id'));
    $user->real_company_id = $user->real_company_id ?: $user->company_id;
    $this->actingAs($user);

    $response = $this->postJson(route('versions-manager.update'), [
        ['site_id' => 'invalid', 'max_version' => 123],
    ]);

    $response->assertStatus(400);
    $response->assertJsonStructure(['errors']);
});

it('updates site versions successfully', function () {
    $user = User::find(config('tests.testing_user_id'));
    $user->real_company_id = $user->real_company_id ?: $user->company_id;
    $this->actingAs($user);

    $response = $this->postJson(route('versions-manager.update'), [
        ['site_id' => 564, 'max_version' => '2.3.1.2'],
    ]);

    $response->assertOk();
    $response->assertJson(['message' => 'Versions updated successfully.']);
    $this->assertDatabaseHas('sys_sites', [
        'id' => 564,
        'max_version' => '2.3.1.2',
    ]);
});

it('returns validation errors for terminals details', function () {
    $user = User::find(config('tests.testing_user_id'));
    $user->real_company_id = $user->real_company_id ?: $user->company_id;
    $this->actingAs($user);

    $response = $this->postJson(route('versions-manager.terminals'), [
        'ids' => ['invalid'],
    ]);

    $response->assertStatus(400);
    $response->assertJsonStructure(['errors']);
});

it('fetches terminal details successfully', function () {
    $user = User::find(config('tests.testing_user_id'));
    $user->real_company_id = $user->real_company_id ?: $user->company_id;
    $this->actingAs($user);

    $response = $this->postJson(route('versions-manager.terminals'), [
        'ids' => [$user->company_id],
    ]);

    $response->assertOk();
    $response->assertJsonStructure([
        1 . '-' . $user->company_id => [
            '*' => ['id', 'terminal_type', 'site_num', 'terminal_num', 'last_ping_datetime', 'terminal_version'],
        ],
        2 . '-' . $user->company_id => [
            '*' => ['id', 'terminal_type', 'site_num', 'terminal_num', 'last_ping_datetime', 'terminal_version'],
        ],
    ]);
});
