##Stock Control > Adjustments


**What is it?**

The adjustments section is where you can make adjustments to your stock levels as needed.

<br />


**What can I do here?**

From this page you can manage the stock levels for your sites. If you are a multi-site user, please ensure you have selected the correct site from the dropdown.


<br />

<span style="text-decoration:underline;">New</span>

To create a new adjustment, click the “New” button. You will now see a page called Adjust Stock. Before starting you should ensure that the date for the adjustment is correct as changing the date will clear all products from it.

To add a product to the adjustment, select that product from the dropdown menu labelled “Product” and then click the “Add Product” label below. You can then add the correct quantity to the product whether positive or negative and click “Create Adjustment” when you are finished. Any notes can be added in the notes section on the upper-left of the page.


<br />

<span style="text-decoration:underline;">View</span>

You can view a saved or completed adjustment by selecting that entry from the table and selecting “View”, this will show you the contents of your adjustment ordered by subcategory along with the option to export the document to csv.


<br />

<span style="text-decoration:underline;">Edit </span>

You can edit a saved adjustment by clicking on the entry from the table and then clicking the “Edit” button. This will bring up your adjustment as you left it when you saved it, you can make your edits and then click the “Create Adjustment” button to save or complete the adjustment.

Please note that a completed adjustment cannot be edited, the adjustment must be saved but not completed to be editable.


<br />

<span style="text-decoration:underline;">Delete</span>

You can delete a saved adjustment by selecting the entry from the table and then clicking the “Delete” button. You must then confirm this action to delete the record from your adjustments.

Please note that a completed adjustment cannot be deleted, the adjustment must be saved but not completed to be deletable. 