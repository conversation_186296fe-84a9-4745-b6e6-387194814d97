# Get Transactions

You can retrieve a set of transactions for a specific date using this api, the data is provided in a nested JSON format.

## Request

### Headers

```php
GET REQUEST (/api/app/json/transactions/{date})

Header: Content-Type - application/json
Header: Accepts - application/json
Header: key - "XXXXXXXXX" // app key provided by newbridge
Header: token - "XXXXXX-XXXX-XXXXX" // token obtained from the auth endpoint

```

### Date
The URI Requires date in YYYY-MM-DD format.

### Response Body

There are some fields in the data which are created as part of the model augmentations that we are not able to remove and remain efficient so you will need to work around these fields.

### Response

```json

[
  {
    "id": 33391672,
    "site_num": 1,
    "terminal_num": 19,
    "order_number": "1578:19",
    "total": "0.00",
    "finalised_date": "2022-03-10 07:19:27",
    "day_part": null,
    "table_number_location": ":",
    "finalised_date_london": "2022-03-10 07:19:27",
    "payment_methods": "Cash",
    "details": [
      {
        "id": 72545676,
        "displayname": "Resi Egg on Toast",
        "sale_value": 0,
        "tax_rate": 5,
        "tax_value": 0,
        "discount_value": 0,
        "trans_id": 33391672,
        "product_guid": "64c8ce13-68ac-4730-b940-521173b1b651",
        "sub_department_guid": "f589a5b7-ba1b-4d5d-b4a7-be5f9d2021d2",
        "department_guid": "8c1f05aa-52ef-4cf9-9405-1922bd6e9b63",
        "tax_value_calculation": 0,
        "product": {
          "id": 290459,
          "guid": "64c8ce13-68ac-4730-b940-521173b1b651",
          "barcode": "290459",
          "stock_code": null,
          "gross_profit": "100%",
          "is_wasteable": false,
          "is_modifier_text": "Product"
        }
      },
      {
        "id": 72545677,
        "displayname": "Poached Egg",
        "sale_value": 0,
        "tax_rate": 12.5,
        "tax_value": 0,
        "discount_value": 0,
        "trans_id": 33391672,
        "product_guid": "f2c277ce-729e-4157-aadd-342fd1e0d853",
        "sub_department_guid": "f589a5b7-ba1b-4d5d-b4a7-be5f9d2021d2",
        "department_guid": "8c1f05aa-52ef-4cf9-9405-1922bd6e9b63",
        "tax_value_calculation": 0,
        "product": {
          "id": 92589,
          "guid": "f2c277ce-729e-4157-aadd-342fd1e0d853",
          "barcode": "000000092589",
          "stock_code": null,
          "gross_profit": "100%",
          "is_wasteable": false,
          "is_modifier_text": "Product"
        }
      },
      {
        "id": 72545678,
        "displayname": "White Toast",
        "sale_value": 0,
        "tax_rate": 5,
        "tax_value": 0,
        "discount_value": 0,
        "trans_id": 33391672,
        "product_guid": "87889bba-dba1-4004-be04-90fd0c2ca7d0",
        "sub_department_guid": "7d68031c-5773-4599-8b05-160fdb075033",
        "department_guid": "8c1f05aa-52ef-4cf9-9405-1922bd6e9b63",
        "tax_value_calculation": 0,
        "product": {
          "id": 92590,
          "guid": "87889bba-dba1-4004-be04-90fd0c2ca7d0",
          "barcode": "000000092590",
          "stock_code": null,
          "gross_profit": "100%",
          "is_wasteable": false,
          "is_modifier_text": "Product"
        }
      },
      {
        "id": 72545679,
        "displayname": "Resi BYO Breakfast",
        "sale_value": 0,
        "tax_rate": 12.5,
        "tax_value": 0,
        "discount_value": 0,
        "trans_id": 33391672,
        "product_guid": "300fc634-e82c-4d4b-ab28-9de408948a8e",
        "sub_department_guid": "f4859d14-0d06-409f-bf09-ebbe5fa5d6f7",
        "department_guid": "ea11c6a9-516b-4021-b234-c6836351d885",
        "tax_value_calculation": 0,
        "product": {
          "id": 177623,
          "guid": "300fc634-e82c-4d4b-ab28-9de408948a8e",
          "barcode": "177623",
          "stock_code": null,
          "gross_profit": "100%",
          "is_wasteable": false,
          "is_modifier_text": "Product"
        }
      },
      {
        "id": 72545680,
        "displayname": "Residents Crispy Bacon",
        "sale_value": 0,
        "tax_rate": 12.5,
        "tax_value": 0,
        "discount_value": 0,
        "trans_id": 33391672,
        "product_guid": "280579cd-6903-43d3-8fe5-30b803674253",
        "sub_department_guid": "f4859d14-0d06-409f-bf09-ebbe5fa5d6f7",
        "department_guid": "8c1f05aa-52ef-4cf9-9405-1922bd6e9b63",
        "tax_value_calculation": 0,
        "product": {
          "id": 179171,
          "guid": "280579cd-6903-43d3-8fe5-30b803674253",
          "barcode": "179171",
          "stock_code": null,
          "gross_profit": "100%",
          "is_wasteable": false,
          "is_modifier_text": "Product"
        }
      }
    ],
    "payments": [
      {
        "trans_id": 33391672,
        "method_type": 1,
        "amount": 0,
        "type": {
          "id": 1,
          "name": "Cash"
        }
      }
    ]
  }
]

```