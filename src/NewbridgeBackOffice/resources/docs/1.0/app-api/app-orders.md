# Process Orders

Processing orders requires that you get the following information from Newbridge before making any attempt to integrate.

<br />

> **Table GUID**- You will need to provide a table GUID for the exact table if you intend to provide eat-in services 
> a single table GUID can be provided if you are providing delivery or collection.

> **Catch All ID** - You will need to provide the ID of a product that exists in Newbridge to which all 
> unknown/un-mapped items will be associated, this is to prevent any difference in financial information 
> between payment processor and Newbridge.

> **Payments** - The payments part of the request is required but is reserved for future development. 
> Please use this exactly as displayed.

> **Delivery Methods** - The available delivery methods are 'collection', 'delivery', 'eat-in' all of these methods 
> require a table GUID to be provided.

> **Order Number** - Must be unique to the company making the request.

- __meta.catch_all_id__ - The ID of the catch-all product used to send any product not 
  correctly added to the Newbridge Backoffice.
- __Promotions__ - The promotions array is optional
- __Products__ - products is required
- **Products.product_guid** - required unless an app_product_id is supplied.
- **Products.app_product_id** - required unless a product_guid is supplied. This is used when products are mapped in the
 Newbridge system as an alternative to using the Products API.


## Request

### Headers
```php
POST REQUEST (/api/app/orders/{site_num})

Header: Content-Type - application/json
Header: Accepts - application/json
Header: key - "XXXXXXXXX" // app key provided by newbridge
Header: token - "XXXXXX-XXXX-XXXXX" // token obtained from the auth endpoint
```

### Body 

```json
{
  "id": 1,
  "order_number": 123454733225,
  "sale_total": 2.30,
  "order_date": "2020-06-26 18:55:00",
  "due_date": "2020-06-26 19:55:00",
  "membership_no": null,
  "customer": {
    "full_name": "John Smith",
    "email": "<EMAIL>",
    "phone": "07700000000",
    "address": {
      "address_line1": "1 Test Avenue",
      "address_line2": "Test Place",
      "address_line3": "Test Town",
      "address_line4": "Test County",
      "postcode": "T3ST"
    }
  },
  "notes": "No Nuts, No guten, no wheat, no barley,No Nuts, No guten, no wheat, no barley,No Nuts, No guten, no wheat, no barley,No Nuts, No guten, no wheat, no barley,No Nuts, No guten, no wheat, no barley,No Nuts, No guten, no wheat, no barley,No Nuts, No guten, no wheat, no barley,No Nuts, No guten, no wheat, no barley",
  "delivery": {
    "method": "collection",
    "table": {
      "guid": "2ffedaa1-dd45-47e4-979f-333b8be9f823"
    }
  },
  "products": [
    {
      "product_guid": null,
      "app_product_id": 123455656,
      "sale_value": 2.30,
      "date": "2020-06-25 18:55:00"
    }
  ],
  "payments": [
    {
      "method_type": 0,
      "amount": 2.30
    }
  ],
  "meta": {
    "catch_all_id": 123
  }
}


```