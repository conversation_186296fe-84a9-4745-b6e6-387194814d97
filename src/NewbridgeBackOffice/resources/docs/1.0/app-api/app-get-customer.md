# Get a Customer

The customer endpoint accepts a customer ID as the fourth parameter of the request url.

## Request

### Headers
```php
GET REQUEST (/api/app/customers/{id})

Header: Content-Type - application/json
Header: Accepts - application/json
Header: key - "XXXXXXXXX" // app key provided by newbridge
Header: token - "XXXXXX-XXXX-XXXXX" // token obtained from the auth endpoint

Parameters: 
    id - the customer id
    company - the company id (as a body parameter)
```

### Response 

```json
{
  "status": "ok",
  "data": {
    "customer": {
      "first_name": "<PERSON>",
      "last_name": "Bloggs",
      "expires": "2021-12-31",
      "membership_no": "1",
      "credit_limit": 0,
      "points_balance": 0,
      "cash_value": 0,
      "points_value": 0,
      "full_name": "<PERSON> Bloggs",
      "credit_balance": 0,
      "group": {
        "guid": "01595ec2-b40c-402e-b1bc-0d6d9e4e70c8",
        "displayname": "Gold Points Scheme",
        "discount_guid": "353c65c5-da74-46ac-9433-902df25a6882",
        "price_level": null,
        "gives_points": 1,
        "promotion_guid": null,
        "group_type": "Loyalty Points",
        "discount": {
          "value": "10.00",
          "CommandUID": "353c65c5-da74-46ac-9433-902df25a6882",
          "displayname": "10% Staff Discount",
          "discountmethod": 0,
          "discount_method_name": "Percentage Off",
          "discount_method_string": "10% Staff Discount (10.00%)"
        }
      }
    }
  }
}
```