@extends('auth.login-template')

@section('content')

    @if (session('status'))
        <div class="alert alert-success animated bounce">
            {{ session('status') }}
        </div>
    @endif
    <div class="container">

    </div>

    <div class="form">
        <img src="https://nbposstrgprod.blob.core.windows.net/nbwebpubblobs/assets/Access_Newbridge_Dark.png" style="width: 90%; padding-top: 0px; margin-bottom: 10px;"/>
        <h1 style="padding: 40px; font-size: 24px;">Reset Password</h1>
        <form class="login-form" method="POST" action="{{ route('password.update') }}">
            {{ csrf_field() }}
            @if($errors->has('username'))
                <div class="alert">
                    {{ $errors->first('username') }}
                </div>
            @endif

            @if($errors->has('password_confirmation'))
                <div class="alert">
                    {{ $errors->first('password_confirmation') }}
                </div>
            @endif

            @if($errors->has('password'))
                <div class="alert">
                    {{ $errors->first('password') }}
                </div>
            @endif
                {{csrf_field()}}
            <input id="token" type="hidden" name="token" value="{{\Illuminate\Support\Facades\Request::segment(3)}}">
            <input id="username" type="text" class="form-control" name="username" value="{{ $username or old('username') }}" required autofocus placeholder="Username">
            <input id="password" type="password" class="form-control" name="password" required placeholder="New Password">
            <input id="password-confirm" type="password" class="form-control" name="password_confirmation" required placeholder="Confirm New Password">

            <button class="button">reset password</button>

        </form>
    </div>

@endsection