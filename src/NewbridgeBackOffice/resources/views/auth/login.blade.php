@extends('auth.login-template')

@section('content')
    @if(config('newbridge.maintenance'))
        <div class="col-xs-12">
            <div class="alert alert-danger" style="text-align: center; font-weight: bold">
                <strong>We are currently performing maintenance, you will not be able to log in at this time.</strong>
            </div>
        </div>
    @endif
<div class="container">

    <div class="form">
        <img src="https://nbposstrgprod.blob.core.windows.net/nbwebpubblobs/assets/Access_Newbridge_Dark.png" style="width: 90%; padding-top: 0px; margin-bottom: 10px;"/>
        <h1 style="padding: 40px; font-size: 24px;">Login</h1>
        <form class="login-form" action="{{route('login')}}" method="post">
            {{ csrf_field() }}
            @if($errors->has('username'))
            <div class="alert  animated bounce">
                {{$errors->first('username')}}
            </div>
            @endif

            @if($errors->has('password'))
            <div class="alert  animated bounce">
                These credentials do not math our records.
            </div>
            @endif

            <input type="text" placeholder="username" name="username" value="{{ old('username') }}"/>
            <input type="password" placeholder="password" name="password"/>

            <div style="padding: 20px;">
                <input type="checkbox" id="remember" name="remember" {{ old('remember') ? 'checked' : '' }}>
                <label for="remember">Remember Me</label>
            </div>

            <button class="button">login</button>

            <br/><br/>

            <a class="forgot-link" href="{{ route('password.request') }}">
                Forgot Your Password?
            </a>
        </form>
    </div>
</div>
@endsection