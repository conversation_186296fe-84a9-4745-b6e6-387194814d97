@extends('layouts.master')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <div class="panel panel-default">
                    <div class="panel-heading">RELOADING</div>

                    <div class="panel-body">
                        RELOADING DATA
                        <br />
                        <br />
                        <br />
                        <button class="btn btn-md btn-primary">Reload Now</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

@endsection

@push('scripts')

    <script>

        function loading(target) {
            $('<div id="loading"><i class="fa fa-spinner fa-spin" style="margin-top: 80px; font-size: 48px;"></i><br /><h3>Loading data please wait</h3></div>').css({
                position: "absolute",
                'min-height': '200px',
                width: "100%",
                height: "100%",
                "text-align": "center",
                top: 0,
                left: 0,
                background: '#ccc',
                background: 'rgba(999,999,999,1.0)',
                'z-index': 99999,
            }).appendTo($(target).css("position", "relative"));
        }

        var timeLeft = 600;
        var timerId = setInterval(countdown, 1000);

        function countdown() {
            if (timeLeft == 0) {
                clearTimeout(timerId);

                getData();

            } else {
                timeLeft--;
            }
        }

        $('button').on('click', function(){
            getData();
        })

        function getData(){
            loading('.panel-body')
            $.ajax({
                type: "GET",
                url: "/randomDataEdit/data",
                success: function (data) {
                    $('.panel-body #loading').remove()
                    window.location.reload()
                },
                fail: function () {

                }
            });
        }
    </script>

@endpush