<div class="form-group col-md-6"  style="min-height: 80px;">
    <label>Select Terminal (optional)</label><br />
    <?php

        $sites = \NewbridgeWeb\Http\Helpers\SiteHelper::mySites(true, false);

    if(count($sites['sites']) == 1) {
        foreach($sites['sites'] as $site) {
            $report['site'] = $site['site_num'];
        }
    }

    $terminals = \NewbridgeWeb\Repositories\Pos::where('company_id', Auth::user()->company_id);

    if($report['site'] != null) {
        $terminals = $terminals->where('site_num', (int) $report['site']);
    }

    $terminals = $terminals->get();
    ?>

    <select id="terminal" name="terminal[]" multiple class="form-control input">
        @if($report['site'] != null)
            @foreach($terminals as $term)
                <option @if(is_array($report['terminal']) && in_array($term->id, $report['terminal'])) selected @endif value="{{$term->id}}"> {{$term->name}} </option>
            @endforeach
        @endif
    </select>
</div>
