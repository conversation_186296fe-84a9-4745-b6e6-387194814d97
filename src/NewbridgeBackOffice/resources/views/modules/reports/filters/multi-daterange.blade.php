<div class="form-group col-md-6 multi-daterange-area second-picker" style="min-height: 80px;" >
    <label>First Date/Time Range</label>
    <input type="hidden" id="start-comparison" value="{{\Carbon\Carbon::parse($filters['start_compare'])}}" name="start_compare">
    <input type="hidden" id="end-comparison" value="{{\Carbon\Carbon::parse($filters['end_compare'])}}" name="end_compare">
    <div id="reportrange2" style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
        <span>{{\Carbon\Carbon::parse($filters['start_compare'])->format('M d, Y H:i:s')}} - {{\Carbon\Carbon::parse($filters['end_compare'])->format('M d, Y H:i:s')}}</span> <b class="caret"></b>
    </div>
</div>
<div class="form-group col-md-6 multi-daterange-area first-picker" style="min-height: 80px;">
    <label>Second Date/Time Range</label>
    <input type="hidden" class="start" id="start" value="{{\Carbon\Carbon::parse($filters['start'])}}" name="start">
    <input type="hidden" class="end" id="end" value="{{\Carbon\Carbon::parse($filters['end'])}}" name="end">
    <div id="reportrange1" style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
        <span>{{\Carbon\Carbon::parse($filters['start'])->format('M d, Y H:i:s')}} - {{\Carbon\Carbon::parse($filters['end'])->format('M d, Y H:i:s')}}</span> <b class="caret"></b>
    </div>
</div>

<script>
    $('#reportrange1').daterangepicker({
        startDate: '{{\Carbon\Carbon::parse($filters['start'])->format('d/m/Y H:i:s')}}',
        endDate: '{{\Carbon\Carbon::parse($filters['end'])->format('d/m/Y H:i:s')}}',
        autoApply: true,
        timePicker: true,
        timePicker24Hour: true,
        locale: {
            format: 'DD/MM/YYYY HH:mm:ss'
        },
        ranges: {
            'Today': [moment(carbonDate).startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
            'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
            'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
            'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
            'This Month': [moment(carbonDate).startOf('month').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('month').add('{{$filters['offset']}}', 'Hours')],
            'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).subtract(1, 'month').endOf('month').add('{{$filters['offset']}}', 'Hours')]
        }
    });
    $('#reportrange1').on('apply.daterangepicker', function (ev, picker) {
        $('#reportrange1 span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
        $('input.start').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
        $('input.end').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
    });
    $('#reportrange1').on('hide.daterangepicker', function (ev, picker) {
        $('#reportrange1 span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
        $('input.start').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
        $('input.end').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
    });
</script>

<script>
    $('#reportrange2').daterangepicker({
        startDate: '{{\Carbon\Carbon::parse($filters['start'])->format('d/m/Y H:i:s')}}',
        endDate: '{{\Carbon\Carbon::parse($filters['end'])->format('d/m/Y H:i:s')}}',
        autoApply: true,
        timePicker: true,
        timePicker24Hour: true,
        locale: {
            format: 'DD/MM/YYYY HH:mm:ss'
        },
        ranges: {
            'Today, Last Year': [moment(carbonDate).startOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years')],
            'Yesterday, Last Year': [moment(carbonDate).subtract(1, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years')],
            'Last 7 Days, Last Year': [moment(carbonDate).subtract(6, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years')],
            'Last 30 Days, Last Year': [moment(carbonDate).subtract(29, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years')],
            'This Month, Last Year': [moment(carbonDate).startOf('month').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years'), moment(carbonDate).endOf('month').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years')],
            'Last Month, Last Year': [moment(carbonDate).subtract(1, 'month').startOf('month').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years'), moment(carbonDate).subtract(1, 'month').endOf('month').add('{{$filters['offset']}}', 'Hours').subtract(1, 'Years')]
        }
    });
    $('#reportrange2').on('apply.daterangepicker', function (ev, picker) {
        $('#reportrange2 span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
        $('#start-comparison').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
        $('#end-comparison').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
    });
    $('#reportrange2').on('hide.daterangepicker', function (ev, picker) {
        $('#reportrange2 span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
        $('#start-comparison').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
        $('#end-comparison').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
    });
</script>