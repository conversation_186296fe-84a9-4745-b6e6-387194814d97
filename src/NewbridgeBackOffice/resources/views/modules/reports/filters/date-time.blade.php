<div class="form-group col-md-12" style="min-height: 80px;">
    <label>Start / End Date</label>
    <input type="hidden" id="start" value="{{\Carbon\Carbon::parse($filters['start'])->startOfDay()->format('Y-m-d')}} " name="start">
    <input type="hidden" id="end" value="{{\Carbon\Carbon::parse($filters['end'])->startOfDay()->format('Y-m-d')}} " name="end">
    <div id="reportrange"
         style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
        <i class="fa fa-calendar"></i>&nbsp;
        <span>{{\Carbon\Carbon::parse($filters['start'])->startOfDay()->format('M d, Y')}} - {{\Carbon\Carbon::parse($filters['end'])->startOfDay()->format('M d, Y')}}</span> <b class="caret"></b>
    </div>
</div>

<div class="form-group col-md-6" style="min-height: 80px;">
    <label>Start Time</label>
    <div class='input-group time' id='timepicker1'>
        <input type='text' class="form-control" id="start_time" value="{{\Carbon\Carbon::parse($filters['start_time'])->format('H:i:s')}}" name="start_time"/>
        <span class="input-group-addon">
            <span class="fa fa-clock-o"></span>
        </span>
    </div>
</div>

<div class="form-group col-md-6" style="min-height: 80px;">
    <label>End Time</label>
    <div class='input-group time' id='timepicker2'>
        <input type='text' class="form-control" id="end_time" value="{{\Carbon\Carbon::parse($filters['end_time'])->format('H:i:s')}}" name="end_time"/>
        <span class="input-group-addon">
            <span class="fa fa-clock-o"></span>
        </span>
    </div>
</div>

<script>
    $(function () {
        $('#reportrange').daterangepicker({
            startDate: '{{\Carbon\Carbon::parse($filters['start'])->format('d/m/Y')}}',
            endDate: '{{\Carbon\Carbon::parse($filters['end'])->format('d/m/Y')}}',
            autoApply: true,
            timePicker: true,
            locale: {
                format: 'DD/MM/YYYY'
            },
            ranges: {
                'Today': [moment(carbonDate).startOf('day'), moment(carbonDate).endOf('day')],
                'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day'), moment(carbonDate).endOf('day')],
                'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day'), moment(carbonDate).endOf('day')],
                'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day'), moment(carbonDate).endOf('day')],
                'This Month': [moment(carbonDate).startOf('month'), moment(carbonDate).endOf('month')],
                'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month'), moment(carbonDate).subtract(1, 'month').endOf('month')]
            }
        });
        $('#reportrange').on('apply.daterangepicker', function (ev, picker) {
            $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY') + ' - ' + picker.endDate.format('MMMM D, YYYY'));
            $('#start').val(picker.startDate.format('YYYY-MM-DD')+' 00:00:00')
            $('#end').val(picker.endDate.format('YYYY-MM-DD'+' 00:00:00'))
        });

        $('#reportrange').on('hide.daterangepicker', function (ev, picker) {
            $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY') + ' - ' + picker.endDate.format('MMMM D, YYYY'));
            $('#start').val(picker.startDate.format('YYYY-MM-DD')+' 00:00:00')
            $('#end').val(picker.endDate.format('YYYY-MM-DD'+' 00:00:00'))
        });


        $('#timepicker1').datetimepicker({
            format: 'HH:mm:ss'
        });

        $('#timepicker2').datetimepicker({
            format: 'HH:mm:ss'
        });
    });
</script>
