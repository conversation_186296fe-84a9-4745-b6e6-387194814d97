@php use Carbon\Carbon; @endphp
<div class="col-md-6" style="min-height: 80px;" id="daterange-area">
    <label>Start / End Date & Time</label>
    <input type="hidden" id="start" value="{{$filters['start']}}" name="start"/>
    <input type="hidden" id="end" value="{{$filters['end']}}" name="end"/>
    <div id="reportrange" style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
        <span>{{Carbon::parse($filters['start'])->format('M d, Y H:i:s')}} - {{Carbon::parse($filters['end'])->format('M d, Y H:i:s')}}</span>
        <b class="caret"></b>
    </div>
    <script>
        $('#reportrange').daterangepicker({
            startDate: '{{Carbon::parse($filters['start'])->format('d/m/Y H:i:s')}}',
            endDate: '{{Carbon::parse($filters['end'])->format('d/m/Y H:i:s')}}',
            autoApply: true,
            timePicker: true,
            timePicker24Hour: true,
            locale: {
                format: 'DD/MM/YYYY HH:mm:ss'
            },
            ranges: {
                'Today': [moment(carbonDate).startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).subtract(1, 'days').endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'This Month': [moment(carbonDate).startOf('month').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('month').add('{{$filters['offset']}}', 'Hours')],
                'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).subtract(1, 'month').endOf('month').add('{{$filters['offset']}}', 'Hours')]
            }
        });
        $('#reportrange').on('apply.daterangepicker', function (ev, picker) {
            $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
            $('#start').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
            $('#end').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
        });
        $('#reportrange').on('hide.daterangepicker', function (ev, picker) {
            $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
            $('#start').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
            $('#end').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
        });
    </script>
</div>