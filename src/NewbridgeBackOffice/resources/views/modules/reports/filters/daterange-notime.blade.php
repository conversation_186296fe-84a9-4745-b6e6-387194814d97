<div class="form-group col-md-6" style="min-height: 80px;">
    <label>Start / End Date</label>
    <input type="hidden" id="start" value="" name="start">
    <input type="hidden" id="end" value="" name="end">
    <div id="daterange"
         style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
        <span></span> <b class="caret"></b>
    </div>
</div>
<script>
    $('#reportrange').daterangepicker({
        startDate: '{{\Carbon\Carbon::parse($filters['start'])->format('d/m/Y H:i')}}',
        endDate: '{{\Carbon\Carbon::parse($filters['end'])->format('d/m/Y H:i')}}',
        autoApply: true,
        timePicker: true,
        locale: {
            format: 'DD/MM/YYYY'
        },
        ranges: {
            'Today': [moment(carbonDate).startOf('day'), moment(carbonDate).endOf('day')],
            'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day'), moment(carbonDate).endOf('day')],
            'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day'), moment(carbonDate).endOf('day')],
            'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day'), moment(carbonDate).endOf('day')],
            'This Month': [moment(carbonDate).startOf('month'), moment(carbonDate).endOf('month')],
            'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month'), moment(carbonDate).subtract(1, 'month').endOf('month')]
        }
    });
    $('#reportrange').on('apply.daterangepicker', function (ev, picker) {
        $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
        $('#start').value(picker.startDate.format('YYYY-MM-DD')+' 00:00:00')
        $('#end').value(picker.endDate.format('YYYY-MM-DD'+' 00:00:00'))
    });

    $('#reportrange').on('hide.daterangepicker', function (ev, picker) {
        $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
        $('#start').value(picker.startDate.format('YYYY-MM-DD')+' 00:00:00')
        $('#end').value(picker.endDate.format('YYYY-MM-DD'+' 00:00:00'))
    });
</script>
