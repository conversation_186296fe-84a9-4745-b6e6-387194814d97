@extends('layouts.master')

@section('section-name')
    Report Results
@stop

@section('content')
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>

    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/reports" class="">Reports</a></li>
        <li>{{$report['name']}}</li>
    </ol>

    <style>
        .panel.wizard, .panel.wizard .panel-heading {

            border-radius: 0px;
        }

        .panel.wizard .panel-footer {
            min-height: 60px;
            clear: both;
        }

        .panel.wizard .panel-heading .active {
            font-weight: bold;
        }

        .panel.wizard label.col-md-4 {
            line-height: 30px;
        }

        .wizard label {
            font-weight: 700 !important;
        }

        .clear {
            clear: both;
        }

        td {
            word-wrap: break-word !important;
        }
    </style>

    @if(!empty($options))
        <div class="row" data-step="1">
            <div class="col-lg-12">
                <div class="panel panel-primary wizard">

                    <div class="panel-heading">
                        @if($options[0] != "")
                            Update Report Date & Time Options
                        @else
                            Run or Export Report
                        @endif
                    </div>
                    <div class="panel-body">
                        <form>
                            {{ csrf_field() }}
                            <input type="hidden" id="company_id" name="company_id" value="{{Auth::user()->company_id}}">
                            <input type="hidden" id="report_id" name="report_id" value="{{$report['id']}}">

                            @if(in_array('comparison_option', $options))
                                @include('modules.reports.filters.comparison-options')
                            @endif

                            @if(in_array('site', $options))
                                @include('modules.reports.filters.site')
                            @endif

                            @if(in_array('terminal', $options))
                                @include('modules.reports.filters.terminal')
                            @endif

                            @if(in_array('supplier', $options))
                                @include('modules.reports.filters.supplier')
                            @endif

                            @if(in_array('groupby', $options))
                                @include('modules.reports.filters.group-by')
                            @endif

                            @if(in_array('subdepartment', $options))
                                @include('modules.reports.filters.subdepartment')
                            @endif

                            @if(in_array('daypart', $options))
                                @include('modules.reports.filters.day-parts')
                            @endif

                            @if(in_array('department', $options))
                                @include('modules.reports.filters.department')
                            @endif

                            @if(in_array('stockperiod', $options))
                                @include('modules.reports.filters.stockperiod')
                            @endif

                            @if(in_array('daterange', $options))
                                @include('modules.reports.filters.daterange')
                            @endif

                            @if(in_array('daterange-notime', $options))
                                @include('modules.reports.filters.daterange-notime')
                            @endif

                            @if(in_array('date_time', $options))
                                @include('modules.reports.filters.date-time')
                            @endif

                            @if(in_array('week-date', $options))
                                @include('modules.reports.filters.week-date')
                            @endif

                            @if(in_array('week-date-unrestricted', $options))
                                @include('modules.reports.filters.week-date-unrestricted')
                            @endif

                            @if(in_array('end-date', $options))
                                @include('modules.reports.filters.end-date')
                            @endif

                            @if(in_array('payment-method', $options))
                                @include('modules.reports.filters.payment-method')
                            @endif

                            @if(in_array('customer', $options))
                                @include('modules.reports.filters.customer')
                            @endif

                            @if(in_array('loyalty', $options))
                                @include('modules.reports.filters.loyalty-groups')
                            @endif

                            @if(in_array('comparison_option_always', $options))
                                @include('modules.reports.filters.comparison-option-always-on')
                            @endif

                            @if(in_array('gross_net', $options))
                                @include('modules.reports.filters.gross_net')
                            @endif

                            @if(in_array('sale-type', $options))
                                @include('modules.reports.filters.sale-type')
                            @endif

                            @if(in_array('only_abb', $options))
                                @include('modules.reports.filters.only-abb')
                            @endif

                            @if(in_array('comparison_option', $options))
                                <script>
                                    $(function () {

                                        $('.second-picker').hide();

                                        $('#comparisonselect').on('change', function () {

                                            let element = $(this);

                                            if (element.val() == 1) {

                                                $('.second-picker').show();
                                                $('.second-picker').css('min-height', '85px');

                                            } else {
                                                $('.second-picker').hide();
                                                $('.second-picker').css('min-height', '85px');
                                            }
                                        });
                                    })

                                </script>
                            @endif


                            @if(in_array('daterange-date', $options))
                                <div class="form-group col-md-12">
                                    <label>Start / End Date & Time</label>
                                    <input type="hidden" id="start-datetime" value="" name="start">
                                    <input type="hidden" id="end-datetime" value="" name="end">
                                    <div id="reportrange-start-date"
                                         style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
                                        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
                                        <span></span> <b class="caret"></b>
                                    </div>
                                </div>
                            @endif

                            @if(in_array('product-select', $options))
                                @include('modules.reports.filters.product')
                            @endif

                            @if(in_array('payment-methods', $options))
                                @include('modules.reports.filters.payment-methods')
                            @endif


                            @if(in_array('product-select-stock', $options))
                                <div class="form-group col-md-6">
                                    <label>Product</label>
                                    <select name="product" class="form-control input" id="product-select">
                                        @foreach($products as $p)
                                            <option @if($product['id'] == $p->id) selected
                                                    @endif value="{{$p->id}}">{{$p->displayname}}</option>
                                        @endforeach
                                    </select>

                                </div>
                            @endif

                            @if(in_array('multi-daterange', $options))
                                @include('modules.reports.filters.multi-daterange')
                            @endif

                            @if(in_array('clerk', $options))
                                @include('modules.reports.filters.clerk')
                            @endif

                            @if(in_array('sortBy', $options))
                                @include('modules.reports.filters.sortBy')
                            @endif

                            <div class="row">
                                <div class="col-md-12">

                                    <div class="form-group col-md-4">
                                        <button class="btn btn-md btn-success" id="submit-form"><i
                                                    class="fa fa-cogs"></i> Run Report
                                        </button>
                                    </div>

                                    <div class="btn-group col-md-4">
                                        <button type="button" class="btn btn-primary btn-md dropdown-toggle"
                                                data-toggle="dropdown">
                                            <i class="fa fa-external-link"></i> Export Report <span
                                                    class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">
                                            <li><a class="export-form" href="#" data-format="xls"
                                                   data-url="{{$report['url']}}">Export XLS</a>
                                            </li>
                                            <li><a class="export-form" href="#" data-format="csv"
                                                   data-url="{{$report['url']}}">Export CSV</a>
                                            </li>
                                            <li><a class="export-form" href="#" data-format="pdf"
                                                   data-url="{{$report['url']}}">Export PDF</a>
                                            </li>
                                        </ul>
                                    </div>
                                    @if(Auth::user()->company->has_xero === 1 && Request::segment(2) == 'financial-summary')
                                        <div class="form-group col-md-4">
                                            <button class="btn btn-md btn-warning" id="xero-export"><i
                                                        class="fa fa-cloud-upload"></i> Xero Export
                                            </button>
                                        </div>
                                    @endif
                                </div>
                            </div>
                    </form>
                </div>
            </div>
        </div>
    @endif

    <div class="row" id="loading-area" style="display: none;">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-body text-center">
                    <div class="col-md-8 col-md-offset-2">
                        <h2>Loading Report...</h2>
                        <div class="progress">
                            <div class="progress-bar progress-bar-danger progress-bar-striped  active"
                                 role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                 style="width: 1%"></div>
                        </div>
                        <h4 id="loading-text">Starting Report...</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="export-loading-area" style="display: none;">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-body text-center">
                    <div class="col-md-8 col-md-offset-2">
                        <h2>Exporting to Xero...</h2>
                        <div class="progress">
                            <div class="progress-bar progress-bar-danger progress-bar-striped  active"
                                 role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"
                                 style="width: 1%"></div>
                        </div>
                        <h4 id="loading-text">Starting Export...</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="download-area" style="display: none;">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-body text-center">
                    <div class="col-md-8 col-md-offset-2">
                        <h2>Report Export Ready!</h2>
                        <button class="btn btn-success" id="download-button" data-guid=""><i class="fa fa-download"></i>
                            Download Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="xero-area" style="display: none;">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-body text-center">
                    <div class="col-md-8 col-md-offset-2">
                        <h2>Report Export Ready!</h2>
                        <button class="btn btn-success" id="download-button" data-guid=""><i class="fa fa-download"></i>
                            Download Report
                        </button>
                        <h4 id="loading-text">Starting Report...</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-heading">
                    Report Results
                </div>
                <div class="panel-body" id="report-results-area">
                    <h3>Use the run or export buttons above to view data for this report!</h3>
                </div>
            </div>
        </div>
    </div>

@stop

@push('scripts')

    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
    <script src="/js/report_validation/report_validation.js"></script>

    <script>

        let xeroAuthUrl = null;
        let signInAttemptsMax = 1000;
        let currentAttempt = 0;


        $(function () {
            $('select').not('.swal2-select').multiselect({
                disableIfEmpty: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function (event) {
                    $(event.target).find('.filter .multiselect-search').focus();
                },
                enableCollapsibleOptGroups: true,
                buttonWidth: '100%'
            });
        })


        $('#xero-create-link').click(function (e) {
            $.ajax({
                type: "POST",
                url: "/xero/submit-summary",
                data: {data: result},
                success: function (data) {

                    if (data.status == 'success') {

                        clearInterval(timer);

                        $('#auth-link-area').hide();
                        $('#create-invoice-area').show();

                        notificationBar('success', 'Invoice Exported')
                    }
                },
                error: function (data) {
                    data = data.responseJSON
                    notificationBar('warning', data.message)
                }
            })
        });

        function openInNewTab(url) {
            var win = window.open(url, '_blank');
            win.focus();
        }

        $(document).on('click', '.export-link', function (e) {

            e.preventDefault()

            var element = $(this);

            var company_id = {{Auth::user()->company_id}};
            var url = element.data('url');
            var site = $('#site').val();
            var format = element.data('format');

            @if(in_array('week-date', $options))

            var data = {
                'company_id': company_id,
                'url': url,
                'site_num': $('#site').val(),
                'format': element.data('format'),
                'start': moment($('#weekstart').val()).format('YYYY-MM-DD HH:mm:ss')
            }

            @else

            var data = {
                'company_id': company_id,
                'url': url,
                'site_num': $('#site').val(),
                'terminal': $('#terminal').val(),
                'format': element.data('format'),
                'product': $('#product-select').val(),
                'start': moment($('#start-datetime').val()).format('YYYY-MM-DD HH:mm:ss'),
                'end': moment($('#end-datetime').val()).format('YYYY-MM-DD HH:mm:ss'),
                'startcomparison': moment($('#start-datetime-comparison').val()).format('YYYY-MM-DD HH:mm:ss'),
                'endcomparison': moment($('#end-datetime-comparison').val()).format('YYYY-MM-DD HH:mm:ss'),
                'stockperiod': moment($('#stockperiod').val()).format('YYYY-MM-DD HH:mm:ss')
            }

            @if(in_array('stockperiod', $options))
                data.start = moment($('#periodStart').val()).format('YYYY-MM-DD HH:mm:ss');
            data.end = moment($('#periodEnd').val()).format('YYYY-MM-DD HH:mm:ss');
            @endif

            var data = {
                'company_id': company_id,
                'url': url,
                'site_num': $('#site').val(),
                'terminal': $('#terminal').val(),
                'format': element.data('format'),
                'product': $('#product-select').val(),
                'start': moment($('#start-datetime').val()).format('YYYY-MM-DD HH:mm:ss'),
                'end': moment($('#end-datetime').val()).format('YYYY-MM-DD HH:mm:ss'),
                'startcomparison': moment($('#start-datetime-comparison').val()).format('YYYY-MM-DD HH:mm:ss'),
                'endcomparison': moment($('#end-datetime-comparison').val()).format('YYYY-MM-DD HH:mm:ss'),
                'stock_period': moment($('#stockperiod').val()).format('YYYY-MM-DD HH:mm:ss'),
                'start_time': $('#start_time').val(),
                'end_time': $('#end_time').val()

            }


            @if(in_array('stockperiod', $options))
                data.start = moment($('#periodStart').val()).format('YYYY-MM-DD HH:mm:ss');
            data.end = moment($('#periodEnd').val()).format('YYYY-MM-DD HH:mm:ss');
            @endif

                    @if(in_array('daterange-notime', $options))
                data.start = moment($('#periodStart').val()).format('YYYY-MM-DD');
            data.end = moment($('#periodEnd').val()).format('YYYY-MM-DD');
            @endif

                    @if(in_array('week-date', $options))
                data.start = moment($('#datepicker-week').val()).format('YYYY-MM-DD');
            @endif

                    @if(in_array('end-date', $options))
                data.start = moment($('#datetimepicker input').val()).format('YYYY-MM-DD HH:mm:ss');
            @endif


            @endif

            var params = $.param(data);
            $.ajax({
                type: "POST",
                url: "/reports/" + url + "/run",
                data: $(form).serialize(),
                success: function (data) {
                    previous_guid = data;
                    clearTimeout(timer);
                    checkStatus(data);
                },
                fail: function () {
                    clearTimeout(timer);
                    notificationBar('error', 'Report Failed');
                }
            });

            openInNewTab('/reports/' + url + '/export/true/' + format + '?' + params)
        })


        $(document).on('click', '.export-form', function (e) {
            e.preventDefault()
            var url = $(this).data('url');
            var form = $('form')
            var format = $(this).data('format')

            $('#report-results-area').empty();
            $('#report-results-area').hide();
            $('#export-loading-area').hide();

            let downloadArea = $('#download-area')
            let xeroArea = $('#export-loading-area')
            downloadArea.hide();
            xeroArea.hide();

            let loader = $('#loading-area');
            loader.show();
            let progressBar = $('#loading-area .progress-bar');
            let progressText = $('#loading-area #loading-text');

            progressBar.show();

            progressText.text('Starting Report');

            progressBar.removeClass('progress-bar-warning');
            progressBar.removeClass('progress-bar-success');
            progressBar.removeClass('progress-bar-danger');

            progressBar.addClass('progress-bar-danger');

            progressBar.attr('aria-valuenow', 1);
            progressBar.css('width', 10 + '%');

            $.ajax({
                type: "POST",
                url: "/reports/export/true/" + format,
                data: $(form).serialize(),
                success: function (data) {
                    // get returned a file path for the generated file for download
                    checkStatus(data);
                },
                fail: function () {
                    // show an error
                }
            });
        });

        $(document).on('click', '#xero-export', function (e) {

            e.preventDefault();

            var url = $(this).data('url');
            var form = $('form');
            var format = $(this).data('format');

            $('#report-results-area').empty();
            $('#report-results-area').hide();

            let runLoader = $('#loading-area');
            runLoader.hide();

            let exportArea = $('#download-area');
            exportArea.hide();
            let loader = $('#export-loading-area');
            loader.show();

            let progressBar = $('#export-loading-area .progress-bar');
            let progressText = $('#export-loading-area #loading-text');

            progressBar.show();

            progressText.text('Starting Data Export');

            progressBar.removeClass('progress-bar-warning');
            progressBar.removeClass('progress-bar-success');
            progressBar.removeClass('progress-bar-danger');

            progressBar.addClass('progress-bar-danger');

            progressBar.attr('aria-valuenow', 10);
            progressBar.css('width', 10 + '%');

            $.ajax({
                type: "POST",
                url: "/reports/run/false/xero",
                data: $(form).serialize(),
                success: function (data) {

                    if (data.status == 'login') {
                        let progressBar = $('#export-loading-area .progress-bar');
                        let progressText = $('#export-loading-area #loading-text');

                        progressBar.show();

                        progressText.text('Checking Authentication (disable popup blocking)');

                        progressBar.removeClass('progress-bar-warning');
                        progressBar.removeClass('progress-bar-success');
                        progressBar.removeClass('progress-bar-danger');

                        progressBar.addClass('progress-bar-warning');

                        progressBar.attr('aria-valuenow', 50);
                        progressBar.css('width', 50 + '%');

                        window.open(data.url, "mywindow", "menubar=0,resizable=0,width=550,height=450");

                        timer = setInterval(checkTokenSuccess, 1500);

                    }
                    if (data.status == 'success') {
                        clearInterval(timer)

                        let progressBar = $('#export-loading-area .progress-bar');
                        let progressText = $('#export-loading-area #loading-text');

                        progressBar.show();

                        progressText.text('Xero Export Successful');

                        progressBar.removeClass('progress-bar-warning');
                        progressBar.removeClass('progress-bar-success');
                        progressBar.removeClass('progress-bar-danger');

                        progressBar.addClass('progress-bar-success');

                        progressBar.attr('aria-valuenow', 100);
                        progressBar.css('width', 100 + '%');

                        notificationBar('success', 'Data posted to Xero successfully!')
                    }
                    if (data.status !== 'success' && data.status !== 'login') {
                        notificationBar(data.status, data.message);
                    }
                },
                error: function (data) {

                    notificationBar(data.responseJSON.status, data.responseJSON.message)
                }
            });
        });

        function checkTokenSuccess() {
            currentAttempt++;

            let form = $('form');

            if (currentAttempt < signInAttemptsMax) {
                $.ajax({
                    type: "GET",
                    url: "/xero/checkSession",
                    success: function (data) {
                        if (data.status == 'success') {

                            clearInterval(timer);

                            let progressBar = $('#export-loading-area .progress-bar');
                            let progressText = $('#export-loading-area #loading-text');

                            progressBar.show();

                            progressText.text('Posting Data to Xero');

                            progressBar.removeClass('progress-bar-warning');
                            progressBar.removeClass('progress-bar-success');
                            progressBar.removeClass('progress-bar-danger');

                            progressBar.addClass('progress-bar-warning');

                            progressBar.attr('aria-valuenow', 75);
                            progressBar.css('width', 75 + '%');

                            $.ajax({
                                type: "POST",
                                url: "/reports/run/false/xero",
                                data: $(form).serialize(),
                                success: function (data) {
                                    let progressBar = $('#export-loading-area .progress-bar');
                                    let progressText = $('#export-loading-area #loading-text');

                                    progressBar.show();

                                    progressText.text('Xero Export Successful');

                                    progressBar.removeClass('progress-bar-warning');
                                    progressBar.removeClass('progress-bar-success');
                                    progressBar.removeClass('progress-bar-danger');

                                    progressBar.addClass('progress-bar-success');

                                    progressBar.attr('aria-valuenow', 100);
                                    progressBar.css('width', 100 + '%');
                                },
                                fail: function () {
                                    notificationBar('error', 'An error occurred when posting to Xero, please try again.')
                                }
                            });

                        } else {
                            clearInterval(timer);

                            timer = setInterval(checkTokenSuccess, 1500);
                        }
                    },
                    fail: function () {
                        notificationBar('error', 'Xero validation failed')
                    }
                });
            } else {
                clearInterval(timer);
                notificationBar('error', 'Authentication timed out, please try again.')
            }
        }

        // get transactions
        $(document).on('click', '.view-transaction', function () {
            var id = $(this).data('id');
            viewTransaction(id)
        })

        function viewTransaction(id) {
            $.ajax({
                type: "GET",
                url: "/transactions/view/" + id,
                success: function (data) {

                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            }).done(function (data) {
                $('#view-modal-area').html(data)
                $('#ViewModal').modal('toggle')
            });
        }

        let previous_guid = null;
        let timer = null;
        let guid = null;

        $(document).on('click', '#submit-form', function (e) {

            e.preventDefault()

            var url = $(this).data('url');
            var form = $('form')

            console.log($(form).serialize());

            if (isBadRequest())
                return;

            let downloadArea = $('#download-area')
            let xeroArea = $('#export-loading-area')
            downloadArea.hide();
            xeroArea.hide();

            $('#report-results-area').empty();
            $('#report-results-area').hide();

            let loader = $('#loading-area');
            loader.show();
            let progressBar = $('#loading-area .progress-bar');
            let progressText = $('#loading-area #loading-text');

            progressBar.show();

            progressText.text('Starting Report')

            progressBar.removeClass('progress-bar-warning');
            progressBar.removeClass('progress-bar-success');
            progressBar.removeClass('progress-bar-danger');

            progressBar.addClass('progress-bar-danger');

            progressBar.attr('aria-valuenow', 1);
            progressBar.css('width', 10 + '%');


            $.ajax({
                type: "POST",
                url: "/reports/run/true/html",
                data: $(form).serialize(),
                success: function (data) {
                    previous_guid = data;
                    clearTimeout(timer);
                    checkStatus(data);
                },
                fail: function () {
                    clearTimeout(timer);
                    notificationBar('error', 'Report Failed');
                }
            });
        });

        function checkStatus(guid) {
            $.ajax({
                type: "GET",
                url: "/reports/check-status/" + guid,
                success: function (data) {

                    var result = JSON.parse(data)

                    if (result.status !== 'complete') {

                        let loader = $('#loading-area');
                        let progressBar = $('#loading-area .progress-bar')
                        let progressText = $('#loading-area #loading-text');

                        progressText.text(result.text);

                        progressBar.removeClass('progress-bar-warning')
                        progressBar.removeClass('progress-bar-success')
                        progressBar.removeClass('progress-bar-danger')

                        progressBar.addClass('progress-bar-' + result.status)

                        progressBar.attr('aria-valuenow', result.percent)
                        progressBar.css('width', result.percent + '%')

                        timer = setTimeout(function () {
                            checkStatus(guid);
                        }, 500);
                    }
                    if (result.status == 'complete') {
                        let loader = $('#loading-area');
                        loader.hide();
                        let progressBar = $('.progress-bar')

                        progressBar.removeClass('progress-bar-warning')
                        progressBar.removeClass('progress-bar-success')
                        progressBar.removeClass('progress-bar-danger')

                        progressBar.addClass('progress-bar-success')

                        progressBar.attr('aria-valuenow', 100)
                        progressBar.css('width', '100%')

                        showReport(result)
                    }
                    if (result.status == 'complete-download') {
                        let loader = $('#loading-area');
                        loader.hide();
                        let progressBar = $('.progress-bar')
                        let downloadArea = $('#download-area')
                        let downloadButton = $('#download-button')

                        progressBar.removeClass('progress-bar-warning')
                        progressBar.removeClass('progress-bar-success')
                        progressBar.removeClass('progress-bar-danger')

                        progressBar.addClass('progress-bar-success')

                        progressBar.attr('aria-valuenow', 100)
                        progressBar.css('width', '100%')

                        progressBar.hide();

                        downloadArea.show();
                        downloadButton.attr('data-guid', guid)
                        downloadButton.attr('data-path', result.filepath)

                        clearTimeout(timer);
                    }
                },
                fail: function () {

                }
            });
        }

        function showStatus(state) {
            loading('#report-results-area');
        }

        function showReport(data) {
            $('#report-results-area').html(data.data)
            $('#report-results-area').show();
            $('#loading').remove();

            let progressBar = $('.progress-bar')
            let progressText = $('#loading-text');

            progressBar.attr('aria-valuenow', 0)
            progressBar.css('width', '0%')

        }

        @if(in_array('stockperiod', $options))

        $('[name="stockperiod"]').on('change', function () {

            var dates = $(this).val().split('::');
            $('#periodStart').val(dates[0]);
            $('#periodEnd').val(dates[1]);

        })

        @endif

        $(document).on('click', '#download-button', function (e) {

            e.preventDefault();

            let button = $(this);
            let path = button.attr('data-path')

            window.open('/reports/download?path=' + path, "DownloadReport", "menubar=0,resizable=0,width=550,height=450");
        })

        $(document).on('click', '#exportaccounts', function (e) {
            e.preventDefault();
        })

    </script>

@endpush
