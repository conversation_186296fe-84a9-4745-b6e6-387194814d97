@extends('layouts.master')

@section('content')
<div class="content">

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Setup Xero Link</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

<form id="XeroForm" method="POST" action="/company/xero/save">
    {{ csrf_field() }}
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Configure Xero Link<br />
                    <small>Tell us which account payments are made to and which contact to create invoices for.</small>
                </div>
                <div class="panel-body">
                    @foreach($paymentMethods as $pm)
                    <div class="form-group col-xs-6">

                        <label><strong>{{$pm['name']}} Bank Account</strong></label>
                        <select class="form-control multiselect" name="bank_account[{{$pm['id']}}]">
                            <option value="">Select a bank account for {{$pm['name']}} payments</option>
                            <option @if($pm['xerolinks']['bank_account_id'] == 'DONOTPOST') selected @endif value="DONOTPOST">Do Not Post to Xero</option>
                            @foreach($bankAccounts as $account)
                                <option @if($pm['xerolinks']['bank_account_id'] == $account->getAccountID()) selected @endif value="{{$account->getAccountID()}}">{{$account->getName()}}</option>
                            @endforeach
                            @foreach($revenueAccounts as $account)
                                <option @if($companyXero['bank_account_id'] == $account->getCode()) selected @endif value="{{$account->getCode()}}">{{$account->getName()}}</option>
                            @endforeach
                        </select>
                    </div>
                    @endforeach

                    <div class="form-group col-xs-12">

                        <label><strong>Sales Account <span class="text-danger">(Required)</span></strong></label>
                        <select class="form-control multiselect" name="sales_account">
                            <option value="">Select the sales account code from Xero</option>
                            @foreach($revenueAccounts as $account)
                                <option @if($companyXero['sales_account_id'] == $account->getCode()) selected @endif value="{{$account->getCode()}}">{{$account->getName()}}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group col-xs-12">
                        <label><strong>Purchase Account <span class="text-danger">(Required)</span></strong></label>
                        <select class="form-control multiselect" name="purchase_account">
                            <option value="">Select the purchases account code from Xero</option>
                            @foreach($revenueAccounts as $account)
                                <option @if($companyXero['purchase_account_id'] == $account->getCode()) selected @endif value="{{$account->getCode()}}">{{$account->getName()}}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group col-xs-12">
                        <label><strong>Invoice Contact <span class="text-danger">(Required)</span></strong></label>
                        <select class="form-control multiselect" name="invoice_contact">
                            <option value="">Select an contact from Xero</option>
                            @foreach($contacts as $contact)
                                <option @if($companyXero['contact_id'] == $contact->getContactID()) selected @endif  value="{{$contact->getContactID()}}">{{$contact->getName()}}</option>
                            @endforeach
                        </select>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Assign Items & Departments<br />
                    <small>Assign products to departments and other financial records.</small>
                </div>
                <div class="panel-body">

                    @foreach($departments as $department)
                    <div class="form-group col-xs-12" style="border: 1px solid #999888; border-radius: 5px;padding: 10px; position: relative; margin-top: 25px;">
                        <span class="form-group-title" style="position: absolute; top: -24px; left: 0px; font-weight: bold; font-size: 16px;">{{$department->displayname}}</span>
                        <div class="col-xs-6">
                            <label><strong>Invoice Item</strong></label>
                            <select class="form-control multiselect" name="departments[{{$department->id}}]">
                                <option value="">Select an item from Xero</option>
                                @foreach($items as $item)
                                        <option @if($department->acc_code == $item['item_code']) selected @endif value="{{$department->guid}}::{{$item['item_code']}}">{{$item['item_name']}}</option>
                                @endforeach

                            </select>
                        </div>

                        <div class="col-xs-6">
                            <label><strong>Tax Type</strong></label>
                            <select class="form-control multiselect" name="departments_tax[{{$department->id}}]">
                                <option value="">Select a tax type</option>
                                @foreach($taxes as $tax)
                                    <option @if($department->tax_code == $tax->code) selected @endif value="{{$department->guid}}::{{$tax->code}}">{{$tax->description}}</option>
                                @endforeach
                            </select>

                        </div>
                    </div>
                    @endforeach

                    <div class="row">
                        <hr />
                    </div>

                    @foreach($currentLinks as $link)
                    <div class="form-group col-xs-12" style="border: 1px solid #999888; border-radius: 5px;padding: 10px; position: relative; margin-top: 25px;">
                        <span class="form-group-title" style="position: absolute; top: -24px; left: 0px;font-weight: bold; font-size: 16px;">{{$link['displayname']}}</span>
                        <div class="col-xs-6">
                            <label><strong>Invoice Item</strong></label>
                            <select class="form-control multiselect" name="types[]">
                                <option value="">Select an item from Xero</option>
                                @foreach($items as $item)
                                    <option @if($link['acc_code'] == $item['item_code']) selected @endif value="{{$link['id']}}::{{$item['item_code']}}">{{$item['item_name']}}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-xs-6">
                            <label><strong>Tax Rate</strong></label>
                            <select class="form-control multiselect" name="types_tax[]">
                                <option value="">Select a tax type</option>
                                @foreach($taxes as $tax)
                                    <option @if($link['tax_code'] == $tax->code) selected @endif value="{{$tax->code}}" >{{$tax->description}}</option>
                                @endforeach
                            </select>

                        </div>
                    </div>
                    @endforeach

                </div>
                <div class="panel-footer">
                    <button class="btn btn-block btn-success">Save Link Configuration</button>
                </div>
            </div>
        </div>
    </div>

</form>

</div>
@endsection

@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@8"></script>

    <script>
        $(function(){
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                }
            })
        });

        $('body').on('change', '.item-select', function(){
            let option = $(this)

            let department = option.data('department')
            let taxtype = option.data('taxtype')
            let id = option.attr('name')

            $("[name='departments_tax["+ id +"]']").val(taxtype);

        })

        let form = $('#XeroForm');
        $('#XeroForm').submit(function(e){
            e.preventDefault();

            $.ajax({
                type: "POST",
                url: "/company/xero/save",
                data: $(form).serialize(),
                dataType: 'json',
                success: function () {
                    Swal.fire(
                        'Saved',
                        'Your Xero settings have been saved!',
                        'success'
                    );
                },
                error: function (data) {
                    Swal.fire(
                        'Error!',
                        data.responseJSON.message,
                        'error'
                    );
                }
            });
        });


    </script>
@endsection
