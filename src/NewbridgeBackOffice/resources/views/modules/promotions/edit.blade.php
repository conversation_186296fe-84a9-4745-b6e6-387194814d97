@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/promotions" class="">Promotions</a></li>
        <li>Edit Promotion</li>
    </ol>

    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Edit Promotion</h4>
        <hr />
        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab"  data-next="#bins" data-prev="" data-save="true">Promotion Details</a></li>
            <li id="step2"><a href="#bins" data-toggle="tab"                    data-next="#products" data-prev="#details" data-save="true">Bins & Bin Discounts</a></li>
            <li id="step3" @if($promotion['promotiontype'] == 1) style="display: none;" @endif><a href="#products" data-toggle="tab"     data-next="" data-prev="#bins" data-save="true">Product Discounts</a></li>
        </ul>
    </div>

    <form id="promotionForm">
        <div class="tab-content">
            {{ csrf_field() }}
            @include('includes.nav-buttons')

            <div class="col-xs-12">
                <div class="alert alert-danger" id="save-warning">Unable to save until the product level discounts have loaded.</div>
            </div>

            <div class="tab-pane active" id="details">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Basic Promotion Information
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <input type="hidden" value="1" name="site_num"/>
                                        <label><strong>Promotion Name</strong>
                                            <br /><small>Give the promotion a name for the interface.</small></label>
                                        <input type="text" class="form-control" name="display_name" value="{{$promotion->displayname}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Receipt Text</strong>
                                            <br /><small>What should be displayed on the reciept?</small></label>
                                        <input type="text" class="form-control" name="receipt_text" value="{{$promotion->receipttext}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Short Description</strong>
                                            <br /><small>What short code should be applied to the check view?</small></label>
                                        <input type="text" class="form-control" name="shortdesc" value="{{$promotion->short_desc}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Promotion Type</strong><br /><small>Choose either 'Mix and Match' which allows you to choose from a range of products or 'Set Menu'.</small></label>
                                        <select class="form-control" name="promotiontype">
                                            <option value="0" @if($promotion->promotiontype == 0)selected @endif>Set Menu</option>
                                            <option value="1" @if($promotion->promotiontype == 1)selected @endif>Mix And Match</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>App Available</strong></label>
                                        <select class="form-control" name="app_available">
                                            <option value="0" @if($promotion->app_available == 0) selected @endif>No</option>
                                            <option value="1" @if($promotion->app_available == 1) selected @endif>Yes</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label><strong>Promotion Description</strong><br /><small>Enter a description for this promotion that will be displayed to customers on certain platforms.</small></label>
                                        <textarea name="description" id="description" class="form-control" cols="30" rows="10">{{$promotion->description}}</textarea>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>

                    <!-- /.panel -->
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Discount Information
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Discount Type</strong><br /><small>What type of discount should be applied to the promotion?</small></label>
                                        <select class="form-control" name="discount_method">
                                            @if($promotion->discount !== null)
                                                <option value="">None</option>
                                                <option value="0" @if($promotion->discount->discountmethod == 0) selected @endif>Percentage Off</option>
                                                <option value="1" @if($promotion->discount->discountmethod == 1) selected @endif>Fixed Amount Off</option>
                                                <option value="2" @if($promotion->discount->discountmethod == 2) selected @endif>Set Price</option>
                                            @else
                                                <option selected value="">None</option>
                                                <option value="0">Percentage Off</option>
                                                <option value="1">Fixed Amount Off</option>
                                                <option value="2">Set Price</option>
                                            @endif
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Discount Value</strong>
                                            <br /><small>How much discount should be given or if using a set price, what is total amount for the items in this promotion?</small></label>
                                            <input name="value" class="form-control" value="{{$promotion->discount !== null ? $promotion->discount->value : ''}}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- /.panel -->
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Dates and Times
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Start Date</strong>
                                            <br /><small>What date will the promotion start?</small></label>
                                        <div class='input-group date' id=''>
                                            <input type='text' class="form-control" name="start_date" value="{{\Carbon\Carbon::parse($promotion->startdate)->format('d/m/Y')}}">
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>End Date</strong>
                                            <br /><small>What date will the promotion end?</small></label>
                                        <div class='input-group date' id=''>
                                            <input type='text' class="form-control" name="end_date" value="{{\Carbon\Carbon::parse($promotion->enddate)->format('d/m/Y')}}">
                                            <span class="input-group-addon">
                                                        <span class="glyphicon glyphicon-calendar"></span>
                                                    </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Start Time</strong>
                                            <br /><small>On each day what time will the promotion start?</small></label>
                                        <div class='input-group time' id=''>
                                            <input type='text' class="form-control" name="start_time" value="{{$promotion->starttime}}">
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-time"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>End Time</strong>
                                            <br /><small>On each day what time will the promotion end?</small></label>
                                        <div class='input-group time' id=''>
                                            <input type='text' class="form-control" name="end_time" value="{{$promotion->endtime}}">
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-time"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Available on Days</strong><br /><small>Which days between the above dates is the promotion active?</small></label>
                                        <select class="form-control" name="days[]" multiple>
                                            <option value="1" @if($promotion->daysofweek & 1) selected @endif>Monday</option>
                                            <option value="2" @if($promotion->daysofweek & 2) selected @endif>Tuesday</option>
                                            <option value="4" @if($promotion->daysofweek & 4) selected @endif>Wednesday</option>
                                            <option value="8" @if($promotion->daysofweek & 8) selected @endif>Thursday</option>
                                            <option value="16" @if($promotion->daysofweek & 16) selected @endif>Friday</option>
                                            <option value="32" @if($promotion->daysofweek & 32) selected @endif>Saturday</option>
                                            <option value="64" @if($promotion->daysofweek & 64) selected @endif>Sunday</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Up-Selling</strong>
                                        <br /><small>Would you like to prompt staff when this promotion is available after selecting items?</small></label>
                                        <select title="select upselling mode" class="form-control" name="upsell">
                                            <option @if($promotion->upsell == 0) selected @endif value="0">False</option>
                                            <option @if($promotion->upsell == 1) selected @endif value="1">True</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="bins">
                <div class="col-md-12">
                    <div class="panel panel-primary" id="singleBinSelection" @if($promotion->promotiontype == 0) style="display: none;" @endif>
                        <div class="panel-heading">
                            Promotion Items
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-12"><p>Choose products in the drop down below that you would like to include in the mix-and-match promotion</p></div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Mix and Match Quantity</strong>
                                            <br /><small>How many items from the mix-n-match will trigger this promotion?</small></label>
                                        <input class="form-control" name="mask" type="number" min="1" value="{{$promotion->mask}}"/>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <style>
                                        .multiselect-group label, .multiselect-group-clickable label {
                                            font-weight: 900 !important;
                                            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
                                        }
                                    </style>
                                    <label><strong>Mix and Match Products</strong><br /><small>Choose your products to be included in the mix and match from the dropdown.</small></label>
                                    <?php
                                    if(!$promotion->promotion_bins->isEmpty() && $promotion->promotiontype == 1) {
                                        $items = $promotion->promotion_bins[0]->promotion_items->toArray();
                                        $items_array = array_column($items, 'PLU_CommandUID');
                                    } else {
                                        $items = [];
                                        $items_array = [];
                                    }
                                    ?>
                                    <select title="select products" class="form-control" name="mix_bin[]" multiple="multiple">
                                        @foreach($sub_departments as $dept)
                                            @if(count($dept->products) > 0)
                                                <optgroup label="{{$dept->displayname}}">
                                                    @foreach($dept->products as $p)
                                                        <option value="{{$p->guid}}" @if(in_array($p->CommandUID, $items_array)) selected @endif>{{$p->displayname}}</option>
                                                    @endforeach
                                                </optgroup>
                                            @endif
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel panel-primary" id="multipleBinSelection" @if($promotion->promotiontype == 1) style="display: none;" @endif>
                        <div class="panel-heading">
                            Promotion Items <br />
                            <small>Create groups of products that will make up this set meal using the form below, use the "Add Product Group" button to create additional selections</small>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                @if(!$promotion->promotion_bins->isEmpty())
                                    <div id="groups">
                                        @foreach($promotion->promotion_bins as $k => $bin)
                                        <?php
                                            if($promotion->promotion_bins !== null) {
                                                $items = $promotion->promotion_bins[$k]->promotion_items->toArray();
                                                $items_array = array_column($items, 'PLU_CommandUID');
                                            } else {
                                                $items = [];
                                                $items_array = [];
                                            }
                                    $group_num = $k + 1;
                                    ?>
                                        <div class="col-md-12" @if($loop->first) id="group-selection-initial" @else id="group-selection-{{$group_num}}" @endif style="border-bottom: 1px solid #e1e1e1; padding-bottom: 20px;">
                                            <div class="col-md-3">
                                                <label class="groupLabel"><strong>Group Name</strong> </label>
                                                <input type="text" class="input input-md form-control group-name" name="groupName[{{$group_num}}]" placeholder="e.g. Starters" data-num="{{$group_num}}" value="{{$bin->displayname}}">
                                            </div>
                                            <div class="col-md-3 product-selection">
                                                <label class="groupLabel"><strong>Product Group {{$group_num}}</strong> </label>
                                                <select class="form-control products" name="groupProducts[{{$group_num}}]" multiple data-num="{{$group_num}}">
                                                    @foreach($sub_departments as $k => $dept)
                                                        @if(count($dept->products) > 0)
                                                            <optgroup label="{{$dept->displayname}}">
                                                                @foreach($dept->products as $p)
                                                                    <option @if(in_array($p->guid, $items_array) && $promotion->promotiontype == 0) selected @endif value="{{$p->guid}}">{{$p->displayname}}</option>
                                                                @endforeach
                                                            </optgroup>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="groupLabel"><strong>Bin Discount Type</strong> </label>
                                                <select class="form-control discount-type" name="groupProductsDiscountType[{{$group_num}}]" data-num="{{$group_num}}">
                                                    <option @if(isset($bin->discount) && $bin->discount == null) selected @endif value="">None</option>
                                                    <option @if(isset($bin->discount) && $bin->discount !== null && $bin->discount->discountmethod == 0) selected @endif value="0">Percentage Off</option>
                                                    <option @if(isset($bin->discount) && $bin->discount !== null && $bin->discount->discountmethod == 1) selected @endif value="1">Fixed Amount Off</option>
                                                    <option @if(isset($bin->discount) && $bin->discount !== null && $bin->discount->discountmethod == 2) selected @endif value="2">Set Price</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="groupLabel"><strong>Bin Discount Value</strong> </label>
                                                <input type="number" class="input input-md form-control discount-value" name="groupProductsDiscountValue[{{$group_num}}]" placeholder="e.g. 10.00" data-num="{{$group_num}}" @if(isset($bin->discount)) value="{{$bin->discount->value}}" @endif>
                                            </div>
                                            <div class="col-md-1 delete" @if($group_num === 1) style="display: none;" @endif>
                                                <label><strong>Delete</strong></label>
                                                <div class="btn btn-md btn-danger delete-group" data-num="{{$group_num}}">Delete</div>
                                            </div>
                                        </div>
                                        <div style="clear: both;"></div>
                                        @endforeach
                                    </div>
                                @else
                                    <div id="groups">
                                        <div class="col-md-12" id="group-selection-initial" style="border-bottom: 1px solid #e1e1e1; padding-bottom: 20px;">
                                            <div class="col-md-4 product-selection">
                                                <label class="groupLabel"><strong>Product Group 1</strong> </label>
                                                <select class="form-control products" name="groupProducts[1]" multiple data-num="1">
                                                    @foreach($sub_departments as $dept)
                                                        @if(count($dept->products) > 0)
                                                            <optgroup label="{{$dept->displayname}}">
                                                                @foreach($dept->products as $p)
                                                                    <option value="{{$p->guid}}">{{$p->displayname}}</option>
                                                                @endforeach
                                                            </optgroup>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="groupLabel"><strong>Bin Discount Type</strong> </label>
                                                <select class="form-control discount-type" name="groupProductsDiscountType[1]" data-num="1">
                                                    <option value="">None</option>
                                                    <option value="0">Percentage Off</option>
                                                    <option value="1">Fixed Amount Off</option>
                                                    <option value="2">Set Price</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="groupLabel"><strong>Bin Discount Value</strong> </label>
                                                <input type="number" class="input input-md form-control discount-value" name="groupProductsDiscountValue[1]" placeholder="e.g. 10.00" data-num="1">
                                            </div>
                                            <div class="col-md-1 delete" style="display: none;">
                                                <label><strong>Delete</strong></label>
                                                <div class="btn btn-md btn-danger delete-group">Delete</div>
                                            </div>
                                        </div>
                                        <div style="clear: both;"></div>
                                    </div>
                                @endif

                                <div class="col-md-12" style="margin-top: 30px;">
                                    <button class="btn btn-md btn-success" id="add-bin">Add Product Group</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="tab-pane" id="products">
                <div class="col-xs-12 alert alert-warning">If you have added products to any of the product groups, please save and re-load the promotion to edit the product level discounts.</div>
                <div id="product-discount-html-area">

                    <h3>Loading Data, Please wait</h3>

                </div>
            </div>

            <div style="clear: both; height: 100px;"></div>
        </div>
    </form>

@stop



@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <script>

        function loadProductsForm(){

            console.log($('#promotiontype').val());

            let form = $('#promotionForm');

            $('#nav-save').hide();

            @if($promotion['promotiontype'] == 1)
            $('#nav-save').show();
            $('#save-warning').hide();
            @endif
            if($('[name="promotiontype"]').val() == "0") {

                let data = $(form).serialize();

                console.log('product discounts loading')

                let groups = [];

                for ($i = 1; $i <= $('.group-name').length; $i++) {
                    groups.push({
                        products: $('.products[data-num="' + $i + '"]').val(),
                        discount_value: $('#groups .discount-value[data-num="' + $i + '"]').val(),
                        discount_type: $('#groups .discount-type[data-num="' + $i + '"]').val(),
                    });
                }

                let error = false;

                _.each(groups, function (group) {

                    if (group.products.length == 0) {
                        notificationBar('error', 'Each bin must contain at least one product!');
                        error = true;
                    }
                });

                if (error) {
                    $('#product-discount-html-area').html('');
                    return false;
                }

                $.ajax({
                    type: "POST",
                    url: "/promotions/product-html/{{Request::segment(3)}}",
                    data: {data: data, groups: groups},
                    success: function (data) {
                        $('#product-discount-html-area').html(data.html);
                        $('#nav-save').show();
                        $('#save-warning').hide();
                    }
                }).fail(function (data) {
                    notificationBar(data.responseJSON.status, data.responseJSON.message)
                });
            }
        }

        $(document).on('click', '#quick_apply_button', function(e){
            e.preventDefault();

            var sub_department = $('#quick_apply_sub_department').val();
            var discount_type = $('#quick_apply_product_type_discount').val();
            var discount_value = $('#quick_discount_value').val();

            if(!sub_department && !discount_type && !discount_value){
                return;
            }
            $('select[data-subdepartmentguid="'+sub_department+'"').each(function(){
                $(this).val(discount_type);
            });
            $('input[data-subdepartmentguid="'+sub_department+'"').each(function(){
                $(this).val(discount_value);
            });
        });

        loadProductsForm();

        $.validator.addMethod(
            "startEndCheck",
            function(value, element) {

                if($(element).attr('name') == 'start_date'){
                    var start = moment($(element).val(), 'DD-MM-YYYY');
                    var end = moment($('[name="end_date"]').val(), 'DD-MM-YYYY')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date'){
                    var start = moment($('[name="start_date"]').val(), 'DD-MM-YYYY');
                    var end = moment($(element).val(), 'DD-MM-YYYY')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'start_time'){

                    var start = moment($(element).val(), 'hh:mm:ss');
                    var end = moment($('[name="end_time"]').val(), 'hh:mm:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_time'){
                    var start = moment($('[name="start_time"]').val(), 'hh:mm:ss');
                    var end = moment($(element).val(), 'hh:mm:ss')

                    return start.isBefore(end);
                }
            },
            "We are ending before we have begun!"
        );

        var group_num = {{count($promotion->promotion_bins)+1}};

        $('select').not('.swal2-select').multiselect({
            buttonWidth: '100%',
            includeSelectAllOption: true,
            enableClickableOptGroups: true,
            enableCollapsibleOptGroups: true,
            collapseOptGroupsByDefault: true,
            enableCaseInsensitiveFiltering: true,
            dropup: true,
            onChange: function (option, checked, select) {
                // if (checked == true) {
                //     $('#DisplayName').val($(option).text())
                // }
            },
            onDropdownShown: function(event) {
                $(event.target).find('.filter input').focus();
            },
            onDropdownHidden: function(event) {

            }
        });

        $(document).on('change', '[name="promotiontype"]', function(){
            let type = $(this).val();

            if(type == 0){
                loadProductsForm();
            }
        });

            $(document).on('click', '.goto', function(e){
                e.preventDefault();
                var tab = $(this).data('goto')

                $('#tabs a[href="#'  + tab + '"]').tab('show')
            });

            // validate signup form on keyup and submit
            $("form").validate({
                ignore: [],
                rules: {
                    display_name: {
                        "required" : true,
                        "minlength" : 3,
                        normalizer: function(value) {
                            return $.trim(value);
                        }
                    },
                    receipt_text: {
                        "required" : true,
                        "minlength" : 3,
                        normalizer: function(value) {
                            return $.trim(value);
                        }
                    },
                    shortdesc: {
                        "required" : true,
                        "minlength" : 3,
                        normalizer: function(value) {
                            return $.trim(value);
                        }
                    },
                    type: "required",
                    start_date: {
                        required: true,
                        startEndCheck: true
                    },
                    end_date: {
                        required: true,
                        startEndCheck: true
                    },
                    start_time: {
                        required: true,
                        startEndCheck: true
                    },
                    end_time: {
                        required: true,
                        startEndCheck: true
                    },
                    'mix_bin[]': {
                        required: function(element){
                            return $('[name="promotiontype"]').val() == 1;
                        }
                    },
                    'groupProducts[]': {
                        required: function(element){
                            return $('[name="promotiontype"]').val() == 0;
                        }
                    },
                    days: "required"
                },
                messages: {
                    'groupProducts1[]': {
                        required: "Please select at least one product"
                    },
                    'mix_bin[]': {
                        required: "Please select at least one product"
                    }
                },
                invalidHandler: function(e, validator){

                    console.log(validator.errorList)

                    if(validator.errorList.length)
                        $('#tabs a[href="#' + jQuery(validator.errorList[0].element).closest(".tab-pane").attr('id') + '"]').tab('show')
                },
                submitHandler: function (form) {

                    console.log($('[name="promotiontype"]:selected').val());

                    if($('[name="promotiontype"]').val() == "1" && $('[name="mix_bin[]"]').val() == '')
                    {
                        notificationBar('error', 'Please choose at least one product to discount')
                        return false;
                    }

                    if($('[name="promotiontype"]').val() == "0" && $('[name="groupProducts[1]"]').val() == '')
                    {
                        notificationBar('error', 'Please choose at least one product to discount')
                        return false;
                    }

                    let formData = $('#promotionForm');
                    let data = formData.serialize();
                    let groups = [];

                    for($i = 1; $i <= group_num; $i++)
                    {
                        groups.push({
                            products: $('.products[data-num="'+$i+'"]').val(),
                            discount_value: $('#groups .discount-value[data-num="'+$i+'"]').val(),
                            discount_type: $('#groups .discount-type[data-num="'+$i+'"]').val(),
                            group_name: $('#groups .group-name[data-num="'+$i+'"]').val()
                        });
                    }

                    groups = groups;


                    $.ajax({
                        type: "PUT",
                        url: "/promotions/edit-single/{{Request::segment(3)}}",
                        data: {data: data, groups: groups},
                        success: function () {
                            notificationBar('success', 'Promotion Updated', '/promotions')
                        }
                    }).fail(function(data) {
                        notificationBar(data.responseJSON.status, data.responseJSON.message)
                    });
                }
            });

        $(function(){

            $('.date').datetimepicker({
                format: 'DD/MM/YYYY',
                toolbarPlacement: 'top'
            });

            $('.time').datetimepicker({
                format: 'HH:mm:ss',
                toolbarPlacement: 'top'
            });

        })

        $('body').on('click', '#add-bin', function(e){

            e.preventDefault();

            addGroup()
        });

        function addGroup(){

            $('#groups').append('<div class="col-xs-12" style="height: 20px;" id="spacer-'+group_num+'"></div>');

            var newGroup = $('#group-selection-initial').clone();
            newGroup.attr('id', 'group-selection-'+group_num);

            newGroup.find('.products').attr('data-num', group_num)
            newGroup.find('.products').val('');
            newGroup.find('.products').attr('name', 'groupProducts['+group_num+']');

            newGroup.find('.discount-value').attr('data-num', group_num);
            newGroup.find('.discount-value').val('');
            newGroup.find('.discount-value').attr('name', 'groupProductsDiscountValue['+group_num+']');

            newGroup.find('.group-name').attr('data-num', group_num);
            newGroup.find('.group-name').val('');
            newGroup.find('.group-name').attr('name', 'groupName['+group_num+']');

            newGroup.find('.discount-type').attr('data-num', group_num);
            newGroup.find('.discount-type').val('');
            newGroup.find('.delete').val('');
            newGroup.find('.discount-type').attr('name', 'groupProductsDiscountType['+group_num+']');

            newGroup.find('.delete').show();
            newGroup.find('.delete').val('');
            newGroup.find('.delete-group').attr('data-num', group_num);

            newGroup.find('.btn-group').remove();
            newGroup.find('.product-selection .groupLabel').html('<strong>Product Group '+group_num+'</strong>')

            $('#groups').append(newGroup)

            $('select[data-num="'+group_num+'"]').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableClickableOptGroups: true,
                enableCollapsibleOptGroups: true,
                collapseOptGroupsByDefault: true,
                enableCaseInsensitiveFiltering: true
            });

            group_num++;
        }

        $('body').on('click', '.delete-group', function(e){

            e.preventDefault();

            var element = $(this).data('num');

            group_num--;

            $('#spacer-'+element).remove()
            $('#group-selection-'+element).remove()

        })

        $('body').on('change', '[name="promotiontype"]', function(e){
            // if the selection is 0 show only the product multi selects and department multi selects
            var element = $(this)

            if(element.val() == '0'){
                $('[name="promotiontype"]').val("0")
                $('#step3').show();
                $('#singleBinSelection').hide()
                $('#multipleBinSelection').show()
            }

            if(element.val() == 1){
                $('[name="promotiontype"]').val(1)
                $('#step3').hide();
                $('#singleBinSelection').show()
                $('#multipleBinSelection').hide()
            }

            //if the selection is 1, show create bins and create new dropdowns for each bin.
        });

    </script>
@stop
