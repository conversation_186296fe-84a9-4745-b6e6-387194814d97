<div id="EditModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                {{--<h4 class="modal-title">Edit Promotion ({{$product->name}}</h4>--}}
                <h4>Edit Promotion ({{$promotion->displayname}})</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="EditForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Promotion Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <input type="hidden" value="1" name="site_num"/>
                                            <div class="form-group">
                                                <label><strong>Promotion Name</strong>
                                                    <br /><small>Give the promotion a name for the interface.</small></label>
                                                <input type="text" class="form-control" name="display_name" value="{{$promotion->displayname}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Receipt Text</strong>
                                                    <br /><small>What should be displayed on the reciept?</small></label>
                                                <input type="text" class="form-control" name="receipt_text" value="{{$promotion->receipttext}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Short Description</strong>
                                                    <br /><small>What short code should be applied to the check view?</small></label>
                                                <input type="text" class="form-control" name="shortdesc" value="{{$promotion->short_desc}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Promotion Type</strong><br /><small>Choose either 'Mix and Match' which allows you to choose from a range of products or 'Set Menu'.</small></label>
                                                <select class="form-control" name="type">
                                                    <option value="0" @if($promotion->promotiontype == 0) selected @endif>Set Price</option>
                                                    <option value="1" @if($promotion->promotiontype == 1) selected @endif>Mix And Match</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- /.panel -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Discount Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Discount Type</strong><br /><small>What type of discount should be applied to the promotion?</small></label>
                                                <select class="form-control" name="discount_method">
                                                    <option value="0" @if($promotion->discount->discountmethod == 0) selected @endif>Percentage Off</option>
                                                    <option value="1" @if($promotion->discount->discountmethod == 1) selected @endif>Fixed Amount Off</option>
                                                    <option value="2" @if($promotion->discount->discountmethod == 2) selected @endif>Set Price</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Discount Value</strong>
                                                    <br /><small>How much discount should be given or if using a set price, what is total amount for the items in this promotion?</small></label>
                                                <input name="value" class="form-control" value="{{$promotion->discount->value}}">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- /.panel -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Dates and Times
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Start Date</strong>
                                                    <br /><small>What date will the promotion start?</small></label>
                                                <div class='input-group date' id=''>
                                                    <input type='text' class="form-control" name="start_date" value="{{\Carbon\Carbon::parse($promotion->startdate)->format('d/m/Y')}}">
                                                    <span class="input-group-addon">
                                                        <span class="glyphicon glyphicon-calendar"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>End Date</strong>
                                                    <br /><small>What date will the promotion end?</small></label>
                                                <div class='input-group date' id=''>
                                                    <input type='text' class="form-control" name="end_date" value="{{\Carbon\Carbon::parse($promotion->enddate)->format('d/m/Y')}}">
                                                    <span class="input-group-addon">
                                                        <span class="glyphicon glyphicon-calendar"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Start Time</strong>
                                                    <br /><small>On each day what time will the promotion start?</small></label>
                                                <div class='input-group time' id=''>
                                                    <input type='text' class="form-control" name="start_time" value="{{$promotion->starttime}}">
                                                    <span class="input-group-addon">
                                                        <span class="glyphicon glyphicon-time"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>End Time</strong>
                                                    <br /><small>On each day what time will the promotion end?</small></label>
                                                <div class='input-group time' id=''>
                                                    <input type='text' class="form-control" name="end_time" value="{{$promotion->endtime}}">
                                                    <span class="input-group-addon">
                                                        <span class="glyphicon glyphicon-time"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Available on Days</strong><br /><small>Which days betweemn the above dates is the promotion active?</small></label>
                                                <select class="form-control" name="days[]" multiple>
                                                    <option value="1" @if($promotion->daysofweek & 1) selected @endif>Monday</option>
                                                    <option value="2" @if($promotion->daysofweek & 2) selected @endif>Tuesday</option>
                                                    <option value="4" @if($promotion->daysofweek & 4) selected @endif>Wednesday</option>
                                                    <option value="8" @if($promotion->daysofweek & 8) selected @endif>Thursday</option>
                                                    <option value="16" @if($promotion->daysofweek & 16) selected @endif>Friday</option>
                                                    <option value="32" @if($promotion->daysofweek & 32) selected @endif>Saturuday</option>
                                                    <option value="64" @if($promotion->daysofweek & 64) selected @endif>Sunday</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Up-Selling</strong>
                                                    <br /><small>Would you like to prompt staff when this promotion is available after selecting items?</small></label>
                                                <select title="select upselling mode" class="form-control" name="upsell">
                                                    <option @if($promotion->upsell == 0) selected @endif value="0">False</option>
                                                    <option @if($promotion->upsell == 1) selected @endif value="1">True</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- /.panel -->
                            <div class="panel panel-default" id="singleBinSelection"  @if($promotion->promotiontype == 0) style="display:none;" @endif>
                                <div class="panel-heading">
                                    Mix and Match Product Selection
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-12"><p>Choose products in the drop down below that you would likle to include in the mix-amd-match promotion</p></div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Mix and Match Quantity</strong>
                                                    <br /><small>How many items from the mix-n-match will trigger this promotion?</small></label>
                                                <input class="form-control" name="mask" type="number" min="1" value="{{$promotion->mask}}"/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <style>
                                                .multiselect-group label, .multiselect-group-clickable label {
                                                    font-weight: 900 !important;
                                                    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
                                                }
                                            </style>
                                            <label><strong>Mix and Match Products</strong><br /><small>Choose your products to be included in the mix and match from the dropdown.</small></label>
                                            <?php
                                            if($promotion->promotion_bins !== null) {
                                                $items = $promotion->promotion_bins[0]->promotion_items->toArray();
                                                $items_array = array_column($items, 'PLU_CommandUID');
                                            } else {
                                                $items = [];
                                                $items_array = [];
                                            }
                                            ?>
                                            <select title="select products" class="form-control" name="mix_bin[]" multiple="multiple">
                                                @foreach($sub_departments as $dept)
                                                    @if(count($dept->products) > 0)
                                                        <optgroup label="{{$dept->displayname}}">
                                                            @foreach($dept->products as $p)
                                                                <option value="{{$p->guid}}" @if(in_array($p->CommandUID, $items_array)) selected @endif>{{$p->displayname}}</option>
                                                            @endforeach
                                                        </optgroup>
                                                    @endif
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- /.panel -->
                            <div class="panel panel-default" id="multipleBinSelection" @if($promotion->promotiontype == 1) style="display:none;" @endif>
                                <div class="panel-heading">
                                    Set Meal Product Selection
                                </div>
                                <div class="panel-body">
                                    <div class="col-md-12">
                                        <p>Create groups of products that will make up this set meal using the form below, use the "Add Group" button to create additional selections..</p>
                                    </div>

                                    <div class="row" id="groups">
                                        @if(count($promotion->promotion_bins) > 0)
                                            @for($i = 0; $i < count($promotion->promotion_bins); $i++)

                                                <?php
                                                $items = $promotion->promotion_bins[$i]->promotion_items->toArray();
                                            $items_array = array_column($items, 'PLU_CommandUID');
                                            ?>

                                                <div class="col-md-6 @if($i == 0) group-selection-initial @else group-selection-{{$i+1}} @endif" style="padding: 20px;">
                                                    <label class="groupLabel"><strong>Product Group {{$i+1}}</strong> @if($i > 0) (<a href="#" class="delete-group" data-num="{{$i+1}}">delete</a>) @endif </label>
                                                    <select class="form-control" name="groupProducts{{$i+1}}[]" multiple data-num="{{$i+1}}">

                                                        @foreach($sub_departments as $dept)
                                                            @if(count($dept->products) > 0)
                                                                <optgroup label="{{$dept->displayname}}">
                                                                    @foreach($dept->products as $p)
                                                                        <option @if(in_array($p->CommandUID, $items_array)) selected @endif value="{{$p->guid}}">{{$p->displayname}}</option>
                                                                    @endforeach
                                                                </optgroup>
                                                            @endif
                                                        @endforeach
                                                    </select>
                                                </div>
                                            @endfor
                                        @endif
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <button class="btn btn-md btn-success" id="add-bin">Add Product Group</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="editForm">Update</button>
            </div>
        </div>
    </div>
</div>

<script>

    var group_num = {{count($promotion->promotion_bins)+1}};
    $('body').on('click', '#EditModal #add-bin', function(e){

        e.preventDefault();


        addEditGroup()
    })

    function addEditGroup(){
        var newGroup = $('#EditModal .group-selection-initial').clone()


        newGroup.removeClass('group-selection-initial');
        newGroup.addClass('group-selection-'+group_num);
        newGroup.find('select').attr('data-num', group_num)
        newGroup.find('select').attr('name', 'groupProducts'+group_num+'[]')
        newGroup.find('select option').removeAttr('selected')
        newGroup.find('.btn-group').remove();
        newGroup.find('.groupLabel').html('<strong>Product Group '+group_num+'</strong> (<a href="#" class="delete-group" data-num="'+group_num+'">delete</a>)')

        $('#EditModal #groups').append(newGroup)

        $('#EditModal select[data-num="'+group_num+'"]').multiselect({
            buttonWidth: '100%',
            includeSelectAllOption: true,
            enableClickableOptGroups: true,
            enableCollapsibleOptGroups: true,collapseOptGroupsByDefault: true,
            enableCaseInsensitiveFiltering: true
        });

        group_num++;
    }

    $('body').on('click', '#EditModal .delete-group', function(e){

        e.preventDefault();

        var element = $(this).data('num');

        $('#EditModal .group-selection-'+element).remove()

    })

    $('body').on('change', '#EditModal [name="type"]', function(e){
        // if the selection is 0 show only the product multi selects and department multi selects
        var element = $(this)

        if(element.val() == 0){
            $('#singleBinSelection').hide()
            $('#multipleBinSelection').show()
        }

        if(element.val() == 1){
            $('#singleBinSelection').show()
            $('#multipleBinSelection').hide()
        }

        //if the selection is 1, show create bins and create new dropdowns for each bin.
    })

    $(function(){

        $('#EditModal .date').datetimepicker({
            format: 'DD/MM/YYYY',
            toolbarPlacement: 'top'
        });

        $('#EditModal .time').datetimepicker({
            format: 'HH:mm:ss',
            toolbarPlacement: 'top'
        });

    })

    $('#editForm').on('click', function(){
        $('#EditForm').submit()
    });

    var form = $('#EditForm');

    $.validator.addMethod(
        "startEndCheck",
        function(value, element) {

            if($(element).attr('name') == 'start_date'){
                var start = moment($(element).val(), 'DD-MM-YYYY');
                var end = moment($('[name="end_date"]').val(), 'DD-MM-YYYY')

                return start.isBefore(end);
            }

            if($(element).attr('name') == 'end_date'){
                var start = moment($('[name="start_date"]').val(), 'DD-MM-YYYY');
                var end = moment($(element).val(), 'DD-MM-YYYY')

                return start.isBefore(end);
            }

            if($(element).attr('name') == 'start_time'){

                var start = moment($(element).val(), 'hh:mm:ss');
                var end = moment($('[name="end_time"]').val(), 'hh:mm:ss')

                return start.isBefore(end);
            }

            if($(element).attr('name') == 'end_time'){
                var start = moment($('[name="start_time"]').val(), 'hh:mm:ss');
                var end = moment($(element).val(), 'hh:mm:ss')

                return start.isBefore(end);
            }
        },
        "We are ending before we have begun!"
    );

    // validate signup form on keyup and submit
    $("#EditForm").validate({
        rules: {
            display_name: "required",
            receipt_text: "required",
            shortdesc: "required",
            type: "required",
            discount_method: "required",
            value: {
                required: true,
                number: true
            },
            start_date: {
                required: true,
                startEndCheck: true
            },
            end_date: {
                required: true,
                startEndCheck: true
            },
            start_time: {
                required: true,
                startEndCheck: true
            },
            end_time: {
                required: true,
                startEndCheck: true
            },
            days: "required"
        },
        messages: {

        },
        submitHandler: function (form) {
            $.ajax({
                type: "PUT",
                url: "/promotions/edit-single/{{$promotion->id}}",
                data: $(form).serialize(),
                success: function () {
                    $('#EditModal').modal('toggle')
                    table.ajax.reload();
                }
            }).fail(function(data) {
                $('#EditModal').modal('toggle')
                notificationBar(data.responseJSON.status, data.responseJSON.message)
            });
        }
    });

</script>
