@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/promotions" class="">Promotions</a></li>
        <li>Edit Promotion</li>
    </ol>

    <div class="row">
    <div class="col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Promotion Image</h4>
        <hr/>
        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab" data-next="" data-prev=""
                                             data-save="true">Images</a></li>
        </ul>
    </div>
    </div>



        <div class="tab-content">
            <form method="POST" id="form" action="/promotions/save-image/{{$promotion->id}}" enctype="multipart/form-data">
                {{ csrf_field() }}

            <div class="tab-pane active" id="details">

                <div class="panel panel-primary">
                    <div class="panel-heading">
                        Promotion Image
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6 col-md-offset-3">
                                <div class="form-group">

                                    <img src="{{$promotion->image_url != null ? $promotion->image_url : 'https://via.placeholder.com/600x200'}}" id="preview-image"
                                         class="img img-responsive mt20" style="width: 600px; height: 200px; margin: 0 auto;">

                                    <label for="promotion-image" class="file-upload btn btn-primary btn-block rounded-pill shadow">
                                        <i class="fa fa-upload mr-2"></i> Browse for file ...
                                        <input id="promotion-image" name="promotion-image" class="upload-image" type="file" style="display: none;">
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel-footer">
                        <button class="btn btn-success btn-block">Upload Images</button>
                    </div>
                </div>
            </div>

            </form>
        </div>

@endsection


@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <script>
        $(function () {
            function readURL(input) {
                if (input.files && input.files[0]) {
                    var reader = new FileReader();

                    reader.onload = function (e) {
                        $('#preview-image').attr('src', e.target.result);
                    }

                    reader.readAsDataURL(input.files[0]); // convert to base64 string
                }
            }

            $("#promotion-image").change(function () {
                readURL(this);
            });

        });
    </script>

@endsection
