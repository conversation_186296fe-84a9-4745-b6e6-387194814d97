<div id="EditModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Edit Role ({{$role['display_name']}})</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="EditForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Role Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <input type="hidden" value="{{$role['id']}}" name="id"/>
                                                <label><strong>Display Name</strong></label>
                                                <input type="text" class="form-control" name="display_name" required value="{{$role['display_name']}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Role Permissions
                                </div>
                                <div class="panel-body">
                                    @foreach($permissions as $p)
                                        @if(count($p->permissions) > 0)
                                            <div class="col-md-12 row">
                                                <div class="col-xs-2">
                                                    <label style="margin-top: 5px;">
                                                        <input type="checkbox" data-toggle="toggle" name="group_permission" value="{{$p['id']}}" />
                                                    </label>
                                                </div>
                                                <div class="col-xs-8">
                                                    <h4>{{$p->name}} <small>Toggle All</small></h4>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <hr />
                                                @foreach($p->permissions as $perm)
                                                    <div class="col-md-6 col-xs-6">
                                                        <div class="form-group">
                                                            <div class="checkbox">
                                                                <label>
                                                                    <input type="checkbox" @if(in_array($perm['id'], $perms)) checked @endif data-group="{{$perm['group']}}" data-toggle="toggle" name="permissions[]" value="{{$perm['id']}}">
                                                                    {{$perm['display_name']}} <i class="fa fa-info-circle" data-toggle="tooltip" title="{{$perm['displayname']}}"></i>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                            <div class="col-md-12">
                                                <hr />
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="editSubmit">Update</button>
            </div>
        </div>
    </div>
</div>

<script>

    $(function(){
        $('[data-toggle="tooltip"]').tooltip();
        $('[type="checkbox"]').bootstrapToggle();
    })

    $('#editSubmit').on('click', function(){
        $('#EditForm').submit()
    })

    var form = $('#EditForm')

    // validate signup form on keyup and submit
    $("#EditForm").validate({
        rules: {
            display_name: "required",
            acc_code: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "PUT",
                url: "/roles/edit-single",
                data: $(form).serialize(),
                success: function () {
                    $('#EditModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    })

    function selectAllGroup(group, on){

        if(on === false) {
            $('#EditModal input[data-group="' + group + '"]').bootstrapToggle('on')
            $('#EditModal input[data-group="'+group+'"]').attr('checked', 'checked');
        } else {
            $('#EditModal input[data-group="' + group + '"]').bootstrapToggle('off')
            $('#EditModal input[data-group="'+group+'"]').removeAttr('checked');
        }
    }

    $('#EditModal [name="group_permission"]').change(function(){
        var group = $(this).val();
        var on = $(this).parent().hasClass('off');

        selectAllGroup(group, on);
    })
</script>