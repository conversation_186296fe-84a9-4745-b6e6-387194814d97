@extends('layouts.master')

@section('section-name') Role Management @stop

@section('content')
    <script>
        var table = null;
    </script>
    <div class="section">
        <div class="row">
            <h3 style="margin-bottom: 20px;">User Role Management</h3>
        </div>
        <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
            <thead>
            <tr>
                <th>ID</th>
                <th>Display Name</th>
                <th>Description</th>
            </tr>
            </thead>
        </table>
    </div>

    <div id="edit-modal-area"></div>
    @include('modules.roles.includes.create-modal')

@stop

@push('scripts')
<script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
<script src="/js/dataTables.editor.min.js"></script>

<script>
    var permissions = {};
    permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge', 'write_roles') == 1 ? 1 : 0 }};
    permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge', 'write_roles') == 1 ? 1 : 0 }};
    permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge', 'write_roles') == 1 ? 1 : 0 }};
</script>

<script src="/js/roles.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

@endpush
