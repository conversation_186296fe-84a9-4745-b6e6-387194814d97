<div id="EditModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Edit Setting Category</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="EditForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <input type="hidden" name="id" value="{{$header->id}}">
                                                <label><strong>Category Name</strong></label>
                                                <input type="text" class="form-control" name="value" value="{{$header->value}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.panel-body -->
                            </div>
                            <!-- /.panel -->
                        </div>
                        <!-- /.col-lg-12 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="editSubmit">Update</button>
            </div>
        </div>
    </div>
</div>

<script>
    $('#editSubmit').on('click', function(){
        $('#EditForm').submit()
    });

    var form = $('#EditForm');

    // validate signup form on keyup and submit
    $("#EditForm").validate({
        rules: {
            value: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "PUT",
                url: "/setting-categories/edit-single",
                data: $(form).serialize(),
                success: function () {
                    $('#EditModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    });
</script>