@extends('layouts.master')

@section('content')
    {{-- Dashboard Page title--}}
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Update Printer Names</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row" style="padding-bottom: 30px;">
        @if (count($errors) > 0)
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <form role="form" id="form" method="post" action="/company/printers/{{$site}}">
            {{ csrf_field() }}
            <div class="panel panel-default">
                <div class="panel-heading">
                    Printer Names
                </div>
                <div class="panel-body">

                    {{csrf_field()}}
                    <div class="form-group col-md-6">
                        <label>Printer 1</label>
                        <input name="printers[]" required class="form-control" value="{{isset($printers[0]) ? $printers[0]->name : 'Kitchen Printer 1'}}">
                    </div>
                    <div class="form-group col-md-6">
                        <label>Printer 2</label>
                        <input name="printers[]" required class="form-control" value="{{isset($printers[1]) ? $printers[1]->name : 'Kitchen Printer 1'}}">
                    </div>
                    <div class="form-group col-md-6">
                        <label>Printer 3</label>
                        <input name="printers[]" required class="form-control" value="{{isset($printers[2]) ? $printers[2]->name : 'Kitchen Printer 1'}}">
                    </div>
                    <div class="form-group col-md-6">
                        <label>Printer 4</label>
                        <input name="printers[]" required class="form-control" value="{{isset($printers[3]) ? $printers[3]->name : 'Kitchen Printer 1'}}">
                    </div>
                    <div class="form-group col-md-6">
                        <label>Printer 5</label>
                        <input name="printers[]" required class="form-control" value="{{isset($printers[4]) ? $printers[4]->name : 'Kitchen Printer 1'}}">
                    </div>
                    <div class="form-group col-md-6">
                        <label>Printer 6</label>
                        <input name="printers[]" required class="form-control" value="{{isset($printers[5]) ? $printers[5]->name : 'Kitchen Printer 1'}}">
                    </div>


                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-md-2 pull-right text-right">
                            <button class="btn btn-success">Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <style>
        input.error {
            border: 1px solid red !important;
        }
        .error {
            font-weight: normal !important;
            color: red !important;
        }
    </style>

@stop
