<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>A File</title>

    <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400;500;600&display=swap" rel="stylesheet">

    <style>
        @page { margin: 100px 25px; }
        header { position: fixed; top: -60px; left: 0px; right: 0px; height: 50px; text-align: center; font-family: 'Roboto Slab', serif;}
        header .title { font-family: 'Roboto Slab', serif; width: 100%; text-align: center;}
        footer { position: fixed; bottom: -60px; left: 0px; right: 0px; height: 50px; font-family: 'Roboto Slab', serif; }
        h1, h2, h3, h4 { font-family: 'Roboto Slab', serif; }
        p { page-break-after: always; }
        p:last-child { page-break-after: never; }
        p:first-of-type { page-break-after: never; }
    </style>
</head>
<body>
<header><span class="title">Newbridge Software - Table Ordering QR Codes</span></header>
<footer>Generated on {{\Carbon\Carbon::now()->format('d/m/Y h:ia')}}</footer>

<main>
    @foreach($tables as $location)
    @if(!$location->tables->isEmpty())
    @php
        $columns = 2;
        $rows = floor($location->tables->count() / $columns);
        $left_over = $location->tables->count() % $columns;
        $counter = 0;
        $innerCount = 0;
    @endphp
    <p>
        <table style="width: 100%">
            @while($counter < $rows)
            <tr>
                @for($i = 1; $i <= $columns; $i++)
                <td style="border: 1px solid black; text-size: 12px;">
                    <table>
                    <tr>
                        <td><img style="width: 100px" src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://ourmenus.co.uk/{{$site->domain}}/home/<USER>" alt=""></td>
                        <td style="padding: 10px; font-family: 'Roboto Slab', serif; font-weight: 600;"> Scan the QR code with your phone to start your order!</td>
                    </tr>
                    <tr>
                        <td colspan="2" style="border-top: 1px dashed darkgray; text-align: center; font-family: 'Roboto Slab', serif; font-weight: 400;">{{$location->displayname}} - {{$location->tables[$innerCount]->displayname}}</td>
                    </tr>
                    </table>
                </td>
                @php $innerCount++; @endphp
                @endfor
            </tr>

                @php $counter++; @endphp
            @endwhile
            @if($left_over > 0)
                <tr>
                    <td style="border: 1px solid black; text-size: 12px;">
                        <table>
                            <tr>
                                <td><img style="width: 100px" src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=https://ourmenus.co.uk/{{$site->domain}}/home/<USER>" alt=""></td>
                                <td style="padding: 10px; font-family: 'Roboto Slab', serif; font-weight: 600;"> Scan the QR code with your phone to start your order!</td>
                            </tr>
                            <tr>
                                <td colspan="2" style="border-top: 1px dashed darkgray; text-align: center; font-family: 'Roboto Slab', serif; font-weight: 400;">{{$location->displayname}} - {{$location->tables[$innerCount]->displayname}}</td>
                            </tr>
                        </table>
                    </td>
                    <td>

                    </td>
                </tr>
                    @php $innerCount++; @endphp
            @endif
                @php $innerCount = 0; @endphp
        </table>
    </p>
    @endif
    @endforeach
</main>

</body>
</html>