@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/company/sites" class="">Manage Sites</a></li>
        <li>Edit Sites</li>
    </ol>

    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Edit Site ({{$site->site_name}}/{{$site->site_num}}/{{$site->id}})</h4>
        <hr />

        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab" data-next="#ourmenus" data-prev="" data-save="true">Site Details</a></li>
            <li id="step2"><a href="#ourmenus" data-toggle="tab"  data-next="" data-prev="#details" data-save="true">OurMenu Options</a></li>
        </ul>

    </div>

    <form id="EditForm" action="/company/sites/edit-single" method="POST" enctype="multipart/form-data">
        {{ csrf_field() }}
        <div class="tab-content">
            {{ csrf_field() }}
            @include('includes.nav-buttons')

            <div class="tab-pane active" id="details">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            Site Information
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <input type="hidden" name="id" value="{{$site->id}}">
                                        <label><strong>Site Name</strong></label>
                                        <input type="text" class="form-control" name="site_name" required value="{{$site->site_name}}"/>
                                    </div>
                                </div>
                                <hr />
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Address Line 1</strong></label>
                                        <input type="text" class="form-control" name="address_line1" value="{{$site->address_line1}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Address Line 2</strong></label>
                                        <input type="text" class="form-control" name="address_line2" value="{{$site->address_line2}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>City/Town</strong></label>
                                        <input type="text" class="form-control" name="city" value="{{$site->city}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>County</strong></label>
                                        <input type="text" class="form-control" name="county" value="{{$site->county}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Postcode</strong></label>
                                        <input type="text" class="form-control" name="postcode" value="{{$site->postcode}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Telephone Number</strong></label>
                                        <input type="text" class="form-control" name="telephone" value="{{$site->telephone}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Timezone</strong></label>
                                        <select name="timezone_id" id="timezone_id" class="form-control">
                                            <option value="">Select a timezone</option>
                                            @foreach(\NewbridgeWeb\Repositories\Timezone::all() as $timezone)
                                                <option value="{{ $timezone->id }}" @if($site->timezone_id == $timezone->id) selected @endif>{{ $timezone->timezone }} (GMT{{ $timezone->gmt_offset >= 0 ? '+' : '' }}{{ $timezone->gmt_offset }})</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <hr />
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>PMS Access Token</strong></label>
                                        <input type="text" class="form-control" name="mews_access_token"  value="{{$site->mews_access_token}}"/>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>External Site ID</strong></label>
                                        <input type="text" class="form-control" name="external_id"  value="{{$site->external_id}}"/>
                                    </div>
                                </div>
                            </div>
                            @if(Auth::user()->hasRole(['newbridge', 'reseller']))
                            <div class="form-group col-sm-12 col-md-6">
                                <label><strong>Allowed Modules</strong><br><small>Activate Modules for the customer site.</small></label>
                                <select name="allowed_modules[]" id="allowed_modules" class="form-control" multiple>
                                    @foreach(\NewbridgeWeb\Repositories\Newbridge\Modules::all() as $module)
                                        <option @if($site->allowed_modules & $module->bit_value) selected @endif value="{{$module->bit_value}}">{{$module->module_name}}</option>
                                    @endforeach
                                </select>

                                <p class="help-block"></p>
                            </div>
                            @else
                                <input type="hidden" name="allowed_modules" value="{{$site->allowed_modules}}">
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="ourmenus">
                @if($site->company->active_integrations & 16)

                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Our Menus URL<br />
                                <small>Define the URL for your OurMenus website.</small>
                            </div>
                            <div class="panel-body">
                                <div class="input-group mb15">
                                    <span class="input-group-addon">https://ourmenus.co.uk/</span>
                                    <input type="text" class="form-control" id="subdomain" value="{{$site->domain}}" name="domain">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                OurMenus Header Image
                            </div>
                            <div class="panel-body">
                                <label for="header-image"
                                       class="file-upload btn btn-primary btn-block rounded-pill shadow"><i
                                            class="fa fa-upload mr-2"></i> Browse for file ...
                                    <input id="header-image" name="header-image" class="upload-image" type="file"
                                           style="display: none;">
                                </label>
                                <img src="{{$site->ourmenus_image}}" id="preview-logo" class="img img-responsive mt20">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                OurMenus Colours
                            </div>
                            <div class="panel-body">
                                <div class="col-xs-12 col-sm-6 col-md-6">
                                    <label>Primary Colour:</label>
                                    <input style="padding: 0px;" type="color" name="ourmenus_colour1" id="colorpicker1" class="form-control"
                                           placeholder="#FFFFFF" value="{{$site->ourmenus_colour1 ? $site->ourmenus_colour1 : '#FFF'}}">
                                </div>
                                <div class="col-xs-12 col-sm-6 col-md-6">
                                    <label>Secondary Colour:</label>
                                    <input style="padding: 0px;" type="color" name="ourmenus_colour2" id="colorpicker2" class="form-control"
                                           placeholder="#FFFFFF" value="{{$site->ourmenus_colour2 ? $site->ourmenus_colour2 : '#FFF'}}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                Opening Hours
                            </div>
                            <div class="panel-body">
                                <div class="col-xs-12"><button class="btn btn-success" id="add-opening">Add Opening Times</button></div>
                                <div id="table-area">
                                    @include('modules.company.sites.time-table')
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            @else

                <h1>Contact support to enable OurMenus ordering website for your business</h1>

            @endif
            </div>

        </div>


    </form>
    <div id="modalArea"></div>
    @include('modules.company.sites.includes.opening-hours-modal')
@stop

@push('scripts')

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>

    <script>
        $(function(){

            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true, onDropdownShown: function (event) {
                    $(event.target).find('.filter input').focus();
                },
                onChange: function (option, checked, select) {

                }
            });

            $('body').on('click', '#add-opening', function(e){
                e.preventDefault();
                $('#OpeningHoursModal').modal('show');

            });

            $('#submit').on('click', function (e) {
                e.preventDefault();

                let form = $('#AddOpeningTimesForm');
                $.ajax({
                    type: "POST",
                    url: "/company/site/{{$site->id}}/add-opening-hours",
                    data: form.serialize(),
                    success: function(data){
                        $('#OpeningHoursModal').modal('hide');
                        $('#table-area').html(data.html)
                        notificationBar('success', 'Times updated!')
                    },
                    error: function(data){
                        notificationBar('error', 'Failed to add opening hours, please check and try again.')
                    }
                })
            });

            $('body').on('click', '.edit-times',  function (e) {
                e.preventDefault();

                let id= $(this).data('id');
                $.ajax({
                    type: "GET",
                    url: "/company/update-opening-times-modal/"+id,
                    success: function(data){
                        $('#modalArea').html(data.html);
                        $('#EditOpeningHoursModal').modal('show');
                    },
                    error: function(data){
                        $('#EditOpeningHoursModal').modal('close');
                        notificationBar('error', 'Failed to get opening hours, please check and try again.')
                    }
                })
            });

            $('body').on('click', '#edit-submit',  function (e) {
                e.preventDefault();

                let form = $('#EditOpeningTimesForm');
                $.ajax({
                    type: "POST",
                    url: "/company/update-opening-hours/"+$(this).data('id'),
                    data: form.serialize(),
                    success: function(data){
                        $('#EditOpeningHoursModal').modal('hide');
                        notificationBar('success', 'Opening times updated')
                        $('#table-area').html(data.html)
                    },
                    error: function(data){
                        $('#EditOpeningHoursModal').modal('close');
                        notificationBar('error', 'Failed to add opening hours, please check and try again.')
                    }
                })
            });

            $('body').on('click', '.delete-times',  function (e) {
                e.preventDefault();

                let id= $(this).data('id');
                $.ajax({
                    type: "POST",
                    url: "/company/delete-opening-hours/"+$(this).data('id'),
                    success: function(data){
                        notificationBar('success', 'Opening times deleted')
                        $('#table-area').html(data.html)
                    },
                    error: function(data){
                        notificationBar('error', 'Failed to delete opening hours, please check and try again.')
                    }
                })
            });

            $('body').on('click', '.default-times',  function (e) {
                e.preventDefault();

                let id= $(this).data('id');
                $.ajax({
                    type: "POST",
                    url: "/company/default-opening-hours/"+$(this).data('id'),
                    success: function(data){
                        notificationBar('success', 'Opening times made primary')
                        $('#table-area').html(data.html)
                    },
                    error: function(data){
                        notificationBar('error', 'Failed to make opening hours primary, please check and try again.')
                    }
                })
            });

            $('#subdomain').on('input', function (e) {

                let data = {domain: $(this).val(), "_token": "{{ csrf_token() }}"}
                $.ajax({
                    type: "POST",
                    url: "/company/site/{{$site->id}}/check-domain",
                    data: data
                })
                    .done(function (data) {

                        if (data.status == 'success') {

                            $('#subdomain').parent().addClass('has-success')
                        } else {
                            $('#subdomain').parent().addClass('has-error')
                        }
                    })
                    .fail(function () {
                        //alert( "error" );
                    })
                    .always(function () {
                        //alert( "complete" );
                    });
            });


            function readURL(input) {
                if (input.files && input.files[0]) {
                    var reader = new FileReader();

                    reader.onload = function (e) {
                        $('#preview-logo').attr('src', e.target.result);
                    }

                    reader.readAsDataURL(input.files[0]); // convert to base64 string
                }
            }

            $("#header-image").change(function () {
                readURL(this);
            });
        })
    </script>
@endpush