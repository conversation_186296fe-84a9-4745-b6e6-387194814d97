<div id="EditOpeningHoursModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Opening Hours</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="EditOpeningTimesForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label><strong>Period Name</strong></label>
                                    <input type="text" class="form-control" name="name" value="{{isset($hours) ? $hours['name'] : ''}}"/>
                                </div>
                                <div class="form-group">
                                    <label><strong>Price Level</strong></label>
                                    <select name="price_level" id="price_level" class="form-control">
                                        <option @if($hours['price_level'] == '') selected @endif  value="">Not Price Level Timing</option>
                                        <option @if($hours['price_level'] ==1) selected @endif value="1">{{isset($sellingPriceNames[0]) ? $sellingPriceNames[0]->name : 'Selling Price 1' }}</option>
                                        <option @if($hours['price_level'] ==2) selected @endif value="2">{{isset($sellingPriceNames[1]) ? $sellingPriceNames[1]->name : 'Selling Price 2' }}</option>
                                        <option @if($hours['price_level'] ==3) selected @endif value="3">{{isset($sellingPriceNames[2]) ? $sellingPriceNames[2]->name : 'Selling Price 3' }}</option>
                                        <option @if($hours['price_level'] ==4) selected @endif value="4">{{isset($sellingPriceNames[3]) ? $sellingPriceNames[3]->name : 'Selling Price 4' }}</option>
                                        <option @if($hours['price_level'] ==5) selected @endif value="5">{{isset($sellingPriceNames[4]) ? $sellingPriceNames[4]->name : 'Selling Price 5' }}</option>
                                    </select>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-primary">
                                        <thead>
                                        <tr>
                                            <th colspan="2">Day</th>
                                            <th>Open</th>
                                            <th>Close</th>
                                        </tr>
                                        </thead>
                                        @if(!isset($hours['times'][1]))
                                        <tr>
                                            <td style="width: 150px">Monday</td>
                                            <td><input type="checkbox" value="1" name="days[1]"></td>
                                            <td><input type="time" class="form-control" name="open[1]"/></td>
                                            <td><input type="time" class="form-control" name="close[1]"/></td>
                                        </tr>
                                        @else
                                            <tr>
                                                <td style="width: 150px">Monday</td>
                                                <td><input type="checkbox" value="1" name="days[1]" checked></td>
                                                <td><input type="time" class="form-control" name="open[1]" value="{{$hours['times'][1]['open']}}"/></td>
                                                <td><input type="time" class="form-control" name="close[1]" value="{{$hours['times'][1]['close']}}"/></td>
                                            </tr>

                                        @endif
                                        @if(!isset($hours['times'][2]))
                                            <tr>
                                                <td>Tuesday</td>
                                                <td><input type="checkbox" value="2" name="days[2]"></td>
                                                <td><input type="time" class="form-control" name="open[2]"/></td>
                                                <td><input type="time" class="form-control" name="close[2]"/></td>
                                            </tr>
                                        @else
                                            <tr>
                                                <td style="width: 150px">Tuesday</td>
                                                <td><input type="checkbox" value="2" name="days[2]" checked></td>
                                                <td><input type="time" class="form-control" name="open[2]" value="{{$hours['times'][2]['open']}}"/></td>
                                                <td><input type="time" class="form-control" name="close[2]" value="{{$hours['times'][2]['close']}}"/></td>
                                            </tr>
                                        @endif

                                        @if(!isset($hours['times'][4]))
                                        <tr>
                                            <td>Wednesday</td>
                                            <td><input type="checkbox" value="4" name="days[4]"></td>
                                            <td><input type="time" class="form-control" name="open[4]"/></td>
                                            <td><input type="time" class="form-control" name="close[4]"/></td>
                                        </tr>
                                        @else
                                            <tr>
                                                <td style="width: 150px">Wednesday</td>
                                                <td><input type="checkbox" value="4" name="days[4]" checked></td>
                                                <td><input type="time" class="form-control" name="open[4]" value="{{$hours['times'][4]['open']}}"/></td>
                                                <td><input type="time" class="form-control" name="close[4]" value="{{$hours['times'][4]['close']}}"/></td>
                                            </tr>
                                        @endif

                                        @if(!isset($hours['times'][8]))
                                        <tr>
                                            <td>Thursday</td>
                                            <td><input type="checkbox" value="8" name="days[8]"></td>
                                            <td><input type="time" class="form-control" name="open[8]"/></td>
                                            <td><input type="time" class="form-control" name="close[8]"/></td>
                                        </tr>
                                        @else
                                            <tr>
                                                <td style="width: 150px">Thursday</td>
                                                <td><input type="checkbox" value="8" name="days[8]" checked></td>
                                                <td><input type="time" class="form-control" name="open[8]" value="{{$hours['times'][8]['open']}}"/></td>
                                                <td><input type="time" class="form-control" name="close[8]" value="{{$hours['times'][8]['close']}}"/></td>
                                            </tr>
                                        @endif

                                        @if(!isset($hours['times'][16]))
                                        <tr>
                                            <td>Friday</td>
                                            <td><input type="checkbox" value="16" name="days[16]"></td>
                                            <td><input type="time" class="form-control" name="open[16]"/></td>
                                            <td><input type="time" class="form-control" name="close[16]"/></td>
                                        </tr>
                                        @else
                                            <tr>
                                                <td style="width: 150px">Friday</td>
                                                <td><input type="checkbox" value="16" name="days[16]" checked></td>
                                                <td><input type="time" class="form-control" name="open[16]" value="{{$hours['times'][16]['open']}}"/></td>
                                                <td><input type="time" class="form-control" name="close[16]" value="{{$hours['times'][16]['close']}}"/></td>
                                            </tr>
                                        @endif

                                        @if(!isset($hours['times'][32]))
                                        <tr>
                                            <td>Saturday</td>
                                            <td><input type="checkbox" value="32" name="days[32]"></td>
                                            <td><input type="time" class="form-control" name="open[32]"/></td>
                                            <td><input type="time" class="form-control" name="close[32]"/></td>
                                        </tr>
                                        @else
                                            <tr>
                                                <td style="width: 150px">Saturday</td>
                                                <td><input type="checkbox" value="32" name="days[32]" checked></td>
                                                <td><input type="time" class="form-control" name="open[32]" value="{{$hours['times'][32]['open']}}"/></td>
                                                <td><input type="time" class="form-control" name="close[32]" value="{{$hours['times'][32]['close']}}"/></td>
                                            </tr>
                                        @endif

                                        @if(!isset($hours['times'][64]))
                                        <tr>
                                            <td>Sunday</td>
                                            <td><input type="checkbox" value="64" name="days[64]"></td>
                                            <td><input type="time" class="form-control" name="open[64]"/></td>
                                            <td><input type="time" class="form-control" name="close[64]"/></td>
                                        </tr>
                                        @else
                                            <tr>
                                                <td style="width: 150px">Sunday</td>
                                                <td><input type="checkbox" value="64" name="days[64]" checked></td>
                                                <td><input type="time" class="form-control" name="open[64]" value="{{$hours['times'][64]['open']}}"/></td>
                                                <td><input type="time" class="form-control" name="close[64]" value="{{$hours['times'][64]['close']}}"/></td>
                                            </tr>
                                        @endif



                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="edit-submit" data-id="{{$hours['id']}}">Save</button>
            </div>
        </div>
    </div>
</div>