<table class="table table-condensed table-primary">
    <thead>
    <tr>
        <th>Name</th>
        <th>Hours</th>
        <th>Default</th>
        <th>Actions</th>
    </tr>
    </thead>
    <tbody>
    @foreach($openings as $opening)
        <tr>
            <td>{{$opening->name}}</td>
            <td>
                @foreach($opening->times as $time)
                    {{\NewbridgeWeb\Http\Helpers\OpeningHours::dayBitToName($time->day)}} [{{$time->open}}] - [{{$time->close}}]<br />
                @endforeach
            </td>
            <td>
                @if($opening->default == 1)
                    Yes
                @else
                    No
                @endif
            </td>
            <td>
                <button class="btn btn-sm btn-primary edit-times"  data-id="{{$opening->id}}">Edit</button>
                <button class="btn btn-sm btn-danger delete-times"  data-id="{{$opening->id}}">Delete</button>
                @if($opening->default != 1)
                    <button class="btn btn-sm btn-warning default-times" data-id="{{$opening->id}}">Make Default</button>
                @endif
            </td>
        </tr>
    @endforeach
    </tbody>
</table>