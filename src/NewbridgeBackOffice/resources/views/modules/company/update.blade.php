@extends('layouts.master')

@section('content')
    {{-- Dashboard Page title--}}
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Update {{$company->company_name}}</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row" style="padding-bottom: 30px;">
        @if (count($errors) > 0)
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <form role="form" id="registerCompanyForm" method="POST">
            {{ csrf_field() }}
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Company Details
                </div>
                <div class="panel-body">
                    <div class="col-lg-12">
                        {{csrf_field()}}
                        <div class="form-group col-md-6">
                            <label>Company Name</label>
                            <input name="company_name" required class="form-control" value="{{$company->company_name}}">
                        </div>
                        <div class="form-group col-md-6">
                            <label>Contact Name</label>
                            <input name="contact_name" required class="form-control" value="{{$company->contact_name}}">
                        </div>
                        <div class="form-group col-md-6">
                            <label>Tax Number</label>
                            <input name="vat_number" required class="form-control" value="{{$company->vat_number}}">
                            <p class="help-block"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Company Settings
                </div>
                <div class="panel-body">
                    <div class="col-lg-6">
                        <div class="form-group col-md-6">
                            <label>Offset Time<br /> <small>If a company has a cash up time of 1am then enter 1.5 or 2 etc</small></label>
                            <input type="number" min="0" max="12" name="time_offset" required class="form-control" value="{{$company->time_offset}}">
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><strong>Accounts Package?</strong></label>
                            <select name="accounts_package" class="form-control">
                                <option value="">None</option>
                                <option @if($company->accounts_package == 'xero') selected @endif value="xero">Xero</option>
                                <option @if($company->accounts_package == 'quickbooks') selected @endif value="quickbooks">Quickbooks</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><strong>Accounts Integration</strong></label>
                            <select name="accounts_integration" class="form-control">
                                <option value="">None</option>
                                <option @if($company->accounts_integration == 'highlevel') selected @endif value="highlevel">HighLevel</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><strong>Accounts Integration URL</strong></label>
                            <input type="text" class="form-control" name="accounts_integration_url" value="{{$company->accounts_integration_url}}"/>
                        </div>
                        <div class="form-group">
                            <label><strong>Accounts Integration Username</strong></label>
                            <input type="text" class="form-control" name="accounts_integration_username" value="{{$company->accounts_integration_username}}"/>
                        </div>
                        <div class="form-group">
                            <label><strong>Accounts Integration Password</strong></label>
                            <input type="text" class="form-control" name="accounts_integration_password" value="{{$company->accounts_integration_password}}"/>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group col-md-6">
                            <label>Spot checks do not affect stock<br /></label>
                            <input type="checkbox" data-toggle="toggle" name="spotchecks_affect_stock" value="1" @if($company->spotchecks_affect_stock == 1) checked @endif>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group col-md-6">
                            <label>Site Specific Clerks<br /></label>
                            <input type="checkbox" data-toggle="toggle" name="site_specific_clerks" value="1" @if($company->site_specific_clerks == 1) checked @endif>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-primary">
                <div class="panel-heading">
                    API Key & Secret
                </div>
                <div class="panel-body">
                    <div class="col-lg-6">
                        <div class="form-group col-md-12">
                            <label>Current Secret Key - Used for app API access</label>
                            <input readonly type="text" id="secret_key" name="secret_key" class="form-control" value="{{$company->secret_key}}">
                        </div>
                    </div>
                    <div class="col-xs-12">
                        <button id="generateKey" class="btn btn-primary">Generate New Key</button>
                    </div>
                </div>
            </div>
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Address Details
                </div>
                <div class="panel-body">
                    <div class="col-lg-12">
                        <div class="form-group col-sm-12 col-md-6">
                            <label>Address Line 1</label>
                            <input name="address_line1" required class="form-control" value="{{$company->address_line1}}">
                            <p class="help-block"></p>
                        </div>
                        <div class="form-group  col-sm-12 col-md-6">
                            <label>Address Line 2</label>
                            <input name="address_line2"  class="form-control" value="{{$company->address_line2}}">
                            <p class="help-block"></p>
                        </div>
                        <div class="form-group col-sm-12 col-md-6">
                            <label>City</label>
                            <input name="city" required class="form-control" value="{{$company->city}}">
                            <p class="help-block"></p>
                        </div>
                        <div class="form-group col-sm-12 col-md-6">
                            <label>County</label>
                            <input name="county" class="form-control" value="{{$company->county}}">
                            <p class="help-block"></p>
                        </div>
                        <div class="form-group col-sm-12 col-md-6">
                            <label>Postcode</label>
                            <input name="postcode" class="form-control" value="{{$company->postcode}}">
                            <p class="help-block"></p>
                        </div>
                        <div class="form-group col-sm-12 col-md-6">
                            <label>Telephone</label>
                            <input name="telephone" class="form-control" value="{{$company->telephone}}">
                            <p class="help-block"></p>
                        </div>
                    </div>
                </div>
            </div>
            {{--<div class="panel panel-primary">--}}
                {{--<div class="panel-heading">--}}
                    {{--Primary User--}}
                {{--</div>--}}
                {{--<div class="panel-body">--}}
                    {{--<div class="col-lg-12">--}}
                        {{--<div class="form-group col-sm-12 col-md-6">--}}
                            {{--<label>Username</label>--}}
                            {{--<input name="username" required class="form-control" value="{{old('username')}}">--}}
                            {{--<p class="help-block"></p>--}}
                        {{--</div>--}}
                        {{--<div class="form-group col-sm-12 col-md-6">--}}
                            {{--<label>Email Address</label>--}}
                            {{--<input name="email" required class="form-control" value="{{old('email')}}">--}}
                            {{--<p class="help-block"></p>--}}
                        {{--</div>--}}
                    {{--</div>--}}
                {{--</div>--}}
            {{--</div>--}}

            <div class="col-md-6 pull-right text-right">
                <div class="form-group">
                    <button type="submit" class="btn btn-success pull-right">Update Company</button>
                </div>
            </div>
        </form>
    </div>

    <style>
        input.error {
            border: 1px solid red !important;
        }
        .error {
            font-weight: normal !important;
            color: red !important;
        }
    </style>

@stop

@section('js')
    @parent
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.16.0/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.16.0/additional-methods.min.js"></script>
    <script>

        $(document).on('click', '#generateKey', function(e){
            e.preventDefault();

            Swal.fire({
                title: 'Are you sure?',
                text: "This will disconnect any apps currently connected to this company!",
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, do it!'
            }).then((result) => {
                if (result.value) {
                    let key = create_UUID();
                    $('#secret_key').val(key);
                }
            })

        });

        function create_UUID(){
            var dt = new Date().getTime();
            var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = (dt + Math.random()*16)%16 | 0;
                dt = Math.floor(dt/16);
                return (c=='x' ? r :(r&0x3|0x8)).toString(16);
            });
            return uuid;
        }

        $(document).ready(function(){

            $.validator.setDefaults({
                submitHandler: function() {
                    var $form = $(form);
                    $form.submit();
                }
            });

            $().ready(function() {

                // validate signup form on keyup and submit
                $("#registerCompanyForm").validate({
                    rules: {
                        contact_name: "required",
                        company_name: "required",
                        // username: {
                        //     required: true,
                        //     minlength: 5
                        // },
                        // email: {
                        //     required: true,
                        //     email: true
                        // }
                    },
                    messages: {
                        name: "Please enter a contact name",
                        company_name: "Please enter the name of the company",
                        username: {
                            required: "Please enter a username",
                            minlength: "Your username must consist of at least 5 characters"
                        },
                        email: "Please enter a valid email address"
                    }
                });

            });
        })
    </script>
    @stop
