@extends('layouts.master')

@section('content')
    <script>
        var table = null;
    </script>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Terminal Management</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Manage Terminals<br />
                    <small>Click a terminal below to manage settings.</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                            <tr>
                                <th>Terminal Number</th>
                                <th>Terminal Name</th>
                                <th>Version</th>
                                <th>Last Ping</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
    <div id="dataModal" class="modal fade" role="dialog">
        <div class="modal-dialog modal-primary">

            <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
{{--                    <button type="button" class="close" data-dismiss="modal">&times;</button>--}}
                    <button type="button" class="close" id="refreshButton" style="color: darkred">Refresh <i class="fa fa-refresh"></i></button>
                    <h4 class="modal-title">Pending Updates</h4>
                </div>
                <div class="modal-body">

                    <div id="updates-area"></div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>

        </div>
    </div>

@stop

@push('scripts')
<script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="/js/dataTables.editor.min.js"></script>
    <script src="/js/renderjson.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

<style>
    .warning-button {
        background: #ff3709!important;
    }
    .add-button {
        background: #419641!important;
    }
</style>

<script>
    var permissions = {};
    permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'edit_sites') == 1 ? 1 : 0 }};
    permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'add_sites') == 1 ? 1 : 0 }};
    permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'delete_sites') == 1 ? 1 : 0 }};
    permissions.can_terminal = {{NewbridgeWeb\Http\Helpers\AbilityHelper::role('newbridge,reseller') == 1 ? 1 : 0 }};


    let site_num = {{$site_num}};
    let company_id = {{$company_id}};

</script>

<script>
    $(function(){
        $(document).on('click', '#refreshButton', function(){
            let terminal_num = $('#terminal_num').val()
            let site_num = $('#site_num').val()

            $('#dataModal .modal-body').html('Refreshing');

            $.ajax({
                type: "GET",
                url: '/company/sites/'+site_num+'/terminals/'+terminal_num+'/updates',
                success: function (data) {

                    $('#dataModal .modal-body').html(data.html);
                },
                fail: function () {
                    notificationBar('error', 'unable to get updates')
                }
            });
        })
    })
</script>

<script src="/js/terminals.js?{{\Carbon\Carbon::now()->timestamp}}"></script>
@endpush
