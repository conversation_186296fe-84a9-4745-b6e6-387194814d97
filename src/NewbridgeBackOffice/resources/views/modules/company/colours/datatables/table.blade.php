@extends('layouts.master')

@section('content')
    <script>
        var table = null;
    </script>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Colour Settings ({{$site->site_name}})</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Create and Manage Colour Settings<br />
                    <small>Click in the table to instantly edit records shown, use the search boxes to filter the list.</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" width="100%">
                        <thead>
                            <tr>
                                <th>Setting Name</th>
                                <th>Setting Description</th>
                                <th>Setting Value</th>
                            </tr>
                        </thead>
                        <tbody>
                        @foreach($settings  as $setting)
                            <tr data-setting="{{$setting->setting_id}}" class="setting-row" data-value="#{{substr($setting->value1, 3)}}">
                                <td>{{$setting->label}}</td>
                                <td>{{$setting->description}}</td>
                                <td><select class="colorselector colour-setting" id="{{$setting->setting_id}}" name="value1" data-setting="{{$setting->setting_id}}">
                                        @foreach($colours as $c)
                                            <option @if('#'.substr($setting->value1, 3) == '#'.substr($c->value, 3)) selected @endif value="{{$c->value}}" data-color="{{'#'.substr($c->value, 3)}}" data-setting="{{$setting->setting_id}}">{{$c->value}}</option>
                                        @endforeach
                                    </select>
                                </td>

                                    {{--<input type="color" name="text-color" id="text-color" class="pull-right colour-setting" data-setting="{{$setting->setting_id}}" value="#{{substr($setting->value1, 3)}}"></td>--}}
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

@stop

@push('scripts')
<script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="/js/dataTables.editor.min.js"></script>
<link rel="stylesheet" href="/css/bootstrap-colorselector.min.css">
<script src="/js/bootstrap-colorselector.min.js"></script>

<script>
    var permissions = {};
    permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'edit_sites') == 1 ? 1 : 0 }};
    permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'add_sites') == 1 ? 1 : 0 }};
    permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'delete_sites') == 1 ? 1 : 0 }};

    var site_num = {{$site_num}}
</script>

<script>

    $('.colorselector').colorselector();

    $('.colour-setting').on('change', function(){
        var element = $(this).data('setting');
        var value = $(this).val();

        var update = {site_num: site_num, setting_id: element, value1: value}

        $.ajax({
            type: "POST",
            url: "/company/sites/"+site_num+"/colours/update",
            data: {data: update},
            success: function () {
                notificationBar('success', 'Colour setting ' + element + ' saved')
            },
            fail: function () {
                notificationBar('error', 'Unable to save colour settings, please try again')
            }
        });


    })

    {{--$('#save-button').click(function(){--}}

        {{--// build something to post--}}

        {{--var updates = [];--}}
        {{--$('.colour-setting').each(function (index, value) {--}}
            {{--var element = $(this).data('setting');--}}
            {{--var value = $(this).val();--}}

            {{--updates.push({'setting_id':element, 'value': value});--}}
        {{--})--}}


        {{--$.ajax({--}}
            {{--type: "POST",--}}
            {{--url: "/company/sites/{{$site->site_num}}/colours/update/",--}}
            {{--data: {data: updates},--}}
            {{--success: function () {--}}
                {{--window.location.reload()--}}
            {{--},--}}
            {{--fail: function () {--}}
                {{--console.warn('error updating colours')--}}
            {{--}--}}
        {{--});--}}
    {{--})--}}
</script>
@endpush
