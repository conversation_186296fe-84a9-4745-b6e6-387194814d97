<html>
<head>
    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">

    <!-- jQuery library -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>

    <!-- Latest compiled JavaScript -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>

    <link href="https://fonts.googleapis.com/css?family=Montserrat|Open+Sans" rel="stylesheet">

    <style>
        h3 {
            font-family: 'Montserrat', sans-serif;
            font-size: 16px;
            font-weight: bold;
        }

        h4 {
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            font-weight: bold;
        }

        th {
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            font-weight: bold;
        }

        thead td {
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            font-weight: bold;
        }

        td {
            font-family: 'Open Sans', sans-serif;
            font-size: 12px;
        }

        .header-cell {
            background: #e6e6e6;
            color: black;
            font-weight: bold;
            border: none !important;
        }

        .light-cell {
            background: white;
            color: black;
            border: none !important;

        }

        .table {
            border: 1px solid #e3e3e3;
        }

        thead {
            background: #e6e6e6;
            color: black;
            font-weight: bold;
        }

        tr.summary {
            font-weight: bold;
        }
    </style>
</head>
<body>

<table>
    <thead>
    <tr>
        <th>Product Name</th>
        <th>Supplier Code</th>
        <th>Quantity Ordered</th>
        <th>Price (Each)</th>
        <th>Price (Total)</th>

    </tr>
    </thead>
    <tbody>
    @foreach($order->transactions as $detail)
        <tr>
            <td>{{$detail->product->displayname}}</td>
            <td>{{$detail->product->stock_code}}</td>
            <td>{{$detail['quantity'] / $detail->product->sku->qty}} x {{$detail->product->sku->displayname}}</td>
            <td>{{$detail['price'] * $detail->product->sku->qty}}</td>
            <td>{{($detail['price'] * $detail->product->sku->qty) * ($detail['quantity'] / $detail->product->sku->qty)}}</td>
        </tr>
    @endforeach

    </tbody>
</table>
</body>
</html>
