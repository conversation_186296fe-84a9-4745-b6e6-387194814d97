@extends('layouts.master')

@section('section-name') Create Order @stop


@section('content')

    <style>
        #OrderTable label.error {
            display: none;
        }
    </style>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Create a new Order <br />
                <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small></h1>
        </div>
    </div>
    <form id="OrderForm">
        {{ csrf_field() }}
    <div class="row">
        <div class="col-sm-6">
            <div class="panel panel-default" id="meta">
                <div class="panel-heading">
                    Order Details
                </div>
                <div class="panel-body">
                    <input type="hidden" name="site" id="site" value="{{\Session::get('current_site')}}">
                    <div class="form-group col-sm-12">
                        <label><strong>Notes</strong></label>
                        <textarea name="notes" class="form-control"></textarea>
                    </div>
                    <div class="form-group col-sm-6">
                        <label><strong>Supplier</strong></label>
                        <select name="supplier_id" id="supplier">
                            <option value="">Select a Supplier</option>
                            @foreach($suppliers as $supplier)
                                <option value="{{$supplier->id}}" >{{$supplier->name}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group col-sm-6">
                        <label><strong>Supplier Email(s)</strong></label>
                        <input type="text" class="supplier_emails form-control" name="supplier_emails" placeholder="<EMAIL>,<EMAIL>">
                    </div>
                    <div class="form-group col-sm-6">
                        <label><strong>Status</strong></label>
                        <select id="status" name="status">
                            <option value="0">New Order</option>
                            <option value="1">Sent</option>
                            <option value="2">Delivered</option>
                            <option value="3">Paid</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-6">
                        <label><strong>Send Method</strong></label>
                        <select id="send_method" name="send_method">
                            <option value="0" selected>Phone</option>
                            <option value="1">Post</option>
                            <option value="2">Email</option>
                        </select>
                    </div>
                    <div class="form-group col-sm-6" style="display: none;">
                        <label><strong>Delivery Date</strong></label>
                        <input type="text" class="form-control" name="delivery_date" id="delivery_date" value="{{ \Carbon\Carbon::now(\NewbridgeWeb\Http\Helpers\TimezoneHelper::getTimezone())->format('d/m/Y H:i:s') }}"/>
                    </div>
                    <script type="text/javascript">
                        $(function () {
                            $('#delivery_date').datetimepicker({
                                format: 'DD/MM/YYYY H:mm:ss',
                                minDate: '{{\NewbridgeWeb\Http\Helpers\StockPeriodHelper::availableStartDate()}}',
                            });
                        });
                    </script>
                    <div class="form-group col-sm-6" style="display: none;">
                        <label><strong>Delivery No</strong></label>
                        <input type="text" class="form-control" name="delivery_no" id="delivery_no"/>
                    </div>
                    <div class="form-group col-sm-6" style="display: none;">
                        <label><strong>Invoice No</strong></label>
                        <input type="text" class="form-control" name="invoice_no" id="invoice_no"/>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <a href="#" onclick="switchAddTab('products')">Products</a> | <a href="#" onclick="switchAddTab('suppliers')">Suppliers</a> | <a href="#" onclick="switchAddTab('departments')">Departments</a> | <a href="#" onclick="switchAddTab('subdepartments')">Sub Departments</a>
                </div>
                <div class="panel-body">
                    <div class="form-group col-sm-12">
                        <div id="products">
                            <div class="row">
                                <div class="col-lg-12 products" data-type="add-tab">
                                    <div class="form-group">
                                        <label><strong>Product</strong></label>
                                        <select class="form-control" id="product-dropdown" multiple>
                                        </select>
                                    </div>
                                    <div class="btn btn-primary btn-block add-product-button" style="margin-top: 10px;">Add Product</div>
                                </div>
                                <div class="col-lg-12 subdepartments" data-type="add-tab" style="display: none;">
                                    <div class="form-group">
                                        <label><strong>Sub Department</strong></label>
                                        <select class="form-control" id="subdepartment-dropdown" multiple>
                                        </select>
                                    </div>
                                    <div class="btn btn-primary btn-block add-subdepartment-button" style="margin-top: 10px;">Add SubDepartment Products</div>
                                </div>
                                <div class="col-lg-12 departments" data-type="add-tab" style="display: none;">
                                    <div class="form-group">
                                        <label><strong>Department</strong></label>
                                        <select class="form-control" id="department-dropdown" multiple>
                                        </select>
                                    </div>
                                    <div class="btn btn-primary btn-block add-department-button" style="margin-top: 10px;">Add Department Products</div>
                                </div>
                                <div class="col-lg-12 suppliers" data-type="add-tab" style="display: none;">
                                    <div class="form-group">
                                        <label><strong>Supplier</strong></label>
                                        <select class="form-control" id="supplier-dropdown" multiple>

                                        </select>
                                    </div>
                                    <div class="btn btn-primary btn-block add-supplier-button" style="margin-top: 10px;">Add Supplier Products</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Order Items
                </div>
                <div class="panel-body">
                        <table id="OrderTable" class="table-striped table" width="100%">
                            <thead>
                                <tr>
                                    <th>PLU</th>
                                    <th>Name</th>
                                    <th>Supplier Code</th>
                                    <th>Supplier</th>
                                    <th>Current Unit Stock</th>
                                    <th>Min Stock</th>
                                    <th>Max Stock</th>
                                    <th>SKU</th>
                                    <th>SKU Price</th>
                                    <th>Qty</th>
                                    <th>{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!} Total</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="copy-example" style="display: none;" data-product="">
                                    <td>
                                        <input data-product="" class="form-control input-sm plu dontSend" type="text" name="plu[]" readonly value="">
                                    </td>
                                    <td style="width: 20%;">
                                        <input data-product="" class="form-control input-sm displayname input dontSend" type="text" name="name[]" placeholder="Product" readonly value="" disabled="true">
                                    </td>
                                    <td>
                                        <input data-product="" class="form-control input-sm pcode input dontSend" type="text" name="code[]" placeholder="Code" readonly value="" disabled="true">
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select data-product="" class="form-control input-sm supplier dontSend" type="text" name="supplier[]">
                                                @foreach(json_decode($data)->suppliers as $supplier)
                                                    <option value="{{$supplier->guid}}">{{$supplier->name}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </td>
                                    <td style="max-width: 150px;">
                                        <input data-product="" class="form-control input-sm current_stock dontSend" type="text" name="current_stock[]" value="" readonly disabled="true">
                                    </td>
                                    <td style="max-width: 100px;">
                                        <input data-product="" class="form-control input-sm min_stock dontSend" type="text" name="min_stock[]" value="" readonly disabled="true">
                                    </td>
                                    <td style="max-width: 100px;">
                                        <input data-product="" class="form-control input-sm max_stock dontSend" type="text" name="max_stock[]" value="" readonly disabled="true">
                                    </td>
                                    <td>
                                        <div class="form-group">
                                            <select data-product="" class="form-control input-sm sku dontSend" type="text" name="sku[]" readonly>
                                                @foreach(json_decode($data)->skus as $sku)
                                                    <option value="{{$sku->guid}}">{{$sku->displayname}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </td>
                                    <td style="max-width: 100px;">
                                        <input data-product="" class="form-control input-sm skuPrice dontSend" type="number" step="any" name="skuPrice[]" value="">
                                    </td>
                                    <td style="max-width: 100px;">
                                        <input data-product="" class="form-control input-sm quantity dontSend" type="number" name="quantity[]" value="" min="1">
                                    </td>
                                    <td style="max-width: 100px;">
                                        <input data-product="" class="form-control input-sm price dontSend" type="text" name="price[]" value="" min="0" readonly>
                                    </td>
                                    <td>
                                        <button class="btn btn-md btn-danger delete-button dontSend" data-product=""><i class="fa fa-trash"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                            <thead>
                                <th colspan="11" class="text-right">Order Total: {!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!} <span id="orderTotal">0.00</span></th>
                            </thead>
                        </table>
                        <div class="row">
                            <hr />
                            <button class="btn btn-block btn-success btn-sm createButtons" id="createButton" disabled="true">Create Order</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    @include('modules.orders.modals.confirm-order')
@stop


@push('scripts')
<script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>


<script>
    var permissions = {};
    permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
    permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
    permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
    const autosave = true;

    var master_products = {!! json_encode(json_decode($data)->products) !!};
    var departments = {!! json_encode(json_decode($data)->departments) !!};
    var subdepartments = {!! json_encode(json_decode($data)->subdepartments) !!};
    var suppliers = {!! json_encode(json_decode($data)->suppliers) !!};
    const module = "orders";
    var suggestions = null;
    @if(isset(json_decode($data)->suggested))
        suggestions = {!! json_decode($data)->suggested !!};
    @endif
    let csrf_token = '{{csrf_token()}}';

</script>
<script src="/js/stocktake_and_ordering/orders_and_deliveries_main.js?{{\Carbon\Carbon::now()->timestamp}}"></script>
<script src="/js/stocktake_and_ordering/autosave_redis.js?{{\Carbon\Carbon::now()->timestamp}}"></script>
@endpush
