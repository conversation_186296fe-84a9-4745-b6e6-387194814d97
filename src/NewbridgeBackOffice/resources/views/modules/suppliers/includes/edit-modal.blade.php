<div id="EditModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Edit Supplier ({{$supplier->name}})</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="EditForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Supplier Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <label><strong>Name</strong></label>
                                                <input type="hidden" value="{{$supplier->id}}" name="id">
                                                <input type="text" class="form-control" name="name" value="{{$supplier->name}}"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <label><strong>Account Number/Code</strong></label>
                                                <input type="text" class="form-control" name="acc_code" value="{{$supplier->acc_code}}"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <label><strong>Supplier Short Code</strong></label>
                                                <input type="text" class="form-control" name="third_party_id" value="{{$supplier->third_party_id}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Contact Details
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Contact Name</strong></label>
                                                <input type="text" class="form-control" name="contact_name" value="{{$supplier->contact_name}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Address Line 1</strong></label>
                                                <input type="text" class="form-control" name="address1" value="{{$supplier->address1}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Address Line 2</strong></label>
                                                <input type="text" class="form-control" name="address2" value="{{$supplier->address2}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Address Line 3</strong></label>
                                                <input type="text" class="form-control" name="address3" value="{{$supplier->address3}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Postcode</strong></label>
                                                <input type="text" class="form-control" name="postcode" value="{{$supplier->postcode}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Telephone</strong></label>
                                                <input type="text" class="form-control" name="tel" value="{{$supplier->tel}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Email</strong></label>
                                                <input type="text" class="form-control" name="email" value="{{$supplier->email}}"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <label for="notes"><strong>Notes</strong></label>
                                                <textarea name="notes" class="form-control">{{$supplier->notes}}</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="editSubmit">Update</button>
            </div>
        </div>
    </div>
</div>

<script>
    $('#editSubmit').on('click', function(){
        $('#EditForm').submit()
    })

    var form = $('#EditForm')

    // validate signup form on keyup and submit
    $("#EditForm").validate({
        rules: {
            name: "required",
            contact_name: "required",
            address1: "required",
            postcode: "required",
            email: "email"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "PUT",
                url: "/suppliers/edit-single",
                data: $(form).serialize(),
                success: function () {
                    $('#EditModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    })
</script>