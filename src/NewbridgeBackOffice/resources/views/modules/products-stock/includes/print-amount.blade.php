<div id="PrintAmountModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>

                <h4>Barcode Printing</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="PrintAmountForm">
                    {{ csrf_field() }}
                    <table class="table table-bordered table-hover table-striped table-condensed">
                        <thead>
                        <tr>
                            <th class="col-md-4">Product</th>
                            <th class="col-md-4">Current Stock</th>
                            <th class="col-md-2">Print Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($productsModal as $product)
                            <tr>
                                <td>{{$product->displayname}}</td>
                                <td>{{$product->currentStock()}}</td>
                                <td><input type="text" class="form-control printAmount" name="print_amount" data-id="{{$product['id']}}" value="1"/></td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="form-group">
                        <label><strong>Label Size</strong></label>
                        <select name="printSize" id="printSize" class="form-control">
                            <option value="38x25">1.5" x 1" (38mm x 25mm)</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="printAmountForm">Update</button>
            </div>
        </div>
    </div>
</div>

<script>

    let printData = {};
    printData.labels = [];

    $('#printAmountForm').on('click', function(e){

        e.preventDefault();

        let items = $('.printAmount');

        _.each(items, function(item){
            let id = $(item).data('id');
            let quantity = $(item).val();
            let data = { id: id, quantity: quantity};

            printData.labels.push(data);
        });

        printData.size = $('#printSize').val();

        $.ajax({
            type: "POST",
            url: "/products/barcodes/data",
            data: {data: printData},
            success: function (data) {
                if(data.status == 'ok'){
                    window.location.href = '/products/barcodes/print'
                } else {
                    notificationBar('error', 'There was an error processing your request, please try again or contact support.')
                }
            },
            fail: function () {
                notificationBar('error', 'There was an error processing your request, please try again or contact support.')
            }
        });

    });

</script>





