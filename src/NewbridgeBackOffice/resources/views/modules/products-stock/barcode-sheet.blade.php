<html>
<head>
    <style>
        @page { size: 38mm 25mm; padding: 1mm; margin: 1mm;}
    </style>
</head>
<body>

@foreach($barcodes as $barcode)
    <table style="@if(!$loop->last) page-break-after: always; @endif font-size: 11px; padding: 0px; margin: 0px; margin-top: 1mm; width: 100%;">
        <tr>
            <td style="text-align: left;"><strong>{{$barcode['displayname']}}</strong></td>
            <td style="text-align: right;">{{$barcode['selling_price_1']}}</td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center; padding-top: 5px;">
                <?php
                    $barcodeNum = str_pad($barcode['barcode'], 12, '0', STR_PAD_LEFT);
                ?>
                {!! '<img style="width: 32mm" src="data:image/png;base64,' . \Milon\Barcode\DNS1D::getBarcodePNG($barcode['barcode'], "EAN13") . '" alt="barcode"   />' !!}
                <br />
                {{ \NewbridgeWeb\Http\Helpers\EANDigit::getDigit($barcodeNum) }}
            </td>
        </tr>
    </table>
@endforeach

</body>
</html>
