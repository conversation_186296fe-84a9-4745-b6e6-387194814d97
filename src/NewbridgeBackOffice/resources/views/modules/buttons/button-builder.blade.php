@extends('layouts.editor')

@section('content')
    <div id="notificationBar"><span class="message"></span></div>

    <style>
        .resizable:hover {
            background: lightblue!important;
        }
        #overlay {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            background: #000;
            opacity: 0.8;
            filter: alpha(opacity=50);
            z-index: 999999;
        }
        #loading {
            width: 50px;
            height: 57px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin: -28px 0 0 -25px;
        }
        .fa-spinner {
            font-size: 40px;
            color: white;
        }
        @if(isset($background))
        body {
            background-color: {{str_replace('#FF', '#', $background->value1)}}!important;
        }
        @endif
    </style>

<div style="width: 1400px; height: 850px; @if(isset($background)) background: {{str_replace('#FF', '#', $background->value1)}}; @endif padding-left: 20px;">
    <div style="float: left; width: 950px; height: 850px;" id="selectableArea">
    <h2 style="color: white;">{{$page->DisplayName}} <button id="select-all-buttons" class="btn btn-sm btn-success">Select All Buttons</button> <button id="deselect-all-buttons" class="btn btn-sm btn-danger" style="display: none;">Deselect All Buttons</button>
        <select class="form-control" id="pages-navigate" style="z-index: 99999;">
            @foreach($pages as $p)
                <option @if($page->id == $p['id']) selected @endif value="{{$p['id']}}">{{$p['DisplayName']}}</option>
            @endforeach
        </select>
    </h2>
    <div class="section" style="clear: both; position: relative;">
        <div class="row" style="position: relative; clear: both;">
            @foreach($topButtons as $b)
                <div id="_{{$b['id']}}" class="resizable resizable-area"
                     data-colspan="{{$b['columnspan']}}"
                     data-rowspan="{{$b['rowspan']}}"
                     data-column="{{$b['column']}}"
                     data-row="{{$b['row']}}"
                     data-viewmodel="{{$b['viewmodel']}}"
                     data-style="{{$b->style}}"
                     data-max-width="{{$b['max-width']}}" data-max-height="{{$b['max-height']}}"
                     style="{{ $b['css-active'] }} top: {{$b['top']+$b['row']*5}}px; left: {{$b['left']}}px; width: {{$b['width']}}px; height: {{$b['height']}}px;" data-id="{{$b['id']}}">
                    <div class="edit" data-id="{{$b['id']}}" style="position: absolute; top: 5px; right: 5px; font-size: 11px !important; width: {{$b['width']-15}}px; height: {{$b['height']-15}}px; z-index: {{$b['z-index']+5}}"> </div>
                    <span class="displayname">{{$b['displayname']}}</span>
                </div>
            @endforeach
        </div>
    </div>
    <div style="clear: both"></div>
    <div class="section" style="clear: both; margin-top: 60px;">
        <div style="width: 248px; height: 545px; margin-left: -13px; float: left; background: antiquewhite; margin-right: 10px; margin-bottom: 5px; padding: 10px; color: black">

            <h4>Instructions</h4>

            <p style="padding: 5px;">Click and drag to select multiple buttons in an area.</p>
            <p style="padding: 5px;">Single click buttons to select & deselect individually</p>
            <p style="padding: 5px;">Double click to edit a button.</p>
            <p style="padding: 5px;">Click any blank area to clear your selection.</p>
        </div>
        <div class="buttoncontainer" style="position: relative; height: 560px; float: left" id="mainButtonArea">
            @foreach($mainButtons as $b)
                <div id="_{{$b['id']}}" class="resizable mainbutton"
                     data-max-width="{{$b['max-width']}}"
                     data-max-height="{{$b['max-height']}}"
                     data-colspan="{{$b['columnspan']}}"
                     data-rowspan="{{$b['rowspan']}}"
                     data-column="{{$b['column']}}"
                     data-row="{{$b['row']}}"
                     data-viewmodel="{{$b['viewmodel']}}"
                     data-style="{{$b->style}}"
                     style="{{ $b['css-active'] }} top: {{$b['top']+$b['row']*5}}px; left: {{$b['left']}}px; width: {{$b['width']}}px; height: {{$b['height']}}px; z-index: {{$b['z-index']}}"  >
                    <div class="edit" data-id="{{$b['id']}}" style="position: absolute; top: 5px; right: 5px; font-size: 11px !important; width: {{$b['width']-15}}px; height: {{$b['height']-15}}px; z-index: {{$b['z-index']+5}};"></div>
                    <span class="displayname">{{$b['displayname']}}</span>
                </div>
            @endforeach
        </div>
    </div>
    <div style="clear: both"></div>
    <div class="section" style="clear: both;">
        <div class="row" style="position: absolute; top: 715px; clear: both;">
            @foreach($bottomButtons as $b)
                <div id="_{{$b['id']}}" class="resizable resizable-area"
                     data-column="{{$b['column']}}"
                     data-row="{{$b['row']}}"
                     data-viewmodel="{{$b['viewmodel']}}"
                     data-max-width="{{$b['max-width']}}" data-max-height="{{$b['max-height']}}" style="{{ $b['css-active'] }} top: {{$b['top']+$b['row']*5}}px; left: {{$b['left']}}px; width: {{$b['width']}}px; height: {{$b['height']}}px;" data-id="{{$b['id']}}" data-style="{{$b->style}}">
                    <span class="displayname">{{$b['displayname']}}</span>
                    <div class="edit" data-id="{{$b['id']}}" style="position: absolute; top: 5px; right: 5px; font-size: 11px !important; width: {{$b['width']-15}}px; height: {{$b['height']-15}}px; z-index: {{$b['z-index']+5}}"></div>
                </div>
            @endforeach
        </div>
    </div>
</div>
    <div class="panel panel-default" style="width: 400px; height: 670px; margin-left: -10px; margin-right: 10px; overflow-y: auto; float: left; margin-top: 100px;">
        <ul class="nav nav-tabs">
            <li class="active"><a data-toggle="tab" href="#massapply">Quick Actions</a></li>
            <li><a data-toggle="tab" href="#products">Products</a></li>
            <li><a data-toggle="tab" href="#style">Style</a></li>
            <li><a data-toggle="tab" href="#copy">Copy</a></li>
        </ul>

        <div class="tab-content">
            <div id="products" class="tab-pane fade in ">
                <div class="panel-body">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label><strong>Quick Apply Products</strong></label>
                            <select class="form-control sub_department" id="sub_department_dropdown" name="sub_department" data-display="Sub Department" multiple="true" style="width: 100%">
                                <option value="">None</option>
                                @foreach($subdepartments as $sd)
                                    @if(!$sd->products->isEmpty())
                                        <optgroup label="{{$sd->displayname}}">
                                            @foreach($sd->products as $p)
                                                <option value="{{$p->guid}}">{{$p->displayname}}</option>
                                            @endforeach
                                        </optgroup>
                                    @endif
                                @endforeach
                            </select>
                            <br />
                            <br />
                            <div class="dropdown">
                                <button class="btn btn-success btn-block dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                    Select Apply Method
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1" style="width: 300px;">
                                    <li style="padding: 5px;"><a style="padding: 10px" href="#" id="apply-subdepartment-alphabetical-button"><i class="fas fa-list-ol">&nbsp;</i>&nbsp; Alphabetical</a></li>
                                    <li style="padding: 5px;"><a style="padding: 10px" href="#" id="apply-subdepartment-button"><i class="fas fa-list-alt">&nbsp;</i>&nbsp; Created Order</a></li>

                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <hr />
                    </div>
                </div>
            </div>
            <div id="massapply" class="tab-pane fade in active">
                <div class="panel-body">
                    <div class="alert alert-danger alert-dismissable alert-custom2" style="display: none;">
                        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
                        <strong>Error:</strong> Please select more than one button to continue
                    </div>
                    <div class="alert alert-danger alert-dismissable alert-custom3" style="display: none;">
                        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
                        <strong>Error:</strong> Please select a sub department from the dropdown list
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <label><strong>Quick Actions</strong></label>
                            <p><small>Only one button can be copied at a time, you can clear multiple buttons. To copy multiple buttons to a new page, please use the copy tab.</small></p>
                            <div class="row" style="margin-left: 0px;">
                                <div style="float: left; margin: 10px;"><a href="#" class="btn btn-warning" id="copy-button"><i class="fas fa-copy"></i> Copy</a></div>
                                <div style="float: left; margin: 10px;"><a href="#" class="btn btn-success" id="paste-button"><i class="fas fa-paste"></i> Paste</a></div>
                                <div style="float: left; margin: 10px;"><a  href="#" class="btn btn-danger" id="mass-clear-button"><i class="fas fa-trash"></i> Clear</a></div>
                            </div>
                            <hr>

                            <div class="form-group">
                                <p><strong>Most Recent Styles </strong><br> <small>Click to apply to selected buttons.</small></p>
                                <div style="clear: both; height: 10px;"></div>
                                @foreach($recentStyles as $k => $style)
                                    <button data-id="{{$style->id}}" class="btn styleApplicatorButton style-apply" data-styleid="{{$style->id}}" style="border-radius: 0 !important; margin: 3px; padding-left: 15px; padding-right: 15px; cursor: pointer; color: {{$style->foreground}}; background: {{$style->color1}}!important;">
                                        Sample Text
                                    </button>
                                @endforeach
                                <div style="clear: both; height: 10px;"></div>
                                <p><strong>Popular Styles</strong><br> <small>Click to apply to selected buttons.</small></p>
                                <div style="clear: both;"></div>
                                    @foreach($defaultStyles as $k => $style)
                                        <button data-id="def-{{$style->id}}" class="btn styleApplicatorButton style-apply" data-styleid="{{$style->id}}" style="border-radius: 0 !important; margin: 3px; padding-left: 15px; padding-right: 15px; cursor: pointer; color: {{$style->foreground}}; background: {{$style->color1}}!important;">
                                            Sample Text
                                        </button>
                                    @endforeach
                            </div>
                        </div>
                        <div class="col-sm-12">
                            <hr />
                        </div>
                    </div>
                </div>
            </div>
            <div id="style" class="tab-pane fade">
                <div class="panel-body" id="middle-buttons">
                    @include('modules.buttons.includes.button-style-form-slim')
                </div>
            </div>
            <div id="copy" class="tab-pane fade">
                <div class="panel-body">
                    @include('modules.buttons.includes.copyToPagesSlim')
                </div>
            </div>
        </div>
    </div>
</div>
    <div style="clear: both"></div>

<div id="modal-area"></div>

@stop

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-overlaps/1.2.3/jquery.overlaps.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>
<link rel="stylesheet" href="//cdn.jsdelivr.net/npm/jquery-typeahead@2.11.0/src/jquery.typeahead.css">
<script src="//cdn.jsdelivr.net/npm/jquery-typeahead@2.11.0/dist/jquery.typeahead.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@viselect/vanilla/lib/viselect.cjs.js"></script>
<script src="/js/ds.min.js"></script>


<script>
    var cellWidth = 85;
    var cellHeight = 55;
    var minWidth = 80;
    var modalUrl = 'get-modal';
</script>

<style>
    .dropdown ul.dropdown-menu {
        height: 300px!important; max-height: 300px !important; width: 100% !important; overflow: auto;
    }
    ul.dropdown-menu {
       width: 100% !important;
    }

    .styleApplicatorButton:active, .styleApplicatorButton:focus {
        border: 1px solid black;
        outline: none;
        box-shadow: none;
    }

</style>

<script type="text/javascript">

</script>

<style>
    .selection-area {
        background: rgba(46, 115, 252, 0.11);
        border: 2px solid rgba(98, 155, 255, 0.81);
        border-radius: 0.1em;
        z-index: 999999999!important;
    }


    body {
        user-select: none;
    }

    .selected {
        -webkit-box-shadow:inset 0px 0px 0px 3px #FFF;
        -moz-box-shadow:inset 0px 0px 0px 3px #FFF;
        box-shadow:inset 0px 0px 0px 3px #FFF;
    }
    .typeahead__item {
        padding-left: 10px !important;
    }
    .typeahead__container {
        font-size: 14px !important;
    }
    .typeahead__list {
        max-height: 300px;
        overflow-y: auto;
    }
    .remove-link {
        font-size: 14px;
        position: absolute;
        top: 12px;
        right: 3px;
        color: white;
    }
    .re-order-link-items {
        position: relative;
        width: 160px !important;
        display: block;
        padding: 5px;
        border: lightblue solid 1px;
        background: white;
        list-style: none;
        cursor: pointer;
        float: left;
    }
    .re-order-link-items:hover {
        background: lightgrey;
    }
    .re-order-link-items.product {
        background-color: #9e58ab;
        color: white;
    }
    .re-order-link-items.product:hover {
        opacity: 0.8;
    }
    .re-order-link-items.payment {
         background-color: #63b5c2;
         color: black;
     }
    .re-order-link-items.payment:hover {
        opacity: 0.8;
    }
    .re-order-link-items.discount {
        background-color: #ad443d;
        color: white;
    }
    .re-order-link-items.discount:hover {
        opacity: 0.8;
    }
    .re-order-link-items.command {
        background-color: #db9840;
        color: black;
    }
    .re-order-link-items.command:hover {
        opacity: 0.8;
    }
    .re-order-link-items.page {
        background-color: #8ccca2;
        color: white;
    }
    .re-order-link-items.page:hover {
        opacity: 0.8;
    }
</style>

<script src="/js/buttons.js?{{\Carbon\Carbon::now()->timestamp}}"></script>
<script src="/js/buttonEditor.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

<script>
    $('#pages-navigate').on('change', function(){
        var id = $(this).val()
        window.location.href = '/screens/manage/'+id
    })
</script>


@endpush
