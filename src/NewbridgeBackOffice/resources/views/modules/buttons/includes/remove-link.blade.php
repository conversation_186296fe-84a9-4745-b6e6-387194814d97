@if($link->product)
    <li class="re-order-link-items removelink product" data-id="{{$link->id}}" data-position="{{$link->position}}"><strong>{{$link->product->displayname}}</strong>
        <br />
        <span>
            <em>{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{$link->product->selling_price_1}}, {{$link->product->subdepartment->displayname}}</em>
        </span>
        <span class="remove-link"><strong> &nbsp; &nbsp; &times; </strong></span>
    </li>
@elseif($link->intcommand && $link->intcommand->Command == 1)
    <li class="re-order-link-items removelink page" data-id="{{$link->id}}" data-position="{{$link->position}}"><strong>{{$link->intcommand->DisplayName}}</strong>
        <br />
        <span>
            <em>Page</em>
        </span>
        <span class="remove-link"><strong> &nbsp; &nbsp; &times; </strong></span>
    </li>
@elseif($link->intcommand && $link->intcommand->Command != 3)
    <li class="re-order-link-items removelink command" data-id="{{$link->id}}" data-position="{{$link->position}}"><strong>{{$link->intcommand->DisplayName}}</strong>
        <br />
        <span>
            <em>Function</em>
        </span>
        <span class="remove-link"><strong> &nbsp; &nbsp; &times; </strong></span>
    </li>
@elseif($link->payment)
    <li class="re-order-link-items removelink payment" data-id="{{$link->id}}" data-position="{{$link->position}}"><strong>{{$link->payment->DisplayName}}</strong>
        <br />
        <span>
            <em>Payment</em>
        </span>
        <span class="remove-link"><strong> &nbsp; &nbsp; &times; </strong></span>
    </li>
@elseif($link->discount)
    <li class="re-order-link-items removelink discount" data-id="{{$link->id}}" data-position="{{$link->position}}"><strong>{{$link->discount->displayname}}</strong>
        <br />
        <span>
            <em>Discount</em>
        </span>
        <span class="remove-link"><strong> &nbsp; &nbsp; &times; </strong></span>
    </li>
@endif