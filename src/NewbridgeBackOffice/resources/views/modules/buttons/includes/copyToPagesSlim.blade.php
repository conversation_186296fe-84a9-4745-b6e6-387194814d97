<div id="copyButtonSlim" style="min-height: 400px;">
    <div class="col-lg-12">
        <h4>Copy <PERSON><PERSON></h4>
        <p>CTRL &amp; Click buttons to select them before copying them to other pages.</p>
        <div class="row">
            <select class="form-control" id="pages" name="pages" multiple="multiple" style="z-index: 99999;">
                @foreach($pages as $page)
                    <option value="{{$page['CommandUID']}}">{{$page['DisplayName']}}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="col-sm-12">
        {{--<div class="form-group">--}}
            {{--<label for="color1" style="padding-top: 7px;">Border Radius</label>--}}

            {{--<div class="form-group input-group pull-right" style="width: 110px;">--}}
                {{--<input type="number" id="border-radius" name="border-radius" class="form-control pull-right" value="{{ isset($button) ?  $button['style']['cornerradius'] : '10' }}">--}}
                {{--<span class="input-group-addon">deg</span>--}}
            {{--</div>--}}
        {{--</div>--}}
        <br />
        <br >
        <button class="btn btn-md btn-primary btn-block" id="copy-selected-button">Copy To Selected Pages</button>
        <button class="btn btn-md btn-success btn-block" id="copy-selected-confirm" style="display: none;">Confirm Copy</button>
        <button class="btn btn-md btn-success btn-block" id="copy-selected-loading" style="display: none;" disabled="disabled"><i class="fa fa-spinner fa-spin"></i> Copying....</button>
    </div>

</div>

<script>
//    setTimeout(function(){
//        updateAllButtonStylesSlim()
//    }, 300)
//
//    $(document).on('keyup. change', '#buttonStyleFormSlim input, #buttonStyleFormSlim select', function () {
//
//        updateAllButtonStylesSlim()
//    });
//
//    function updateAllButtonStylesSlim()
//    {
//        var gradient = $('#backgroundType').val();
//
//        buttonStyles.color1 = $('#color1').val();
//        buttonStyles.color2 = $('#color2').val();
//        buttonStyles.textSize = $('#text-size').val();
//        buttonStyles.textColor = $('#text-color').val();
//        buttonStyles.borderRadius = $('#border-radius').val();
//        buttonStyles.gradient = gradient;
//
//        buttonStyles.button_type = $('#button_type').val();
//        buttonStyles.page = $('#button-page-selection').val();
//
//        var active = $("body .example-button.active");
//        var inactive = $("body .example-button.inactive");
//        var selectedButtons = $('.resizable.selected');
//
//        if(gradient == 1){
//
//            buttonStyles.color3 = buttonStyles.color2;
//            buttonStyles.color4 = buttonStyles.color1;
//
//            active.css('background', 'linear-gradient('+buttonStyles.color3+','+ buttonStyles.color4+')')
//            inactive.css('background', 'linear-gradient('+buttonStyles.color1+','+ buttonStyles.color2+')')
//            selectedButtons.css('background', 'linear-gradient('+buttonStyles.color1+','+ buttonStyles.color2+')')
//
//        } else {
//
//            inactive.css('background', buttonStyles.color1)
//            active.css('background', buttonStyles.color2)
//            selectedButtons.css('background', buttonStyles.color1)
//
//            buttonStyles.color2 = buttonStyles.color1;
//            buttonStyles.color3 = buttonStyles.color2;
//            buttonStyles.color4 = buttonStyles.color2;
//        }
//
//        var elms = $('.example-button span')
//
//        _.each(elms, function(e){
//            var height = $(e).parent().height()/2
//            var elHeight = $(e).height()
//            var diff = height-(elHeight/2);
//            $(e).css('top', diff+'px')
//        })
//
//        selectedButtons.css('gradient', buttonStyles.gradient)
//        selectedButtons.css('border-radius', buttonStyles.borderRadius+'px')
//        selectedButtons.css('font-size', buttonStyles.textSize+'px')
//        selectedButtons.css('color', buttonStyles.textColor)
//
//        var text = JSON.stringify(buttonStyles)
//
//        $('.resizable.selected').each(function(){
//            $(this).attr('data-style', text)
//            $(this).attr('data-style-changed', 'true');
//        })
//
//
//        inactive.css('border-radius', buttonStyles.borderRadius+'px')
//        inactive.css('font-size', buttonStyles.textSize+'px')
//        inactive.css('color', buttonStyles.textColor)
//
//        active.css('border-radius', buttonStyles.borderRadius+'px')
//        active.css('font-size', buttonStyles.textSize+'px')
//        active.css('color', buttonStyles.textColor)
//    }
//
//    function saveMassButtonStyle()
//    {
//        var updatedButtonStyles = [];
//
//        $('.resizable').each(function(){
//            var styleChanged = $(this).attr('data-style-changed')
//            var style = $(this).attr('data-style')
//            var id = $(this).attr('id')
//
//            if(styleChanged && style) {
//                updatedButtonStyles.push({id: id, style: style})
//            }
//        })
//
//        if(updatedButtonStyles.length > 0) {
//
//            $.ajax({
//                url: '/buttons/mass-apply-style-slim',
//                data: {buttons: updatedButtonStyles},
//                error: function () {
//                    $('#info').html('<p>An error has occurred</p>');
//                    $('#overlay').remove()
//                },
//                type: 'POST',
//                success: function () {
//                    location.reload()
//                }
//            })
//
//        } else {
//            alert('Please edit a style before submitting changes.')
//        }
//
//    }

</script>