<div id="ButtonEditModal" class="modal fade" style="height: 100vh">
    <style>
        .multiselect-item.multiselect-group label{
            font-weight: bold!important;
        }
    </style>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                {{--<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>--}}
                <h4 class="modal-title">Button Editor</h4>
            </div>
            <div class="modal-body" >
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="DisplayName"><strong>Button Text</strong></label>
                            <input type="text" class="input form-control" name="DisplayName" id="DisplayName" value="{{$button['displayname']}}" placeholder="Name this button">
                        </div>
                    </div>
                </div>
                <ul class="nav nav-tabs" id="nav-tabs">
                    <li class="active" data-save-action="products"><a data-toggle="tab" href="#menu1">Button Actions</a></li>
                    {{--                    <li><a data-toggle="tab" href="#menu3">Copy Button</a></li>--}}
                </ul>
                <div class="tab-content">
                    <div id="menu1" class="tab-pane fade in active">
                        <div class="row" style="padding-top: 20px;">
                            <div class="col-sm-8">
                                <div class="col-lg-12">

                                    <form id="form-user_v1" name="form-user_v1">
                                        {{ csrf_field() }}
                                        <div class="typeahead__container">
                                            <div class="typeahead__field">
                                                <div class="typeahead__query">
                                                    <input class="js-typeahead-user_v1 form-control form-control-lg" style="width: 100%" name="user_v1[query]" placeholder="Search" autocomplete="off">
                                                </div>
                                                {{--                                                <div class="typeahead__button">--}}
                                                {{--                                                    <button type="submit">--}}
                                                {{--                                                        <i class="typeahead__search-icon"></i>--}}
                                                {{--                                                    </button>--}}
                                                {{--                                                </div>--}}
                                            </div>
                                        </div>
                                    </form>

                                </div>
                            </div>
                            <div class="col-sm-2">
                                <button type="button" class="btn btn-success" id="save-products">Add to Button</button>
                            </div>
                        </div>
                    </div>

                    {{--                    <div id="menu2" class="tab-pane fade">--}}
                    {{--                        <div class="row" style="padding-top: 20px;">--}}
                    {{--                            <div class="col-sm-8">--}}
                    {{--                                <div class="form-group">--}}
                    {{--                                    <label><strong>Functions</strong></label><br />--}}
                    {{--                                    <select class="form-control" id="commands" name="commands">--}}
                    {{--                                        @if(!empty($intCommand['pages']))--}}
                    {{--                                            <optgroup class="group-1" label="Pages">--}}
                    {{--                                                @foreach($intCommand['pages'] as $c)--}}
                    {{--                                                    <option value="1_{{$c['CommandUID']}}">{{$c['DisplayName']}}</option>--}}
                    {{--                                                @endforeach--}}
                    {{--                                            </optgroup>--}}
                    {{--                                        @endif--}}
                    {{--                                        @if(!empty($intCommand['commands']))--}}
                    {{--                                        <optgroup class="group-1" label="Commands">--}}
                    {{--                                            @foreach($intCommand['commands'] as $c)--}}
                    {{--                                                <option value="1_{{$c['CommandUID']}}">{{$c['DisplayName']}}</option>--}}
                    {{--                                            @endforeach--}}
                    {{--                                        </optgroup>--}}
                    {{--                                        @endif--}}
                    {{--                                        @if(!empty($intCommand['payments']))--}}
                    {{--                                            <optgroup class="group-1" label="Payment Types">--}}
                    {{--                                                @foreach($intCommand['payments'] as $c)--}}
                    {{--                                                    <option value="2_{{$c['CommandUID']}}">{{$c['DisplayName']}}</option>--}}
                    {{--                                                @endforeach--}}
                    {{--                                            </optgroup>--}}
                    {{--                                        @endif--}}
                    {{--                                            @if(!empty($intCommand['courses']))--}}
                    {{--                                                <optgroup class="group-3" label="Courses">--}}
                    {{--                                                    @foreach($intCommand['courses'] as $c)--}}
                    {{--                                                        <option value="4_{{$c['CommandUID']}}">{{$c['DisplayName']}}</option>--}}
                    {{--                                                    @endforeach--}}
                    {{--                                                </optgroup>--}}
                    {{--                                            @endif--}}
                    {{--                                    </select>--}}
                    {{--                                </div>--}}
                    {{--                            </div>--}}
                    {{--                            <div class="col-sm-2">--}}
                    {{--                                <button type="button" class="btn btn-success" id="save-commands" style="margin-top: 25px;">Add Function</button>--}}
                    {{--                            </div>--}}

                    {{--                        </div>--}}
                    {{--                    </div>--}}

                    {{--                    <div id="discounts" class="tab-pane fade">--}}
                    {{--                        <div class="row" style="padding-top: 20px;">--}}
                    {{--                            <div class="col-sm-8">--}}
                    {{--                                <div class="form-group">--}}
                    {{--                                    <label><strong>Discounts</strong></label><br />--}}
                    {{--                                    <select class="form-control" id="selectDiscount" name="discounts">--}}
                    {{--                                        @if(!empty($intCommand['discounts']))--}}
                    {{--                                            <optgroup class="group-3" label="Discounts">--}}
                    {{--                                                @foreach($intCommand['discounts'] as $c)--}}
                    {{--                                                    <option value="3_{{$c['CommandUID']}}">{{$c['displayname']}}</option>--}}
                    {{--                                                @endforeach--}}
                    {{--                                            </optgroup>--}}
                    {{--                                        @endif--}}
                    {{--                                    </select>--}}
                    {{--                                </div>--}}
                    {{--                            </div>--}}
                    {{--                            <div class="col-sm-2">--}}
                    {{--                                <button type="button" class="btn btn-success" id="save-discounts" style="margin-top: 25px;">Add Discount</button>--}}
                    {{--                            </div>--}}

                    {{--                        </div>--}}
                    {{--                    </div>--}}

                    {{--                    <div id="menu3" class="tab-pane fade">--}}
                    {{--                        <h4>Copy Button</h4>--}}
                    {{--                        <p>By copying this button to selected pages any existing links, styling and text will be replaced. Please make sure the details are correct before proceeding.</p>--}}
                    {{--                        <div class="row">--}}
                    {{--                            <div class="col-sm-8">--}}
                    {{--                                <select class="form-control" id="pages" name="pages" multiple="multiple">--}}
                    {{--                                    @foreach($intCommand['pages'] as $page)--}}
                    {{--                                    <option value="{{$page['CommandUID']}}">{{$page['DisplayName']}}</option>--}}
                    {{--                                    @endforeach--}}
                    {{--                                </select>--}}
                    {{--                            </div>--}}
                    {{--                            <div class="col-sm-2">--}}
                    {{--                                <button class="btn btn-md btn-primary" id="copy-button" data-id="{{$button['id']}}">Copy To Selected Pages</button>--}}
                    {{--                                <button class="btn btn-md btn-success" id="copy-confirm" data-id="{{$button['id']}}" style="display: none;">Confirm Copy</button>--}}
                    {{--                                <button class="btn btn-md btn-success" id="copy-loading" data-id="{{$button['id']}}" style="display: none;" disabled="disabled"><i class="fa fa-spinner fa-spin"></i> Copying....</button>--}}
                    {{--                            </div>--}}
                    {{--                        </div>--}}
                    {{--                    </div>--}}
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <hr />
                        <h4>Button Actions</h4>
                        <p><small>Actions programmed below happen in the order shown, to reorder functions and products drag and drop the buttons.</small></p>
                        <style>
                            #sortable2, #sortable2 li {
                                text-indent: 0px;
                                margin: 0px;
                                padding: 0px;
                            }
                            #sortable2 li {
                                width: 130px;
                                margin: 5px;
                                padding: 3px;
                            }
                        </style>
                        <ul id="sortable2" class="sortable grid">
                            @foreach($button['links'] as $k => $l)
                                @include('modules.buttons.includes.remove-link', ['link' => $l])
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success float-left" id="save-name">Save & Close</button>
            </div>
        </div>
    </div>
</div>
<script>

    $('#sortable2').sortable().bind('sortupdate', function() {

        var ordered = [];

        var links = $('#sortable2 li');

        $.each(links, function(key, val){
            $(val).attr('data-position', key)

            var link = $(val)
            ordered.push({ id: link.data('id'), position: key})

        });

        saveLinkOrder(ordered);

    });
    // global vars
    var saveAction = 'products';
    var buttonGuid = '{{$button['guid']}}';
    var buttonId = {{$button['id']}};
    buttonStyles = {};
    buttonStyles.id = {{$button['id']}};

</script>