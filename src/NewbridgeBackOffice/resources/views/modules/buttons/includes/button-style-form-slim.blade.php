<div id="buttonStyleFormSlim">
    <div class="col-lg-12">
        <h4><PERSON><PERSON></h4>
        <div class="form-group hidden">
            <label><strong>Background Type</strong></label>
            <select class="form-control" id="backgroundType" name="backgroundType" onchange="showHideGradient()">
                <option selected value="0">Solid</option>
            </select>
        </div>
    </div>
    <div class="col-sm-12" id="solidbg">
        <div class="form-group">
            <label for="color1"><strong>Background Colour</strong></label>
            <input type="color" name="color1" id="color1" class="pull-right"
                   value="{{ isset($button) ? $button['style']['color1'] : '#F3F3F3' }}">
        </div>
        <div class="form-group">
            <label for="color1"><strong>Text Colour</strong></label>
            <input type="color" name="text-color" id="text-color" class="pull-right"
                   value="{{ isset($button) ? $button['style']['foreground'] : '#000000' }}">
        </div>
        <div class="form-group">
            <label for="color1" style="padding-top: 7px;"><strong>Text Size</strong></label>
            <div class="form-group input-group pull-right" style="width: 110px;">
                <input type="number" id="text-size" name="text-size" class="form-control" class="pull-right"
                       value="{{ isset($button) ? $button['style']['fontsize'] : '16'}}">
                <span class="input-group-addon">px</span>
            </div>
        </div>
        {{--    <dic class="form-group">--}}
        {{--        <div class="dummybutton">--}}
        {{--            Style Check--}}
        {{--        </div>--}}
        {{--    </dic>--}}
        {{--    <div class="form-group">--}}
        {{--        <button class="btn btn-success btn-md btn-block" onclick=""><i class="fa fa-paste"></i> Paste Style</button>--}}
        {{--    </div>--}}
        <div class="form-group">
            <button class="btn btn-success btn-md btn-block" onclick="saveMassButtonStyle()">Apply Button Styles
            </button>
        </div>
    </div>
    <div style="clear:both;"></div>
    {{--<hr />--}}
    {{--<div class="col-sm-12">--}}
    {{--    <div class="form-group">--}}
    {{--        <label for="color1" style="padding-top: 7px;">Text Size</label>--}}
    {{--        <div class="form-group input-group pull-right" style="width: 110px;">--}}
    {{--            <input type="number" id="text-size" name="text-size" class="form-control" class="pull-right" value="{{ isset($button) ? $button['style']['fontsize'] : '16'}}">--}}
    {{--            <span class="input-group-addon">px</span>--}}
    {{--        </div>--}}
    {{--    </div>--}}
    {{--</div>--}}
    {{--<div class="col-sm-12">--}}
    {{--    <div class="form-group">--}}
    {{--        <label for="color1" style="padding-top: 7px;">Border Radius</label>--}}

    {{--        <div class="form-group input-group pull-right" style="width: 110px;">--}}
    {{--            <input type="number" id="border-radius" name="border-radius" class="form-control pull-right" value="{{ isset($button) ?  $button['style']['cornerradius'] : '10' }}">--}}
    {{--            <span class="input-group-addon">deg</span>--}}
    {{--        </div>--}}
    {{--    </div>--}}
    {{--    <br />--}}
    {{--    <br >--}}

    {{--</div>--}}
</div>

<style>
    .dummybutton {
        width: 80%;
        height: 35px;
        background-color: blue;
        color: white;
        text-align: center;

    }
</style>

<script>
    setTimeout(function () {
        updateAllButtonStylesSlim()
    }, 300)

    $('#buttonStyleFormSlim').on('ButtonStyleSelect', function (event, styleData) {
        $('#color1').val(styleData.color1);
        $('#color2').val(styleData.color2);
        $('#text-color').val(styleData.textColor);
        $('#text-size').val(styleData.textSize);
    })

    $(document).on('keyup. change', '#buttonStyleFormSlim input, #buttonStyleFormSlim select', function () {

        updateAllButtonStylesSlim()
    });

    function updateAllButtonStylesSlim() {
        var gradient = $('#backgroundType').val();

        buttonStyles.color1 = $('#color1').val();
        buttonStyles.color2 = $('#color2').val();
        buttonStyles.textSize = $('#text-size').val();
        buttonStyles.textColor = $('#text-color').val();
        buttonStyles.borderRadius = $('#border-radius').val();
        buttonStyles.gradient = gradient;

        buttonStyles.button_type = $('#button_type').val();
        buttonStyles.page = $('#button-page-selection').val();

        var active = $("body .example-button.active");
        var inactive = $("body .example-button.inactive");
        var selectedButtons = $('.resizable.selected');

        if (gradient == 1) {

            buttonStyles.color3 = buttonStyles.color2;
            buttonStyles.color4 = buttonStyles.color1;

            active.css('background', 'linear-gradient(' + buttonStyles.color3 + ',' + buttonStyles.color4 + ')')
            inactive.css('background', 'linear-gradient(' + buttonStyles.color1 + ',' + buttonStyles.color2 + ')')
            selectedButtons.css('background', 'linear-gradient(' + buttonStyles.color1 + ',' + buttonStyles.color2 + ')')

        } else {

            inactive.css('background', buttonStyles.color1)
            active.css('background', buttonStyles.color2)
            selectedButtons.css('background', buttonStyles.color1)

            buttonStyles.color2 = buttonStyles.color1;
            buttonStyles.color3 = buttonStyles.color2;
            buttonStyles.color4 = buttonStyles.color2;
        }

        var elms = $('.example-button span')

        _.each(elms, function (e) {
            var height = $(e).parent().height() / 2
            var elHeight = $(e).height()
            var diff = height - (elHeight / 2);
            $(e).css('top', diff + 'px')
        })

        selectedButtons.css('gradient', buttonStyles.gradient)
        selectedButtons.css('border-radius', buttonStyles.borderRadius + 'px')
        selectedButtons.css('font-size', buttonStyles.textSize + 'px')
        selectedButtons.css('color', buttonStyles.textColor)

        var text = JSON.stringify(buttonStyles)

        $('.resizable.selected').each(function () {
            $(this).attr('data-style', text)
            $(this).attr('data-style-changed', 'true');
        })


        inactive.css('border-radius', buttonStyles.borderRadius + 'px')
        inactive.css('font-size', buttonStyles.textSize + 'px')
        inactive.css('color', buttonStyles.textColor)

        active.css('border-radius', buttonStyles.borderRadius + 'px')
        active.css('font-size', buttonStyles.textSize + 'px')
        active.css('color', buttonStyles.textColor)
    }

    function saveMassButtonStyle() {
        var updatedButtonStyles = [];

        $('.resizable').each(function () {
            var styleChanged = $(this).attr('data-style-changed')
            var style = $(this).attr('data-style')
            var id = $(this).attr('id')

            if (styleChanged && style) {
                updatedButtonStyles.push({id: id, style: style})
            }
        })

        if (updatedButtonStyles.length > 0) {

            $.ajax({
                url: '/buttons/mass-apply-style-slim',
                data: {buttons: updatedButtonStyles},
                error: function () {
                    $('#info').html('<p>An error has occurred</p>');
                    $('#overlay').remove()
                },
                type: 'POST',
                success: function () {
                    location.reload()
                }
            })

        } else {
            notificationBar('error', 'Please edit a style before submitting changes.')
        }

    }

</script>