@extends('layouts.master')

@section('content')
    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h3>Update Guestline Connection
            <br />
            <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small>
        </h3>
    </div>

    <form id="CreateForm" action="" method="POST">
        {{ csrf_field() }}
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Update Guestline Connection
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Default Site</strong></label><br>
                                    <select name="defaultsite" id="defaultsite" class="form-control">
                                        <option value="">None</option>
                                        @foreach($sites as $site)
                                            <option @if($credential->value6 == $site->site_num) selected @endif value="{{$site->site_num}}">{{$site->site_name}}</option>
                                        @endforeach
                                    </select>
                                    @error('defaultsite')
                                    <span class="text-danger"><small>{{$message}}</small></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Hotel Name</strong></label>
                                    <input type="text" class="form-control" name="name" value="{{$credential->value1}}"/>
                                    @error('name')
                                    <span class="text-danger"><small>{{$message}}</small></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Site ID</strong></label>
                                    <input type="text" class="form-control" name="site_id" value="{{$credential->value2}}"/>
                                    @error('device_id')
                                    <span class="text-danger"><small>{{$message}}</small></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Device ID</strong></label>
                                    <input type="text" class="form-control" name="device_id" value="{{$credential->value3}}"/>
                                    @error('site_id')
                                    <span class="text-danger"><small>{{$message}}</small></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Username</strong></label>
                                    <input type="text" class="form-control" name="username" value="{{$credential->value4}}"/>
                                    @error('username')
                                        <span class="text-danger"><small>{{$message}}</small></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Password</strong></label>
                                    <input type="text" class="form-control" name="password" value="{{$credential->value5}}"/>
                                    @error('password')
                                    <span class="text-danger"><small>{{$message}}</small></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Region</strong></label>
                                    <select name="region" id="region" class="form-control">
                                        <option value="we" @if($credential->value8=='we' || empty($credential->value8)) selected @endif>Western Europe</option>
                                        <option value="ap" @if($credential->value8=='ap') selected @endif>Asia Pacific</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel-footer">
                        <div class="row">
                            <div class="col-xs-2 pull-right">
                                <button class="btn btn-success" type="submit">Update</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

@endsection