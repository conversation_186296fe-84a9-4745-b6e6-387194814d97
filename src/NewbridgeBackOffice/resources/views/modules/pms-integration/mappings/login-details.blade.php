@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/company/sites" class="">Manage Sites</a></li>
        <li>Edit Sites</li>
    </ol>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">PMS Configuration
                <br />
                <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small>
            </h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>


    <form id="mapping-form" action="/company/pms-configuration/guestline/save-key" method="POST" enctype="multipart/form-data">
        @if ($errors->any())
            <div class="alert alert-danger">
                Please ensure all fields have a value before submitting this form
            </div>
        @endif

        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Enter Your access details for your PMS. <br /> <small>This will be provided by your PMS Provider usually by email.</small>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="service"><strong>Device ID</strong></label>
                                    <input type="text" name="device_id" value="{{old('device_id')}}" class="form-control input-md">
                                </div>
                            </div>
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="service"><strong>Site ID</strong></label>
                                    <input type="text" name="site_id" value="{{old('site_id')}}" class="form-control input-md">
                                </div>
                            </div>
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="service"><strong>Username</strong></label>
                                    <input type="text" name="username" value="{{old('username')}}" class="form-control input-md">
                                </div>
                            </div>
                            <div class="col-xs-12 col-md-6">
                                <div class="form-group">
                                    <label for="service"><strong>Password</strong></label>
                                    <input type="text" name="password" value="{{old('password')}}" class="form-control input-md">
                                </div>
                            </div>
                            <div class="col-xs-12">
                                <button class="btn btn-success btn-block">Save and Configure Integration</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

@endsection