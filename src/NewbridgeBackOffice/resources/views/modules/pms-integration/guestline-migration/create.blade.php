@extends('layouts.master')

@section('content')
    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h3>Create Guestline Migration</h3>
    </div>

    <form id="CreateForm" action="" method="POST">
        {{ csrf_field() }}
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Create Guestline Migration
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Target Company</strong></label><br>
                                    <select name="company" id="company" class="form-control">
                                        <option value="">None</option>
                                        @foreach($companies as $company)
                                            <option value="{{$company->company_id}}">{{$company->company_name}}</option>
                                        @endforeach
                                    </select>
                                    @error('company')
                                    <span class="text-danger"><small>{{$message}}</small></span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Site ID</strong></label>
                                    <input type="text" class="form-control" name="site_id"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Device ID</strong></label>
                                    <input type="text" class="form-control" name="device_id"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Username</strong></label>
                                    <input type="text" class="form-control" name="username"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Password</strong></label>
                                    <input type="text" class="form-control" name="password"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel-footer">
                        <div class="row">
                            <div class="col-xs-2 pull-right">
                                <button class="btn btn-success" type="submit">Create</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

@endsection