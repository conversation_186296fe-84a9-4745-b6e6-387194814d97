@extends('layouts.master')

@section('section-name') View Transactions @stop

@section('content')
    <script>
        var table = null;
    </script>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">View Transactions</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row" data-step="1">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-heading">
                    Transaction Filters
                </div>
                <div class="panel-body">
                    <form>
                        {{ csrf_field() }}
                        <div class="form-group col-md-12">
                            <label>Select Site</label><br />

                            <?php
                            $sites = \NewbridgeWeb\Repositories\Sites::where('company_id', Auth::user()->company_id)->get();
                            ?>

                            {{--<select name="site" class="form-control input">--}}
                                {{--@foreach($sites as $site)--}}
                                    {{--<option @if($report['site'] == $site->site_num) selected @endif value="{{$site->site_num}}">{{$site->site_name}}</option>--}}
                                {{--@endforeach--}}
                            {{--</select>--}}
                        </div>

                        <div class="form-group col-md-12">
                            <label>Start / End Date & Time</label>
                            <input type="hidden" id="start-datetime" value="" name="start">
                            <input type="hidden" id="end-datetime" value="" name="end">
                            <div id="reportrange-start"
                                 style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
                                <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
                                <span></span> <b class="caret"></b>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group col-md-6">
                                <button class="btn btn-md btn-success">Update Results</button>
                            </div>

                        </div>
                    </form>

                    <hr/>

                </div>
            </div>
        </div>
    </div>


    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    View Transactions<br />
                    <small>Select a row and click view transaction to view all the details.</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                            <tr>
                                <th width="100">ID</th>
                                <th>Date / Time</th>
                                <th>Clerk</th>
                                <th>Sub Total</th>
                                <th>Discount Amount</th>
                                <th>Total</th>
                                <th>Order Number</th>
                                <th>Location - Table</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="view-modal-area"></div>

@stop

@push('scripts')
<script src="//cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="//cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="//cdn.datatables.net/plug-ins/1.10.19/dataRender/datetime.js"></script>

<script>
    var permissions = {};
    permissions.can_view = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'view_transactions') == 1 ? 1 : 0 }};

    @if(!Request::input('start') && !Request::input('end'))
        var start1 = moment(carbonDate).subtract(6, 'days').startOf('day');
        var end1 = moment(carbonDate).endOf('day');
    @else
        var start1 = moment("{{Request::input('start')}}");
        var end1 = moment("{{Request::input('end')}}");
    @endif

    var openTransaction = false;
    @if(Request::get('transaction_id') && Request::get('transaction_id') != '')
        openTransaction = {{Request::get('transaction_id')}}
    @endif

    function cb(start, end) {
        $('#reportrange-start span').html(start.format('MMMM D, YYYY HH:mm:ss') + ' - ' + end.format('MMMM D, YYYY HH:mm:ss'));
    }

    $('#reportrange-start').daterangepicker({
        startDate: start1,
        endDate: end1,
        autoApply: true,
        timePicker: true,
        locale: {
            format: 'DD/MMM/YYYY HH:mm:ss'
        },
        ranges: {
            'Today': [moment(carbonDate).startOf('day'), moment(carbonDate).endOf('day')],
            'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day'), moment(carbonDate).subtract(1, 'days').endOf('day')],
            'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day'), moment(carbonDate).endOf('day')],
            'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day'), moment(carbonDate).endOf('day')],
            'This Month': [moment(carbonDate).startOf('month'), moment(carbonDate).endOf('month')],
            'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month'), moment(carbonDate).subtract(1, 'month').endOf('month')]
        },
    }, function(start, end, label) {
        $('#start-datetime').val(start.format('YYYY-MM-DD HH:mm:ss'))
        $('#end-datetime').val(end.format('YYYY-MM-DD HH:mm:ss'))
    }, cb);

    $('#reportrange-start').on('apply.daterangepicker', function (ev, picker) {
        $('#reportrange-start span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
    });

    cb(start1, end1)

</script>

<script src="/js/transactions.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

<script>
 $(function(){
     $('#dateSearch').datetimepicker({
         format: 'DD/MM/YYYY HH:mm:ss',
     });
 })
</script>
@endpush
