<div id="EditModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Edit User ({{$user->username}})</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="EditForm" autocomplete="off">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic User Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Username</strong></label>
                                                <input readonly autocomplete="off" type="text" name="username"
                                                       class="form-control" value="{{$user->username}}">
                                                <input type="hidden" value="{{$user->id}}" name="id">
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Email Address</strong></label>
                                                <input autocomplete="off" readonly type="text" class="form-control"
                                                       name="email" value="{{$user->email}}"/>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Name</strong></label>
                                                <input autocomplete="off" readonly type="text" class="form-control"
                                                       name="name" value="{{$user->name}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Change Password
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>New Password</strong></label>
                                                <input autocomplete="off" readonly type="password" class="form-control"
                                                       name="new_password" value=""/>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Confirm New Password</strong></label>
                                                <input autocomplete="off" readonly type="password" class="form-control"
                                                       name="confirm_new_password" value="" disabled/>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label>
                                                    <input type="checkbox" readonly name="email_password" value="1">
                                                    Email new password to the user?
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Area & Site Restrictions
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-4 col-xs-6">
                                            <div class="form-group">
                                                <label for="available_sites">Accessible Sites</label>
                                                <select name="available_sites[]" id="available_sites"
                                                        class="multiselect form-control" multiple>
                                                    @foreach($sites as $site)
                                                        <option @if(in_array($site->site_num, $userSites)) selected
                                                                @endif value="{{$site->site_num}}">{{$site->site_name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-xs-6">
                                            <div class="form-group">
                                                <label for="available_sites">Accessible Areas</label>
                                                <select name="areas[]" id="areas" class="multiselect form-control"
                                                        multiple>
                                                    @foreach($areas as $area)
                                                        <option @if(in_array($area->guid, $user->areas->pluck('area_guid')->toArray())) selected
                                                                @endif value="{{$area->guid}}">{{$area->area_name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default" id="roles">
                                <div class="panel-heading">
                                    Manage Roles & Permissions
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        @foreach($roles as $r)
                                            <div class="col-md-4 col-xs-6">
                                                <div class="form-group">
                                                    <div class="checkbox">
                                                        <label>
                                                            <input type="checkbox" data-toggle="toggle" name="roles[]"
                                                                   @if(in_array($r['id'], $currentRoles)) checked
                                                                   @endif value="{{$r['id']}}">
                                                            {{$r['display_name']}}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                                <div id="qr-area"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="editSubmit">Update</button>
            </div>
        </div>
    </div>
</div>

<script>
    $('#editSubmit').on('click', function () {
        $('#EditForm').submit()
    })

    var companyExt = '{{ '@'.strtolower(str_replace(' ', '', $company->company_name)) }}';

    $('body').on('blur', '[name="username"]', function () {
        var val = $(this).val().split('@')
        $(this).val(val[0] + companyExt)
    })

    jQuery.validator.addMethod("usernameTaken", function (value, element) {

        var isSuccess = false;
        var val = value.split('@');
        var username = val[0] + companyExt;

        $.ajax({
            type: "GET",
            url: "/users/check-username/" + val,
            async: false,
            success: function (data) {
                isSuccess = data.taken == 0;
            }
        });
        return isSuccess;
    }, "Username taken, please choose an alternative");

    $(function () {
        $('#roles [type="checkbox"]').bootstrapToggle();
        $('[data-toggle="tooltip"]').tooltip();

        $('body').on('focus', 'input', function () {
            $(this).removeAttr('readonly')
        })
        $('body').on('click', 'input', function () {
            $(this).removeAttr('readonly')
        })

        $('body').on('keyup', '[name="new_password"]', function () {
            var el = $(this)

            if (el.val() != '') {
                $('[name="confirm_new_password"]').attr('disabled', false)
            } else {
                $('[name="confirm_new_password"]').attr('disabled', true)
            }

        })
    })

    var form = $('#EditForm')

    // validate signup form on keyup and submit
    $("#EditForm").validate({
        rules: {
            first_name: "required",
            last_name: "required",
            username: {
                required: true
            },
            email: "required",
            confirm_new_password: {
                required: function (element) {
                    if ($('[name="new_password"]').val() != '') {
                        return true
                    }
                    return false;
                },
                equalTo: '[name="new_password"]'
            }
        },
        submitHandler: function (form) {
            $.ajax({
                type: "PUT",
                url: "/users/edit-single",
                data: $(form).serialize(),
                success: function (data) {
                    if (data.redirect) {
                        window.location.href = '/'
                    } else {

                        $('#EditModal').modal('toggle')
                        table.ajax.reload();

                    }
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    })
</script>