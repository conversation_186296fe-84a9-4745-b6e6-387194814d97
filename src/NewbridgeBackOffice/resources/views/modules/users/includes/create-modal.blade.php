<div id="CreateModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Add User</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="CreateForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic User Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Username</strong></label>
                                                <input readonly autocomplete="off" type="text" name="username"
                                                       class="form-control" placeholder="Username">
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Email Address</strong></label>
                                                <input autocomplete="off" readonly type="text" class="form-control"
                                                       name="email"/>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Name</strong></label>
                                                <input autocomplete="off" readonly type="text" class="form-control"
                                                       name="name"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Area & Site Restrictions
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-4 col-xs-6">
                                            <div class="form-group">
                                                <label for="available_sites">Accessible Sites</label>
                                                <select name="available_sites[]" id="available_sites"
                                                        class="multiselect form-control" multiple>
                                                    @foreach($sites as $site)
                                                        <option value="{{$site->site_num}}">{{$site->site_name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-xs-6">
                                            <div class="form-group">
                                                <label for="available_sites">Accessible Areas</label>
                                                <select name="areas[]" id="areas" class="multiselect form-control"
                                                        multiple>
                                                    @foreach($areas as $area)
                                                        <option value="{{$area->guid}}">{{$area->area_name}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Change Password
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>New Password</strong></label>
                                                <input autocomplete="off" readonly type="password" class="form-control"
                                                       name="password"/>
                                            </div>
                                        </div>
                                        <div class="col-sm-6 col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Confirm New Password</strong></label>
                                                <input type="password" class="form-control" name="confirm_password"
                                                       disabled/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Manage Roles & Permissions
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        @foreach($roles as $r)
                                            <div class="col-md-4 col-xs-6">
                                                <div class="form-group">
                                                    <div class="checkbox">
                                                        <label>
                                                            <input type="checkbox" data-toggle="toggle" name="roles[]"
                                                                   value="{{$r['id']}}">
                                                            {{$r['display_name']}}
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="createSubmit">Create</button>
            </div>
        </div>
    </div>
</div>