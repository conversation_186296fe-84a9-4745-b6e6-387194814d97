@php use NewbridgeWeb\Http\Helpers\CurrencyHelper;use NewbridgeWeb\Http\Helpers\TimezoneHelper; @endphp
<style>
    .voids_reasons {
        font-size: 14px;
        font-weight: bold;
    }
</style>

<div id="ViewModal" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>View Order</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <style>
                    .void {
                        color: red;
                        text-decoration: line-through;
                    }
                </style>
                <form id="CreateForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Transaction Details
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="col-md-4 col-xs-12"><strong>Placed Date:</strong></div>
                                            <div class="col-md-8 col-xs-12">{{\Carbon\Carbon::parse($data['created_at'], 'UTC')->setTimezone(TimezoneHelper::getTimezone())->format('d/m/Y H:i')}}</div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="col-md-4 col-xs-12"><strong>Due Date:</strong></div>
                                            <div class="col-md-8 col-xs-12">{{\Carbon\Carbon::parse($data['data']['duetime'], 'UTC')->setTimezone(TimezoneHelper::getTimezone())->format('d/m/Y H:i')}}</div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="col-md-4 col-xs-12"><strong>Order #:</strong></div>
                                            <div class="col-md-8 col-xs-12">{{$data['data']['order_number']}}</div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="col-md-4 col-xs-12"><strong>Table #:</strong></div>
                                            <div class="col-md-8 col-xs-12">{{$data['data']['location_name']}}
                                                / {{$data['data']['table_number']}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="panel panel-primary">
                                <div class="panel-heading">
                                    Transaction Items
                                    <a hre="#" class="pull-right" data-toggle="collapse" style="color: white;"
                                       data-target="#items">Show/Hide
                                    </a>
                                </div>
                                <style>
                                    #items .product {
                                        background-color: white;
                                        color: black;
                                        padding: 5px;
                                    }

                                    #items .modifier {
                                        background-color: lightgoldenrodyellow;
                                        color: black;
                                        padding: 5px;
                                    }

                                    #items .discount {
                                        background-color: lightgreen;
                                        color: black;
                                        padding: 5px;
                                    }

                                    #items .refund, #items .void {
                                        background-color: red;
                                        color: white;
                                        padding: 5px;
                                    }

                                    .item {
                                        text-align: left;
                                    }

                                    .price {
                                        text-align: right;
                                    }

                                    .original-price {
                                        text-decoration: line-through;
                                    }

                                    .discount-price {
                                        color: red;
                                    }

                                    .discount .price {
                                        color: black;
                                    }

                                    .void .price {
                                        text-decoration: line-through;
                                    }

                                    #items .freetext {
                                        background-color: white;
                                        color: red;
                                        padding: 5px;
                                    }

                                    #items .expenses {
                                        background-color: cornflowerblue;
                                        color: white;
                                        padding: 5px;
                                    }

                                    #items .wastage {
                                        background-color: dodgerblue;
                                        color: white;
                                        padding: 5px;
                                    }

                                    #items .wastage .price {
                                        text-decoration: line-through;
                                    }
                                </style>
                                <div id="items" class="panel-body in">

                                    @if(isset($data['data']['details']))
                                        @foreach($data['data']['details'] as $d)

                                            <div class="col-md-12 product">
                                                <div class="col-md-8 item"> {{$d['displayname']}}</div>
                                                <div class="col-md-4 price">
                                                    <span class="price">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($d['net_value'], 2)}}</span>
                                                </div>
                                            </div>

                                            @if(isset($d['details']))
                                                @foreach($d['details'] as $m)
                                                    <div class="col-md-10 col-md-offset-2 modifier">
                                                        <div class="col-md-8 item"><i
                                                                    class="fa fa-subtract"></i> {{$m['displayname']}}
                                                        </div>
                                                        <div class="col-md-4 price">
                                                            <span class="price">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($m['net_value'], 2)}}</span>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif
                                        @endforeach
                                    @endif

                                </div>
                                <div class="panel-footer">
                                    <div class="row">
                                        <div class="col-md-2" style="text-align: center;">
                                            <strong>Subtotal</strong> <br/>
                                            <span>{!! CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($data['data']['total'], 2)}}</span>
                                        </div>

                                        <div class="col-md-2" style="text-align: center;">
                                            <strong>Total</strong><br/>
                                            <span>{!! CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($data['data']['total'], 2)}}</span>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="panel panel-primary">
                                <div class="panel-heading">
                                    Payment Information
                                    <a href="#" class="pull-right" data-toggle="collapse" style="color: white;"
                                       data-target="#payments">Show/Hide
                                    </a>
                                </div>
                                <div id="payments" class="panel-body collapse in">
                                    {{-- Genereted by js? --}}
                                </div>
                            </div>

                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
