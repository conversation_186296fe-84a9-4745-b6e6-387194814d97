<div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 item-area" style="background-color: #fff; margin-top: 20px;" id="order{{$order['id']}}">
    <div class="col-xs-12 order-list">
        <ul class="list-group" style="margin-bottom: 0px;">
            <li class="list-group-item list-header">
                <div class="row">
                    <div class="col-xs-6">
                        <strong>Table:</strong> {{$order['data']['location_name']}} - {{$order['data']['table_number']}}<br />

                        <strong>Due:</strong> {{\Carbon\Carbon::parse($order['data']['duetime'], 'UTC')->setTimezone(\NewbridgeWeb\Http\Helpers\TimezoneHelper::getTimezone())->format('d/m/Y H:i')}}<br />

                        <strong>Order No:</strong> {{$order['data']['order_number']}}
                    </div>
                    <div class="col-xs-6">
                        <strong>Customer:</strong> {{isset($order['data']['customer']['customer_name']) ? $order['data']['customer']['customer_name'] : ''}}<br />
                        <strong>Phone:</strong> {{isset($order['data']['customer']['customer_phone']) ? $order['data']['customer']['customer_phone'] : ''}}
                    </div>
                </div>
            </li>
        </ul>
        <ul class="list-group" style="min-height: 250px; max-height: 250px; margin-top: 0px; padding: 0px; overflow: auto; line-height: 10px;">

            @php
                $i = 0;
            @endphp
            @foreach($order['data']['details'] as $product)
                <li class="list-group-item product"><strong>{{$product['displayname']}}</strong></li>
                @if(isset($product['details']))
                    @foreach($product['details'] as $mod)
                        <li class="list-group-item modifier"> {{$mod['displayname']}}</li>
                        @php
                            $i = $i+1;
                        @endphp
                    @endforeach
                @endif
                @php
                    $i = $i+1;
                @endphp
            @endforeach
        </ul>
        @if($i > 9)
            <div class="col-xs-12" style="line-height: 20px; width: 100%; height: 20px; background: lightblue; color: #000; text-align: center"><i class="fa fa-sort-down"></i> scroll down for more <i class="fa fa-sort-down"></i></div>
        @endif

        @if($order['platform'] == 'newbridge')
        <div class="col-xs-6" style="margin: 0px; padding: 0px"><button class="btn btn-block btn-danger complete-button status-change" data-status="rejected" data-id="{{$order['id']}}">REJECT</button></div>
        <div class="col-xs-6" style="margin: 0px; padding: 0px"><button class="btn btn-block btn-success complete-button status-change" data-status="accepted" data-id="{{$order['id']}}">ACCEPT</button></div>
        <div><button class="btn btn-block btn-warning complete-button status-change" data-status="completed" data-id="{{$order['id']}}">COMPLETE</button></div>
        @endif
    </div>
</div>