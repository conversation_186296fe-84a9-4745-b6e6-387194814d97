<head>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.0/css/bootstrap.min.css">
    <link href="https://fonts.googleapis.com/css?family=Roboto+Slab:400,700" rel="stylesheet">
</head>
<body>

<style>
    body {
        font-size: 11px;
        font-family: 'Roboto Slab', serif;
    }
    td {
        padding: 2px;
    }
</style>
    <table style="width: 500px; border: white; margin: 0 auto;" class="text-center">
        <tr>
            <td><strong>{{$company->company_name}}</strong></td>
        </tr>
        <tr>
            <td>{{$company->address_line1}}, {{$company->address_line2}}</td>
        </tr>
        <tr>
            <td>{{$company->postcode}}</td>
        </tr>
    </table>
    <table style="width: 500px; border: white; margin: 0 auto; border-bottom: 1px dotted black" class="text-left">
        <tr>
            <td><strong>Table Number:</strong></td>
            <td class="text-right">{{$data->table_location}}</td>
        </tr>
        <tr>
            <td><strong>Date:</strong></td>
            <td class="text-right">{{\Carbon\Carbon::parse($data->finalised_date)->format('D d M Y H:i')}}</td>
        </tr>
        @if($data->customer)
            <tr>
                <td><strong>Customer:</strong></td>
                <td class="text-right">{{$data->customer->first_name}} {{$data->customer->last_name}}</td>
            </tr>
        @endif
    </table>

    @if($data->details)
    {{-- PRODUCTS --}}
    <table style="width: 500px; border: white 0; margin: 0 auto;" class="text-left">
        <tr>
            <td colspan="2"></td>
        </tr>
        <tr>
            <td colspan="2" class="text-center"><strong>Products</strong></td>
        </tr>
        @foreach($data->details as $d)
            @if($d->product && $d->command == 3 && ($d->command_type == 0 || $d->command_type == 6) )
                <tr>
                    <td @if($d->detail_id != null) style="text-indent: 30px;" @endif> @if($d->command_type === 6)(REFUND) @endif {{ucfirst($d->product->displayname)}}</td>
                    <td class="text-right">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($d->net_value, 2)}}</td>
                </tr>
            @endif
        @endforeach
        <tr>
            <td colspan="2"></td>
        </tr>
    </table>
    {{-- TOTAL --}}
    <table style="width: 500px; border: white 0; margin: 0 auto; border-bottom: 1px dotted black; margin-top: 20px;" class="text-left">
        <tr>
            <td><strong>Total</strong></td>
            <td class="text-right"><strong>{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($data->subtotal, 2)}}</strong></td>
        </tr>
    </table>

    {{-- DISCOUNTS --}}
    <table style="width: 500px; border: white 0; margin: 0 auto; border-bottom: 1px dotted black; margin-top: 20px;" class="text-left">
        <tr>
            <td colspan="2" class="text-center"><strong>Discounts</strong></td>
        </tr>
        @foreach($data->details as $d)
            @if(($d->command == 9 || $d->command == 10) && !in_array($d->command_type, [5,6]))
                <tr>
                    <td>{{$d->discount->displayname}}</td>
                    <td class="text-right">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($d->discount_value, 2)}}</td>
                </tr>
            @endif
        @endforeach

    </table>

    {{-- SUBTOTAL --}}
    <table style="width: 500px; border: white 0; margin: 0 auto; border-bottom: 1px dotted black; margin-top: 20px;" class="text-left">
        <tr>
            <td colspan="2"></td>
        </tr>
        <tr>
            <td><strong>Total</strong></td>
            <td class="text-right"><strong>{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($data->total, 2)}}</strong></td>
        </tr>
    </table>

    {{-- PAYMENTS --}}
    <table style="width: 500px; border: white 0; margin: 0 auto; border-bottom: 1px dotted black; margin-top: 20px;" class="text-left">
        <tr>
            <td colspan="2" class="text-center"><strong>Payments</strong></td>
        </tr>
        @foreach($data->payments as $payment)
        <tr>
            <td>{{$payment->type['name']}}</td>
            <td class="text-right">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($payment->amount, 2)}}</td>
        </tr>
        @endforeach
    </table>

    {{-- TAX --}}
    <table style="width: 500px; border: white 0; margin: 0 auto; border-bottom: 1px dotted black; margin-top: 20px; margin-bottom: 50px;" class="text-left">
        <tr>
            <td colspan="2" class="text-center"><strong>TAX Breakdown</strong></td>
        </tr>
        @foreach($tax as $rate)
            <tr>
                <td>{{$rate->tax_rate}}%</td>
                <td class="text-right">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($rate->tax_value_sum, 2)}}</td>
            </tr>
        @endforeach

        <tr style="border-top: 1px dotted black">
            <td colspan="2" class="text-center"><strong>Tax Number:</strong> {{$company->vat_number}} </td>
        </tr>
    </table>

    @endif

</body>
