<script>
    let table = null;
</script>


<div class="tab-pane" id="transactions">
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading">
                Customer Transactions
            </div>
            <div class="panel-body">
                <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Date / Time</th>
                        <th>Total</th>
                        <th>Points Earned</th>
                        <th>Points Spent</th>
                        <th>View Transaction</th>
                    </tr>

                </table>
            </div>
        </div>
    </div>
</div>



@push('scripts')
    <script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="/js/dataTables.editor.min.js"></script>

    <script>let customer_id = {!! $customer['id'] !!} </script>

    <script src="/js/customer-transactions.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

@endpush
