@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/customers" class="">Customers</a></li>
        <li>Create Customer</li>
    </ol>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Create a new Customer</h1>
        </div>
    </div>

    <form id="CreateForm" method="POST" action="/customers/create">
        {{ csrf_field() }}

        <div class="tab-pane active" id="details">
            <div class="row">
                <div class="col-md-12">

                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Basic Customer Information
                        </div>
                        <div class="panel-body">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Prefix</strong></label>
                                    <select class="form-control" name="prefix">
                                        <option value="Mr">Mr</option>
                                        <option value="Mrs">Mrs</option>
                                        <option value="Miss">Miss</option>
                                        <option value="Master">Master</option>
                                        <option value="Dr">Dr</option>
                                        <option value="Sir">Sir</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>First Name</strong></label>
                                    <input type="text" class="form-control" name="first_name" placeholder="Joe"/>
                                    @if($errors->has('first_name'))
                                        <div class="text-danger">
                                            {{ $errors->first('first_name') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Last Name</strong></label>
                                    <input type="text" class="form-control" maxlength="30" name="last_name"
                                           placeholder="Bloggs"/>
                                    @if($errors->has('last_name'))
                                        <div class="text-danger">
                                            {{ $errors->first('last_name') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Email Address</strong></label>
                                    <input type="text" class="form-control" maxlength="255" email name="email"
                                           placeholder="<EMAIL>"/>
                                    @if($errors->has('email'))
                                        <div class="text-danger">
                                            {{ $errors->first('email') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Telephone Number</strong></label>
                                    <input type="text" class="form-control" maxlength="30" name="telephone"
                                           placeholder="02920990810"/>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><strong>Marketing Emails</strong><br />
                                        <small>Would the customer like to receive marketing emails from your company?</small></label>
                                    <select class="form-control" name="opt_in" id="opt_in">
                                        <option value="0" selected>No</option>
                                        <option value="1">Yes</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-primary panel-collapse">
                    <div class="panel-heading">
                        Customer Address
                        <span class="pull-right clickable"><i class="glyphicon glyphicon-chevron-down"></i></span>
                    </div>
                    <div class="panel-body collapse">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Address Line 1</strong></label>
                                    <input type="text" class="form-control" name="address_line1"
                                           placeholder="e.g. 20 Centre Court"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Address Line 2</strong></label>
                                    <input type="text" class="form-control" name="address_line2"
                                           placeholder="e.g. Treforest Industrial Estate"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>City</strong></label>
                                    <input type="text" class="form-control" name="city" placeholder="e.g. Cardiff"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>County</strong></label>
                                    <input type="text" class="form-control" name="county"
                                           placeholder="e.g. Cardiff"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Postcode</strong></label>
                                    <input type="text" class="form-control" name="postcode"
                                           placeholder="e.g. CF37 5YR"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        Customer Type & Loyalty
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Loyalty Scheme</strong></label>
                                    <select class="form-control" name="group_guid">
                                        <option value="">Please select a group...</option>
                                        @foreach($groups as $g)
                                            <option value="{{$g->guid}}">{{$g->displayname}}</option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('group_guid'))
                                        <div class="text-danger">
                                            {{ $errors->first('group_guid') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Customer Type</strong></label>
                                    <select class="form-control" name="type_guid">
                                        <option value="">Please select a type...</option>
                                        @foreach($types as $t)
                                            <option value="{{$t->guid}}">{{$t->displayname}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Membership Number</strong></label>
                                    <input type="text" class="form-control" name="membership_no"
                                           placeholder="123456"/>
                                    @if($errors->has('membership_no'))
                                        <div class="text-danger">
                                            {{ $errors->first('membership_no') }}
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Loyalty Points Balance</strong></label>
                                    <input type="text" class="form-control" name="points_balance"
                                           placeholder="10.00"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Expiry Date</strong></label>
                                    <input type="text" class="form-control datepicker" name="expires" placeholder=""
                                           value="{{\Carbon\Carbon::now()->addYear()->toDateString()}}"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        Linked Customers
                    </div>
                    <div class="panel-body">
                        <div class="col-lg-6">
                            <select class="form-control page-type-selection" name="customer_link">
                                <option value="">Please select a Customer...</option>
                                @foreach($customers as $c)
                                    <option value="{{$c->id}}">{{$c->prefix}} {{$c->first_name}} {{$c->last_name}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-lg-6">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        Customer Notes
                    </div>
                    <div class="panel-body">
                        <textarea class="form-control" name="notes"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" style="padding-bottom: 20px;">
            <div class="col-md-12">
                <div class="col-md-2 pull-right">
                    <button id="createSubmit" class="btn btn-md btn-success pull-right">Save <i class="fa fa-save"></i>
                    </button>
                </div>
            </div>
        </div>

    </form>

    <div class="clearfix" style="min-height: 100px;"></div>
@stop



@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>


    <script>
        var permissions = {};
        permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_customers') == 1 ? 1 : 0 }};
        permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_customers') == 1 ? 1 : 0 }};
        permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_customers') == 1 ? 1 : 0 }};

        var groups = {!! $groups_select !!};

        var types = {!! $types_select !!};

    </script>

    <script>
        $('.datepicker').datepicker({
            dateFormat: 'yy-mm-dd'
        });
    </script>

    <script src="/js/customer.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

    <script>

        $(document).on('click', '.panel-heading span.clickable', function(e){
            var $this = $(this);
            if(!$this.hasClass('panel-collapsed')) {
                $this.parents('.panel').find('.panel-body').slideUp();
                $this.addClass('panel-collapsed');
                $this.find('i').removeClass('glyphicon-chevron-up').addClass('glyphicon-chevron-down');
            } else {
                $this.parents('.panel').find('.panel-body').slideDown();
                $this.removeClass('panel-collapsed');
                $this.find('i').removeClass('glyphicon-chevron-down').addClass('glyphicon-chevron-up');
            }
        })
    </script>

@stop
