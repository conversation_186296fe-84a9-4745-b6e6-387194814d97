@extends('layouts.master')

@section('section-name') Receipt Programming @stop

@section('content')

    <section>
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Receipt Layouts</h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>
        <section>
            <div class="row">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <strong>Select Site</strong><br/>
                        <small>Select which site you would like to edit receipts for</small>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label><strong>Select a site</strong></label>
                                    <select name="site_num" id="site" class="form-control">
                                        <option value="">Select a site</option>
                                        @foreach($sites as $site)
                                            <option value="{{$site->site_num}}">{{$site->site_name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- /.row (nested) -->
                    </div>
                    <!-- /.panel-body -->
                </div>
                <!-- /.panel -->
            </div>
        </section>

    @stop

    @push('scripts')
        <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.js"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
        <script>
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: false,
                enableCaseInsensitiveFiltering: true,        onDropdownShown: function(event) {            $(event.target).find('.filter input').focus();        },
                maxHeight: 200,
                dropup: true
            });

            $(function(){
                $(document).on('change', '#site', function(){
                    window.location.href = '/receipts/list/'+$(this).val()
                })
            })

        </script>
@endpush