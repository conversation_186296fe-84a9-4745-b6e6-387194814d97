<div class="panel panel-primary">
    <div class="panel-heading">
        Permissions
    </div>
    <div class="panel-body">
        <div class="row">
            @role(['newbridge','reseller'])
            <div class="col-md-3 col-xs-6">
                <div class="form-group">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" data-toggle="toggle" name="ishidden">
                            Is Hidden?
                        </label>
                    </div>
                </div>
            </div>
            @endrole

            @foreach($accessLevels as $access)
                @if($access->hidden == 0 || Auth::user()->hasRole('newbridge'))
                    <div class="col-md-3 col-xs-6">
                        <div class="form-group">
                            <div class="checkbox">
                                @if(isset($permissionGroup))
                                    @include('modules.clerk-permission-groups.includes.permissions-label-block-edit')
                                @else
                                    @include('modules.clerk-permission-groups.includes.permissions-label-block')
                                @endif
                            </div>
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    </div>
</div>
