@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/clerks/permission-groups" class="">Permission Groups</a></li>
        <li>Create Permission Group</li>
    </ol>

    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Create Permission Group</h4>
        <hr/>
    </div>

    <form id="CreateForm">
        {{ csrf_field() }}
        <div class="row col-md-12">
            <button id="nav-save" class="btn btn-md btn-success pull-right" data-url="/products"
                    style="width: 100px; margin: 10px 0px;">Save
                <i class="fa fa-save"></i>
            </button>
        </div>
        <div class="row col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Name
                </div>
                <div class="panel-body">
                    <div class="form-group col-xs-6">
                        <label><strong>Name</strong></label>
                        <input maxlength="24" class="input-md form-control" type="text" name="name">
                    </div>
                </div>
            </div>
        </div>
        <div class="row col-md-12">
            @include('modules.clerk-permission-groups.includes.permissions');
        </div>
    </form>

@stop

@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>
    <script>
        $('#createSubmit').on('click', function () {
            $('#CreateForm').submit()
        });

        var form = $('#CreateForm')

        // validate signup form on keyup and submit
        $("#CreateForm").validate({
            ignore: [],
            rules: {
                name: {
                    required: true,
                    maxlength: 24
                }
            },
            invalidHandler: function (e, validator) {
                if (validator.errorList.length)
                    $('#tabs a[href="#' + jQuery(validator.errorList[0].element).closest(".tab-pane").attr('id') + '"]').tab('show')
            },
            submitHandler: function (form) {

                let hasTraining = $('[name="accesslevel[32768]"]:checked').val();
                if (hasTraining !== undefined) {
                    Swal.fire({
                        title: 'Training Mode Selected',
                        text: "This permission group will not be able to open certain tables and no transaction data will be saved!",
                        type: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'I\'m Sure'
                    }).then((result) => {
                        if (result.value) {
                            createPermissionGroup()
                        }
                    })

                } else {
                    createPermissionGroup()
                }

            }
        });

        function createPermissionGroup() {
            var data = $(form).serialize();

            $.ajax({
                type: "POST",
                url: "/clerks/permission-groups/add",
                data: $(form).serialize(),
                success: function (data) {
                    notificationBar('success', 'Permission Group Created!', '/clerks/permission-groups')
                },
                fail: function (data) {
                    data = data.responseJSON
                    notificationBar('error', data.message)
                },
                error: function (data) {
                    data = data.responseJSON
                    notificationBar('error', data.message)
                }
            });
        }
    </script>
@stop
