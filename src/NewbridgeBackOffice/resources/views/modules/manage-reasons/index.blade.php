@extends('layouts.master')

@section('section-name') Manage Reasons @stop

@section('content')

    <div class="row">
        <div class="panel panel-primary">
            <div class="panel-heading">
                Manage Reasons
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label><strong>Select a reason type</strong></label>
                            <select name="reasonType" id="reasonType" class="form-control">
                                @foreach($reasontypes as $type)
                                    <option value="{{$type->reason_id}}">{{$type->reason_type}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <!-- /.row (nested) -->
            </div>
            <!-- /.panel-body -->
        </div>
        <!-- /.panel -->
    </div>
    <div id="html-area">

    </div>


@stop

@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <script>

        $(document).on('change', '#reasonType', function (e) {
            e.preventDefault();

            let value = $(this).val();


        });



@stop