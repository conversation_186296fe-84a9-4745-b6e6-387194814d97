@extends('layouts.master')

@section('content')

    <ol class="breadcrumb" style="width: 100%">
        <li><a href="/reasons" class="">Manage Reasons</a></li>
        <li>Create {{$reasontype->reason_type}} Reasons</li>
    </ol>

    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Create {{$reasontype->reason_type}} Reasons</h4>
        <hr />

        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#reason" data-toggle="tab">New {{$reasontype->reason_type}} Reason</a></li>

        </ul>
    </div>

    <form id="CreateReasonForm">
        {{ csrf_field() }}
        <div class="tab-content">
            <div class="tab-pane active" id="details">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Basic Reason Information
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Reason Text</strong></label>
                                        <input type="text" class="form-control" name="reason"/>
                                    </div>
                                </div>
                                @if($reasontype->is_stockable == 1)
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Take Stock?</strong>
                                        </label>
                                        <select name="stock" id="stock" class="form-control">
                                            <option value="1">Yes</option>
                                            <option value="0">No</option>
                                        </select>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="col-md-2 pull-right">
                    <button class="btn btn-md btn-success pull-right" id="createForm1" data-url="/reasons">Save <i class="fa fa-save"></i></button>
                </div>
                <div class="col-md-2 pull-right">
                    <button class="btn btn-md btn-primary pull-right" id="createForm2" data-url="/reasons/create/{{$reasontype->reason_id}}">Save &amp; Create Another <i class="fa fa-save"></i></button>
                </div>
            </div>
        </div>
    </form>


@stop



@section('js')
    @parent
<script>

    $('#createForm1').on('click', function(e){
    e.preventDefault();

    url = $(this).attr('data-url')

    createFormSubmit()

    })

    $('#createForm2').on('click', function(e){
    e.preventDefault();

    url = $(this).attr('data-url')

    createFormSubmit()

    })


    function createFormSubmit(){

        let form = $('#CreateReasonForm')


        $.ajax({
            type: "POST",
            url: "/reasons/add/{{Request::segment(3)}}",
            data: $(form).serialize(),
            success: function () {
                notificationBar('success', 'Your Reason has been created', url)
            }
        }).fail(function(data) {
            notificationBar(data.responseJSON.status, data.responseJSON.message)
        });
    }


</script>
@stop