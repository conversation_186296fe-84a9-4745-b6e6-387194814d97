@extends('layouts.master')

@section('section-name') Clerk Management @stop

@section('content')
    @php
        $sites = \NewbridgeWeb\Http\Helpers\SiteHelper::mySites(false);
        $company = \NewbridgeWeb\Repositories\Company::find(Auth::user()->company_id);
    @endphp

    <script>
        var table = null;
    </script>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Clerk Management
                @if($company->site_specific_clerks == 1)
                <br />
                <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small>
                @endif
            </h1>
        </div>
    </div>

    @if($company->site_specific_clerks == 1)
        <div class="col-xs-12">
            @include('modules.site-select.index')
        </div>
    @endif

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <PERSON><PERSON> and Manage Clerk's<br />
                    <small>Click in the table to instantly edit records shown, use the search boxes to filter the list.</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Full Name</th>
                                <th>Short Name</th>
                                <th>Pin</th>
                                <th>Employee Number</th>
                                <th>Permission Group</th>
                                <th>Default Screen</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

@stop

@push('scripts')
<script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="/js/dataTables.editor.min.js"></script>

<script>
    var permissions = {};
    permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_clerks') == 1 ? 1 : 0 }};
    permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_clerks') == 1 ? 1 : 0 }};
    permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_clerks') == 1 ? 1 : 0 }};
</script>

<script>
    var pages = {!! $screens !!};
    var permissionGroups = {!! $permissionGroups !!}
</script>

<script src="/js/clerks.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

@endpush
