@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/clerks" class="">Clerks</a></li>
        <li>Edit Clerk</li>
    </ol>

    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Edit Clerk ({{$clerk->full_name}})</h4>
        <hr />

        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab" data-next="#roleswages" data-prev="" data-save="false">Clerk Details</a></li>
            <li id="step2"><a href="#roleswages" data-toggle="tab" data-next="#pages" data-prev="#details" data-save="false">Roles & Wages</a></li>
            <li id="step3"><a href="#pages" data-toggle="tab" data-next="#permissions" data-prev="#roleswages" data-save="false">Page Restrictions</a></li>
            <li id="step4"><a href="#permissions" data-toggle="tab" data-next="#commands" data-prev="#pages" data-save="false">Permissions</a></li>
            <li id="step5"><a href="#commands" data-toggle="tab" data-next="" data-prev="#permissions" data-save="true">Sign on Actions</a></li>
        </ul>

    </div>

    <form id="EditForm">
        {{ csrf_field() }}
        <div class="tab-content">

            @include('includes.nav-buttons')

            <div class="tab-pane active" id="details">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Basic Clerk Information
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Full Name</strong></label>
                                        <input name="id" type="hidden" value="{{$clerk->id}}"></input>
                                        <input type="text" class="form-control" name="full_name" value="{{$clerk->full_name}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Short Name</strong></label>
                                        <input type="text" class="form-control" name="short_name" value="{{$clerk->short_name}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Pin</strong></label>
                                        <input type="text" class="form-control" name="pin" value="{{$clerk->pin}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Employee Number</strong></label>
                                        <input type="text" class="form-control" name="employee_no" value="{{$clerk->employee_no}}"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Default Shift Start</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon" id="basic-addon1"><i class="fa fa-clock"></i></span>
                                            <input type="text" id="timepicker1" class="form-control" name="default_shift_start" value="{{$clerk->default_shift_start}}"/>
                                        </div>
                                        {{--                                        <input type="number" id="timepicker1" class="form-control" name="default_shift_start"/>--}}
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Default Shift End</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon" id="basic-addon1"><i class="fa fa-clock"></i></span>
                                            <input type="text" id="timepicker2" class="form-control" name="default_shift_end" value="{{$clerk->default_shift_end}}"/>
                                        </div>
                                    </div>
                                </div>
                                <script>
                                    $('#timepicker1').datetimepicker({
                                        format: 'HH:mm'
                                    });
                                    $('#timepicker2').datetimepicker({
                                        format: 'HH:mm'
                                    });
                                </script>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Permission Group</strong></label>
                                        <select name="permission_group_guid" class="form-control">
                                            <option value="">None selected</option>
                                            @foreach($permissionGroups as $permissionGroup)
                                                <option @if($permissionGroup->guid == $clerk->permission_group_guid && $permissionGroup->access_level == $clerk->accesslevel) selected @endif value="{{$permissionGroup->guid}}">{{$permissionGroup->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Clerk Area</strong></label>
                                        <select name="area_guid" class="form-control">
                                            @foreach($areas as $a)
                                                <option @if($clerk->area_guid == $a->guid) selected @endif value="{{$a->guid}}">{{$a->area_name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Default Screen</strong></label>
                                        <select name="default_screen_guid" class="form-control">
                                            @foreach($screens as $v)
                                                <option @if($clerk->default_screen_guid == $v->CommandUID) selected @endif value="{{$v->CommandUID}}">{{$v->DisplayName}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>


            <div class="tab-pane" id="roleswages">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Job Roles & Wages
                        </div>
                        @ability('newbridge,owner,reseller', 'clerk_wages')
                        <div class="panel-body">

                            <div class="row">
                                @ability('newbridge,owner,reseller', 'clerk_wages')
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Pay Rate</strong></label>
                                        @if($clerk->salary == 0)
                                            <input type="number" class="form-control" name="rate" value="{{$clerk->rate}}"/>
                                        @else
                                            <input type="number" class="form-control" name="rate" value="{{$clerk->salary_rate}}"/>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Salary or Hourly?</strong></label>
                                        <select name="salary" class="form-control">
                                            <option @if($clerk->salary == 0) selected @endif value="0">Hourly</option>
                                            <option @if($clerk->salary == 1) selected @endif value="1">Salary</option>
                                        </select>
                                    </div>
                                </div>
                                @endability
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="alert alert-warning col-md-6 col-sm-12" id="salary-roles-alert" style="display: none;">
                                        <strong>Salary Staff</strong>
                                        <p>For salary staff there is no need to input the rate for each job role. Just
                                            set a default and enable the required roles.</p>
                                    </div>
                                    <table class="table-striped table">
                                        <thead>
                                        <tr>
                                            <th>Job Role</th>
                                            <th>Pay Rate</th>
                                            <th>Role Enabled</th>
                                            <th>Is Default?</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($roles as $role)
                                            <tr>
                                                <td>{{$role->displayname}}</td>
                                                <td>
                                                    <input type="number" name="rolerate[{{$role->id}}]"
                                                           class="rate-input form-control input-md"  @if(isset($attachedRoles[$role->id])) value="{{$attachedRoles[$role->id]['rate']}}" @else value="0" @endif />
                                                </td>
                                                <td>
                                                    <input type="checkbox" name="hasrole[{{$role->id}}]"
                                                           value="{{$role->id}}" @if(isset($attachedRoles[$role->id])) checked="checked" @endif>
                                                </td>
                                                <td>
                                                    <input class="defaultCheckbox" type="radio" name="isdefault"  @if(isset($attachedRoles[$role->id]) && $attachedRoles[$role->id]['isdefault'] == 1) checked="checked" @endif value="{{$role->id}}"/>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        @endability
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="pages">
                <div class="col-md-12">
                    <div class="panel panel-primary" id="singleBinSelection">
                        <div class="panel-heading">
                            Allow Page Access<br />
                            <small>Turning on the options below allows access to the page.</small>
                        </div>
                        <div class="panel-body">
                            <div class="row">

                                @foreach($restrictedPages as $page)
                                    <div class="col-md-4 col-xs-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <label>
                                                    <input type="checkbox" data-toggle="toggle" name="pages[]" value="{{$page->RestrictedCommand}}" @if($clerk->navigationlevel & $page->RestrictedCommand) checked @endif data-on="Allow" data-off="Block">
                                                    {{$page->DisplayName}}?
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="permissions">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Clerk Permissions
                        </div>
                        <div class="panel-body">
                            <div class="alert alert-warning">
                                <h3>Clerk Permissions have moved</h3>
                                <p>
                                    To set permissions for clerks you need to create <a href="/clerks/permission-groups">Clerk Permission Groups</a>,
                                    these groups make it easier to manage the permissions for multiple clerks at the same time.
                                    To assign a permission group to this clerk go to the 'Clerk Details' tab and select one from the dropdown.
                                </p>
                            </div>
                            <div class="row">
                                @role(['newbridge','reseller'])
                                <div class="col-md-3 col-xs-6">
                                    <div class="form-group">
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox" data-toggle="toggle" disabled name="">
                                                Is Hidden?
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                @endrole

                                @foreach($accessLevels as $access)
                                    @if($access->hidden == 0 || Auth::user()->hasRole('newbridge'))
                                        <div class="col-md-3 col-xs-6">
                                            <div class="form-group">
                                                <div class="checkbox">
                                                    <label>
                                                        <input type="checkbox" disabled data-toggle="toggle" name="" value="{{$access->bit}}" @if($clerk->accesslevel & $access->bit) checked @endif>
                                                        {{$access->displayname}}?
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="commands">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Sign On Actions<br />
                            <small>These actions are performed when the clerk signs on</small>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-xs-8">
                                    <label><strong>Add a Clerk Action</strong></label>
                                    <select class="form-control" id="int_commands" name="int_commands">
                                        @if(!empty($intCommand['pages']))
                                            @foreach($intCommand['pages'] as $k => $pageGroup)
                                                <optgroup class="group-1" label="Pages ({{$k}})">
                                                    @foreach($pageGroup as $c)
                                                        <option value="{{$c['CommandUID']}}">{{$c['displayname']}}</option>
                                                    @endforeach
                                                </optgroup>
                                            @endforeach
                                        @endif
                                        @if(!empty($intCommand['commands']))
                                            <optgroup class="group-2" label="Commands">
                                                @foreach($intCommand['commands'] as $c)
                                                    <option value="{{$c['CommandUID']}}">{{$c['displayname']}}</option>
                                                @endforeach
                                            </optgroup>
                                        @endif
                                    </select>
                                </div>
                                <div class="col-sm-2">
                                    <button type="button" class="btn btn-success" id="save-commands" style="margin-top: 25px;">Add Action</button>
                                </div>
                                <div class="col-md-12">
                                    <hr />
                                    <h4>Clerk Sign On Actions</h4>
                                    <p><small>Actions programmed below happen in the order shown, to reorder actions drag and drop the buttons.</small></p>
                                    <style>
                                        #sortable2, #sortable2 li {
                                            text-indent: 0px;
                                            margin: 0px;
                                            padding: 0px;
                                        }
                                        #sortable2 li {
                                            width: 130px;
                                            margin: 5px;
                                            padding: 3px;
                                        }
                                    </style>
                                    <ul id="sortable2" class="sortable grid">

                                        @foreach($clerk->actions as $action)
                                            <li class="btn btn-sm btn-danger removelink" data-id="{{$action->id}}" data-position="{{$action->position}}">{{$action->display_name}} <span style="font-size: 14px;"><strong> &nbsp; &nbsp; &times; </strong></span></li>
                                        @endforeach

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <div style="clear: both; height: 100px;"></div>
        </div>



    </form>

@stop



@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/jquery-typeahead@2.11.0/src/jquery.typeahead.css">
    <script src="//cdn.jsdelivr.net/npm/jquery-typeahead@2.11.0/dist/jquery.typeahead.min.js"></script>

    <script>

        $(function() {
            $('#permissions [type="checkbox"], #pages [type="checkbox"]').bootstrapToggle({
                on: 'Allow',
                off: 'Block'
            });
        });

        $('#int_commands').multiselect({
            buttonWidth: '100%',
            includeSelectAllOption: true,
            enableCaseInsensitiveFiltering: true
        });


        var form = $('#EditForm')

        // validate signup form on keyup and submit
        $("#EditForm").validate({
            ignore: [],
            rules: {
                full_name: "required",
                short_name: "required",
                pin: "required",
                employee_no: "required",
                default_screen_id: "required"
            },
            invalidHandler: function(e, validator){
                if(validator.errorList.length)
                    $('#tabs a[href="#' + jQuery(validator.errorList[0].element).closest(".tab-pane").attr('id') + '"]').tab('show')
            },
            submitHandler: function (form) {

                let hasTraining = $('[name="accesslevel[32768]"]:checked').val();

                if(hasTraining !== undefined) {
                    Swal.fire({
                        title: 'Training Mode Selected',
                        text: "This clerk will not be able to open certain tables and no transaction data will be saved!",
                        type: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'I\'m Sure'
                    }).then((result) => {
                        if (result.value) {
                            updateClerk()
                        }
                    })

                } else {
                    updateClerk()
                }

            }
        });

        function updateClerk(){
            $.ajax({
                type: "PUT",
                url: "/clerks/edit-single",
                data: $(form).serialize(),
                success: function () {
                    notificationBar('success', 'Clerk updated!', '/clerks')
                },
                fail: function (data) {
                    data = data.responseJSON
                    notificationBar('error', data.message)
                },
                error: function (data) {
                    data = data.responseJSON
                    notificationBar('error', data.message)
                }
            });
        }

        $(document).on('click', '.goto', function(e){
            e.preventDefault();
            var tab = $(this).data('goto')

            $('#tabs a[href="#'  + tab + '"]').tab('show')
        })

        $(document).on('click', '#save-commands', function(e){
            let select = $('select#int_commands')
            let command = select.val();
            let command_text = select.text();
            let command_count = $('#sortable2 li').length;
            command_count++;

            if(command != null && command != ''){

                $.ajax({
                    type: "POST",
                    url: "/clerks/add-action",
                    data: {clerk: {{$clerk->id}}, command: command, position: command_count},
                    success: function (data) {
                        let link = data.link;
                        $('#sortable2').append('<li class="btn btn-sm btn-danger removelink" data-id="'+link.id+'" data-position="'+link.position+'">'+link.display_name+' <span style="font-size: 14px;"><strong> &nbsp; &nbsp; &times; </strong></span></li>')
                        notificationBar('success', 'Sign on action added!')
                    },
                    fail: function () {
                        notificationBar('error', 'There was an error in your entry, please try again.')
                    }
                });

            }
        })

        $(document).on('click', '.removelink', function(e){
            e.preventDefault();

            let id = $(this);

            $.ajax({
                type: "DELETE",
                url: "/clerks/delete-action/"+id.data('id'),
                success: function () {
                    id.remove();
                    notificationBar('success', 'Action removed!')
                },
                fail: function () {
                    notificationBar('error', 'There was an error in your entry, please try again.')
                }
            });
        });

        function saveLinkOrder(ordered) {

            $.ajax({
                type: 'POST',
                url: '/clerks/reorder-actions',
                data: {data: ordered},
                dataType: 'json',
                success: function(data) {
                    notificationBar('success', 'Clerk action order updated')
                },
                error: function() {
                    notificationBar('error', 'Clerk action order not updated, please try again')
                }
            });
        }

        $('#sortable2').sortable().bind('sortupdate', function() {

            var ordered = [];

            var links = $('#sortable2 li');

            $.each(links, function(key, val){
                $(val).attr('data-position', key)

                var link = $(val)
                ordered.push({ id: link.data('id'), position: key})

            });

            saveLinkOrder(ordered);

        });

        $(document).on('change', '[name="salary"]', function (e) {

            e.preventDefault();
            let value = $(this).val();
            if (value == 1) {
                $('.rate-input').attr('disabled', 'disabled');
                $('#salary-roles-alert').show();
            } else {
                $('.rate-input').removeAttr('disabled');
                $('#salary-roles-alert').hide();
            }

        });

        $(document).on('change', '[name="rate"]', function (e) {

            e.preventDefault();

            let rate = $('[name="rate"]').val();
            let rates = $('.rate-input');

            rates.each(function(){
                let value = $(this).val();

                if(parseFloat(value) != parseFloat(rate)){
                    $(this).val(parseFloat(rate))
                }

            })

        })

        $('body').on('click', '[name="isdefault"]', function(){

            let role = $('[name="isdefault"]:checked').val();
            let enabled = $('[name="hasrole['+role+']"]');

            enabled.prop('checked', false);
            enabled.prop('checked', true);

        });



    </script>
@stop
