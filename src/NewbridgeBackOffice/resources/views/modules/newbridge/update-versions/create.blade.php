@extends('layouts.master')

@section('section-name') Version Create @stop

@section('content')
    <section>
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Create New Version</h1>
            </div>
        </div>
        <section id="uploadFiles">
            <div class="row">
                <form id="uploadform" method="POST" action="/newbridge/update-versions/upload"  enctype="multipart/form-data">
                    {{ csrf_field() }}
                    <div class="col-md-12 col-sm-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <strong>Upload a New POS Version</strong>
                            </div>
                            <div class="panel-body">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <label><strong>Label / Display Name</strong></label>
                                                <input type="text" class="form-control" name="label" value="{{old('label')}}"/>
                                                @error('label')
                                                    <div class="text-danger">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Version</strong></label>
                                                <input type="text" class="form-control" maxlength="30" name="version" value="{{old('version')}}"/>
                                                @error('version')
                                                <div class="text-danger">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Minimum Version</strong></label>
                                                <input type="text" class="form-control" maxlength="30" name="minimum_version" value="{{old('minimum_version')}}"/>
                                                @error('minimum_version')
                                                <div class="text-danger">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <label><strong>File</strong></label>
                                                <input type="file" class="form-control" maxlength="30" name="updatezip" value="{{old('updatezip')}}"/>
                                                @error('updatezip')
                                                <div class="text-danger">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12" style="min-height: 300px;">
                                            <label for=""><strong>Release Notes</strong></label>
                                            <textarea id="summernote" name="changelog" style="min-height: 300px;" rows="50"></textarea>

                                            <script>
                                                $(document).ready(function() {
                                                    $('#summernote').summernote({
                                                        height: 300,             });
                                                });
                                            </script>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.row (nested) -->
                            </div>
                            <div class="panel-footer">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-md btn-success" id="uploadbutton">
                                            Upload
                                        </button>
                                    </div>

                                </div>
                            </div>
                            <!-- /.panel-body -->
                        </div>
                        <!-- /.panel -->
                    </div>
                    <!-- /.col-lg-6 -->
                </form>
            </div>
        </section>
    </section>

    <script>
        function loading(target) {
            $('<div id="loading"><i class="fa fa-spinner fa-spin" style="margin-top: 80px; font-size: 48px;"></i><br /><h3>Loading data please wait</h3></div>').css({
                position: "absolute",
                'min-height': '200px',
                width: "100%",
                height: "100%",
                "text-align": "center",
                top: 0,
                left: 0,
                background: '#ccc',
                background: 'rgba(999,999,999,1.0)',
                'z-index': 99999,
            }).appendTo($(target).css("position", "relative"));
        }

        $('#uploadform').submit(function( event ) {
            loading('.panel-body')
        });

        $(document).on('change', ':file', function () {
            var input = $(this),
                numFiles = input.get(0).files ? input.get(0).files.length : 1,
                label = input.val().replace(/\\/g, '/').replace(/.*\//, '');

            $(this).parent().parent().parent().find('.file-name').val(label)
        });
    </script>

@stop

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">

    <!-- include summernote css/js -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>
@endpush
