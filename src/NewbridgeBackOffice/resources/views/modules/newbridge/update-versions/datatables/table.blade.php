@extends('layouts.master')

@section('section-name') POS Version Management @stop

@section('content')
    <script>
        var table = null;
    </script>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">POS Versions</h1>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Create and Manage POS Versions
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                        <tr>
                            <th>ID</th>
                            <th>Label</th>
                            <th>Version</th>
                            <th>Min Version</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @include('modules.newbridge.update-versions.includes.notes-modal')

@stop

@push('scripts')
<script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="/js/dataTables.editor.min.js"></script>

<link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
<script>
    $(document).ready(function () {
        $('select').not('.swal2-select').multiselect({
            buttonWidth: '100%',
            includeSelectAllOption: false,
            enableCaseInsensitiveFiltering: true,
            enableClickableOptGroups: true,
            enableCollapsibleOptGroups: true,
            collapseOptGroupsByDefault: true,
            maxHeight: 350,
        });
    });
</script>

<script>
    var permissions = {};
    permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge', 'manage_versions') == 1 ? 1 : 0 }};
    permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge', 'manage_versions') == 1 ? 1 : 0 }};
    permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge', 'manage_versions') == 1 ? 1 : 0 }};
</script>

<script src="/js/update-versions.js?{{\Carbon\Carbon::now()->timestamp}}"></script>
@endpush
