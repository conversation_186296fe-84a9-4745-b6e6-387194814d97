@extends('layouts.master')

@section('section-name') Convert Table Items @stop


@section('content')

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Bill Items</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <br/>
                    <small></small>
                </div>
                <div class="panel-body">

                    <table class="tale-striped table-condensed">
                        <thead>
                        <tr>
                            <th>Table Number</th>
                            <th>Customer Name</th>
                            <th>Seat Time</th>
                        </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{$bill['TableLocationName']}} - {{$bill['TableNumber']}}</td>
                                <td></td>
                                <td>{{\Carbon\Carbon::parse($bill['Created_At'])}}</td>
                            </tr>
                        </tbody>
                    </table>


                    <table class="tale-striped table-condensed">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Price</th>
                                <th>Date & Time</th>
                            </tr>
                        </thead>
                        <tbody>
                        @foreach($bill['Items']['TRAN_Details'] as $detail)
                            @if(isset($detail['IsVoid']) && $detail['IsVoid'] == 'false')
                            <tr>
                                <td>{{$detail['DisplayName']}}</td>
                                <td>{{$detail['SalePrice']}}</td>
                                <td>{{\Carbon\Carbon::parse($detail['Date'])}}</td>
                            </tr>
                            @endif
                        @endforeach
                        </tbody>
                    </table>

                    <table class="tale-striped table-condensed">
                        <thead>
                        <tr>
                            <th>Bill Total</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{$bill['TotalToPay']}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

@stop