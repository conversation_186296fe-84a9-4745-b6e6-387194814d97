@extends('layouts.master')

@section('section-name') Gap Analysis @stop


@section('content')

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">GAP Analysis</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Gap Analysis Summary<br/>
                    <small></small>
                </div>
                <div class="panel-body">
                    <table class="table-responsive table-condensed table-striped">
                        <thead>
                        <tr>
                            <th>Stock Control</th>
                            <th>Loyalty</th>
                            <th>Gift Cards</th>
                            <th>Scheduled Reports</th>
                            <th>Clerks & Attendance</th>
                            <th>Promotions</th>
                            <th>PMS</th>
                            <th>Ordering App</th>
                            <th>PaymentSense</th>
                            @if(isset($data['summary']['smart-gift']))    <th>Smart Gift</th> @endif
                        </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{$data['summary']['stock_control']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['loyalty']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['giftcards']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['reporting']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['clerk_attendance']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['promotions']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['pms']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['ordering_app']}}/{{$data['site_count']}}</td>
                                <td>{{$data['summary']['paymentsense']}}/{{$data['site_count']}}</td>
                                @if(isset($data['summary']['smart-gift']))  <td>{{$data['summary']['smart-gift']}}/{{$data['site_count']}}</td> @endif
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Gap Analysis Breakdown<br/>
                    <small></small>
                </div>
                <div class="panel-body">
                    <table class="table-responsive table-condensed table-striped">
                        <thead>
                        <tr>
                            <th>Site Name</th>
                            <th>Stock Control</th>
                            <th>Loyalty</th>
                            <th>Gift Cards</th>
                            <th>Scheduled Reports</th>
                            <th>Clerks & Attendance</th>
                            <th>Promotions</th>
                            <th>PMS</th>
                            <th>Ordering App</th>
                            <th>PaymentSense</th>
                            @if(isset($data['summary']['smart-gift'])) <th>Smart Gift</th> @endif
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($data['sites'] as $site)
                        <tr>
                            <td><strong>{{$site['name']}}</strong></td>
                            <td>{{$site['stock_control']}}</td>
                            <td>{{$site['loyalty']}}</td>
                            <td>{{$site['giftcards']}}</td>
                            <td>{{$site['reporting']}}</td>
                            <td>{{$site['clerk_attendance']}}</td>
                            <td>{{$site['promotions']}}</td>
                            <td>{{$site['pms']}}</td>
                            <td>{{$site['ordering_app']}}</td>
                            <td>{{$site['paymentsense']}}</td>
                           @if(isset($site['smart-gift'])) <td>{{$site['smart-gift']}}</td> @endif
                        </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
