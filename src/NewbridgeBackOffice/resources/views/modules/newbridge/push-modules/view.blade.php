@extends('layouts.master')

@section('section-name') Mo<PERSON>les to Push @stop


@section('content')

    <style>
        .action-button {
            cursor: pointer;
        }
        .big-button {
            padding: 10px;
            background-color: #e4e4e4;
            border: 1px solid black;
            text-align: left;
            margin-bottom: 15px;
            cursor: pointer;
        }
        .big-button .left {
            text-align: left;
        }
        .big-button .right {
            text-align: right;
        }
        .big-button.started {
            background: #fefacb;
            color: black;
        }
        .big-button.in-progress {
            background: #67b168;
            color: white;
        }
        .big-button.awaiting-collection {
            background: #eb9316;
            color: black;
        }
        .big-button.failed {
            background: darkred;
            color: white;
        }
        .big-button.success {
            background: green;
            color: white;
        }

    </style>

<div class="row">
    <div class="col-lg-12">
        <h1 class="page-header">Newbridge Re-Sync</h1>
        <h4>Send data from the back-office to the connected terminals.</h4>
    </div>
</div>


    <div class="col-md-12">

        @foreach ($modules as $module)

            <div class="row action-button">
                <div class="col-xs-12 big-button" id="{{$module->update_id}}" data-id="{{$module->update_id}}" data-company="{{Auth::user()->company_id}}">
                    <div class="col-xs-6 left" style="font-size: 16px; font-weight: bold;">{{$module->module}}</div>
                    <div class="col-xs-6 right"><i class="fa fa-cogs"></i> Sync Data</div>
                    <div class="col-xs-12 terminals pull-right" style="display: none; text-align: right">
                        @foreach($terminals as $terminal)
                            <div class="btn btn-warning pull-right terminal" id="terminal{{$terminal->terminal_num}}" disabled><i class="fa fa-spinner fa-spin"></i> {{$terminal->name}} <span class="remaining"></span></div>
                        @endforeach
                    </div>
                </div>
            </div>

                 {{--<a class="btn btn-sm btn-danger btn-block" id="resync" data-id="{{$module->update_id}}" data-company="{{Auth::user()->company_id}}"> Module name: ({{$module->module}}) Status:() Re-Sync</a><br><br>--}}

        @endforeach

    </div>

<script>

    let timer = null;
    let keys = [];
    let initial = true;
    let statuses = {!! $statuses !!};

    $ (function() {

        if(initial){

            if(statuses.results.length > -1) {

                updateStatus(statuses);

                _.each(statuses.results, function (status) {
                    keys.push(status.key)
                });


                if (keys.length > -1) {
                    timer = setInterval(checkStatus, 10000);
                }
            }

        }

        $(document).on('click', '.big-button', function(e){
            let update_id = $(this).data('id');
            let company_id = $(this).data('company');
            let site_num = '{{$site_num}}';

            let button = $(this)
            let check_key = 'resync:'+company_id+':'+update_id;

            if(!_.includes(keys, check_key)) {

                $.ajax({
                    type: "POST",
                    url: '/dev/re-sync',
                    data: {update_id: update_id, company_id: company_id, site_num: site_num},
                    success: function (data) {
                        // if data.status = nok etc here
                        if (!_.includes(keys, data.key)) {
                            keys.push(data.key)
                        }
                        timer = setInterval(checkStatus, 10000);

                    }
                })
            } else {
                console.warn('module already started processing')
            }

        });

        function checkStatus() {

                $.ajax({
                    type: "POST",
                    url: '/dev/re-sync/check',
                    data: {key: keys},
                    success: function(data){
                        updateStatus(data);
                    }
                })

        }

        function updateStatus(data) {

            _.each(data.results, function(status){

                let button = $('#'+status.update_id)
                let right = button.find('.right')
                let left = button.find('.left')

                button.addClass(status.status)
                // button.removeClass('')
                if(status.status === 'started'){
                    right.html('STARTED <i class="fa fa-spinner fa-spin"></i>')
                }
                if(status.status === 'in-progress'){
                    right.html('IN PROGRESS ('+status.queued+')<i class="fa fa-spinner fa-spin"></i>')
                }
                if(status.status === 'awaiting-collection'){
                    button.find('.terminals').show()
                    _.each(status.terminals, function(terminal){
                        terminalStatus(terminal, button)
                    });
                    right.html('AWAITING COLLECTION (' +status.queued+ ') <i class="fa fa-spinner fa-spin"></i>')

                }
                if(status.status === 'collecting'){
                    right.html('COLLECTING (' +status.queued+ '/'+ status.remaining +') <i class="fa fa-spinner fa-spin"></i>')
                    _.each(status.terminals, function(terminal){
                        terminalStatus(terminal, button)
                    });
                }
                if(status.status === 'failed'){
                    right.html('FAILED <i class="fa fa-times"></i>')
                }

                if(status.status === 'completed'){

                    right.html('SUCCESS <i class="fa fa-check-circle"></i>')
                    button.addClass('success');
                    let terminals = button.find('.terminal');
                    _.each(terminals, function(terminal){

                        let term = $(terminal)
                        let ti = term.find('i')

                        term.removeClass('btn-warning');
                        term.addClass('btn-success');
                        ti.removeClass('fa-spinner');
                        ti.removeClass('fa-spin');
                        ti.addClass('fa-check-circle');

                    });

                    button.addClass('.success');

                    $.ajax({
                        type: "POST",
                        url: '/dev/re-sync/completed',
                        data: {key: status.key},
                        success: function(data){
                            var index = keys.indexOf(status.key);

                            if (index > -1) {
                               keys = keys.splice(index, 1);
                               key_result = keys.splice(index, 1);
                            }

                            if(keys.length === -1){
                                clearInterval(timer);
                            }
                        },
                        error: function(){
                            notificationBar('error', 'Unable to complete job!')
                        }
                    })
                }

            })

        }

        function terminalStatus(terminal, button)
        {
            let term = $(button).find('#terminal'+terminal.terminal_num);

            if(parseFloat(terminal.remaining) === 0) {

                let i = term.find('i');
                term.removeClass('btn-warning');
                term.addClass('btn-success');
                i.removeClass('fa-spinner');
                i.removeClass('fa-spin');
                i.addClass('fa-check-circle');
            }

        }
    })

</script>


@stop
