@extends('layouts.master')

@section('section-name') Exports @stop

@section('content')

    <section>
        <div class="row" style="margin-bottom:40px;">
            <div class="col-lg-12">
                <h1 class="page-header">Debug Job Monitoring</h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>
        <div class="panel panel-primary">
            <div class="panel-heading">
                <i class="fa fa-file-import"></i>
                <span>Debug Job Monitoring</span>
            </div>
            <div class="panel-body">
                <div class="table-responsive">
                    <table class="table table-striped table-condensed">
                        <thead>
                        <tr>
                            <th>Type</th>
                            <th>Unique ID</th>
                            <th>JobID</th>
                            <th>Status</th>
                            <th>Start</th>
                            <th>End</th>
                            <th>Exception</th>
                            <th>Actions</th>
                        </tr>
                        </thead>
                        <tbody>
                        @if(!empty($activeImports))
                            @foreach($activeImports as $import)
                                <tr>
                                    <td>{{$import->type ? $import->type : 'Unknown'}}</td>
                                    <td>{{$import->uuid}}</td>
                                    <td>{{$import->jobId}}</td>
                                    <td>{{$import->status}}</td>
                                    <td>{{\Carbon\Carbon::parse($import->start)->format('d/m/Y H:i:s')}}</td>
                                    <td>{{\Carbon\Carbon::parse($import->end)->format('d/m/Y H:i:s')}}</td>
                                    <td>{{$import->exception}}</td>
                                    <td style="">
                                        <a href="/newbridge/debug-data/delete/{{$import->uuid}}" class="btn btn-danger">Delete</a>
                                        @if($import->status != \NewbridgeWeb\Repositories\JobStatus\JobStatus::STATUS_COMPLETE && $import->download_path != null)
                                            <a href="#" class="btn btn-default">Download</a>
                                        @elseif($import->status == \NewbridgeWeb\Repositories\JobStatus\JobStatus::STATUS_COMPLETE && $import->download_path != null)
                                            <a href="/newbridge/debug-data/download/{{str_replace('/debug-export-files/', '', $import->download_path)}}" class="btn btn-success">Download</a>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </section>


@stop

@push("scripts")
    <script>
        window.setTimeout( function() {
            window.location.reload();
        }, 30000);
    </script>
@endpush

