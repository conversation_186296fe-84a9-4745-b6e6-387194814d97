@extends('layouts.master')

@section('section-name') Assign Screens @stop


@section('content')

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Assign Products to Screens</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Auto assign products to screens<br/>
                    <small></small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                        <tr>
                            <th>Sub Department Name</th>
                            <th>Button Size</th>
                            <th>Button Color</th>
                            <th>Assign Order</th>
                            <th>Create</th>
                        </tr>
                        </thead>
                        <tbody>
                            @foreach($results as $k => $v)
                                <tr>
                                    <td>{{$v['displayname']}}</td>
                                    <td>
                                        <select required name="button_size_{{$k}}" id="button_size_{{$k}}" data-subdepartment="{{$k}}">
                                            <option value="">Select a Size</option>
                                            @foreach($v['sizes'] as $k2 => $v2)
                                                <option value="{{$k2}}">{{$k2}} / {{$v2['pages']}} Pages</option>
                                            @endforeach
                                            <option value="wines_by_glass">Wine By Glass & Bottle (4 Sizes)</option>
                                            <option value="halves_doubles">Halves & Doubles</option>
                                        </select>
                                    </td>
                                    <td>
                                        <div class="dropdown">
                                            <button class="btn btn-default dropdown-toggle" type="button" data-toggle="dropdown" style="width: 100%;">Select a Style
                                                <span class="caret"></span></button>
                                            <ul class="dropdown-menu">
                                                @foreach($defaultStyles as $k3 => $style)
                                                    <li data-id="def-{{$style->id}}" data-subdepartment="{{$k}}" class="style-apply" style="cursor: pointer; background: red; padding: 20px; color: {{$style->foreground}}; background: linear-gradient(to bottom, {{$style->color1}} 0%,{{$style->color2}} 100%) !important;"></li>
                                                @endforeach
                                            </ul>
                                        </div>
                                        <input class="selected_style" type="hidden" id="selected_style_{{$k}}" value="">
                                    </td>
                                    <td>
                                        <select required name="button_order_{{$k}}" id="button_order_{{$k}}" data-subdepartment="{{$k}}">
                                            <option value="">Select an Order</option>
                                            <option value="asc">Alphabetical</option>
                                            <option value="id">Import Order</option>
                                        </select>
                                    </td>
                                    <td><button class="btn btn-success create-pages" data-subdepartment="{{$k}}">Create Page(s)</button></td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(function(){
            $('body').on('click', '.create-pages', function(){
                let button = $(this);
                let subdepartment = button.attr('data-subdepartment');
                let size = $('#button_size_'+subdepartment).val();
                let order = $('#button_order_'+subdepartment).val();

                button.html('<i class="fa fa-spin fa-spinner"></i> Creating');
                button.attr('disabled', true);

                let color = $('#selected_style_'+subdepartment).val();

                $.ajax({
                    type: "GET",
                    url: "/create-page-sub/"+subdepartment+"/"+size+"/"+color+"/"+order,
                    success: function (data) {
                        button.html('Done');
                        button.attr('disabled', true);
                    },
                    fail: function () {
                        notificationBar('error', 'Sorry, there was a problem. Try again.!');
                        button.html('Create');
                        button.removeAttr('disabled');
                    },
                    error: function () {
                        notificationBar('error', 'Sorry, there was a problem. Try again.!');
                        button.html('Create');
                        button.removeAttr('disabled');
                    },
                });
            });

            $('body').on('click', '.style-apply', function(){
                let element = $(this);
                let style = element.data('id');
                let subdepartment = element.data('subdepartment')

                console.log('element', element)
                console.log('style', style)

                $('#selected_style_'+subdepartment).val(style);

                let button = element.parent().parent().find('button')
                let bg = element.css('background');
                let color = element.css('color');

                    button.text('Selected')
                    button.css('background', bg)
                    button.css('color', color)
                    button.text('Selected')


            });
        })
    </script>

@endsection