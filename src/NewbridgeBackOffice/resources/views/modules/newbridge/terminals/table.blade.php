@extends('layouts.master')

@section('section-name') Terminals & Versions @stop


@section('content')
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">All Terminals & Versions</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    View terminals and versions<br/>
                    <small></small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                        <tr>
                            <th width="50px">ID</th>
                            <th>Company Name</th>
                            <th>Site Name</th>
                            <th>Terminal</th>
                            <th>Version</th>
                            <th>Last Ping</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

@stop

@push('scripts')
    <script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="/js/dataTables.editor.min.js"></script>

    <script>
        var permissions = {};
        permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_payment_types') == 1 ? 1 : 0 }};
        permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_payment_types') == 1 ? 1 : 0 }};
        permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_payment_types') == 1 ? 1 : 0 }};
    </script>

    <script src="/js/newbridge/terminals.js?{{\Carbon\Carbon::now()->toAtomString()}}"></script>
@endpush
