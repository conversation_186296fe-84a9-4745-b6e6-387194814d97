@extends('layouts.master')

@section('content')
    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h3>Create Recipe
            @if(\Session::get('user.company.site_specific_products') == 1)
                <br />
                <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small>
                @endif</h3>
    </div>

    <form id="CreateForm">
        {{ csrf_field() }}
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Basic Recipe Information
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label><strong>Name</strong></label>
                                    <input type="text" class="form-control" name="name"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Recipe Ingredients
                    </div>
                    <div class="panel-body">
                        <div id="ingredients">
                            <div class="row">
                                <div class="col-lg-7">
                                    <div class="form-group">
                                        <label><strong>Product</strong></label>
                                        <select class="form-control" id="product_1" name="product[]">
                                            @foreach($departments as $dep)
                                                <optgroup label="{{$dep['displayname']}}" class="{{$dep['guid']}}">
                                                    @foreach($dep['products'] as $p)
                                                        <option value="{{$p['guid']}}">{{$p['displayname']}}</option>
                                                    @endforeach
                                                </optgroup>
                                            @endforeach
                                            <optgroup label="Recipes" class="">
                                                @foreach($recipes as $recipe)
                                                    <option value="{{$recipe['guid']}}">{{$recipe['name']}}</option>
                                                @endforeach
                                            </optgroup>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <label><strong>Quantity</strong></label>
                                        <input type="number" class="form-control" name="quantity[]" min="0"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-4">
                                <div class="form-group">
                                    <button class="btn btn-sm btn-primary" id="addRow">Add Row <i class="fa fa-plus"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="panel-footer">
                        <div class="row">
                            <div class="col-xs-2 pull-right">
                                <button class="btn btn-success" id="createSubmit">Create</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <script>
        var row_id = 1;
        var departments = {!! json_encode($departments) !!};
        var recipes = {!! json_encode($recipes) !!}

        $('body').on('click', '#addRow', function(e){
            e.preventDefault()
            addRow();
        })

        $(document).ready(function(){
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                },
                onChange: function (option, checked, select) {
                }
            });
        })

        /**
         * Add a row to the ingredient selection
         * lopps through and adds the products to the list
         * and creates searchable multiselect
         */
        function addRow() {

            row_id++;

            $('#ingredients').append('<div class="row" id="row_'+row_id+'"><div class="col-lg-7"><div class="form-group"><select class="form-control" id="product_'+row_id+'" name="product[]"></select></div></div><div class="col-lg-3"><div class="form-group"><input type="number" class="form-control" name="quantity[]" min="0"/></div></div> <div class="col-lg-1"><div class="form-group"><button class="btn btn-danger remove-row" data-row="'+row_id+'">Delete</button></div></div></div>');

            _.each(departments, function(d){
                $('#product_'+row_id).append('<optgroup label="'+d.displayname+'" class="'+d.guid+'" id="dept_'+d.id+'_'+row_id+'"></optgroup>')
                _.each(d.products, function(p){
                    $('#dept_'+d.id+'_'+row_id).append('<option value="'+p.guid+'">'+p.displayname+'</option>');
                })
            });

            $('#product_'+row_id).append('<optgroup label="Recipes" class="recipes" id="recipes"></optgroup>')

            _.each(recipes, function(p){
                $('#recipes').append('<option value="'+p.guid+'">'+p.name+'</option>');
            })


            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                },
                onChange: function (option, checked, select) {

                }
            });
        }

        $('body').on('click', '.remove-row', function(e){
            e.preventDefault()
            var row = $(this).data('row')
            //delete

            $('#row_'+row).remove();
            notificationBar('success', 'Row Removed!')
        })

        $('#createSubmit').on('click', function(e){
            e.preventDefault()
            $('#CreateForm').submit()
        })

        var form = $('#CreateForm')

        // validate signup form on keyup and submit
        $("#CreateForm").validate({
            rules: {
                name: "required"
            },
            submitHandler: function (form) {
                $.ajax({
                    type: "POST",
                    url: "/recipes/create",
                    data: $(form).serialize(),
                    success: function () {
                        notificationBar('success', 'Recipe Created!', '/recipes')
                    },
                    fail: function () {
                        notificationBar('error', 'There was an error in your entry, please try again.')
                    }
                });
            }
        })

    </script>

@endsection