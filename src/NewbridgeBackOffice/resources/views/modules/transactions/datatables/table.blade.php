@php use Carbon\Carbon;use NewbridgeWeb\Http\Helpers\AbilityHelper;use NewbridgeWeb\Http\Helpers\TimezoneHelper; @endphp
@extends('layouts.master')

@section('section-name')
    View Transactions
@stop

@section('content')
    <script>
        var table = null;
    </script>
    <style>
        i {
            font-size: 25px;
        }
    </style>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">View Transactions <br/>
                <small> @if(\Session::get('current_site') != 0)
                        {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}}
                    @endif</small></h1>
        </div>
    </div>
    <div class="col-lg-12">
        @include('modules.site-select.index', ['sites' => \NewbridgeWeb\Http\Helpers\SiteHelper::mySites(true, false)])
    </div>
    <div class="row" data-step="1">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-heading">
                    Transaction Filters
                </div>
                <div class="panel-body">
                    <form>
                        {{ csrf_field() }}
                        <div class="form-group col-md-6">
                            <label for="department"><strong>Department</strong> <small>(optional)</small></label>
                            <select id="department" name="department[]" multiple class="form-control input">
                                @foreach($departments as $department)
                                    <option @if(!empty(\Illuminate\Support\Facades\Request::get('department')) && in_array($department->guid, \Illuminate\Support\Facades\Request::get('department')) ) selected
                                            @endif value="{{$department->guid}}">{{$department->displayname}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="subdepartment"><strong>Subdepartment</strong> <small>(optional)</small></label>
                            <select id="subdepartment" name="subdepartment[]" multiple class="form-control input">
                                @foreach($subdepartments as $subdepartment)
                                    <option @if(!empty(\Illuminate\Support\Facades\Request::get('subdepartment')) && in_array($subdepartment->guid, \Illuminate\Support\Facades\Request::get('subdepartment')) ) selected
                                            @endif value="{{$subdepartment->guid}}">{{$subdepartment->displayname}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label for="product"><strong>Product</strong> <small>(optional)</small></label>
                            <select id="product" name="product[]" multiple class="form-control input">
                                @foreach($products as $product)
                                    <option @if(!empty(\Illuminate\Support\Facades\Request::get('product'))  && in_array($product->guid, \Illuminate\Support\Facades\Request::get('product'))) )
                                            selected
                                            @endif value="{{$product->guid}}">{{$product->displayname}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-12">
                            <label><strong>Start / End Date & Time</strong></label>
                            <input type="hidden" id="start-datetime"
                                   value="{{Carbon::now(TimezoneHelper::getTimezone())->subDays(7)->format('Y-m-d H:i:s')}}"
                                   name="start">
                            <input type="hidden" id="end-datetime"
                                   value="{{Carbon::now(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s')}}"
                                   name="end">
                            <div id="reportrange-start"
                                 style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
                                <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
                                <span></span> <b class="caret"></b>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group col-md-6">
                                <button class="btn btn-md btn-success">Update Results</button>
                            </div>
                        </div>
                    </form>
                    <hr/>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    View Transactions<br/>
                    <small>Select a row and click view transaction to view all the details.</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                        <tr>
                            <th width="100">ID</th>
                            <th>Date / Time</th>
                            <th>Clerk</th>
                            <th>SubTotal</th>
                            <th>Discount</th>
                            <th>Total</th>
                            <th>Order Number</th>
                            <th>Method</th>
                            <th>Table</th>
                            <th>Room</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="view-modal-area"></div>
@stop

@push('scripts')
    <script src="//cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <script src="//cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
    <script src="//cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
    <script src="//cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="//cdn.datatables.net/plug-ins/1.10.19/dataRender/datetime.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.21/moment-timezone-with-data.min.js"></script>

    <script>
        var permissions = {};
        permissions.can_view = {{AbilityHelper::ability('newbridge,owner,reseller', 'view_transactions') == 1 ? 1 : 0 }};
        var userTimezone = "{{ TimezoneHelper::getTimezone() }}";

        $(function () {
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                maxHeight: '200',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function (event) {
                    $(event.target).find('.filter input').focus();
                }
            })
        });

        @if(!Request::input('start') && !Request::input('end'))
            var start1 = moment(carbonDate).subtract(7, 'days');
            var end1 = moment(carbonDate);
        @else
            var start1 = moment("{{Request::input('start')}}");
            var end1 = moment("{{Request::input('end')}}");

            $('#start-datetime').val(start1.format('YYYY-MM-DD HH:mm:ss'))
            $('#end-datetime').val(end1.format('YYYY-MM-DD HH:mm:ss'))
        @endif

        var openTransaction = false;
        @if(Request::get('transaction_id') && Request::get('transaction_id') != '')
            openTransaction = {{Request::get('transaction_id')}}
                @endif

            function
        cb(start, end)
        {
            $('#reportrange-start span').html(start.format('MMMM D, YYYY HH:mm:ss') + ' - ' + end.format('MMMM D, YYYY HH:mm:ss'));
        }

        $('#reportrange-start').daterangepicker({
            startDate: start1,
            endDate: end1,
            autoApply: true,
            timePicker: true,
            locale: {
                format: 'DD/MMM/YYYY HH:mm:ss'
            },
            ranges: {
                'Today': [moment(carbonDate).startOf('day'), moment(carbonDate).endOf('day')],
                'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day'), moment(carbonDate).subtract(1, 'days').endOf('day')],
                'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day'), moment(carbonDate).endOf('day')],
                'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day'), moment(carbonDate).endOf('day')],
                'This Month': [moment(carbonDate).startOf('month'), moment(carbonDate).endOf('month')],
                'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month'), moment(carbonDate).subtract(1, 'month').endOf('month')]
            },
        }, function (start, end, label) {
            $('#start-datetime').val(start.format('YYYY-MM-DD HH:mm:ss'))
            $('#end-datetime').val(end.format('YYYY-MM-DD HH:mm:ss'))
        }, cb);

        $('#reportrange-start').on('apply.daterangepicker', function (ev, picker) {
            $('#reportrange-start span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
        });

        cb(start1, end1)

        let site = {{Request::input('site') ? Request::input('site') : 1 }};

        @if($ids == null)
            let transactions = null;
        @else
            let transactions = '{!!  $ids !!}';
        @endif

    </script>

    <script src="/js/transactions.js?{{Carbon::now()->timestamp}}"></script>

    <script>
        $(function () {
            $('#dateSearch').datetimepicker({
                format: 'DD/MM/YYYY HH:mm:ss',
            });
        })
    </script>
@endpush
