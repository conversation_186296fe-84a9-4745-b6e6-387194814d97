@extends('layouts.master')

@section('content')
    <div class="content">

        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Setup {{ucfirst($data->package)}} Link</h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>

        <form id="XeroForm" method="POST" action="/accounts/configure/save">

            {{ csrf_field() }}

            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Configure {{ucfirst($data->package)}} Link<br />
                            <small>Tell us which account payments are made to and which contact to create invoices for.</small>
                        </div>
                        <div class="panel-body">

                            @foreach($data->paymentMethods as $pm)
                                <div class="form-group col-xs-6">
                                    <label><strong>{{ucfirst($pm['name'])}} Bank Account</strong></label>
                                    <select class="form-control multiselect" name="bank_account[{{$pm['id']}}]">
                                        <option value="">Select a bank account for {{$pm['name']}} payments</option>

                                        <option @if(isset($pm['xerolinks']['bank_account_id']) && $pm['xerolinks']['bank_account_id'] == 'DONOTPOST') selected @endif value="DONOTPOST">Do Not Post to {{ucfirst($data->package)}}</option>
                                        @foreach($data->accounts['bank'] as $account)
                                            <option @if(isset($pm['xerolinks']['bank_account_id']) && $pm['xerolinks']['bank_account_id'] == $account['id']) selected @endif value="{{$account['id']}}::{{$account['name']}}">{{$account['name']}}</option>
                                        @endforeach

                                        @if($data->package !== 'quickbooks')
                                            @foreach($data->accounts['revenue'] as $account)
                                                <option @if(isset($pm['xerolinks']['bank_account_id']) && $pm['xerolinks']['bank_account_id'] == $account['id']) selected @endif value="{{$account['id']}}::{{$account['name']}}">{{$account['name']}}</option>
                                            @endforeach
                                        @endif

                                    </select>
                                </div>
                            @endforeach

                            <div class="form-group col-xs-12">
                                <label><strong>Sales Account <span class="text-danger">(Required)</span></strong></label>
                                <select class="form-control multiselect" name="sales_account">
                                    <option value="">Select the sales account code from {{ucfirst($data->package)}}</option>
                                    @foreach($data->accounts['revenue'] as $account)
                                        <option @if($data->mappings['company']['sales_account_id'] == $account['id']) selected @endif value="{{$account['id']}}::{{$account['name']}}">{{$account['name']}}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group col-xs-12">
                                <label><strong>Purchase Account <span class="text-danger">(Required)</span></strong></label>
                                <select class="form-control multiselect" name="purchase_account">
                                    <option value="">Select the purchases account code from {{ucfirst($data->package)}}</option>
                                    @foreach($data->accounts['revenue'] as $account)
                                        <option @if($data->mappings['company']['sales_account_id'] == $account['id']) selected @endif value="{{$account['id']}}::{{$account['name']}}">{{$account['name']}}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group col-xs-12">
                                <label><strong>Invoice Contact <span class="text-danger">(Required)</span></strong></label>
                                <select class="form-control multiselect" name="invoice_contact">
                                    <option value="">Select an contact from {{ucfirst($data->package)}}</option>
                                    @foreach($data->customers as $customer)
                                        <option @if($data->mappings['company']['contact_id'] == $customer['id']) selected @endif value="{{$customer['id']}}::{{$customer['name']}}">{{$customer['name']}}</option>
                                    @endforeach
                                </select>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Assign Items & Departments<br />
                            <small>Assign products to departments and other financial records.</small>
                        </div>
                        <div class="panel-body">
                            @if(isset($data->mappings['departments']))
                                @foreach($data->mappings['departments'] as $department)
                                    <div class="form-group col-xs-12" style="border: 1px solid #999888; border-radius: 5px;padding: 10px; position: relative; margin-top: 25px;">
                                        <span class="form-group-title" style="position: absolute; top: -24px; left: 0px; font-weight: bold; font-size: 16px;">{{$department['displayname']}}</span>
                                        <div class="col-xs-6">
                                            <label><strong>Invoice Item</strong></label>
                                            <select class="form-control multiselect" name="departments[{{$department['id']}}]">
                                                <option value="">Select an item from {{ucfirst($data->package)}}</option>
                                                @foreach($data->products as $item)
                                                    <option @if($department['acc_code'] == $item['id']) selected @endif value="{{$department['guid']}}::{{$item['id']}}::{{$item['name']}}">{{$item['name']}}</option>
                                                @endforeach

                                            </select>
                                        </div>

                                        <div class="col-xs-6">
                                            <label><strong>Tax Type</strong></label>
                                            <select class="form-control multiselect" name="departments_tax[{{$department['id']}}]">
                                                <option value="">Select a tax type</option>
                                                @foreach($data->taxcodes as $tax)
                                                    <option @if($department['tax_code'] == $tax['id']) selected @endif value="{{$department['guid']}}::{{$tax['id']}}::{{$tax['name']}}">{{$tax['name']}}</option>
                                                @endforeach
                                            </select>

                                        </div>
                                    </div>
                                @endforeach

                            <div class="row">
                                <hr />
                            </div>
                            @endif

                            @if(isset($data->mappings['departments']))
                                @foreach($data->mappings['commands'] as $link)
                                    <div class="form-group col-xs-12" style="border: 1px solid #999888; border-radius: 5px;padding: 10px; position: relative; margin-top: 25px;">
                                        <span class="form-group-title" style="position: absolute; top: -24px; left: 0px;font-weight: bold; font-size: 16px;">{{$link['displayname']}}</span>
                                        <div class="col-xs-6">
                                            <label><strong>Invoice Item</strong></label>
                                            <select class="form-control multiselect" name="types[]">
                                                <option value="">Select an item from {{ucfirst($data->package)}}</option>
                                                @foreach($data->products as $item)
                                                    <option @if($link['acc_code'] == $item['id']) selected @endif value="{{$link['id']}}::{{$item['id']}}::{{$item['name']}}">{{$item['name']}}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div class="col-xs-6">
                                            <label><strong>Tax Rate</strong></label>
                                            <select class="form-control multiselect" name="types_tax[]">
                                                <option value="">Select a tax type</option>
                                                @foreach($data->taxcodes as $tax)
                                                    <option @if($link['tax_code'] == $tax['id']) selected @endif value="{{$tax['id']}}::{{$tax['name']}}" >{{$tax['name']}}</option>
                                                @endforeach
                                            </select>

                                        </div>
                                    </div>
                                @endforeach
                            @endif

                            @if(isset($data->integration_departments))
                                @foreach($data->integration_departments as $link)
                                    <div class="form-group col-xs-12" style="border: 1px solid #999888; border-radius: 5px;padding: 10px; position: relative; margin-top: 25px;">
                                        <span class="form-group-title" style="position: absolute; top: -24px; left: 0px;font-weight: bold; font-size: 16px;">{{$link['displayname']}}</span>
                                        <div class="col-xs-6">
                                            <label><strong>Invoice Item</strong></label>
                                            <select class="form-control multiselect" name="types[]">
                                                <option value="">Select an item from {{ucfirst($data->package)}}</option>
                                                @foreach($data->products as $item)
                                                    <option @if($link['acc_code'] == $item['id']) selected @endif value="{{$link['id']}}::{{$item['id']}}::{{$item['name']}}">{{$item['name']}}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div class="col-xs-6">
                                            <label><strong>Tax Rate</strong></label>
                                            <select class="form-control multiselect" name="types_tax[]">
                                                <option value="">Select a tax type</option>
                                                @foreach($data->taxcodes as $tax)
                                                    <option @if($link['tax_code'] == $tax['id']) selected @endif value="{{$tax['id']}}::{{$tax['name']}}" >{{$tax['name']}}</option>
                                                @endforeach
                                            </select>

                                        </div>
                                    </div>
                                @endforeach
                            @endif


                        </div>
                        <div class="panel-footer">
                            <button class="btn btn-block btn-success">Save Link Configuration</button>
                        </div>
                    </div>
                </div>
            </div>

        </form>

    </div>
@endsection

@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@8"></script>

    <script>
        $(function(){
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                }
            })
        });

        $('body').on('change', '.item-select', function(){
            let option = $(this)

            let department = option.data('department')
            let taxtype = option.data('taxtype')
            let id = option.attr('name')

            $("[name='departments_tax["+ id +"]']").val(taxtype);

        })

        let form = $('#XeroForm');
        $('#XeroForm').submit(function(e){
            e.preventDefault();

            $.ajax({
                type: "POST",
                url: "/accounts/configure/save",
                data: $(form).serialize(),
                dataType: 'json',
                success: function () {
                    Swal.fire(
                        'Saved',
                        'Your {{ucfirst($data->package)}} settings have been saved!',
                        'success'
                    );
                },
                error: function (data) {
                    Swal.fire(
                        'Error!',
                        data.responseJSON.message,
                        'error'
                    );
                }
            });
        });


    </script>
@endsection
