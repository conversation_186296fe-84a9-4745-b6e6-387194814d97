@extends('layouts.master')

@section('content')

    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/accounts" class="">Accounting</a></li>
        <li><a href="/accounts" class="">Date Range Selection</a></li>
        <li>Preview Invoices</li>
    </ol>

    <div class="content">

        @if(count($invoices->invoices) > 0 )

        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">Preview Invoices for period ({{count($invoices->invoices)}})</h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>
        <script>
            let current = 1;
            let max = {{count($invoices->invoices)}};

            $(document).on('click', '#nextInvoice', function(e){
                e.preventDefault();
                nextInvoice();
            });

            $(document).on('click', '#prevInvoice', function(e){
                e.preventDefault();
                prevInvoice();
            });

            $(function(){
                changeTag()
            });

            function nextInvoice(){
                if(current < max){

                    $('#invoice-'+current).hide();
                    $('#invoice-'+(current+1)).show();

                    current = current+1;

                    changeTag()
                }
            }

            function prevInvoice(){
                if(current > 1) {
                    $('#invoice-' + current).hide();
                    $('#invoice-' + (current -1)).show();
                    current = current-1;

                    changeTag()
                }
            }

            function changeTag(){
                let table = $('#invoice-'+current);
                let date = table.attr('data-date');

                let string = date + ' ('+current+'/'+max+')';

                $('#currentInvoiceText').text(string);
            }
        </script>

        @if(isset($invoices->errors) && count($invoices->errors) > 0)
                <div class="alert alert-danger text-center"><h4>An Error Occurred</h4><br />{{$invoices->errors[0]['message']}} @if($invoices->errors[0]['code'] == 429) <br /> <span id="counter"></span> @endif </div>
                @if($invoices->errors[0]['code'] == 429)
                <script>
                    var count = 60, timer = setInterval(function() {
                        $("#counter").html(count-- + ' seconds remaining');
                        if(count == 1){
                            clearInterval(timer)
                            $("#counter").html('Please try again!');
                        };
                    }, 1000);
                </script>
                @endif
        @else
            <div class="row" style="margin: 40px 40px 40px 40px; border: 2px solid slategrey; padding: 20px 80px 80px 80px;">
                <div class="col-md-2 col-md-offset-2" style="text-align: right; font-size: 25px; padding-top: 50px;">
                    <i class="fa fa-chevron-circle-left" id="prevInvoice" style="cursor:pointer; padding-right: 30px;"></i>
                </div>
                <div class="col-md-4">
                <h2 style="text-align: center;">Preview Invoices <br /><small> {{\Carbon\Carbon::parse($invoices->start)->format('D d M Y')}} - {{\Carbon\Carbon::parse($invoices->end)->format('D d M Y')}} <br />
                         <span id="currentInvoiceText"></span> </small></h2>
                </div>
                <div class="col-md-2" style="text-align: left; font-size: 25px; padding-top: 50px;">
                    <i style="cursor:pointer; padding-left: 30px;" class="fa fa-chevron-circle-right" id="nextInvoice"></i>
                </div>

                <div class="col-md-12">
                    @foreach($invoices->invoices as $invoice)
                        @include('modules.accounts.invoices.invoice')
                    @endforeach
                </div>
                <div class="col-xs-12">
                    <a href="#" class="btn btn-success btn-lg btn-block submitInvoice"><i class="fa fa-upload"></i> Post Data to {{ucfirst(Auth::user()->company->accounts_package)}}</a>
                </div>

                <div class="col-xs-12" id="moreDates" style="display: none;">
                    <a href="/accounts" class="btn btn-success btn-lg btn-block"><i class="fa fa-calendar"></i> Pick new Dates</a>
                </div>
            </div>
        @endif

    </div>

    <script>
        let mainInvoices = {!! json_encode($invoices) !!};
        let errors = [];

        $(document).on('click', '.submitInvoice', function(e){
            e.preventDefault();

            let element = $(this);
            let invoiceCount = mainInvoices.status.count;

            element.hide();

            processing(0, invoiceCount)
            setTimeout(function() {
                    processInvoices()
                }, 100
            )
        });

        function processInvoices() {

            _.each(mainInvoices.invoices, function(invoice, key){
                setTimeout(function() {
                    $.ajax({
                        async: true,
                        type: "POST",
                        url: "/accounts/invoices/send/" + key,
                        success: function (data) {
                            let element = $('.submitInvoice')
                            let invoiceCount = mainInvoices.status.count;

                            if((key+1) === mainInvoices.status.count){
                                notificationBar('success', 'Processing Invoices Complete!')
                            } else {
                                $('#current-id').html((key + 1) + '/' + invoiceCount)
                            }

                            $('#table3-'+(key + 1)).append('<tr><td colspan="3" class="alert alert-success text-center"><h3>Invoice posted</h3></td></tr>')

                            $('#moreDates').show();
                        },
                        error: function (data) {
                            console.log(data)
                            data = data.responseJSON;
                            let invoiceCount = mainInvoices.status.count;

                            if((key+1) === mainInvoices.status.count) {
                                notificationBar('error', 'Completed posting with errors, please check each invoice to see the errors reported')
                            } else {
                                $('#current-id').html((key + 1) + '/' + invoiceCount)
                            }

                            if(data.errors) {
                                _.each(data.errors, function (error) {
                                    if(error.account) {
                                        $('#table2-' + (key + 1)).append('<tr><td colspan="3" class="alert alert-danger text-center">Account: ' + error.account + ' - ' + error.message + '</td></tr>')
                                    } else {
                                        $('#table2-' + (key + 1)).append('<tr><td colspan="3" class="alert alert-danger text-center">' + error.message + '</td></tr>')
                                    }
                                });
                            } else {
                                $('#table3-' + (key + 1)).append('<tr><td colspan="3" class="alert alert-danger text-center">' + data.message + '</td></tr>')
                            }

                        }

                    });
                }, 100)

            });

        }

        function processing(current, max){
            Swal.fire({
                title: 'Processing Invoices',
                html: '<span id="current-id">'+current+'/'+max+'</span>',
                allowOutsideClick: false,
                onBeforeOpen: () => {
                    Swal.showLoading()
                }
            })
        }

    </script>

    @endif

@stop