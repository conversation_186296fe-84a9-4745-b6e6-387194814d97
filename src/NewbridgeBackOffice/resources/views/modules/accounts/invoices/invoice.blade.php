
<div class="row" style=" @if($loop->first) display: block; @else display: none; @endif " id="invoice-{{$loop->iteration}}" data-date="{{\Carbon\Carbon::parse($invoice['date'])->format('d/m/Y')}}">
    <h3>Invoice - {{\Carbon\Carbon::parse($invoice['date'])->format('D d M Y')}}</h3>
    <table class="table-responsive table table-condensed table-striped" id="table-{{$loop->iteration}}">
        <thead>
            <tr>
                <th>Product/Service Name</th>
                <th>Posting To</th>
                <th>Total Amount</th>
            </tr>
        </thead>
        <tbody>
        @php
            $deptTotal = 0;
        @endphp
        @if(isset($invoice['department_lines']) && !empty($invoice['department_lines']))
            @foreach($invoice['department_lines'] as $line)
                @php
                    $deptTotal = ($deptTotal+$line['value']);
                @endphp
                <tr>
                    <td>{{$line['name']}}</td>
                    <td>{{$line['accounting_code_name']}} - {{$line['accounting_tax_code_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
        @endif
        @if(isset($invoice['addition_lines']) && !empty($invoice['addition_lines']))
            @foreach($invoice['addition_lines'] as $line)
                @php
                    $deptTotal = ($deptTotal+$line['value']);
                @endphp
                <tr>
                    <td>{{$line['name']}}</td>
                    <td>{{$line['accounting_code_name']}} - {{$line['accounting_tax_code_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
        @endif
        @if(isset($invoice['subtraction_lines']) && !empty($invoice['subtraction_lines']))
            @foreach($invoice['subtraction_lines'] as $line)
                @php
                    $deptTotal = ($deptTotal+$line['value']);
                @endphp
                <tr>
                    <td>{{$line['name']}}</td>
                    <td>{{$line['accounting_code_name']}} - {{$line['accounting_tax_code_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
        @endif
        @if(isset($invoice['mod_department_lines']) && !empty($invoice['mod_department_lines']))
            @foreach($invoice['mod_department_lines'] as $line)
                @php
                    $deptTotal = ($deptTotal+$line['value']);
                @endphp
                <tr>
                    <td>Exception: {{$line['name']}}</td>
                    <td>{{$line['accounting_code_name']}} - {{$line['accounting_tax_code_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
        @endif
        <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td colspan="1"><strong>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($deptTotal, 2)}}</strong></td>
        </tr>
        </tbody>
    </table>
    @php
    $expenseTotal = 0
    @endphp
    <table class="table-responsive table table-condensed table-striped" id="table3-{{$loop->iteration}}">
        <thead>
        <tr>
            <th colspan="3">Expenses/Paid Out</th>
        </tr>
        </thead>
        @if(isset($invoice['expenses_lines']) && !empty($invoice['expenses_lines']))
            @foreach($invoice['expenses_lines'] as $line)
                @php
                    $expenseTotal = ($expenseTotal+$line['value']);
                @endphp
                <tr>
                    <td>{{$line['name']}}</td>
                    <td>{{$line['accounting_code_name']}} - {{$line['accounting_tax_code_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
        @endif
        <tr>
            <td colspan="2"><strong>Total</strong></td>
            <td colspan="1"><strong>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($expenseTotal, 2)}}</strong></td>
        </tr>
    </table>

    @if(isset($invoice['payment_lines']) && !empty($invoice['payment_lines']))
    <table class="table-responsive table table-condensed table-striped" id="table2-{{$loop->iteration}}">
        <thead>
            <tr>
                <th colspan="3">Payments</th>
            </tr>
        </thead>
        <tbody>
            @php
            $paymentTotal = 0;
            @endphp
            @foreach($invoice['payment_lines'] as $line)
                @php
                $paymentTotal = ($paymentTotal+$line['value']);
                @endphp
                <tr>
                    <td colspan="1">{{$line['name']}}</td>
                    <td>{{$line['bank_account_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
            <tr>
                <td colspan="2"><strong>Total</strong></td>
                <td colspan="1"><strong>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($paymentTotal, 2)}}</strong></td>
            </tr>
        <tr>
            @if($invoice['valid'] === true)
                <td colspan="3" class="alert alert-success text-center">
                    <strong>VALID:</strong> For this invoice the payments match the sum of the invoice items and is valid!
                </td>
            @endif
            @if($invoice['valid'] === false)
                <td colspan="3" class="alert alert-danger text-center">
                    <strong>INVALID:</strong> For this invoice the payments DO NOT match the sum of the invoice items and is not valid!
                </td>
            @endif
        </tr>
        </tbody>
    </table>
    @endif
    @if(isset($invoice['journal_payment_lines']) && !empty($invoice['journal_payment_lines']))
        <table class="table-responsive table table-condensed table-striped" id="table2-{{$loop->iteration}}">
            <thead>
            <tr>
                <th colspan="3">Payments</th>
            </tr>
            </thead>
            <tbody>
            @php
                $paymentTotal = 0;
            @endphp
            @foreach($invoice['journal_payment_lines'] as $line)
                @php
                    $paymentTotal = ($paymentTotal+$line['value']);
                @endphp
                <tr>
                    <td colspan="1">{{$line['name']}}</td>
                    <td>{{$line['accounting_code_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
            <tr>
                <td colspan="2"><strong>Total</strong></td>
                <td colspan="1"><strong>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($paymentTotal, 2)}}</strong></td>
            </tr>
{{--            <tr>--}}
{{--                @if($invoice['valid'] === true)--}}
{{--                    <td colspan="3" class="alert alert-success text-center">--}}
{{--                        <strong>VALID:</strong> For this invoice the payments match the sum of the invoice items and is valid!--}}
{{--                    </td>--}}
{{--                @endif--}}
{{--                @if($invoice['valid'] === false)--}}
{{--                    <td colspan="3" class="alert alert-danger text-center">--}}
{{--                        <strong>INVALID:</strong> For this invoice the payments DO NOT match the sum of the invoice items and is not valid!--}}
{{--                    </td>--}}
{{--                @endif--}}
{{--            </tr>--}}
            </tbody>
        </table>
    @endif
    @if(isset($invoice['balance_lines']) && !empty($invoice['balance_lines']))
        <table class="table-responsive table table-condensed table-striped" id="table2-{{$loop->iteration}}">
            <thead>
            <tr>
                <th colspan="3">Balances</th>
            </tr>
            </thead>
            <tbody>
            @php
                $balanceTotal = 0;
            @endphp
            @foreach($invoice['balance_lines'] as $line)
                @php
                    $balanceTotal = ($balanceTotal+$line['value']);
                @endphp
                <tr>
                    <td colspan="1">{{$line['name']}}</td>
                    <td>{{$line['accounting_code_name']}}</td>
                    <td>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($line['value'], 2)}}</td>
                </tr>
            @endforeach
            <tr>
                <td colspan="2"><strong>Total</strong></td>
                <td colspan="1"><strong>{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}{{number_format($balanceTotal, 2)}}</strong></td>
            </tr>
{{--            <tr>--}}
{{--                @if($invoice['valid'] === true)--}}
{{--                    <td colspan="3" class="alert alert-success text-center">--}}
{{--                        <strong>VALID:</strong> For this invoice the payments match the sum of the invoice items and is valid!--}}
{{--                    </td>--}}
{{--                @endif--}}
{{--                @if($invoice['valid'] === false)--}}
{{--                    <td colspan="3" class="alert alert-danger text-center">--}}
{{--                        <strong>INVALID:</strong> For this invoice the payments DO NOT match the sum of the invoice items and is not valid!--}}
{{--                    </td>--}}
{{--                @endif--}}
{{--            </tr>--}}
            </tbody>
        </table>
    @endif

    <table class="table-responsive table table-condensed table-striped" id="table3-{{$loop->iteration}}">
        <tr>
            <td colspan="3">
                <div id="posting-result-{{$loop->iteration}}"></div>
            </td>
        </tr>
    </table>

    <style>
        /* Customize the label (the container) */
        .container {
            display: block;
            position: relative;
            padding-left: 35px;
            margin-bottom: 12px;
            cursor: pointer;
            font-size: 22px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Hide the browser's default checkbox */
        .container input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        /* Create a custom checkbox */
        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 25px;
            width: 25px;
            background-color: #eee;
        }

        /* On mouse-over, add a grey background color */
        .container:hover input ~ .checkmark {
            background-color: #ccc;
        }

        /* When the checkbox is checked, add a blue background */
        .container input:checked ~ .checkmark {
            background-color: #2196F3;
        }

        /* Create the checkmark/indicator (hidden when not checked) */
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the checkmark when checked */
        .container input:checked ~ .checkmark:after {
            display: block;
        }

        /* Style the checkmark/indicator */
        .container .checkmark:after {
            left: 9px;
            top: 5px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 3px 3px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }

            /* Customize the label (the container) */
        .container {
            display: block;
            position: relative;
            padding-left: 35px;
            margin-bottom: 12px;
            cursor: pointer;
            font-size: 22px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Hide the browser's default radio button */
        .container input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        /* Create a custom radio button */
        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 25px;
            width: 25px;
            background-color: #eee;
            border-radius: 50%;
        }

        /* On mouse-over, add a grey background color */
        .container:hover input ~ .checkmark {
            background-color: #ccc;
        }

        /* When the radio button is checked, add a blue background */
        .container input:checked ~ .checkmark {
            background-color: #2196F3;
        }

        /* Create the indicator (the dot/circle - hidden when not checked) */
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }

        /* Show the indicator (dot/circle) when checked */
        .container input:checked ~ .checkmark:after {
            display: block;
        }

        /* Style the indicator (dot/circle) */
        .container .checkmark:after {
            top: 9px;
            left: 9px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: white;
        }



    </style>
</div>
