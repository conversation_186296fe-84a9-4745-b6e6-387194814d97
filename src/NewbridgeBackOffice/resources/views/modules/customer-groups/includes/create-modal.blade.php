<div id="CreateModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Add Loyalty Scheme</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="CreateForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Scheme Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Scheme Name</strong><br /><small>e.g. Gold Club</small></label>
                                                <input type="text" class="form-control" name="displayname" placeholder="Gold Club"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Scheme Type</strong><br /><small>Select the type of reward.</small></label>
                                                <select name="group_type" class="input form-control input-md">
                                                    <option value="none">No Rewards</option>
                                                    <option value="discount">Discount</option>
                                                    {{--<option value="discount">Price Level</option>--}}
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Earn Points</strong><br /><small>Would you like your customers to earn points?</small></label>
                                                <select name="earn_points" class="input form-control input-md">
                                                    <option value="no">No</option>
                                                    <option value="yes">Yes</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row discounttype discount" id="discount" style="display: none;">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Discount Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Discount Type</strong><br /><small>What type of discount should be applied to the promotion?</small></label>
                                                <select class="form-control" name="discountmethod" id="discountmethod">
                                                    <option value="0">Percentage Off</option>
                                                    <option value="1">Fixed Amount Off</option>
                                                    {{--<option value="2">Set Price</option>--}}
                                                    <option value="3">Price Level</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 discountvalue" id="discountvalue" style="display: none;">
                                            <div class="form-group">
                                                <label><strong>Discount Value</strong>
                                                    <br /><small>How much discount should be given or if using a set price, what is total amount for the items in this promotion?</small></label>
                                                <input name="value" class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-lg-6 pricelevel" id="pricelevel" style="display: none;">
                                            <div class="form-group">
                                                <label><strong>Select a Price Level</strong></label>
                                                <br /><small>Select which price level to change to</small></label>
                                                <select name="price_level" class="input form-control input-md">
                                                    <option value="">Select a price level</option>
                                                    <option value="2">Price Level 2</option>
                                                    <option value="3">Price Level 3</option>
                                                    <option value="4">Price Level 4</option>
                                                    <option value="5">Price Level 5</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{--<div class="row discounttype" id="pricepoint" style="display: none;">--}}
                    {{--<div class="col-lg-12">--}}
                    {{--<div class="panel panel-default">--}}
                    {{--<div class="panel-heading">--}}
                    {{--Price Level Information--}}
                    {{--</div>--}}
                    {{--<div class="panel-body">--}}
                    {{--<div class="row">--}}
                    {{--<div class="col-lg-6">--}}
                    {{--<div class="form-group">--}}
                    {{--<label><strong>Select a Price Level</strong></label>--}}
                    {{--<select name="price_level" class="input form-control input-md">--}}
                    {{--<option value="">Select a price level</option>--}}
                    {{--<option value="2">Price Level 2</option>--}}
                    {{--<option value="3">Price Level 3</option>--}}
                    {{--<option value="4">Price Level 4</option>--}}
                    {{--<option value="5">Price Level 5</option>--}}
                    {{--</select>--}}
                    {{--</div>--}}
                    {{--</div>--}}
                    {{--</div>--}}
                    {{--</div>--}}
                    {{--</div>--}}
                    {{--</div>--}}
                    {{--</div>--}}

                    <div class="row points" id="points" style="display: none;">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Point Earning Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>How many Points?</strong><br /><small>How many points will be earned per {!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!} spent?</small></label>
                                                <input type="number" min="0.5" max="100" name="points_earned" class="form-control"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Points Value</strong><br /><small>How much is each point worth in pounds?</small></label>
                                                <input type="number" min="0" max="100" name="points_value" class="form-control"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="createSubmit">Create</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).on('change', '[name="group_type"]', function(){

        var val = $(this).val();

        if(val == ''){
            $('.discounttype').hide();
        } else {
            $('.discounttype').hide();
            $('.' + val).show();
        }
    })

    $(document).on('change', '[name="earn_points"]', function(){

        var val = $(this).val();

        if(val == 'no'){
            $('.points').hide();
        } else {
            $('.points').show();
        }
    })

    $(document).on('change', '#discountmethod', function(){
        var val = $(this).val()
        if(val < 3){
            $('.pricelevel').hide()
            $('.discountvalue').show()
        } else {
            $('.pricelevel').show()
            $('.discountvalue').hide()
        }
    })
</script>