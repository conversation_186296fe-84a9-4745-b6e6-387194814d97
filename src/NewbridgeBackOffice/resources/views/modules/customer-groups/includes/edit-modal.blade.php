<div id="EditModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <h4>Edit Loyalty Scheme ({{$group->displayname}})</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="EditForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Scheme Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Scheme Name</strong><br /><small>e.g. Gold Club</small></label>
                                                <input type="text" class="form-control" name="displayname" placeholder="Gold Club" value="{{$group->displayname}}"/>
                                                <input type="hidden" name="id" value="{{$group->id}}">
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Scheme Type</strong><br /><small>Select the type of reward.</small></label>
                                                <select name="group_type" class="input form-control input-md">
                                                    <option value="none" selected>No Rewards</option>
                                                    {{--<option value="points" @if($group->gives_points == 1) selected @endif>Earn Points</option>--}}
                                                    <option value="discount" @if($group->discount_guid != null) selected @endif>Discount</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Earn Points</strong><br /><small>Would you like your customers to earn points?</small></label>
                                                <select name="earn_points" class="input form-control input-md">
                                                    <option value="yes" @if($group->gives_points == 1) selected @endif>Yes</option>
                                                    <option value="no" @if($group->gives_points != 1) selected @endif>No</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row discounttype discount" id="discount" @if($group->discount_guid == null) style="display: none;" @endif>
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                            <div class="panel-heading">
                                Discount Information
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label><strong>Discount Type</strong><br /><small>What type of discount should be applied to the promotion?</small></label>
                                            <select class="form-control discountmethod" name="discountmethod">
                                                <option value="0" @if($group->discount && $group->discount->discountmethod == 0) selected @endif>Percentage Off</option>
                                                <option value="1" @if($group->discount && $group->discount->discountmethod == 1) selected @endif>Fixed Amount Off</option>
                                                {{--<option value="2" @if($group->discount && $group->discount->discountmethod == 2) selected @endif>Set Price</option>--}}
                                                <option value="3" @if($group->discount && $group->discount->discountmethod == 3) selected @endif>Price Level</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 discountvalue" @if($group->discount && $group->discount->discountmethod != 1) style="display: none;" @endif >
                                        <div class="form-group">
                                            <label><strong>Discount Value</strong>
                                            <br /><small>How much discount should be given?</small></label>
                                            <input name="value" class="form-control" @if($group->discount) value="{{$group->discount->value}}" @endif>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 pricelevel" @if($group->discount && $group->discount->discountmethod != 3) style="display: none;" @endif >
                                        <div class="form-group">
                                            <label><strong>Select a Price Level</strong>
                                                <br /><small>Which price level should be applied to this customer group?</small></label>
                                            <select name="price_level" class="input form-control input-md">
                                                <option value="">Select a price level</option>
                                                <option value="2" @if($group->price_level == 2) selected @endif>Price
                                                    Level 2
                                                </option>
                                                <option value="3" @if($group->price_level == 3) selected @endif>Price
                                                    Level 3
                                                </option>
                                                <option value="4" @if($group->price_level == 4) selected @endif>Price
                                                    Level 4
                                                </option>
                                                <option value="5" @if($group->price_level == 4) selected @endif>Price
                                                    Level 5
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>

                    {{--<div class="row discounttype" id="pricepoint" @if($group->price_level == null) style="display: none;" @endif>--}}
                        {{--<div class="col-lg-12">--}}
                            {{--<div class="panel panel-default">--}}
                                {{--<div class="panel-heading">--}}
                                    {{--Price Level Information--}}
                                {{--</div>--}}
                                {{--<div class="panel-body">--}}
                                    {{--<div class="row">--}}
                                        {{--<div class="col-lg-6">--}}
                                            {{--<div class="form-group">--}}
                                                {{--<label><strong>Select a Price Level</strong></label>--}}
                                                {{--<select name="price_level" class="input form-control input-md">--}}
                                                    {{--<option value="">Select a price level</option>--}}
                                                    {{--<option value="2" @if($group->price_level == 2) selected @endif>Price Level 2</option>--}}
                                                    {{--<option value="3" @if($group->price_level == 3) selected @endif>Price Level 3</option>--}}
                                                    {{--<option value="4" @if($group->price_level == 4) selected @endif>Price Level 4</option>--}}
                                                    {{--<option value="5" @if($group->price_level == 4) selected @endif>Price Level 5</option>--}}
                                                {{--</select>--}}
                                            {{--</div>--}}
                                        {{--</div>--}}
                                    {{--</div>--}}
                                {{--</div>--}}
                            {{--</div>--}}
                        {{--</div>--}}
                    {{--</div>--}}

                    <div class="row points" id="points" @if($group->gives_points != 1) style="display: none;" @endif>
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Point Earning Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>How many Points?</strong><br /><small>How many points will be earned per {!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!} spent?</small></label>
                                                <input type="number" min="0.5" max="100" name="points_earned" class="form-control" value="{{$group->points_per_amount}}"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Points Value</strong><br /><small>How much is each point worth in pounds?</small></label>
                                                <input type="number" min="0" max="100" name="points_value" class="form-control" value="{{$group->points_spending_value}}"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="editSubmit">Edit</button>
            </div>
        </div>
    </div>
</div>

<script>

    // $('body').on('change', '#EditModal [name="discountmethod"]', function(){
    //     var val = $(this).val()
    //
    //     if(val < 3){
    //         $('.pricelevel').hide()
    //         $('.discountvalue').show()
    //     } else {
    //         $('.pricelevel').show()
    //         $('.discountvalue').hide()
    //     }
    // })

    $('#editSubmit').on('click', function(){
        $('#EditForm').submit()
    })

    var form = $('#EditForm')

    // validate signup form on keyup and submit
    $("#EditForm").validate({
        rules: {
            displayname: "required",
            group_type: "required",
            discountmethod: {
                required: function(element){
                    if($('[name="group_type"]').val() == 'discount'){
                        return true
                    } else {
                        return false;
                    }
                }
            },
            value: {
                required: function(element){
                    if($('[name="discountmethod"]').val() < 3){
                        return true
                    } else {
                        return false;
                    }
                }
            },
            price_level: {
                required: function(element){
                    if($('[name="discountmethod"]').val() == 3){
                        return true
                    } else {
                        return false;
                    }
                }
            },
            points_earned: {
                required: function(element){
                    if($('[name="group_type"]').val() == 'points'){
                        return true
                    } else {
                        return false;
                    }
                }
            },
            points_value: {
                required: function(element){
                    if($('[name="group_type"]').val() == 'points'){
                        return true
                    } else {
                        return false;
                    }
                }
            },

        },
        submitHandler: function (form) {
            $.ajax({
                type: "PUT",
                url: "/customers/groups/edit-single",
                data: $(form).serialize(),
                success: function () {
                    $('#EditModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    })
</script>