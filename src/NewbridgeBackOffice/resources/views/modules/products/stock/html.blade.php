{{--<div class="col-lg-12" style="border: #0a0a0a 1px">--}}
<form class="inlineEditForm">
    {{ csrf_field() }}
<div class="col-xs-12">
    @if($site->allowed_modules & 1)
    <div class="col-xs-12" style="color: white; background-color: #888a85; padding: 5px; text-align: center;">
        Current Stock: {{$product->product_quantity}}
    </div>

    <div class="row">
        <div class="col-lg-6">
            <label><strong>Stock Type</strong></label>
            <div class="form-group">
                <select name="stock_type" class="form-control stock-type-select">
                    <option value="">Choose One...</option>
                    <option @if($product->sku_guid == null && $product->recipe_guid == null && $product->plu_parent_guid == null) selected @endif value="1">Non Stock</option>
                    <option @if($product->sku_guid != null) selected @endif value="2">Stock Unit</option>
                    <option @if($product->recipe_guid != null) selected @endif value="3">Recipe</option>
                    <option @if($product->plu_parent_guid != null) selected @endif value="4">Parent Product</option>
                </select>
            </div>
        </div>
    </div>

    <div class="sku_area" @if($product->sku_guid == null) style="display: none;" @endif>
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group sku-data">
                    <label><strong>Stock Unit</strong></label>
                    <select name="sku_guid" class="form-control">
                        <option value="">Choose One...</option>
                        @foreach($skus as $v)
                            <option @if($product->sku_guid == $v->guid) selected @endif value="{{$v->guid}}">{{$v->displayname}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="form-group">
                    <label><strong>Supplier</strong></label>
                    <select class="form-control" name="supplier_guid">
                        <option value="">Please select one</option>
                        @foreach($suppliers as $v)
                            <option @if($product->supplier_guid == $v->guid) selected @endif value="{{$v->guid}}">{{$v->name}}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <div class="form-group">
                    <label><strong>Min Stock</strong></label>
                    <input type="text" class="form-control" maxlength="30" name="min_stock"
                           value="{{!$product->min_stock ? 0 : $product->min_stock}}"/>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="form-group">
                    <label><strong>Max Stock</strong></label>
                    <input type="text" class="form-control" maxlength="30" name="max_stock"
                           value="{{!$product->max_stock ? 0 : $product->max_stock}}">
                </div>
            </div>
            @if ($companyDetails['integrations'] & 1)
                <div class="col-lg-6">
                    <div class="form-group">
                        <label><strong>ML Amount (1 Pint = 586 ML)</strong></label><br>
                        <input type="text" class="form-control" name="ml_amount" value="{{$product->ml_amount}}"/>
                        </label>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="form-group">
                        <label><strong>Reference No.</strong></label><br>
                        <input type="text" class="form-control" name="third_party_reference" value="{{$product->third_party_reference}}"/>
                        </label>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <div class="row recipe_area" @if($product->recipe_guid === null) style="display: none;" @endif>
        <div class="col-lg-6">
            <div class="form-group">
                <label><strong>Recipe</strong></label>
                <select class="form-control" name="recipe_guid">
                    <option value="">None</option>
                    @foreach($recipes as $v)
                        <option @if($product->recipe_guid == $v->guid) selected @endif value="{{$v->guid}}">{{$v->name}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="form-group">
                <label><strong>Recipe Qty</strong></label>
                <input type="number" class="form-control" name="recipe_qty" value="{{$product->recipe_qty}}"/>
            </div>
        </div>
    </div>

    <div class="row parent_area" @if($product->plu_parent_guid == null) style="display: none;" @endif>
        <div class="col-lg-6">
            <div class="form-group parent-data">
                <label><strong>Parent Stock Record</strong></label>
                <select class="form-control" name="plu_parent_guid">
                    <option value="">None</option>
                    @foreach($parents as $p)
                        <option @if($product->plu_parent_guid == $p->guid) selected @endif value="{{$p->guid}}">{{$p->displayname}}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="col-lg-6 parent-data">
            <div class="form-group">
                <label><strong>Stock Consumed</strong></label>
                <input type="text" class="form-control" maxlength="30" name="sku_quantity" value="{{$product->sku_quantity}}"/>
            </div>
        </div>
    </div>
    @else
    <div class="alert alert-warning">To manage stock for your products please activate the stock module on your account.</div>
    @endif
</div>
</form>
{{--</div>--}}