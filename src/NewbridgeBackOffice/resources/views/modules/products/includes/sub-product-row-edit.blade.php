@foreach($product->children as $child)
<div class="row">
    <div class="col-xs-12" class="sub-product-row" style="margin-top: 15px;">
        <div class="col-xs-12 col-md-4">
            <label><strong>Product Name</strong> <a href="/products/edit/{{$child->id}}"><i class="fa fa-edit"></i></a></label>
            <input class="form-control sub_product_name" name="sub_product_name[{{$child->guid}}]" value="{{$child->displayname}}">
        </div>
        <div class="col-xs-6 col-md-2">
            <label><strong>Price</strong></label>
            <input class="form-control sub_product_price" name="sub_product_price[{{$child->guid}}]" type="number" min="0" value="{{$child->selling_price_1}}">
        </div>
        <div class="col-xs-6 col-md-2">
            <label><strong>Barcode</strong></label>
            <input class="form-control sub_product_barcode" name="sub_product_barcode[{{$child->guid}}]" type="text" value="{{$child->barcode}}">
        </div>
        <div class="col-xs-6 col-md-2">
            <label><strong>Stock Quantity</strong></label>
            <input class="form-control sub_product_stock_quantity" name="sub_product_stock_quantity[{{$child->guid}}]" type="number" min="0.01" value="{{$child->sku_quantity}}">
        </div>
        <div class="col-xs-12 col-md-1">
            <label><strong>Priority</strong></label>
            <input class="form-control sub_product_priority" name="sub_product_priority[{{$child->guid}}]" type="number" min="0" value="{{$child->child_priority}}">
        </div>
        <div class="col-xs-6 col-md-1">
            <a href="/products/edit/{{$child->id}}" class="btn btn-primary" style="margin-top: 25px;"><i class="fa fa-edit"></i> Edit</a>
        </div>
    </div>
</div>
@endforeach