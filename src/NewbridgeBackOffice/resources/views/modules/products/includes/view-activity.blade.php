<div id="ViewActivityModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>

                <h4>Activity</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <div class="row">
                    <div class="col-md-12">
                        <table id="example" class="table table-striped table-bordered" cellspacing="0">
                            <thead>
                            <tr>
                                <th>Date / Time</th>
                                <th>User</th>
                                <th>Type</th>
                                <th>New</th>
                                <th>Old</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($activity as $a)

                                @php
                                    $changes = json_decode($a->properties, true);
                                @endphp
                                @if(isset($changes['attributes']))
                                <tr>
                                        <td>{{\Carbon\Carbon::parse($a->created_at, 'UTC')->setTimezone(\NewbridgeWeb\Http\Helpers\TimezoneHelper::getTimezone())->format('d/m/Y H:i:s')}}</td>
                                    <td>@if($a->causer !== null){{$a->causer->username}} @else Pos Terminal
                                        Update @endif</td>
                                    <td>{{$a->description}}</td>
                                    <td style="width: 30%">
                                        <ul style="list-style: none; text-indent: 0px; padding-left: 0px; margin-left: 0px;">
                                            @foreach($changes['attributes'] as $kh => $ch)
                                                @if($kh != 'productinfo')
                                                    <li><strong>{{$kh}}:</strong> {{$ch}}</li>
                                                @endif
                                            @endforeach
                                        </ul>
                                    </td>
                                    <td style="width: 30%">
                                        @if(isset($changes['old']))
                                            <ul style="list-style: none; text-indent: 0px; padding-left: 0px; margin-left: 0px;">
                                                @foreach($changes['old'] as $kh1 => $ch1)
                                                    @if($kh1 != 'productinfo')
                                                        <li><strong>{{$kh1}}:</strong> {{$ch1}}</li>
                                                    @endif
                                                @endforeach
                                            </ul>
                                        @endif
                                    </td>
                                </tr>
                                {{--                                                            <td>{{isset($a->changes()->old) ? json_encode(json_decode($a->changes())->old) : ''}}</td>--}}
                                @endif
                            @endforeach
                            </tbody>
                        </table>
                    </div>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    {{--                <button type="submit" class="btn btn-primary" id="printAmountForm">Update</button>--}}
                </div>
            </div>
        </div>
    </div>






