@extends('layouts.master')

@section('content')
    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h3>Mass Update Products</h3>
        <div class="col-xs-12 row" style="font-size: 16px; ">
            @foreach($products as $k => $p)
                <span class="label label-primary">{{$p->displayname}}</span>
            @endforeach
        </div>
        <br />
        <hr />

        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab" data-next="#modifiers" data-prev="" data-save="false">Product Details</a></li>
            <li id="step2"><a href="#modifiers" data-toggle="tab" data-next="#printers" data-prev="#details" data-save="false">Modifiers</a></li>
            <li id="step3"><a href="#printers" data-toggle="tab" data-next="" data-prev="#modifiers" data-save="false">Printing</a></li>
        </ul>
    </div>

    <form id="EditProductForm">
        <div class="tab-content">
            {{ csrf_field() }}
            @include('includes.nav-buttons')

            <div class="tab-pane active" id="details">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Basic Product Information
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6" id="product-name">
                                    <div class="form-group">
                                        <label><strong>Name</strong></label>
                                        <input type="text" class="form-control" name="displayname" data-display="Product Name"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Short Description</strong></label>
                                        <input type="text" class="form-control" maxlength="30" name="short_desc" data-display="Product Short Name"/>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="departments">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Department</strong></label>
                                        <select class="form-control" name="department_guid" data-display="Department">
                                            @foreach($departments as $dep)
                                                <option value="{{$dep->guid}}">{{$dep->displayname}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Sub Department</strong></label>
                                        <select class="form-control" name="sub_department_guid" data-display="Sub Department">
                                            @foreach($subdepartments as $dep)
                                                <option value="{{$dep->guid}}">{{$dep->displayname}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Barcode</strong></label>
                                        <input type="text" class="form-control" name="barcode" value="" data-display="Barcode"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Product Type</strong></label>
                                        <select class="form-control" name="is_modifier" data-display="Product Type">
                                            <option value="">Select One</option>
                                            <option value="0">Product</option>
                                            <option value="1">Modifier</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-4 isRestricted">
                                    <div class="form-group">
                                        <label><strong>Age Restricted Product?</strong></label><br>
                                        <label class="btn btn-default">
                                            <input type="checkbox" name="age_restricted" value="1" data-display="Age Restricted Product" data-change="false"/> Yes
                                        </label>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Weighed Product?</strong></label><br>
                                        <label class="btn btn-default">
                                            <input id="soldbyweight" type="checkbox" name="soldbyweight" value="1" data-display="Weighed Product" data-change="false"/> Yes
                                        </label>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Automatic Product List?</strong><br><small>
                                                If active the till will automatically show the below products as options when selling this product.
                                            </small></label><br />
                                        <label class="btn btn-default">
                                            <input id="show_child_list" type="checkbox" name="show_child_list" value="1" data-display="Auto Product List" data-change="false"/> Yes
                                        </label>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" id="pricing">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Pricing
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[0]) ? $sellingPriceNames[0]->name : 'Selling Price' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_1" value="0.00" data-display="Selling Price"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Cost Price</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="costprice" value="0.00" data-display="Cost Price"/>
                                        </div>
                                    </div>
                                </div>

                                @if((int)Auth::user()->company->use_tax_rules === 0)
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label><strong>Tax Rate</strong></label>
                                            <select class="form-control" name="tax_guid" data-display="Tax Rate">
                                                @foreach($taxrates as $t)
                                                    <option @if($t->defaultvalue == true) selected @endif value="{{$t->guid}}">{{$t->displayname}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @else
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label><strong>Tax Rule</strong></label>
                                            <select class="form-control" name="tax_rule_guid" data-display="Tax Rule">
                                                @foreach($taxRules as $rule)
                                                    <option value="{{$rule->guid}}">{{$rule->name}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" id="pricing-additional">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Additional Pricing (optional)
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[1]) ? $sellingPriceNames[1]->name : 'Selling Price 2' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_2" data-display="Selling Price 2"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[2]) ? $sellingPriceNames[2]->name : 'Selling Price 3' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_3" data-display="Selling Price 3"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[3]) ? $sellingPriceNames[3]->name : 'Selling Price 4' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_4" data-display="Selling Price 4"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[4]) ? $sellingPriceNames[4]->name : 'Selling Price 5' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_5" data-display="Selling Price 5"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="modifiers">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Modifier Groups
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Select Modifiers</strong></label>
                                        <select class="form-control" id="add-group" name="add-group">
                                            @foreach($modGroups as $g)
                                                <option value="{{$g['CommandUID']}}">{{$g['DisplayName']}}</option>
                                            @endforeach
                                        </select>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <br />
                                                <button class="btn btn-success" id="add-modifier">Add Modifier</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <style>
                                        ul.sortable-list {
                                            width: 100%;
                                            padding: 0px;
                                            margin: 0px;
                                            padding-right: 20%;
                                        }
                                        ul.sortable-list li {
                                            padding: 10px;
                                            background-color: #FCFBE3;
                                            border: 1px #5E83BA solid;
                                            list-style: none;
                                            text-indent: 0px;
                                            margin: 5px;
                                        }
                                        .sortable-delete {
                                            float: right;
                                            cursor: pointer;
                                            color: red;
                                            font-size: 14px;
                                        }
                                    </style>
                                    <div class="form-group">
                                        <label><strong>Order Selected Modifiers</strong></label>
                                        <input type="hidden" name="groups" data-display="Modifier Groups"/>
                                        <ul class="sortable sortable-list">
                                            {{--<li>Item 1 <span class="sortable-delete">x</span></li>--}}
                                            {{--<li>Item 2 <span class="sortable-delete">x</span></li>--}}
                                            {{--<li>Item 3 <span class="sortable-delete">x</span></li>--}}
                                            {{--<li>Item 4 <span class="sortable-delete">x</span></li>--}}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <!-- /.row (nested) -->
                        </div>
                        <!-- /.panel-body -->
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="printers">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Printer Options
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="form-group col-md-6">
                                    <h4>Do Not Print</h4>
                                    <label class="btn btn-default" style="margin: 10px;">
                                        <input type="checkbox" name="kitchen_printers[0]" value="0" data-display="Do Not Print"> Do not print to any printers
                                    </label>
                                </div>
                                <div class="form-group col-md-6">
                                    <h4>Receipt Print?</h4>
                                    <label class="btn btn-default" style="margin: 10px;">
                                        <input type="checkbox" name="kitchen_printers[1]" value="1" data-display="Reciept Printers"> Print to Receipt
                                    </label>
                                </div>
                                <div class="form-group col-md-6">
                                    <h4>Kitchen Print?</h4>
                                    <label class="btn btn-default" style="margin: 10px; min-width: 150px;">
                                        <input type="checkbox" name="kitchen_printers[2]" value="2" data-display="Kitchen Printer 1"> {{isset($printers[0]) ? $printers[0]-> name : 'Kitchen Printer 1' }}
                                    </label>
                                    <label class="btn btn-default" style="margin: 10px; min-width: 150px;">
                                        <input type="checkbox" name="kitchen_printers[4]" value="4" data-display="Kitchen Printer 2"> {{isset($printers[1]) ? $printers[1]-> name : 'Kitchen Printer 2' }}
                                    </label>
                                    <label class="btn btn-default" style="margin: 10px; min-width: 150px;">
                                        <input type="checkbox" name="kitchen_printers[8]" value="8" data-display="Kitchen Printer 3"> {{isset($printers[2]) ? $printers[2]-> name : 'Kitchen Printer 3' }}
                                    </label>
                                    <label class="btn btn-default" style="margin: 10px; min-width: 150px;">
                                        <input type="checkbox" name="kitchen_printers[16]" value="16" data-display="Kitchen Printer 4"> {{isset($printers[3]) ? $printers[3]-> name : 'Kitchen Printer 4' }}
                                    </label>
                                    <label class="btn btn-default" style="margin: 10px; min-width: 150px;">
                                        <input type="checkbox" name="kitchen_printers[32]" value="32" data-display="Kitchen Printer 5"> {{isset($printers[4]) ? $printers[4]-> name : 'Kitchen Printer 5' }}
                                    </label>
                                    <label class="btn btn-default" style="margin: 10px; min-width: 150px;">
                                        <input type="checkbox" name="kitchen_printers[64]" value="64" data-display="Kitchen Printer 6"> {{isset($printers[5]) ? $printers[5]-> name : 'Kitchen Printer 6' }}
                                    </label>
                                </div>
                                {{--<div class="col-lg-6">--}}
                                    {{--<div class="form-group">--}}
                                        {{--<h4>Use Courses?</h4>--}}
                                        {{--<label class="btn btn-default" style="margin: 10px; min-width: 150px;">--}}
                                            {{--<input type="checkbox" name="follow_course_separator" value="1" data-display="Follow Course Separator"> Follow Course Separator--}}
                                        {{--</label>--}}
                                        {{--<label class="btn btn-default" style="margin: 10px; min-width: 150px;">--}}
                                            {{--<input type="checkbox" name="follow_course_separator" value="0" data-display="Don't Follow Course Separator"> Don't Follow--}}
                                        {{--</label>--}}
                                    {{--</div>--}}
                                {{--</div>--}}
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>KP Category</strong></label>
                                        <select name="kp_category" id="kp_category" data-display="KP Category">
                                            <option value="">Please Select One...</option>
                                            @foreach($kp_categories as $kpc)
                                                <option value="{{$kpc->CommandUID}}">{{$kpc->DisplayName}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div style="clear: both; height: 100px;"></div>
        </div>

    </form>

    @include('modules.products.includes.confirm-modal')
@stop



@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <style>
        .shepherd-max-z {
            z-index: 999999999;
        }
    </style>

    <script>

        var modifierCount = 0;
        var groups = [];

        $('.sortable').sortable().bind('sortupdate', function() {
            groups = [];

            var elements = $('.sortable li');
            var position = 0;

            $.each(elements, function(index, value){
                groups.push({ guid: $(value).data('id'), position: position });
                position++;
            })

            $('[name="groups"]').val(JSON.stringify(groups))

        });

        $(function(){
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                },
                onChange: function(element, checked) {

                    let changedElement = $(element).parent();

                    checkAndUpdateField(changedElement);

                }
            })
        });

        function checkAndUpdateField(element){
            let field = $(element);
            let value = field.val();

            if(value != '' && value != null) {
                console.log('not null')
                field.attr('prev_value', value);
                field.attr('data-change', true);
            } else {
                field.attr('data-change', false);
            }
        }

        $('#add-modifier').on('click', function(e){
            e.preventDefault();

            var item = $('#add-group')
            var guid =  item.val()

            var exists = checkModifierExists(guid)

            if(exists === false) {

                var name = item.find(':selected').text()

                $('.sortable').append('<li data-order="' + modifierCount + '" data-id="' + guid + '">' + name + ' <span class="sortable-delete">x</span></li>')

                var newGroup = {guid: guid, position: modifierCount};

                groups.push(newGroup);
                $('[name="groups"]').val(JSON.stringify(groups))

                modifierCount++;
            }
        });


        function checkModifierExists(value)
        {
            var foundCount = 0;

            if(groups.length > 0) {
                $.each(groups, function (index, group) {
                    if (group.guid == value) {
                        foundCount++;
                    }
                });
            }

            if(foundCount > 0)
            {
                return true;
            } else {
                return false;
            }

        }

        $(document).on('click', '.sortable-delete', function(e){

            var guid = $(this).parent().data('id');
            $(this).parent().remove();

            $.each(groups, function (key, group) {

                if (group.guid == guid) {
                    groups.splice(key, 1)
                    $('[name="groups"]').val(JSON.stringify(groups))
                }
            });

            modifierCount--;

        });

        $('body').on('change, keyup, blur', '.form-control, select, textarea', function(i){

            var element = $(this)

            if (element.val() != '' && element.val() != null) {
                element.attr('data-change', true);
            } else {
                element.attr('data-change', false);
            }

        });

        $('body').on('click', '#printers [type="checkbox"], [type="checkbox"]', function(){

            let element = $(this);
            let name = element.attr('name');
            let checked = $('[name="'+name+'"]:checked');

            if(checked.length > 0){
                element.attr('data-change', true);
            } else {
                element.attr('data-change', false);
            }

        });

        $('body').on('click', '#soldbyweight', function(){
            let val = $('#soldbyweight:checked').val();
            console.log(val)
            if(val !== undefined){
                $('#soldbyweight').attr('data-change', true);
            } else {
                $('#soldbyweight').attr('data-change', false);
            }
        })

        // $(document).on('click,change,keyup,blur', '[name="groups"]', function(){
        //
        //     var element = $(this)
        //
        //     if(element.val() != '' && element.val() != null) {
        //         element.attr('data-change', true);
        //     } else {
        //         element.attr('data-change', false);
        //     }
        //
        // })
        //
        // $(document).on('click,change,keyup,blur', '[name="tax_guid"]', function(){
        //
        //     var element = $(this)
        //
        //     if(element.val() != '' && element.val() != null) {
        //         element.attr('data-change', true);
        //     } else {
        //         element.attr('data-change', false);
        //     }
        //
        // })


        $.fn.clearValidation = function(){var v = $(this).validate();$('[name]',this).each(function(){v.successList.push(this);v.showErrors();});v.resetForm();v.reset();};

        $(function(){
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                },
                dropup: true,
                onChange: function (option, checked, select) {
                }
            })
        })


        function cancelChange()
        {
            $('#ConfirmModal').modal('hide')
        }

        $(document).on('keyup, change', '[name="displayname"]', function () {
            $('[name="short_desc"]').val($(this).val());
        });

        $(document).on('keyup, change', '[name="displayname"]', function () {
            $(document).find('[name="short_desc"]').val($(this).val());
        });

        var url = '/products';

        $('#editForm').on('click', function(e){
            e.preventDefault();

            url = $(this).attr('data-url')

            editFormSubmit()

        })

        function editFormSubmit(){
            $('#EditProductForm').submit()
        }

        /**
         * avaialble status : error, success, warning
         * @param status
         * @param message
         */
        // function notificationBar(status, message, url) {
        //     $('#notificationBar').removeClass('error');
        //     $('#notificationBar').removeClass('ok');
        //     $('#notificationBar').removeClass('success');
        //     $('#notificationBar').addClass(status);
        //     $('#notificationBar .message').text(message);
        //     $('#notificationBar').delay(0).fadeIn();
        //     $('#notificationBar').delay(2500).fadeOut(function(){
        //         if(url) {
        //             window.location.href = url;
        //         }
        //     });
        // }

        var form = $('#EditProductForm')

        // validate signup form on keyup and submit
        $("#EditProductForm").validate({
            ignore: [],
            rules: {
            },
            messages: {
                name: "Please enter a contact name",
                company_name: "Please enter the name of the company",
                username: {
                    required: "Please enter a username",
                    minlength: "Your username must consist of at least 5 characters"
                },
                email: "Please enter a valid email address",
                department_guid: {
                    required: "Please choose which department this product belongs to"
                },
                sub_department_guid: {
                    required: "Please choose which sub department this product belongs to"
                },
                supplier_guid: {
                    required: "Please select this product's supplier"
                },
                selling_price_1: {
                    required: "Please enter a price for this product"
                },
                cost_price: {
                    required: "Please enter a cost price per serving/unit for this product"
                },
                sku_price: {
                    required: "Please provide a price for a full case of this product"
                }
            },
            invalidHandler: function(e, validator){
                if(validator.errorList.length)
                    $('#tabs a[href="#' + jQuery(validator.errorList[0].element).closest(".tab-pane").attr('id') + '"]').tab('show')
            },
            submitHandler: function (form) {

                //confirm changes
                var formData = null;
                var formData = getFormData();

                $('#ConfirmModal #fieldlist').html('');

                function getFormData(){

                    var changed = $('[data-change="true"]')
                    var array = [];

                    var groups = $('[name="groups"]');
                    if(groups.val() != '')
                    {
                        var data = {}

                        data.name = groups.attr('name');
                        data.display = groups.data('display');
                        data.value = groups.val();

                        if(data.name != undefined && data.display != undefined) {
                            array.push(data)
                        }
                    }

                    _.each(changed, function(change){

                        var data = {}
                        data.name = $(change).attr('name');
                        data.display = $(change).data('display');
                        data.value = $(change).val();

                        if(data.name != undefined && data.display != undefined) {
                            array.push(data)
                        }

                    });

                    return array;
                }

                _.each(formData, function(field)
                {
                    if(field.name != 'ids') {
                        $('#ConfirmModal #fieldlist').append('<li>' + field.display + '</li>')
                    }
                })

                if(formData.length > 0)
                {
                    $('#ConfirmModal').modal('show');
                } else {
                    notificationBar('warning', 'No field values have changed')
                }


                $('#confirm-mass').on('click', function(){
                    $.ajax({
                        type: "PUT",
                        url: "/products/edit-mass",
                        data: formData,
                        success: function () {
                            $('#ConfirmModal').modal('hide');
                            notificationBar('success', 'The selected products have been updated!', url)
                        },
                    }).fail(function(data) {
                        notificationBar(data.responseJSON.status, data.responseJSON.message)
                    });
                })

            }
        })

        $(document).on('click', '.goto', function(e){
            e.preventDefault();
            var tab = $(this).data('goto')

            $('#tabs a[href="#'  + tab + '"]').tab('show')
        })

    </script>
@stop
