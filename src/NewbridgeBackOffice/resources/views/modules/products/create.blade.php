@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/products" class="">Products</a></li>
        <li>Create Product</li>
    </ol>

    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Create Product</h4>
        <hr />

        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab" data-next="#stock" data-prev="" data-save="true">Product Details</a></li>
            <li id="step2"><a href="#stock" data-toggle="tab" data-next="#modifiers" data-prev="#details" data-save="true">Stock Control</a></li>
            <li id="step3"><a href="#modifiers" data-toggle="tab" data-next="#printers" data-prev="#stock" data-save="true">Modifiers</a></li>
            <li id="step4"><a href="#printers" data-toggle="tab" data-next="#sub-products" data-prev="#modifiers" data-save="true">Printing</a></li>
            <li id="step5" style="display: none;"><a href="#sub-products" data-toggle="tab" data-next="#info" data-prev="#printers" data-save="true">Child Products</a></li>
            <li id="step6"><a href="#info" data-toggle="tab" data-next="" data-prev="#sub-products" data-save="true">Information</a></li>
        </ul>
    </div>

    <form id="CreateProductForm">
        <div class="tab-content">
            {{ csrf_field() }}
            @include('includes.nav-buttons')

            <div class="tab-pane active" id="details">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Basic Product Information
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6" id="product-name">
                                    <div class="form-group">
                                        <label><strong>Name</strong></label>
                                        <input type="text" class="form-control" name="displayname"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Short Description</strong></label>
                                        <input type="text" class="form-control" maxlength="30" name="short_desc"/>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="departments">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Department</strong></label>
                                        <select class="form-control" name="department_guid">
                                            @foreach($departments as $dep)
                                                <option value="{{$dep->guid}}">{{$dep->displayname}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Sub Department</strong></label>
                                        <select class="form-control" name="sub_department_guid">
                                            @foreach($subdepartments as $dep)
                                                <option value="{{$dep->guid}}">{{$dep->displayname}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">

                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Barcode</strong></label>
                                        <input type="text" class="form-control" name="barcode" value=""/>
                                    </div>
                                </div>

                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Supplier Code</strong></label>
                                        <input type="text" class="form-control" name="stock_code" value=""/>
                                    </div>
                                </div>

                                @if((int)Auth::user()->company->active_integrations & 8 || (int)Auth::user()->company->active_integrations & 16 || (int)Auth::user()->company->active_integrations & 32)
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label><strong>Product App ID</strong></label>
                                            <input type="text" class="form-control" name="third_party_id" />
                                        </div>
                                    </div>
                                @endif

                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Product Type</strong></label>
                                        <select class="form-control" name="is_modifier">
                                            <option value="0">Product</option>
                                            <option value="1">Modifier</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            @if((int)Auth::user()->company->active_integrations & 8 || (int)Auth::user()->company->active_integrations & 16)
                            <div class="row">
                                <div class="col-xs-12">
                                    <div class="form-group">
                                        <label><strong>Product Description</strong></label>
                                        <textarea class="form-control" name="description"></textarea>
                                    </div>
                                </div>
                            </div>
                            @endif
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Image Path</strong></label>
                                        <input type="text" class="form-control" maxlength="191" name="image_url" value=""/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-3 ean13">
                                    <div class="form-group">
                                        <label><strong>EAN 13 Barcode</strong></label><br>
                                        <label class="btn btn-default">
                                            <input type="checkbox" name="ean13" value="1"/> Yes
                                        </label>
                                    </div>
                                </div>
                                <div class="col-lg-3 isWeighted">
                                    <div class="form-group">
                                        <label><strong>Weighted Product?</strong></label><br>
                                        <label class="btn btn-default">
                                            <input type="checkbox" name="weighed_product" value="1"/> Yes
                                        </label>
                                    </div>
                                </div>
                                <div class="col-lg-3 isWeighted">
                                    <div class="form-group">
                                        <label><strong>Manual Weight Entry?</strong></label><br>
                                        <label class="btn btn-default">
                                            <input type="checkbox" name="ismanualweight" value="1"/> Yes
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" id="pricing">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Pricing
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[0]) ? $sellingPriceNames[0]->name : 'Selling Price' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_1"
                                                   value="0.00"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Cost Price</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="costprice" value="0.00"/>
                                        </div>
                                    </div>
                                </div>
                                @if((int)Auth::user()->company->use_tax_rules === 0)
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Tax Rate</strong></label>
                                        <select class="form-control" name="tax_guid">
                                            @foreach($taxrates as $t)
                                                <option value="{{$t->guid}}" data-rate="{{$t->rate}}">{{$t->displayname}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                @else
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Tax Rule</strong></label>
                                        <select class="form-control" name="tax_rule_guid">
                                            @foreach($taxRules as $rule)
                                                <option value="{{$rule->guid}}">{{$rule->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                @endif
                            </div>
                            <div class="row" id="gparea">
                                <hr />
                                <div class="col-xs-12">
                                    <h4>Product GP</h4>
                                    <div class="progress" style="height: 40px; background-color: #888">
                                        <div class="progress-bar progress-bar-warning progress-bar-striped" role="progressbar" aria-valuenow="65"
                                             aria-valuemin="0" aria-valuemax="100" style="width: 65%; height: 40px;">
                                            <span style="line-height: 40px; font-weight: bold; font-size: 14px;">100% Gross Profit</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label><strong>Desired GP</strong></label>
                                        <input type="number" class="input input-md form-control" min="0" max="100" id="desiredgp"/>
                                    </div>
                                </div>
                                <div class="col-xs-6">
                                    <div class="form-group">
                                        <label><strong>Suggested Selling Price</strong></label>
                                        <input type="text" class="input input-md form-control" id="suggested-price" disabled="true" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6" id="pricing-additional">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Additional Pricing (optional)
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[1]) ? $sellingPriceNames[1]->name : 'Selling Price 2' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_2"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[2]) ? $sellingPriceNames[2]->name : 'Selling Price 3' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_3"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[3]) ? $sellingPriceNames[3]->name : 'Selling Price 4' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_4"/>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>{{isset($sellingPriceNames[4]) ? $sellingPriceNames[4]->name : 'Selling Price 5' }}</strong></label>
                                        <div class="input-group">
                                            <span class="input-group-addon">{!! \NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}</span>
                                            <input type="text" class="form-control" name="selling_price_5"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="tab-pane" id="stock">
                @if($site->allowed_modules & 1)
                <div class="col-md-12">
                    <div class="panel panel-primary">
                            <div class="panel-heading">
                                Stock Control Method
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <h4>Will this product be monitored for stock control?</h4>
                                        <div class="btn-group" data-toggle="buttons">
                                            <label class="btn btn-default active">
                                                <input type="radio" name="stock" id="option1" value="none" checked> Non
                                                Stock
                                            </label>
                                            <label class="btn btn-default">
                                                <input type="radio" name="stock" value="sku" id="option2"> Stock Unit
                                            </label>
                                            <label class="btn btn-default">
                                                <input type="radio" name="stock" value="recipe" id="option3"> Recipe
                                            </label>
                                            <label class="btn btn-default">
                                                <input type="radio" name="stock" value="parent" id="option4"> Parent Product
                                            </label>
                                        </div>

                                        <br/><br/>
                                        <p class="parent-data" style="display: none;">If this product has a parent product or if you sell a partial
                                            unit of a product, please select the product and quantity in the selection are
                                            below.</p>
                                        <p class="recipe-data" style="display: none;">If this product is a recipe, please select the recipe in the
                                            dropdown and quantity below.</p>
                                        <p class="sku-data" style="display: none;">If this product is a primary stock product, please choose a
                                            stock
                                            keeping unit below.</p>
                                    </div>
                                </div>
                            </div>

                    </div>
                </div>

                <div class="col-md-12">
                    <div class="panel panel-primary sku-data">
                        <div class="panel-heading">
                            Stock Keeping Unit
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <!-- TODO: hide this area if above is selected as true? -->
                                <div class="col-lg-6 ">
                                    <div class="form-group sku-data">
                                        <label><strong>Stock Unit</strong></label>
                                        <select name="sku_guid" class="form-control">
                                            <option value="">Choose One...</option>
                                            @foreach($skus as $v)
                                                <option value="{{$v->guid}}">{{$v->displayname}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Supplier</strong></label>
                                        <select class="form-control" name="supplier_guid">
                                            <option value="">Please select one</option>
                                            @foreach($suppliers as $v)
                                                <option value="{{$v->guid}}">{{$v->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row sku-data">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Min Stock</strong></label>
                                        <input type="text" class="form-control" maxlength="30" name="min_stock"
                                               value="0.00"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Max Stock</strong></label>
                                        <input type="text" class="form-control" maxlength="30" name="max_stock"
                                               value="0.00">
                                    </div>
                                </div>
                                @if ($companyDetails['integrations'] & 1)
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label><strong>ML Amount (1 Pint = 586 ML)</strong></label><br>
                                            <input type="text" class="form-control" name="ml_amount" value=""/>

                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label><strong>Reference No.</strong></label><br>
                                            <input type="text" class="form-control" name="third_party_reference" value=""/>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="panel panel-primary parent-data">
                        <div class="panel-heading">
                            Parent Product Assignment
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6 parent-data">
                                    <div class="form-group">
                                        <label><strong>Parent Stock Record</strong></label>
                                        <select class="form-control" name="plu_parent_guid">
                                            <option value="">None</option>
                                            @if($parents != null)
                                                @foreach($parents as $p)
                                                    <option value="{{$p->guid}}">{{$p->displayname}}</option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6 parent-data">
                                    <div class="form-group">
                                        <label><strong>Stock Consumed</strong></label>
                                        <input type="text" class="form-control" maxlength="30" name="sku_quantity"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="panel panel-primary recipe-data">
                        <div class="panel-heading">
                            Recipes
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Recipe</strong></label>
                                        <select class="form-control" name="recipe_guid">
                                            <option value="">None</option>
                                            @foreach($recipes as $v)
                                                <option value="{{$v->guid}}">{{$v->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Recipe Qty</strong></label>
                                        <input type="number" class="form-control" min="1" name="recipe_qty"/>
                                    </div>
                                </div>
                            </div>
                            <!-- /.row (nested) -->
                        </div>
                        <!-- /.panel-body -->
                    </div>
                </div>
                @else
                    <div class="panel-heading">
                        Stock Control
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="alert alert-warning">
                                Activate stock control in your package to assign stock to your products
                            </div>
                        </div>
                    </div>
                @endif
            </div>


            <div class="tab-pane" id="modifiers">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Modifier Groups
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Select Modifiers</strong></label>
                                        <select class="form-control" id="add-group" name="add-group">
                                            @foreach($modGroups as $g)
                                                <option value="{{$g['CommandUID']}}">{{$g['DisplayName']}}</option>
                                            @endforeach
                                        </select>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <br />
                                                <button class="btn btn-success" id="add-modifier">Add Modifier</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <style>
                                        ul.sortable-list {
                                            width: 100%;
                                            padding: 0px;
                                            margin: 0px;
                                            padding-right: 20%;
                                        }
                                        ul.sortable-list li {
                                            padding: 10px;
                                            background-color: #FCFBE3;
                                            border: 1px #5E83BA solid;
                                            list-style: none;
                                            text-indent: 0px;
                                            margin: 5px;
                                        }
                                        .sortable-delete {
                                            float: right;
                                            cursor: pointer;
                                            color: red;
                                            font-size: 14px;
                                        }
                                    </style>
                                    <div class="form-group">
                                        <label><strong>Order Selected Modifiers</strong></label>
                                        <input type="hidden" name="groups">
                                        <ul class="sortable sortable-list">
                                            {{--<li>Item 1 <span class="sortable-delete">x</span></li>--}}
                                            {{--<li>Item 2 <span class="sortable-delete">x</span></li>--}}
                                            {{--<li>Item 3 <span class="sortable-delete">x</span></li>--}}
                                            {{--<li>Item 4 <span class="sortable-delete">x</span></li>--}}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <!-- /.row (nested) -->
                        </div>
                        <!-- /.panel-body -->
                    </div>
                </div>

            </div>


            <div class="tab-pane" id="printers">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Printer Options
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="form-group col-md-6">
                                    <h4>Receipt Print?</h4>
                                    <label class="btn btn-default" style="margin: 10px;">
                                        <input type="checkbox" checked name="kitchen_printers[]" value="1"> Print to Receipt
                                    </label>
                                </div>
                                <div class="form-group col-md-6">
                                    <h4>Kitchen Print?</h4>
                                    <div class="form-group col-md-6">
                                        <label class="btn btn-default btn-block" style="margin: 10px; min-width: 150px;">
                                            <input type="checkbox" name="kitchen_printers[]"  class="pull-left" value="2"> {{isset($printers[0]) ? $printers[0]->name : 'Kitchen Printer 1' }}
                                        </label>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="btn btn-default btn-block" style="margin: 10px; min-width: 150px;">
                                            <input type="checkbox" name="kitchen_printers[]"  class="pull-left" value="4"> {{isset($printers[1]) ? $printers[1]->name : 'Kitchen Printer 2' }}
                                        </label>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="btn btn-default btn-block" style="margin: 10px; min-width: 150px;">
                                            <input type="checkbox" name="kitchen_printers[]"  class="pull-left" value="8"> {{isset($printers[2]) ? $printers[2]->name : 'Kitchen Printer 3' }}
                                        </label>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="btn btn-default btn-block" style="margin: 10px; min-width: 150px;">
                                            <input type="checkbox" name="kitchen_printers[]"  class="pull-left" value="16"> {{isset($printers[3]) ? $printers[3]->name : 'Kitchen Printer 4' }}
                                        </label>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="btn btn-default btn-block" style="margin: 10px; min-width: 150px;">
                                            <input type="checkbox" name="kitchen_printers[]"  class="pull-left" value="32"> {{isset($printers[4]) ? $printers[4]->name : 'Kitchen Printer 5' }}
                                        </label>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="btn btn-default btn-block" style="margin: 10px; min-width: 150px;">
                                            <input type="checkbox" name="kitchen_printers[]" class="pull-left" value="64"> {{isset($printers[5]) ? $printers[5]->name : 'Kitchen Printer 6' }}
                                        </label>
                                    </div>
                                </div>
                                {{--<div class="col-lg-6">--}}
                                    {{--<div class="form-group">--}}
                                        {{--<label><strong>Follow Course Separator</strong></label>--}}
                                        {{--<input type="checkbox" name="follow_course_separator" value="1" data-toggle="toggle">--}}
                                    {{--</div>--}}
                                {{--</div>--}}
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>KP Category</strong></label>
                                        <select name="kp_category" id="kp_category">
                                            <option value="">Please Select One...</option>
                                            @foreach($kp_categories as $kpc)
                                                <option value="{{$kpc->CommandUID}}">{{$kpc->DisplayName}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="tab-pane" id="info">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Additional Product Information
                        </div>
                        <div class="panel-body">
                            <div id="summernote">

                            </div>
                            <textarea name="productinfo" id="productinfo" cols="0" rows="0" style="display: none;"></textarea>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-md-4 pull-left">
                                <button class="btn btn-md btn-warning pull-left goto" data-goto="printers"><i class="fa fa-chevron-circle-left"></i> Back</button>
                            </div>
                            <div class="col-md-2 pull-right">
                                <button class="btn btn-md btn-success pull-right" id="editForm1" data-url="/products">Save <i class="fa fa-save"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="sub-products">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Child/Stock Products
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Automatic Product List?</strong><br><small>
                                                If active the till will automatically show the below products as options when selling this product.
                                            </small></label>
                                        <br>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <label class="btn btn-default">
                                        <input type="checkbox" name="show_child_list" value="1"/> Yes
                                    </label>
                                </div>
                            </div>
                            <div class="row">
                                <hr>
                            </div>
                            <div class="row">
                                <div id="child-product-rows">
                                    @for($i=0; $i < 1; $i++)
                                        @include('modules.products.includes.sub-product-row')
                                    @endfor
                                </div>
                                <div class="row">
                                    <br />
                                    <br />
                                    <div class="col-xs-12">
                                        <button class="btn btn-success pull-right" id="add-child-product"><i class="fa fa-plus"></i> Add Child</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="clearfix" style="min-height: 100px;"></div>
        </div>

    </form>

    <div class="modal fade" id="confirm-delete" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">

                </div>
                <div class="modal-body">
                    ...
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-danger btn-ok">Delete</a>
                </div>
            </div>
        </div>
    </div>

    <div style="height: 100px; clear: both;"></div>
@stop



@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>
    {{--<link href="//gitcdn.github.io/bootstrap-toggle/2.2.2/css/bootstrap-toggle.min.css" rel="stylesheet">--}}
    {{--<script src="//gitcdn.github.io/bootstrap-toggle/2.2.2/js/bootstrap-toggle.min.js"></script>--}}

    <!-- Include stylesheet -->
    <link href="//cdnjs.cloudflare.com/ajax/libs/summernote/0.8.12/summernote.css" rel="stylesheet">

    <script src="//cdnjs.cloudflare.com/ajax/libs/summernote/0.8.12/summernote.min.js"></script>

    <style>
        .shepherd-max-z {
            z-index: 999999999;
        }
    </style>

    <script>
        $('#summernote').summernote({
            minHeight: 300,
            fontSizes: ['8', '9', '10', '11', '12', '14', '18'],
            toolbar: [
                // [groupName, [list of button]]

                ['style', ['bold', 'italic', 'underline', 'clear']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['height', ['height']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'video']],
                ['view', ['fullscreen']]
            ]
        });
    </script>

    <script>

        var modifierCount = 0;
        var groups = [];

        $('.sortable').sortable();

        $('.sortable').sortable().bind('sortupdate', function() {
            groups = [];

            var elements = $('.sortable li');
            var position = 0;

            $.each(elements, function(index, value){
                groups.push({ guid: $(value).data('id'), position: position });
                position++;
            })

            $('[name="groups"]').val(JSON.stringify(groups))

        });

        $(function(){
            $('#CreateProductForm select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                }
            })
        });

        $('#add-modifier').on('click', function(e){
            e.preventDefault();

            var item = $('#add-group')
            var guid =  item.val()

            var exists = checkModifierExists(guid)

            if(exists === false) {

                var name = item.find(':selected').text()

                $('.sortable').append('<li data-order="' + modifierCount + '" data-id="' + guid + '">' + name + ' <span class="sortable-delete">x</span></li>')

                var newGroup = {guid: guid, position: modifierCount};

                groups.push(newGroup);
                $('[name="groups"]').val(JSON.stringify(groups))

                modifierCount++;
            }
        });


        function checkModifierExists(value)
        {
            var foundCount = 0;

            if(groups.length > 0) {
                $.each(groups, function (index, group) {
                    if (group.guid == value) {
                        foundCount++;
                    }
                });
            }

            if(foundCount > 0)
            {
                return true;
            } else {
                return false;
            }

        }

        $(document).on('click', '.sortable-delete', function(e){

            var guid = $(this).parent().data('id');
            $(this).parent().remove();

            $.each(groups, function (key, group) {

                if (group.guid == guid) {
                    groups.splice(key, 1)
                    $('[name="groups"]').val(JSON.stringify(groups))
                }
            });

            modifierCount--;

        });

        $.fn.clearValidation = function(){var v = $(this).validate();$('[name]',this).each(function(){v.successList.push(this);v.showErrors();});v.resetForm();v.reset();};


        $('[name="sku_guid"]').on('change', function(){

            let text = $(this).find("option:selected").text()

            if(text.length > 0){
                $('#step5').show();
                $('.yes-add-prods').show();
                $('.no-add-prods').hide();
            } else {
                $('#step5').hide()
                $('.yes-add-prods').hide();
                $('.no-add-prods').show();
            }
        });


        function swapStock() {

            var value = $('[name="stock"]:checked').val();

            if (value == 'none') {
                $('.sku-data').hide();
                $('.recipe-data').hide();
                $('.parent-data').hide();
                $('.isWeighted').show();
            }

            if (value == 'sku') {
                $('.sku-data').show();
                $('.parent-data').hide();
                $('.recipe-data').hide();
                $('.isWeighted').show();
            }

            if (value == 'recipe') {
                $('.sku-data').hide();
                $('.parent-data').hide();
                $('.recipe-data').show();
                $('[name="weighed_product"]:checked').click();
                $('.isWeighted').hide();

            }

            if (value == 'parent') {
                $('.sku-data').hide();
                $('.parent-data').show();
                $('.recipe-data').hide();
                $('[name="weighed_product"]:checked').click();
                $('.isWeighted').hide();
            }

        }

        $('body').on('change', '[name="stock"]', function () {
            swapStock();

            $("#CreateProductForm").clearValidation();
        })


        $(document).on('keyup, change', '[name="displayname"]', function () {
            $('[name="short_desc"]').val($(this).val());
        })

        $(document).on('keyup, change', '[name="displayname"]', function () {
            $(document).find('[name="short_desc"]').val($(this).val());
        })

        $(document).on('change', '[name="stock"]', function () {
            var r = confirm("If you change this option, your current selection will be lost.");
            if (r != true) {
                return false;
            } else {
                swapStock();
            }
        })

        swapStock();

        var url = '/products';

        $('#createForm1').on('click', function(e){
            e.preventDefault();

            url = $(this).attr('data-url')

            createFormSubmit()

        })

        $('#createForm2').on('click', function(e){
            e.preventDefault();

            url = $(this).attr('data-url')

            createFormSubmit()

        })

        function createFormSubmit(){
            $('#CreateProductForm').submit()
        }

        var form = $('#CreateProductForm')

        // validate signup form on keyup and submit
        $("#CreateProductForm").validate({
            ignore: [],
            rules: {
                displayname: "required",
                short_desc: "required",
                department_guid: "required",
                sub_department_guid: "required",
                sku_guid: {
                    required: function(){
                        return $('[name="stock"]:checked').val() == 'sku';
                    }
                },
                min_stock: {
                    required: true,
                    number: true
                },
                max_stock: {
                    required: true,
                    number: true
                },
                selling_price_1: {
                    required: true,
                    number: true,
                    min: 0.00
                },
                sku_quantity: {
                  required: function(){
                      return $('[name="stock"]:checked').val() == 'parent';
                  }
                },
                plu_parent_guid: {
                    required: function(){
                        return $('[name="stock"]:checked').val() == 'parent';
                    }
                },
                recipe_guid: {
                    required: function(){
                        return $('[name="stock"]:checked').val() == 'recipe';
                    }
                },
                recipe_qty: {
                    required: function(){
                        return $('[name="stock"]:checked').val() == 'recipe';
                    }
                },
                tax_guid: {
                  required: true
                },
                costprice: {
                    required: true,
                    number: true,
                    min: 0
                }
            },
            messages: {
                name: "Please enter a contact name",
                company_name: "Please enter the name of the company",
                username: {
                    required: "Please enter a username",
                    minlength: "Your username must consist of at least 5 characters"
                },
                email: "Please enter a valid email address",
                department_guid: {
                    required: "Please choose which department this product belongs to"
                },
                sub_department_guid: {
                    required: "Please choose which sub department this product belongs to"
                },
                supplier_guid: {
                    required: "Please select this product's supplier"
                },
                selling_price_1: {
                    required: "Please enter a price for this product"
                },
                cost_price: {
                    required: "Please enter a cost price per serving/unit for this product"
                },
                sku_price: {
                    required: "Please provide a price for a full case of this product"
                }
            },
            invalidHandler: function(e, validator){
                if(validator.errorList.length)
                    $('#tabs a[href="#' + jQuery(validator.errorList[0].element).closest(".tab-pane").attr('id') + '"]').tab('show')
            },
            submitHandler: function (form) {
                $('[name=".note-editable"]').val($('.ql-editor').html());
                $(form).serialize();

                if(!validateChildRows()){

                    // set some errors

                    return false;
                };

                $.ajax({
                    type: "POST",
                    url: "/products/create",
                    data: $(form).serialize(),
                    success: function () {
                        notificationBar('success', 'Your product has been created', url)
                    }
                }).fail(function(data) {
                    notificationBar(data.responseJSON.status, data.responseJSON.message)
                });
            }
        });

        $('body').on('click', '#add-child-product', function(e){
            e.preventDefault();

            $.ajax({
                type: "GET",
                url: "/products/add-child-row",
                success: function (data) {
                    $('#child-product-rows').append(data.html);
                }
            }).fail(function(data) {
                notificationBar(data.responseJSON.status, data.responseJSON.message)
            });
        });

        $(document).on('click', '.goto', function(e){
            e.preventDefault();
            var tab = $(this).data('goto')

            $('#tabs a[href="#'  + tab + '"]').tab('show')
        });

        function validateChildRows(){
            return true;
        }

    </script>

    <script>

        $(function(){

            function setProgress(gp){
                $('.progress-bar').css('width', gp+'%').attr('aria-valuenow', gp);
                $('.progress-bar span').text(gp+'% Gross Profit')

                if(gp < 50 ){
                    $('.progress-bar span').removeClass('progress-warning')
                    $('.progress-bar span').removeClass('progress-success')
                    $('.progress-bar span').addClass('progress-danger')
                }
                if(gp > 50 && gp < 65){
                    $('.progress-bar span').addClass('progress-warning')
                    $('.progress-bar span').removeClass('progress-success')
                    $('.progress-bar span').removeClass('progress-danger')
                }
                if(gp > 65)
                {
                    $('.progress-bar span').removeClass('progress-warning')
                    $('.progress-bar span').addClass('progress-success')
                    $('.progress-bar span').removeClass('progress-danger')
                }
            }

            function setupGP(sellingPrice, costPrice){

                if(costPrice > 0 && sellingPrice > 0)
                {
                    // calculate the GP
                    let rate = parseFloat($('[name="tax_guid"] option:selected').data('rate'));
                    rate = (rate/100)+1;

                    let vat = sellingPrice/rate;
                    vat = sellingPrice-vat;

                    let gp = (((sellingPrice-vat)-costPrice) / (sellingPrice-vat) * 100);

                    $('#gparea').show();
                    $('#suggested-price').val(sellingPrice);
                    $('#desiredgp').val(parseFloat(gp).toFixed(2));

                    setProgress(parseFloat(gp).toFixed(2))

                } else {
                    $('#gparea').hide();
                }
            }

            setupGP();

            function notionalPrice(desiredGP, costPrice){

                setProgress(desiredGP);

                let rate = parseFloat($('[name="tax_guid"] option:selected').data('rate'))
                let suggestedPrice = ((100 * costPrice) / (100-desiredGP));

                suggestedPrice = (suggestedPrice + (suggestedPrice*(rate/100)));

                $('#suggested-price').val(parseFloat(suggestedPrice).toFixed(2));

            }

            $(document).on('change', '[name="selling_price_1"], [name="costprice"], [name="tax_guid"]', function(){

                let sellingPrice = parseFloat($('[name="selling_price_1"]').val());
                let costPrice = parseFloat($('[name="costprice"]').val());

                setupGP(sellingPrice, costPrice);
            });

            $(document).on('change', '#desiredgp', function(){

                let desiredGP = $(this).val();
                let costPrice = $('[name="costprice"]').val();

                if(costPrice > 0) {
                    notionalPrice(desiredGP, costPrice);
                } else {
                    return false;
                }
            });
        });

        $('body').on('click', '.delete-row', function(e){
            e.preventDefault();

            $(this).parent().parent().remove();
        })


    </script>
@stop
