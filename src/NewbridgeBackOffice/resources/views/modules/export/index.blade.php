@extends('layouts.master')

@section('section-name') Exports @stop

@section('content')

    <section>
        <div class="row" style="margin-bottom:40px;">
            <div class="col-lg-12">
                <h1 class="page-header">My exports  </h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <i class="fa fa-file-export"></i>
                    <span>Exports</span>
                    <a href="/exports/create" class="pull-right" style="color:white;">Create Export</a>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-condensed">
                            <thead>
                                <tr>
                                    <th>File</th>
                                    <th>Options</th>
                                    <th>Status</th>
                                    <th>Link</th>
                                </tr>
                            </thead>
                            <tbody>
                            @if(!$exports->isEmpty())
                            @foreach($exports as $export)

                            <tr>
                                <td>
                                    @if($export->status == 2)
                                    {{substr($export->file_location, 9)}}
                                    @elseif($export->status == 1)
                                        Export still in progress
                                    @else
                                        Export unsuccessful. File was not created.
                                    @endif
                                </td>
                                <td>
                                    @if(isset($export->options))
                                        <button class="export-options">Details</button>
                                    <div class="export-options-view" style="display:none;">
                                    @foreach(json_decode($export->options, true) as $key => $option)
                                        @if(is_array($option))
                                                <strong>{{ $key }} </strong>:
                                            @foreach($option as $subKey => $nestedOption) {{$nestedOption ?? "all" }}, @endforeach
                                                <br />
                                            @else
                                            <strong>{{ $key }} </strong>: {{$option ?? "all" }} <br />
                                            @endif
                                        @endforeach
                                    </div>
                                    @endif
                                </td>
                                <td title="{{ $export->updated_at }}">
                                    @if($export->status == 2) Export successful
                                    @elseif($export->status == 1) Export in progress
                                    @else Failed with errors
                                    @endif</td>
                                <td>
                                    @if($export->status == 2)
                                        <!-- foreach(files as file) -->
                                    <a target="_blank" href="/exports/download?path={{($export->file_location)}}" class="btn btn-sm btn-primary">Download Export</a>
                                    @elseif($export->status == 1)
                                        <a class="btn btn-sm btn-warning pending-export" data-id="{{($export->id)}}">Check Again</a>
                                    @else
                                        <a class="btn btn-sm btn-default delete-export" data-id="{{($export->id)}}">Delete Export</a>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                            @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
    </section>


@stop

@push("scripts")



    <script>

        window.addEventListener('DOMContentLoaded', () => {
            let exportToggleList = document.querySelectorAll(".export-options");
            let exportDeleteableList = document.querySelectorAll(".delete-export")
            let pendingList = document.querySelectorAll(".pending-export")

            exportToggleList.forEach(trigger => trigger.addEventListener("click", toggleOptions));
            exportDeleteableList.forEach(trigger => trigger.addEventListener("click", deleteExport));
            pendingList.forEach(trigger => trigger.addEventListener("click", () => location.reload()));
        });

        function toggleOptions() {
            const parent = event.currentTarget.parentNode;
            let options = parent.querySelector(".export-options-view");
            if(options.style.display === "none")
                options.setAttribute("style",
                    "display:block; position:absolute; background:white; padding:20px; border-radius:20px; " +
                    "border:solid 1px #222;");
            else
                options.setAttribute("style", "display:none;");
        }

        function deleteExport() {
            const target = event.currentTarget;
            Swal.fire({
                title: "Are you sure?",
                text: "Are you sure you want to delete this export?",
                type: "warning",
                confirmButtonText: "Yes, delete",
                cancelButtonText: 'No, cancel!',
                reverseButtons: true,
                cancelButtonColor: 'red',
                confirmButtonColor: 'green',
                showCancelButton: true,
                showConfirmButton: true,
            })
            .then((result) => {
                if(result.value === true){
                    fetch(`/exports/${target.dataset.id}`, {
                        method: "DELETE"
                    }).then((res) => {
                        console.log(res);
                        location.reload();
                    }).catch((err) => {
                        console.log(err);
                    })
                }
            });

        }

    </script>


@endpush

