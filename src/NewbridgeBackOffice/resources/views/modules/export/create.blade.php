@extends('layouts.master')

@section('section-name') Report Results @stop

@section('content')
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>

    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li class=""><a href="/exports"><i class="fa fa-file-export"></i> Exports</a></li>
        <li>Create Export</li>
    </ol>

    <style>
        .panel.wizard, .panel.wizard .panel-heading {

            border-radius: 0px;
        }

        .panel.wizard .panel-footer {
            min-height: 60px;
            clear: both;
        }

        .panel.wizard .panel-heading .active {
            font-weight: bold;
        }

        .panel.wizard label.col-md-4 {
            line-height: 30px;
        }

        .wizard label {
            font-weight: 700 !important;
        }

        .clear {
            clear: both;
        }
    </style>

    <div class="row" data-step="1">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-heading">
                    Export Options
                </div>
                <div class="panel-body">
                    <form id="export-form" name="export-form">
                        {{ csrf_field() }}
                        <input type="hidden" id="company_id" name="company_id" value="{{Auth::user()->company_id}}">
{{--                        <input type="hidden" id="export_id" name="export_id" value="1">--}}

                        <div class="row">

                            <div class="form-group col-md-6">
                                <label>Site</label>
                                <select name="site[]" id="site" multiple>
                                    <option value="0">All</option>
                                    @foreach(NewbridgeWeb\Http\Helpers\SiteHelper::mySites()['sites'] as $site)
                                        <option value="{{ $site['site_num'] }}">{{ $site['site_name'] }}</option>
                                    @endforeach
                                </select>

                            </div>

                            <div class="form-group col-md-6">
                                <label>Start / End Date & Time</label>
                                <input type="hidden" id="start" value="{{$filters['start']}}" name="start">
                                <input type="hidden" id="end" value="{{$filters['end']}}" name="end">
                                <div id="reportrange"
                                     style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
                                    <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
                                    <span>{{\Carbon\Carbon::parse($filters['start'])->format('M d, Y H:i:s')}} - {{\Carbon\Carbon::parse($filters['end'])->format('M d, Y H:i:s')}}</span>
                                    <b class="caret"></b>
                                </div>
                            </div>

                        </div>

                        <div class="row">

                            <div class="form-group col-md-6">
                                <label><strong>Exports to include</strong><small>(Can select multiple)</small></label>
                                <select name="export_type[]" id="export_type" multiple>
                                    <option value="1">Transaction Details</option>
                                    <option value="2">Transaction Payment Details</option>
                                </select>
                            </div>

                            <div class="form-group col-md-6">
                                <label><strong>Recipients</strong><small>(Comma separated for multiple)</small></label>
                                <textarea rows="1" name="recipients"
                                          placeholder="<EMAIL>, <EMAIL>"
                                          class="form-control"></textarea>
                            </div>

                        </div>


                        <div class="row">

                            <div class="col-md-12">
                                <div id="errors" class="alert"></div>
                                <div class="form-group col-md-4">
                                    <button class="btn btn-md btn-success" id="submit-form"><i
                                                class="fa fa-cogs"></i> Start Export
                                    </button>
                                </div>


                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="pre-loading-area">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-heading">
                    Export Status
                </div>
                <div class="panel-body" id="report-results-area">
                    {{--@include($report['view'])--}}

                    <h3>Use the run button to export your data, you will be emailed when the export is complete.</h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="loading-area" style="display: none;">
        <div class="col-lg-12">
            <div class="panel panel-primary wizard">
                <div class="panel-body text-center">
                    <div class="col-md-8 col-md-offset-2">
                        <h2>Export Started</h2>
                        <h4 id="loading-text">Your export is in progress. You may leave this page. It will be emailed to
                            you when ready. Alternatively you can view its status on your Exports page.</h4>
                        <a href="/exports" class="btn btn-primary">Go to Exports</a>
                    </div>
                </div>
            </div>
        </div>
    </div>


@stop

@push('scripts')

    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">

    <script type="text/javascript" src="//cdn.jsdelivr.net/bootstrap.daterangepicker/2/daterangepicker.js"></script>
    <script>
        $(function () {
            $('select').not('.swal2-select').multiselect({
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function (event) {
                    $(event.target).find('.filter .multiselect-search').focus();
                },
                enableCollapsibleOptGroups: true,
                buttonWidth: '100%'
            });

            const submitButton = document.querySelector('#submit-form');
            submitButton.addEventListener('click', validateAndSubmitForm);
        })

        $('#reportrange').daterangepicker({
            startDate: '{{\Carbon\Carbon::parse($filters['start'])->format('d/m/Y H:i:s')}}',
            endDate: '{{\Carbon\Carbon::parse($filters['end'])->format('d/m/Y H:i:s')}}',
            autoApply: true,
            timePicker: true,
            timePicker24Hour: true,
            locale: {
                format: 'DD/MM/YYYY HH:mm:ss'
            },
            ranges: {
                'Today': [moment(carbonDate).startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).subtract(1, 'days').endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('day').add('{{$filters['offset']}}', 'Hours')],
                'This Month': [moment(carbonDate).startOf('month').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).endOf('month').add('{{$filters['offset']}}', 'Hours')],
                'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month').add('{{$filters['offset']}}', 'Hours'), moment(carbonDate).subtract(1, 'month').endOf('month').add('{{$filters['offset']}}', 'Hours')]
            }
        });
        $('#reportrange').on('apply.daterangepicker', function (ev, picker) {
            $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
            $('#start').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
            $('#end').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
        });
        $('#reportrange').on('hide.daterangepicker', function (ev, picker) {
            $('#reportrange span').html(picker.startDate.format('MMMM D, YYYY HH:mm:ss') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm:ss'));
            $('#start').val(picker.startDate.format('YYYY-MM-DD HH:mm:ss'))
            $('#end').val(picker.endDate.format('YYYY-MM-DD HH:mm:ss'))
        });

        function validateAndSubmitForm(e) {

            e.preventDefault();
            let errors = [];
            const errorBox = document.querySelector("#errors");
            errorBox.innerHTML = "";
            errorBox.classList.remove("alert-warning");

            let recipients = document.querySelector("[name='recipients']");
            const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (recipients.value.length < 4 || !recipients.value.includes("@")) {

                errors.push("A valid email address is required");

            } else {

                recipients.value.split(",").forEach((address, n) => {
                    if (!pattern.test(address.trim())) {
                        errors.push(`The email address at position ${n +1} isn't valid`);
                    }
                })

            }

            const exportTypes = document.querySelector("[name='export_type[]']");

            console.log("Export Types = " + exportTypes.value)

            if(exportTypes.value == null || exportTypes.value === "")
                errors.push("Please select one or more exports to run");

            if(errors.length > 0){
                errorBox.classList.add("alert-warning");
                errors.forEach(error => {
                    errorBox.appendChild(document.createTextNode(error + "   "));
                    errorBox.appendChild(document.createElement("br"));
                });
                return;
            }

            let data = $("#export-form").serialize();
            const exportForm = document.querySelector("#export-form");
            const preLoadingArea = document.querySelector("#pre-loading-area");
            const loadingArea = document.querySelector("#loading-area");
            console.log(data);

            $.ajax({
                type: "POST",
                url: "/exports/create-request",
                data: data,
                success: function (data) {
                    console.log(data)
                    preLoadingArea.setAttribute("style", "display:none");
                    exportForm.reset();
                    loadingArea.setAttribute("style", "display:block");
                },
                fail: function (data) {
                    console.log(data)
                }
            });

        }


    </script>

@endpush
