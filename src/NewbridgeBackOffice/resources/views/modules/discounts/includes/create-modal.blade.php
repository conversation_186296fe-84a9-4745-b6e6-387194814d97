<div id="CreateModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                {{--<h4 class="modal-title">Edit Discount ({{$product->name}}</h4>--}}
                <h4>Create New Discount</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="CreateForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Discount Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Discount Name</strong>
                                                <br /><small>Give the discount a name for the interface.</small></label>
                                                <input type="text" class="form-control" name="display_name"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Receipt Text</strong>
                                                    <br /><small>What should be displayed on the reciept?</small></label>
                                                <input type="text" class="form-control" name="receipt_text"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- /.panel -->
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Discount Details
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Discount Type</strong><br /><small>What type of discount should be applied to the discount?</small></label>
                                                <select class="form-control" name="discount_method">
                                                    <option value="0">Percentage Off</option>
                                                    <option value="1">Fixed Amount Off</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Discount Value</strong>
                                                    <br /><small>How much discount should be given?</small></label>
                                                <input name="value" class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Discount Departments</strong>
                                                    <br /><small>Optional, which departments should the discount apply to?</small></label>
                                                    <select class="form-control" name="departments[]" multiple>
                                                        @foreach($departments as $department)
                                                            <option value="{{$department->guid}}">{{$department->displayname}}</option>
                                                        @endforeach
                                                    </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-xs-6">
                                            <div class="form-group">
                                                <div class="btn-group" data-toggle="buttons">
                                                    <label class="btn btn-primary">
                                                        <input type="checkbox" value="1" name="KeyPad"> Show Keypad?
                                                    </label>
                                                    <br />
                                                    <br />
                                                    <small>Use this to enter a custom discount amount when using the discount.</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="createForm">Create</button>
            </div>
        </div>
    </div>
</div>
