@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Products</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <script src="/js/sortable.min.js"></script>
    <link rel="stylesheet" href="/css/sortable/sortable-theme-light.css">

    <div class="row">
        <div class="col-md-12">
            <table id="example" class="table table-striped table-bordered" cellspacing="0">
                <thead>
                    <tr>
                        <th></th>
                        <th>Description</th>
                        <th>Department</th>
                        <th>Supplier</th>
                        <th>Price</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
    {{--<div class="section">
        <div class="row">
            <table id="example">
                <thead>
                <tr>
                    <th>ID</th>
                    <th>Number</th>
                    <th>Description</th>
                </tr>
                </thead>
            </table>
        </div>
    </div> --}}

@stop

@section('js')
    @parent
    <script>
        var departments = {!! $departments !!};
        var suppliers = {!! $departments !!};


    </script>
    <script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="/js/dataTables.editor.min.js"></script>

@stop