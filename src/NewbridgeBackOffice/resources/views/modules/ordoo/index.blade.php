@extends('layouts.master')

@section('section-name') Order Management @stop

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Product App Availability
                    <br />
                    <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small>
                </h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="col-lg-12">
        @include('modules.site-select.index', ['sites' => \NewbridgeWeb\Http\Helpers\SiteHelper::mySites(false, false)])
    </div>


    <div class="row">
        <form id="form">
            {{ csrf_field() }}
            <div class="col-lg-12">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h4>Update App Available Products <br/>
                            <small style="color: white">Change selections below to enable or disable products in your
                                third party apps.
                            </small>
                        </h4>
                    </div>
                    <div class="panel-body">
                        <select class="form-control" id="products" name="products[]" multiple="multiple">
                            @foreach($subDepartments as $sd)

                                @if(!$sd->products->isEmpty())
                                    <optgroup label="{{$sd->displayname}}">
                                        @foreach($sd->products as $p)
                                            <option @if($p->appAvailability != null) selected @endif value="{{$p->id}}">{{$p->displayname}}</option>
                                        @endforeach
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    </div>
                    <div class="panel-footer">
                        <button class="btn btn-success btn-block" id="updatebutton">Update Availability</button>
                    </div>
                </div>

            </div>
        </form>
    </div>

@stop


@push('scripts')

    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script>
        $(document).ready(function () {
            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: false,
                enableCaseInsensitiveFiltering: true,
                enableClickableOptGroups: true,
                enableCollapsibleOptGroups: true,
                collapseOptGroupsByDefault: true,
                maxHeight: 350,
                onDropdownShown: function (event) {
                    $(event.target).parent().find('.filter input').focus();
                }
            });
        });

        $('body').on('click', '#updatebutton', function(e){
            e.preventDefault();

            $.ajax({
                type: "POST",
                url: "/products/update-availability",
                data: $('#form').serialize(),
                success: function (data) {
                    console.log(data);
                    notificationBar('success', 'Product app availability updated!')
                },
                fail: function () {
                    notificationBar('error', 'Error updating app availability')
                }
            });
        })


    </script>

@endpush
