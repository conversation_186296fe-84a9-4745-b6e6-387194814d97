@extends('layouts.master')

@section('section-name') Loyalty Schemes @stop


@section('content')
    <script>
        var table = null;
    </script>
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">View Alarms ({{$alarm->displayname}})</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    View {{$alarm->displayname}} Results <br />
                    <small>View results for this alarm</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive table-condensed" id="table" width="100%">
                        <thead>
                            <tr>
                                <th>Date / Time</th>
                                <th>Alarm Text</th>
                                <th>View Transaction</th>
                            </tr>
                        </thead>
                        <tbody>
                        @foreach($alarm->results as $result)
                            <tr>
                                <td>{{Carbon\Carbon::parse($result->created_at, 'UTC')->setTimezone(\NewbridgeWeb\Http\Helpers\TimezoneHelper::getTimezone())->format('d/m/Y H:i:s')}}</td>
                                <td>{{$result->alarm_text}}</td>
                                <td>
                                    @if ((int)$alarm->alarm_period === 1)
                                        <a id="viewTrans{{$result->transaction_id}}" class="btn btn-primary btn-sm" href="#viewTrans{{$result->transaction_id}}" onclick="viewTransactionFromAnywhere({{$result->transaction_id}})">View Transaction</a>
                                    @endif
                                    @if ($alarm->alarm_period > 1)
                                        <a class="btn btn-primary btn-sm" href="/transactions?transactions={{base64_encode($result->transactions)}}">View Transactions</a>
                                    @endif
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

@stop