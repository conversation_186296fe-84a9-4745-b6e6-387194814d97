@extends('layouts.master')

@section('content')
    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Create Company Alarm</h4>
        <hr/>

        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab">Alarm Details</a></li>
        </ul>
    </div>
    <form id="CreateForm">
        {{ csrf_field() }}
        <div class="tab-content">
            <div class="tab-pane active" id="details">

                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Alarm Details
                        </div>
                        <div class="panel-body">

                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Alarm Name</strong><br/>
                                            <small>e.g. Void Limit</small>
                                        </label>
                                        <input type="text" class="form-control" name="displayname"
                                               placeholder="Void Limit"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Alarm Type</strong><br/>
                                            <small>Select the type of transaction information to monitor.</small>
                                        </label>
                                        <select name="type_id" class="input form-control input-md">
                                            @foreach($alarms as $alarm)
                                            <option value="{{$alarm->id}}">{{$alarm->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Trigger Type</strong><br/>
                                            <small>Select whether the trigger is a Cash or Quantity</small>
                                        </label>
                                        <select name="value_type" class="input form-control input-md">
                                            <option value="0">Amount</option>
                                            <option value="1">Quantity</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label class="trigger-cash"><strong>Trigger Value</strong><br/>
                                            <small>What is the trigger value in {!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!} for this alarm?</small>
                                        </label>
                                        <label class="trigger-quantity" style="display: none;"><strong>Trigger Quantity</strong><br/>
                                            <small>What is the trigger quantity for this alarm?</small>
                                        </label>
                                        <input type="number" name="value" class="form-control input"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Alarm Period</strong><br/>
                                            <small>Select the period of time the alarm should monitor.</small>
                                        </label>
                                        <select name="alarm_period" class="input form-control input-md">
                                            @foreach($alarm_periods as $alarm_period)
                                                <option value="{{$alarm_period->id}}">{{$alarm_period->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Alarm Message</strong><br/>
                                            <small>Customise the message displayed for this alarm.</small>
                                        </label>
                                        <textarea name="message_string" class="form-control"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class="row" style="padding-bottom: 50px;">
        <div class="col-md-12">
            <div class="col-md-6 pull-right">
                <button class="btn btn-md btn-success pull-right" id="createSubmit">Create
                    <i class="fa fa-save"></i></button>
            </div>
        </div>
    </div>
@stop


@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.13/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <script>
    let alarms = {!! json_encode($alarms) !!};

    $('[name="type_id"]').on('change', function(){

        let type_id = $(this).val()

        let alarm = _.find(alarms, function(alarm){
           return parseInt(alarm.id) === parseInt(type_id);
        });

        $('[name="message"]').val(alarm.default_message)

    })

    $('[name="value_type"]').on('change', function(){

        let type = $(this).val();

        if(type == 0)
        {
            $('.trigger-cash').show();
            $('.trigger-quantity').hide();
        } else {
            $('.trigger-cash').hide();
            $('.trigger-quantity').show();
        }
    })

    // validate signup form on keyup and submit
    $('#createSubmit').on('click', function(){
        $('#CreateForm').submit()
    })


    $("#CreateForm").validate({
        rules: {
            displayname: "required",
            type_id: "required",
            alarm_period: "required",
            value_type: "required",
            value: {
                min: 0,
                number: true,
                required: true
            },
            message_string: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/alarms/create",
                data: $(form).serialize(),
                success: function () {
                    notificationBar('success', 'Your alarm has been created, click okay to return to the Alarm menu', '/alarms', 'Alarm Created!' );

                },
                fail: function () {
                    notificationBar('error', 'There was an error creating your alarm, please try again.', '', 'Error!')
                }
            });
        }
    })

</script>

@stop