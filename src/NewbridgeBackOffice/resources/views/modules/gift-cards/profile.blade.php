@extends('layouts.master')

@section('content')
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/customers/gift-cards" class="">Gift Cards</a></li>
        <li>Gift Card Profile</li>
    </ol>

    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Customer Profile ({{$customer->first_name}} {{$customer->last_name}})</h4>

        <hr />

        <ul id="tabs" class="nav nav-tabs">
            <li class="active" id="step1"><a href="#details" data-toggle="tab">Customer Details</a></li>
            <li id="step3"><a href="#transactions" data-toggle="tab">Gift Card Transactions</a></li>
        </ul>
    </div>

    <form id="EditForm">
        {{ csrf_field() }}
        <div class="tab-content">
            @include('modules.gift-cards.includes.profile.details')
            @include('modules.gift-cards.includes.profile.transactions')
        </div>
    </form>
@stop



@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>


    <script>
        $('.editSubmit').on('click', function(e){
            e.preventDefault()
            $('#EditForm').submit()
        })

        var form = $('#EditForm')

        // validate signup form on keyup and submit
        $("#EditForm").validate({
            rules: {
                email: {
                    email: true
                },
                membership_no: {
                    required: true
                }
            },
            submitHandler: function (form) {
                $.ajax({
                    type: "PUT",
                    url: "/customers/gift-cards/edit-single",
                    data: $(form).serialize(),
                    success: function () {
                        notificationBar('success', 'Gift Card Updated!', '/customers/gift-cards')
                    },
                    fail: function () {
                        notificationBar('error', 'There was an error in your entry, please try again.')
                    }
                });
            }
        })

    </script>

    <script>
        $('.datepicker').datepicker({
            dateFormat: 'yy-mm-dd'
        });
    </script>

@stop