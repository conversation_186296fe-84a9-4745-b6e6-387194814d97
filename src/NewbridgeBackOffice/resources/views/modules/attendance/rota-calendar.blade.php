@extends('layouts.master')

@section('section-name') Clerk <PERSON><PERSON> @stop


@section('content')

    <link href='/fullcalendar/core/main.css' rel='stylesheet'/>
    <link href='/fullcalendar/daygrid/main.css' rel='stylesheet'/>
    <link href='/fullcalendar/timeline/main.css' rel='stylesheet'/>
    <link href='/fullcalendar/timegrid/main.css' rel='stylesheet'/>
    <link href='/fullcalendar/list/main.css' rel='stylesheet'/>
    <link href='/fullcalendar/resource-timeline/main.css' rel='stylesheet'/>

    <script src='/fullcalendar/core/main.js'></script>
    <script src='/fullcalendar/daygrid/main.js'></script>
    <script src='/fullcalendar/list/main.js'></script>
    <script src='/fullcalendar/timeline/main.js'></script>
    <script src='/fullcalendar/resource-common/main.js'></script>
    <script src='/fullcalendar/resource-timeline/main.js'></script>
    <script src='/fullcalendar/resource-timegrid/main.js'></script>
    <script src='/fullcalendar/timegrid/main.js'></script>
    <script src='/fullcalendar/interaction/main.js'></script>



    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Clerk Rota<br />
                <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small></h1>
        </div>
    </div>
    @include('modules.site-select.index', ['sites' => \NewbridgeWeb\Http\Helpers\SiteHelper::mySites(false, false)])
    <section>
        <div class="row">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <strong>Rota Calendar</strong><br/>
                    <small>View Rota information</small>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div id='calendar' style=""></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="editForm" class="modal fade">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">×</span> <span
                                    class="sr-only">close</span></button>
                        <h4 id="modalTitle" class="modal-title"></h4>
                    </div>
                    <div id="modalBody" class="modal-body">

                    </div>
                    <div class="modal-footer">
                        <div class="col-xs-4 text-center">
                            <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                        </div>
                        <div class="col-xs-4 text-center">
                            <button type="button" class="btn btn-danger" id="delete-shift-button">Delete</button>
                        </div>
                        <div class="col-xs-4 text-center">
                            <button type="button" class="btn btn-success" id="save-shift-button">Save</button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>



        <style>
            .eventStriped {
                background-image: linear-gradient(45deg, #A9A9A9 25%, #808080 25%, #808080 50%, #A9A9A9 50%, #A9A9A9 75%, #808080 75%, #808080 100%);
                {{--background-image: linear-gradient(45deg, {{$bgColor}} 25%, {{$bgColor}} 25%, {{$bgColor}} 50%, {{$bgColor}} 50%, {{$bgColor}} 75%, {{$bgColor}} 75%, {{$bgColor}} 100%);--}}
                background-size: 56.57px 56.57px;
                color: black;
                border: 1px solid #A9A9A9;
                opacity: 0.6;
            }
        </style>


    <script>

        let events = {!! json_encode($events) !!};
        let resources = {!! json_encode($resources) !!};
        let viewStyle = 'resourceTimelineDay';

        document.addEventListener('DOMContentLoaded', function () {

            let calendarEl = document.getElementById('calendar');

            let calendar = new FullCalendar.Calendar(calendarEl, {
                selectable: true,
                customButtons: {
                    hours12: {
                        text: '12H',
                        click: function () {
                            calendar.setOption('slotDuration', '12:00')
                        }
                    },
                    hours24: {
                        text: '24H',
                        click: function () {
                            calendar.setOption('slotDuration', '24:00')
                        }
                    },
                    hours6: {
                        text: '6H',
                        click: function () {
                            calendar.setOption('slotDuration', '06:00')
                        }
                    },
                    hours2: {
                        text: '2H',
                        click: function () {
                            calendar.setOption('slotDuration', '02:00')
                        }
                    },
                    hours1: {
                        text: '1H',
                        click: function () {
                            calendar.setOption('slotDuration', '01:00')
                        }
                    },
                    hours05: {
                        text: '30M',
                        click: function () {
                            calendar.setOption('slotDuration', '00:30')
                        }
                    },
                    hours025: {
                        text: '15M',
                        click: function () {
                            calendar.setOption('slotDuration', '00:15')
                        }
                    }
                },
                header: {
                    left: 'prev,next today hours025 hours05 hours1 hours12 hours24',
                    center: 'title',
                    right: 'dayGridMonth,resourceTimelineWeek,resourceTimelineDay,listWeek',
                },
                views: {
                    dayGrid: {
                        titleFormat: { year: 'numeric', month: 'long', day: '2-digit' }
                    },
                    timeGrid: {
                        titleFormat: { year: 'numeric', month: 'long', day: '2-digit' }
                    },
                    week: {
                        titleFormat: { year: 'numeric', month: 'long', day: '2-digit' },
                        columnHeaderFormat: { weekday: 'long', month: 'long', day: '2-digit' }
                    },
                    day: {
                        titleFormat: { year: 'numeric', month: 'long', day: '2-digit' }
                    }
                },
                schedulerLicenseKey: 'GPL-My-Project-Is-Open-Source',
                plugins: ['resourceTimeline', 'list', 'dayGrid', 'timeGrid', 'list', 'interaction'],
                defaultView: viewStyle,
                resourceGroupField: 'groupId',
                editable: true,
                resources: resources,
                events: events,
                slotDuration: '01:00',
                eventOverlap: false,
                eventClick: function (event, jsEvent, view) {

                    $('#modalTitle').html(event.event.title);
                    $('#modalBody').html(event.event.description);
                    $('#eventUrl').attr('href', event.event.url);

                    loadEditModal(event.event.id);

                },
                select: function(info){
                    clickedDate = [info.startStr, info.endStr];
                    clickedResource = info.resource.id;


                    loadNewEventModal(clickedResource, clickedDate);
                },
                eventDrop: function(info) {

                    let newShift = info.event.id;
                    let newInTime = moment(info.event.start).format('YYYY-MM-DD HH:mm');
                    let newOutTime = moment(info.event.end).format('YYYY-MM-DD HH:mm');

                    let oldInTime = moment(info.oldEvent.start).format('YYYY-MM-DD HH:mm');
                    let oldOutTime = moment(info.oldEvent.end).format('YYYY-MM-DD HH:mm');

                    let compare = comparison(newInTime, newOutTime, oldInTime, oldOutTime);

                    if (compare){

                        Swal.fire({
                            title: 'Update Shift',
                            text: 'Are you sure you would like to modify this shift?',
                            type: 'question',
                            confirmButtonText: 'Yes i\'m sure',
                            cancelButtonText: 'No, cancel!',
                            reverseButtons: true,
                            confirmButtonColor: 'green',
                            showCancelButton: true,
                        }).then(function (result) {
                            if(result.value){
                                submitUpdate(newShift, newInTime, newOutTime, false);
                            } else {
                                info.revert();
                            }
                        })

                    } else {
                        info.revert();
                    }

                },
                eventResize: function(eventResizeInfo) {

                    let newShift = eventResizeInfo.event.id;
                    let newInTime = moment(eventResizeInfo.event.start).format('YYYY-MM-DD HH:mm');
                    let newOutTime = moment(eventResizeInfo.event.end).format('YYYY-MM-DD HH:mm');

                    let oldInTime = moment(eventResizeInfo.prevEvent.start).format('YYYY-MM-DD HH:mm');
                    let oldOutTime = moment(eventResizeInfo.prevEvent.end).format('YYYY-MM-DD HH:mm');

                    let compare = comparison(newInTime, newOutTime, oldInTime, oldOutTime);

                    if (compare){

                        Swal.fire({
                            title: 'Update Shift',
                            text: 'Are you sure you would like to modify the rota?',
                            type: 'question',
                            confirmButtonText: 'Yes i\'m sure',
                            cancelButtonText: 'No, cancel!',
                            reverseButtons: true,
                            cancelButtonColor: 'red',
                            confirmButtonColor: 'green',
                            showCancelButton: true,
                        }).then(function (result) {
                            if(result.value){
                                submitUpdate(newShift, newInTime, newOutTime, false);
                            } else {
                                eventResizeInfo.revert()
                            }
                        })

                    }else {

                        eventResizeInfo.revert();
                    }
                },
                eventRender: function(info){
                    if(info.event.backgroundColor == '') {
                        $(info.el).addClass('eventStriped');
                    } else {
                        $(info.el).css('background-image', 'linear-gradient(45deg, '+info.event.backgroundColor+' 25%, #808080 25%, #808080 50%, '+info.event.backgroundColor+' 50%, '+info.event.backgroundColor+' 75%, #808080 75%, #808080 100%');
                    }

                }

            });

            calendar.render();

            function loadEditModal(id)
            {
                $.ajax({
                    type: "GET",
                    url: "/clerks/attendance/get-modal/"+id+'/rota',
                    success: function (data) {

                        let modal = $('#editForm');
                        let modalBody = $('#modalBody');
                        modal.modal('show');
                        modalBody.html(data.html);

                        $('#timepicker1').datetimepicker({
                            format: 'DD/MM/YYYY HH:mm',
                            defaultDate: moment(data.start),
                            enabledDates: [
                                moment(data.start),
                                moment(data.end)
                            ]
                        });
                    },
                    fail: function () {
                        notificationBar('error', 'Unable to load shift data')
                    }
                });
            }


            function loadNewEventModal() {

                $.ajax({
                    type: "POST",
                    url: "/clerks/rota/add",
                    data: {id: clickedResource, date: clickedDate},
                    success: function (data) {

                        addEvent(data.event);

                    },
                    error: function (data) {
                        data = data.responseJSON;
                        notificationBar('error', data.message)
                    }
                });
            }

            function addEvent(event) {

                // TODO: AJAX this event to the server and add to calendar if successful
                calendar.addEvent(event);
                events.push(event)
            }

            $('#add-shift-clerk').on('click', function(e){
                e.preventDefault()

                var start = $('#addShift [name="start"]').val();
                var end = $('#addShift [name="end"]').val();
                var clerk = $('#addShift [name="clerk"]').val();
                var site = $('#addShift [name="site"]').val();

                if(clerk == '')
                {

                    notificationBar('error','Please select a clerk to add the shift for')
                    return false;
                }

                if(site == '')
                {

                    notificationBar('error','Please select a site to add the shift for')
                    return false;
                }

                $.ajax({
                    type: "POST",
                    url: "/clerks/attendance/add",
                    data: {start:start, end: end, clerk: clerk, site: site},
                    success: function (data) {
                        notificationBar('success', 'Your shift has been added!')

                        addEvent(data.event);
                    },
                    error: function(data) {
                        data = data.responseJSON;
                        notificationBar('error', data.message);
                    }
                });

            });

            $('#save-shift-button').on('click', function(e){
                e.preventDefault();

                let shift = $('#editForm #shift').val();
                let inTime = $('#editForm #in').val();
                let outTime = $('#editForm #out').val();
                let shiftType = $('#editForm #shift_type').val();
                let jobRole = $('#editForm #role_guid').val();


                if(shift !== '')
                {

                    submitUpdate(shift, inTime, outTime, true, shiftType, jobRole);

                } else {
                    notificationBar('error', 'Failed to update shift, please try again.')
                    $('#editForm').modal('hide');
                }

            });

            function submitUpdate(shift, inTime, outTime, isModal = false, shiftType, jobRole){

                $.ajax({
                    type: "POST",
                    url: "/clerks/rota/update",
                    data: {id: shift, 'in': inTime, out: outTime, shiftType: shiftType, jobRole: jobRole},
                    success: function (data) {

                        if(isModal) {
                            notificationBar('success', 'The rota has been updated!')
                        }

                        let event = calendar.getEventById(shift)
                        event.remove();
                        addEvent(data.event);
                        if(isModal) {
                            $('#editForm').modal('hide');
                        }

                    },
                    error: function (data) {
                        data = data.responseJSON;
                        notificationBar('error', data.message);
                    }
                });
            }

            function comparison(inTime, outTime, oldInTime, oldOutTime){

                return (inTime != oldInTime || outTime != oldOutTime);

            }

            $('#delete-shift-button').on('click', function(e){
                e.preventDefault()

                let shift = $('#editForm #shift').val();

                if(shift !== '')
                {

                    $.ajax({
                        type: "POST",
                        url: "/clerks/attendance/delete/"+shift,
                        success: function (data) {

                            notificationBar('success', 'The shift has been deleted!')

                            let event = calendar.getEventById(shift)
                            event.remove();

                            $('#editForm').modal('hide');

                        },
                        error: function (data) {
                            data = data.responseJSON;
                            notificationBar('error', data.message);
                        }
                    });

                } else {
                    notificationBar('error', 'Failed to remove shift, please try again.')
                    $('#editForm').modal('hide');
                }

            });

            $('body').on('click', '#split-shift-button',  function(e){
                e.preventDefault();

                let shift = $('#editForm #shift').val();
                let split_time = $('#split_time').val();
                let length = $('#split_length').val();

                if(shift !== '')
                {

                    submitSplit(shift, split_time, length)

                } else {
                    notificationBar('error', 'Failed to update shift, please try again.')
                    $('#editForm').modal('hide');
                }

            });

            function submitSplit(shift, time, length){

                $.ajax({
                    type: "POST",
                    url: "/clerks/rota/split",
                    data: {id: shift, 'split_time': time, 'split_length': length},
                    success: function (data) {

                        let event = calendar.getEventById(shift)
                        event.remove();

                        _.each(data.events, function(event){
                            event.backgroundColor = 'darkred';
                            addEvent(event);
                        })

                        $('#editForm').modal('hide');

                    },
                    error: function (data) {
                        data = data.responseJSON;
                        notificationBar('error', data.message);
                        let event = calendar.getEventById(shift);
                        event.remove();
                        addEvent(data.event);

                    }
                });
            }

        });

    </script>

@endsection
