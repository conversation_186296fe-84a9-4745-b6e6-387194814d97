@extends('layouts.master')

@section('section-name') Weekly Rota @stop

@section('content')
    <div class="row">
        <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
            <h4>Weekly Rota</h4>
            <hr/>
        </div>
    </div>
    <form id="CreateForm">
        {{ csrf_field() }}
        <div class="row">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Select a Clerk
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="form-group col-md-6">
                            <label>Select Clerk</label><br/>
                            <select name="clerk" id="clerk" class="form-control input">
                                <option value="">Please Select a Clerk</option>
                                @foreach($clerks as $clerk)
                                    <option value="{{$clerk->guid}}">{{$clerk->full_name}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group col-md-6">
                            <label>Select Site</label><br/>
                            <select id="site" name="site" class="form-control input">
                                {{-- CHECK IF THE USER CAN USE THE ALL OPTION--}}
                                @if($sites['all'] == true)
                                    <option value="">All Sites</option>
                                @endif
                                {{-- GET LIST OF SITES FROM THE HELPER AND POPULATE--}}
                                @foreach($sites['sites'] as $site)
                                    <option @if(\Session::get('current_site') == $site['site_num']) selected @endif value="{{$site['site_num']}}">{{$site['site_name']}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="html-area">

        </div>
    </form>

@stop


@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <script>
        $(document).on('change', '#clerk', function (e) {
            e.preventDefault();

            let value = $(this).val();
            let form = $('#CreateForm');

            getData(value);

        });

        function getData(clerk){
            // TODO: Loading Swal

            $.ajax({
                type: "GET",
                url: "/clerk/rota/get-form/" + clerk,
                success: function (data) {
                    $('#html-area').html(data.html)

                    $('.start-date').datetimepicker({
                        format: 'DD/MM/YYYY HH:mm',
                        toolbarPlacement: 'top',


                    });

                    $('.end-date').datetimepicker({
                        format: 'DD/MM/YYYY HH:mm',
                        toolbarPlacement: 'top'
                    });

                    $('input:checkbox').removeAttr('checked');
                }
            }).fail(function () {
                notificationBar('error', 'unable to load data')
            });
        }

        $( document ).ready(function() {
            $(document).on('dp.change', '.start-date',function(e) {
                $(this).parent().parent().find('input:checkbox:first').attr('checked', 'checked');
            });
            $(document).on('dp.change', '.end-date',function(e) {
                $(this).parent().parent().find('input:checkbox:first').attr('checked', 'checked');
            });

            $(document).on('change', '.shift-type',function(e) {
                $(this).parent().parent().find('input:checkbox:first').attr('checked', 'checked');

                let option = $(this).find('option:selected');
                let values = {
                    value: option.val(),
                    default: option.data('isdefault'),
                    timed: option.data('timed')
                }

                if(parseInt(values.default) !== 1 && parseInt(values.timed) !== 1 ){
                    $(this).parent().parent().find('.date-picker-start').attr('readonly','readonly');
                    $(this).parent().parent().find('.date-picker-end').attr('readonly','readonly');
                }else {
                    $(this).parent().parent().find('.date-picker-start').removeAttr('readonly','readonly');
                    $(this).parent().parent().find('.date-picker-end').removeAttr('readonly','readonly');
                }

            });

        });

        $('#create-rota-button').on('click', function(e){
            e.preventDefault();
            $('#CreateForm').submit()
        });


        var form = $('#CreateForm');

        $.validator.addMethod(
            "startEndCheck",
            function(value, element) {

                if($(element).attr('name') == 'start_date_0'){
                    var start = moment($(element).val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($('[name="end_date_0"]').val(), 'DD-MM-YYYY hh:ss')

                    console.log(start);

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date_0'){
                    var start = moment($('[name="start_date_0"]').val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($(element).val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'start_date_1'){
                    var start = moment($(element).val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($('[name="end_date_1"]').val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date_1'){
                    var start = moment($('[name="start_date_1"]').val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($(element).val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'start_date_2'){
                    var start = moment($(element).val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($('[name="end_date_2"]').val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date_2'){
                    var start = moment($('[name="start_date_2"]').val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($(element).val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'start_date_3'){
                    var start = moment($(element).val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($('[name="end_date_3"]').val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date_3'){
                    var start = moment($('[name="start_date_3"]').val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($(element).val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'start_date_4'){
                    var start = moment($(element).val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($('[name="end_date_4"]').val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date_4'){
                    var start = moment($('[name="start_date_4"]').val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($(element).val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'start_date_5'){
                    var start = moment($(element).val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($('[name="end_date_5"]').val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date_5'){
                    var start = moment($('[name="start_date_5"]').val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($(element).val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'start_date_6'){
                    var start = moment($(element).val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($('[name="end_date_6"]').val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }

                if($(element).attr('name') == 'end_date_6'){
                    var start = moment($('[name="start_date_6"]').val(), 'DD-MM-YYYY hh:ss');
                    var end = moment($(element).val(), 'DD-MM-YYYY hh:ss')

                    return start.isBefore(end);
                }
            },
            "We are ending before we have begun!"
        );

        $("#CreateForm").validate({
            rules: {

            },
            messages: {

            },
            submitHandler: function (form) {

                let data = $(form).serialize();

                $.ajax({
                    type: "POST",
                    url: "/clerks/rota/weekly/create",
                    data: data,
                    success: function (data) {
                        notificationBar('success', 'Your weekly rota for '+data.clerk+' has been saved', '/clerks/rota/weekly')
                    }
                }).fail(function(data) {
                    notificationBar(data.responseJSON.status, data.responseJSON.message)
                });
            }
        });
    </script>
@stop