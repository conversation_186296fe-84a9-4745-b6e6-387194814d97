@php use NewbridgeWeb\Http\Helpers\TimezoneHelper; @endphp
<div class="row">
    <div class="col-sm-12">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#edit-shift" data-toggle="tab">Edit Shift</a></li>
            <li><a href="#split-shift" data-toggle="tab">Split Shift</a></li>
        </ul>
        <div class="tab-content">
            <div class="tab-pane active" id="edit-shift">
                <div class="form-group col-xs-12">
                    <br/>
                    <label><strong>Shift Date & Time</strong></label>
                    <input type="hidden" id="shift" value="{{$shift->id}}" name="shift"/>
                    <input type="hidden" id="in" value="{{$shift->in}}" name="in"/>
                    <input type="hidden" id="out" value="{{$shift->out}}" name="out"/>
                    <div id="reportrangeEdit" style="cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
                        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
                        <span>{{\Carbon\Carbon::parse($shift->in)->setTimezone(TimezoneHelper::getTimezone())->format('M d, Y H:i:s')}} - {{\Carbon\Carbon::parse($shift->out)->setTimezone(TimezoneHelper::getTimezone())->format('M d, Y H:i:s')}}</span>
                        <b class="caret"></b>
                    </div>
                    <script>

                        $('#reportrangeEdit').daterangepicker({
                            startDate: '{{\Carbon\Carbon::parse($shift->in)->setTimezone(TimezoneHelper::getTimezone())->format('d/m/Y H:i')}}',
                            endDate: '{{\Carbon\Carbon::parse($shift->out)->setTimezone(TimezoneHelper::getTimezone())->format('d/m/Y H:i')}}',
                            autoApply: true,
                            timePicker: true,
                            timePicker24Hour: true,
                            locale: {
                                format: 'DD/MM/YYYY HH:mm'
                            },
                            ranges: {
                                '(Bar Early) 11am - 6pm': [moment(carbonDate).startOf('day').add(11, 'Hours'), moment(carbonDate).startOf('day').add(18, 'Hours')],
                                '(Bar Late) 6pm - 1am': [moment(carbonDate).startOf('day').add(18, 'Hours'), moment(carbonDate).startOf('day').add(25, 'Hours')],
                                '(Cafe/Bar Early) 8am - 4pm': [moment(carbonDate).startOf('day').add(8, 'Hours'), moment(carbonDate).startOf('day').add(16, 'Hours')],
                                '(Cafe/Bar Late) 4pm - 1am': [moment(carbonDate).startOf('day').add(16, 'Hours'), moment(carbonDate).startOf('day').add(25, 'Hours')],
                                '(Kitchen Early) 6am - 2pm': [moment(carbonDate).startOf('day').add(6, 'Hours'), moment(carbonDate).startOf('day').add(14, 'Hours')],
                                '(Kitchen Late)  3pm - 11pm': [moment(carbonDate).startOf('day').add(15, 'Hours'), moment(carbonDate).startOf('day').add(24, 'Hours')],
                            }
                        });

                        $('#reportrangeEdit').on('apply.daterangepicker', function (ev, picker) {
                            $('#reportrangeEdit span').html(picker.startDate.format('MMMM D, YYYY HH:mm') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm'));
                            $('#in').val(picker.startDate.format('YYYY-MM-DD HH:mm'));
                            $('#out').val(picker.endDate.format('YYYY-MM-DD HH:mm'));
                        });

                        $('#reportrangeEdit').on('hide.daterangepicker', function (ev, picker) {
                            $('#reportrangeEdit span').html(picker.startDate.format('MMMM D, YYYY HH:mm') + ' - ' + picker.endDate.format('MMMM D, YYYY HH:mm'));
                            $('#in').val(picker.startDate.format('YYYY-MM-DD HH:mm'));
                            $('#out').val(picker.endDate.format('YYYY-MM-DD HH:mm'));
                        });
                    </script>
                </div>

                <div class="form-group col-xs-12">
                    <label><strong>Shift Type</strong></label>
                    <select class="form-control" name="shift_type" id="shift_type">
                        <option value="none">None Selected</option>
                        @foreach($shiftTypes as $types)
                            <option @if($shift->shift_type == $types->id) selected
                                    @endif value="{{$types->id}}">{{$types->type}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group col-xs-12">
                    <label><strong>Job Role</strong></label>
                    <select class="form-control" name="role_guid" id="role_guid">
                        <option value="none">None Selected</option>
                        @foreach($clerkRoles as $role)
                            <option @if($shift->role_guid == $role->guid) selected
                                    @endif value="{{$role->guid}}">{{$role->displayname}}</option>
                        @endforeach
                    </select>
                </div>

                <div class="col-xs-12">

                </div>
            </div>
            <div class="tab-pane" id="split-shift">
                <div class="form-group">
                    <div class="form-group col-md-6" style="min-height: 80px;">
                        <label><strong>End Time</strong></label>
                        <div class='input-group time' id='timepicker1'>
                            <input type='text' class="form-control" id="split_time"
                                   value="{{\Carbon\Carbon::parse($shift->in)->setTimezone(TimezoneHelper::getTimezone())->format('d/m/Y H:i')}}"
                                   name="split_time"/>
                            <span class="input-group-addon">
                                <span class="fa fa-calendar-o"></span>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-group col-md-6" style="min-height: 80px;">
                        <label><strong>Break Length</strong></label>
                        <select name="split_length" id="split_length" class="multiselect form-control">
                            <option value="15">15 Minutes</option>
                            <option value="20">20 Minutes</option>
                            <option value="25">25 Minutes</option>
                            <option value="30">30 Minutes</option>
                            <option value="45">45 Minutes</option>
                            <option value="60">60 Minutes</option>
                        </select>
                    </div>
                </div>
                <div class="col-xs-12">
                    <br/>
                    <div class="col-xs-3 text-center pull-right">
                        <button type="button" class="btn btn-success" id="split-shift-button">Split Shift</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>