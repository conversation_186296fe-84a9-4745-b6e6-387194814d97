@extends('layouts.editor')

@section('content')
    <style>
        .resizable:hover {
            background: lightblue !important;
        }

        #overlay {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            background: #000;
            opacity: 0.8;
            filter: alpha(opacity=50);
            z-index: 999999;
        }

        #loading {
            width: 50px;
            height: 57px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin: -28px 0 0 -25px;
        }

        .fa-spinner {
            font-size: 40px;
            color: white;
        }

        body {
            font-size: 12px;
        }

        .Item_Background {
            width: 100%;
            background: #1b6d85;
            min-height: 50px;
            overflow: auto;
            border-left: 4px solid red;
            border-right: 4px solid red;
            margin-bottom: 7px;
        }

        .Item_Attribute2 {
            float: right;
            margin-right: 30px;
        }

        .Item_Attribute1 {
            color: black;
        }


        .Item_Foreground {
            padding: 10px;
            color: white;
            height: 100%;
            font-weight: bold;
        }

        .Item_Modifier_Background {
            width: 80%;
            height: 40px;
            background: #1c94c4;
            float: right;
            padding: 5px;
            border-left: 3px solid green;
            border-right: 3px solid green;
            margin-bottom: 10px;
            margin-right: 10px;
            margin-top: 5px;
        }

        .Item_Modifier_Foreground {
            padding-left: 20px;
            font-weight: bold;
        }

        .Item_Modifier_Attribute1 {
            color: black;
            font-weight: bold;
        }

        .Item_Modifier_Attribute2 {
            float: right;
            margin-right: 20px;
            color: white;
        }

        .Item_CashBack_Background {
            width: 100%;
            background: #1b6d85;
            min-height: 50px;
            overflow: auto;
            border-left: 4px solid red;
            border-right: 4px solid red;
            margin-bottom: 7px;
        }

        .Item_CashBack_Attribute2 {
            float: right;
            margin-right: 30px;
        }

        .Item_CashBack_Attribute1 {
            color: black;
        }


        .Item_CashBack_Foreground {
            padding: 10px;
            color: white;
            height: 100%;
            font-weight: bold;
        }

        .Item_Payment_Background {
            width: 100%;
            background: #1b6d85;
            min-height: 50px;
            overflow: auto;
            border-left: 4px solid red;
            border-right: 4px solid red;
            margin-bottom: 7px;
        }

        .Item_Payment_Attribute2 {
            float: right;
            margin-right: 30px;
        }

        .Item_Payment_Attribute1 {
            color: black;
        }


        .Item_Payment_Foreground {
            padding: 10px;
            color: white;
            height: 100%;
            font-weight: bold;
        }

    </style>

    <div style="width: 1000px; height: 690px; background: #2e6da4; padding-left: 20px;">

        <h2 style="color: white;">{{$site->site_name}}</h2>

        <div style="width: 400px; height: 560px; float: left">
            <div class="panel panel-default"
                 style="max-height: 540px; margin-left: -10px; margin-right: 10px; overflow-y: auto;">
                <div class="panel-body Check_View_Background">
                    <div class="Item_Background">
                        <div class="Item_Foreground"><span style="position: relative; top: 10px;">Item Name</span>
                            <span class="pull-right" style="position: relative; top: 10px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}2.50</span>
                        </div>
                    </div>
                    <div class="Item_Background">
                        <div class="Item_Foreground"><span style="position: relative; top: 10px;">Item Name</span>
                            <span class="pull-right" style="position: relative; top: 10px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}2.50</span>
                            <span class="Item_Attribute2" style="padding: 0px; text-align: center;"><span class="Item_Attribute1">SET</span><br /> Happy Hour</span>
                        </div>
                    </div>
                    <div class="Item_Background">
                        <div class="Item_Foreground"><span style="position: relative; top: 11px;">Item Name</span>
                            <span class="pull-right" style="position: relative; top: 10px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}2.50</span>
                            <span class="Item_Attribute2" style="padding: 0px; text-align: center;"><span class="Item_Attribute1">SET</span><br /> Happy Hour</span>
                        </div>
                        <div class="Item_Modifier_Background">
                            <div class="Item_Modifier_Foreground"><span style="position: relative; top: 7px;">Modifier Name</span>
                                <span class="Item_Modifier_Foreground pull-right"  style="position: relative; top: 7px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}1.00</span>
                                <span class="Item_Modifier_Attribute2" style="padding: 0px; text-align: center;"><span class="Item_Modifier_Attribute1">SET</span><br /> Happy Hour</span>
                            </div>
                        </div>
                    </div>
                    <div class="Item_Background">
                        <div class="Item_Foreground"><span style="position: relative; top: 10px;">Item Name</span>
                            <span class="pull-right" style="position: relative; top: 10px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}2.50</span>
                            <span class="Item_Attribute2" style="padding: 0px; text-align: center;"><span class="Item_Attribute1">SET</span><br /> Happy Hour</span>
                        </div>
                    </div>
                    <div class="Item_Background">
                        <div class="Item_Foreground"><span style="position: relative; top: 10px;">Item Name</span>
                            <span class="pull-right" style="position: relative; top: 10px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}2.50</span>
                            <span class="Item_Attribute2" style="padding: 0px; text-align: center;"><span class="Item_Attribute1">SET</span><br /> Happy Hour</span>
                        </div>
                    </div>
                    <div class="Item_CashBack_Background">
                        <div class="Item_CashBack_Foreground"><span style="position: relative; top: 10px;">CashBack</span>
                            <span class="pull-right" style="position: relative; top: 10px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}10.00</span>
                        </div>
                    </div>
                    <div class="Item_Payment_Background">
                        <div class="Item_Payment_Foreground"><span style="position: relative; top: 10px;">Cash</span>
                            <span class="pull-right" style="position: relative; top: 10px;">{!! NewbridgeWeb\Http\Helpers\CurrencyHelper::symbol(Auth::user()->company_id) !!}-20.00</span>
                        </div>
                    </div>
                    <div class="alert alert-warning">
                        <strong>This is a colour representation of the check view only, please use this to pick your colour scheme.</strong>
                        The sizes and proportions are not an exact replica of the check view.
                    </div>
                </div>
            </div>
        </div>

            <div class="pull-right">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        Manage Pos Colour Settings<br />
                        <small>Click in the table to instantly edit records shown, use the search boxes to filter the list.</small>
                    </div>
                    <div class="panel-body" style="border: 2px solid white; max-height: 550px; overflow: auto;">
                            <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                                <thead>
                                <tr>
                                    <th>Setting</th>
                                    <th>Value</th>
                                </tr>
                                </thead>
                                <tbody>
                                    @foreach($settings  as $setting)
                                        <tr data-setting="{{$setting->setting_id}}" class="setting-row" data-value="#{{substr($setting->value1, 3)}}">
                                            <td>{{$setting->label}}</td>
                                            <td><input type="color" name="text-color" id="text-color" class="pull-right colour-setting" data-setting="{{$setting->setting_id}}" value="#{{substr($setting->value1, 3)}}"></td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>

                    </div>
                    <div class="panel-footer">
                        @ability('newbridge,owner,reseller', 'write_settings')
                        <button class="btn btn-md btn-success" id="save-button">Save Changes</button>
                        @endability
                    </div>
                </div>
            </div>
    </div>

    <script>
        $('.colour-setting').on('change', function(){
            var element = $(this).data('setting');
            var value = $(this).val();

            var isForeground = element.indexOf("Foreground") !== -1;
            var isAttribute = element.indexOf('Attribute') !== -1;

            if(isForeground || isAttribute){
                $('.'+element).css("color", value+'!important');
            } else {
                $('.'+element).css("background-color", value+'!important');
            }

        })
        $(function(){

            $('.colour-setting').each(function (index, value) {
                var element = $(this).data('setting');
                var value = $(this).val();

                var isForeground = element.indexOf("Foreground") !== -1;
                var isAttribute = element.indexOf('Attribute') !== -1;

                if(isForeground || isAttribute){
                    $('.'+element).css("color", value+'!important');
                } else {
                    $('.'+element).css("background-color", value+'!important');
                }

            });

        })

        $('#save-button').click(function(){

            // build something to post

            var updates = [];
            $('.colour-setting').each(function (index, value) {
                var element = $(this).data('setting');
                var value = $(this).val();

                updates.push({'setting_id':element, 'value': value});
            })


            $.ajax({
                type: "POST",
                url: "/pos-settings/colours/update/{{$site->site_num}}",
                data: {data: updates},
                success: function () {
                    window.location.reload()
                },
                fail: function () {
                    console.warn('error updating colours')
                }
            });
        })

    </script>
@stop

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-overlaps/1.2.3/jquery.overlaps.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>

    <script src="/js/colors.js?{{\Carbon\Carbon::now()->timestamp}}"></script>
    <script src="/js/colourEditor.js?{{\Carbon\Carbon::now()->timestamp}}"></script>

@endpush