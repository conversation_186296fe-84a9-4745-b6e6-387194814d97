@extends('layouts.master')

@section('content')
    <script>
        var table = null;
    </script>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Settings ({{$terminal->site->site_name}} / {{$terminal->name}})</h1>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Manage Pos Settings<br />
                    <small>Click in the table to instantly edit records shown, use the search boxes to filter the list.</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Label</th>
                                <th>Description</th>
                                <th>Value 1</th>
                                <th>Value 2</th>
                                <th>Value 3</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="copy_modal" class="modal fade">
        <div class="modal-dialog modal-md">
            <div class="modal-content" style="border-radius: 21px;">
                <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                    <h4>Copy Setting to Terminals</h4>
                </div>
                <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="panel-heading">
                                            <h3>Copy Setting</h3>
                                            <p>Press any of the buttons below to copy the selected setting's value to that terminal.</p>
                                        </div>
                                        @foreach($terminals as $terminal)
                                            <div class="col-md-4" style="margin-top: 15px;">
                                                <button class="btn btn-md btn-primary copySetting" data-id="{{$terminal->id}}">Copy to {{$terminal->name}}</button>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Done</button>
                </div>
            </div>
        </div>
    </div>
@stop

@push('scripts')
<script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
<script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
<script src="/js/dataTables.editor.min.js"></script>

<script>
    var permissions = {};
    permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_settings') == 1 ? 1 : 0 }};
    permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_settings') == 1 ? 1 : 0 }};
    permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'write_settings') == 1 ? 1 : 0 }};

    var site_num = {{$site_num}};
    var terminal_num = {{$terminal_num}};
    var categories = {!! $categories !!}
</script>

<script src="/js/pos-settings.js"></script>
@endpush