@extends('layouts.master')

@section('section-name') SubDepartment Product Order @stop

@section('content')

{{--    <div class="row">--}}
{{--        <div class="col-lg-12">--}}
{{--            <h1 class="page-header">SubDepartment Product Order</h1>--}}
{{--        </div>--}}
{{--    </div>--}}

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">SubDepartment Product Order ({{$subdepartment->displayname}})<br />
                <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small></h1>
        </div>
    </div>

    @if($company->site_specific_products == 1)
        @include('modules.site-select.index', ['sites' => \NewbridgeWeb\Http\Helpers\SiteHelper::mySites(false, false)])
    @endif

    <div class="row">
        <form>
            {{ csrf_field() }}
            <div class="col-md-12">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        Sub Department Ordering<br />
                        <small>Drag and drop to re-order the sub departments shown below.</small>
                    </div>
                    <div class="panel-body">
                        <div class="col-md-12">
                            <style>
                                ul.sortable-list {
                                    width: 100%;
                                    padding: 0px;
                                    margin: 0px;
                                    padding-right: 20%;
                                }
                                ul.sortable-list li {
                                    padding: 10px;
                                    background-color: #FCFBE3;
                                    border: 1px #5E83BA solid;
                                    list-style: none;
                                    text-indent: 0px;
                                    margin: 5px;
                                }
                            </style>
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-xs-12">
                                        <button class="btn btn-success btn-md save-order">Save Order</button>
                                    </div>
                                </div>
                                <input type="hidden" name="products">
                                <script>
                                    let products = [];
                                </script>
                                <ul class="sortable sortable-list">
                                    @foreach($subdepartment['products'] as $product)
                                        <li data-order="{{$product->priority}}" data-id="{{$product->id}}">{{$product->displayname}}</li>
                                        <script>
                                            products.push({ id: '{{$product->id}}', priority: {{$product->priority}} })
                                        </script>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="row">
                                <div class="col-xs-12">
                                    <button class="btn btn-success btn-md save-order">Save Order</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

@stop

@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <script>

        $('.sortable').sortable();

        $('.sortable').sortable().bind('sortupdate', function() {
            products = [];

            var elements = $('.sortable li');
            var position = 0;

            $.each(elements, function(index, value){
                products.push({ id: $(value).data('id'), priority: position });
                position++;
            });

            $('[name="products"]').val(JSON.stringify(products))

        })


        // $('[name="subdepartments"]').val(JSON.stringify(subdepartments))


        $(document).on('click', '.save-order', function(e){

            e.preventDefault();

            $.ajax({
                type: "post",
                url: "/sub-departments/product-priority/{{$subdepartment->id}}",
                data: { data: $('[name="products"]').val() },
                success: function (data) {

                    notificationBar(data.status, data.message, '/sub-departments')

                }
            }).fail(function (data) {
                notificationBar(data.status, data.message)
            });
        })


    </script>

@endsection