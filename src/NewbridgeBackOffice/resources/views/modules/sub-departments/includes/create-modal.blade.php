<div id="CreateModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 21px;">
            <div class="modal-header" style="background-color: #2e6da4; color: white; border-radius: 20px 20px 0 0">
                {{--<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>--}}
                {{--<h4 class="modal-title">Edit Product ({{$product->name}}</h4>--}}
                <h4>Add Sub-Department</h4>
            </div>
            <div class="modal-body" style="overflow: auto; max-height: 70vh;">
                <form id="CreateForm">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    Basic Department Information
                                </div>
                                <div class="panel-body">
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Name</strong></label>
                                                <input type="text" class="form-control" name="displayname"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Account Code</strong></label>
                                                <input type="text" class="form-control" maxlength="30" name="acc_code"/>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label><strong>Print Priority</strong></label>
                                                <input type="number" class="form-control" maxlength="30" name="print_priority"/>
                                            </div>
                                        </div>
                                        @if((int)Auth::user()->company->active_integrations & 8 || (int)Auth::user()->company->active_integrations & 16)
                                        <div class="col-xs-6">
                                            <label><strong>Ordering Times</strong>
                                            <br /><small>Leaving this blank will make the products available during normal operating hours.</small></label>
                                            <select class="form-control multiselect" multiple name="opening_times[]">
                                                @foreach($openings as $opening)
                                                    <option value="{{$opening['bit_value']}}">{{$opening->name}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @endif
                                    </div>
                                    @if((int)Auth::user()->company->active_integrations & 8 || (int)Auth::user()->company->active_integrations & 16)
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <div class="form-group">
                                                <label><strong>Description</strong> <br />(This is visible to customers in connected apps)</label>
                                                <textarea class="form-control" name="description"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" id="createSubmit">Create</button>
            </div>
        </div>
    </div>
</div>