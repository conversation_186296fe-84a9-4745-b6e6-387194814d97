@extends('layouts.master')

@section('section-name') Import Progress @stop

@section('content')

    <section>
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">File Import</h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>
        <section id="uploadFiles">
            <div class="row" style="padding:30px">

                <h1 class="text-center">Loading Your Imports Now..    <i class="fas fa-cloud-upload-alt fa-2x"></i></h1>
                    <div id="loading-area" class="progress">

                        <div class="progress-bar progress-bar-striped progress-bar-danger progress-bar-success progress-bar-warning active"
                             role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                <h4 class="text-center" id="loading-text"></h4>
                <ul id="errors-list"></ul>

            </div>
        </section>
    </section>

    <style>

        .errorList {
            background-color: #f5f5f5;
            border: solid 1px #222222;
            padding:10px;
            margin:50px 0px;
            line-height:1.8;

        }

        .errorList > li {
            list-style: none;
        }
    </style>


    <script>

        window.addEventListener('DOMContentLoaded', (event) => {
            checkStatus("{!! $import->guid !!}");
        });


        function checkStatus(guid)
        {

            $.ajax({
                type: "GET",
                url: "/import/"+guid+"/check-status",
                success: function (data) {

                    let loader = $('#loading-area');
                    let progressBar = $('#loading-area .progress-bar')
                    progressBar.show();
                    let progressText = $('#loading-text');
                    let errorList = document.querySelector('#errors-list');
                    let timer;

                    let result = JSON.parse(data);
                    if(result.status === "warning"){

                        progressText.text(result.text);
                        progressBar.removeClass('progress-bar-warning')
                        progressBar.removeClass('progress-bar-success')
                        progressBar.removeClass('progress-bar-danger')
                        progressBar.addClass('progress-bar-'+ result.status)
                        progressBar.attr('aria-valuenow', result.percent)
                        progressBar.css('width', result.percent+'%')
                        timer = setTimeout(function(){
                            checkStatus(guid);
                        }, 2000);
                    }
                    if(result.status === "success")
                    {
                        progressBar.removeClass('progress-bar-warning')
                        progressBar.removeClass('progress-bar-success')
                        progressBar.removeClass('progress-bar-danger')
                        progressText.text(result.text);
                        progressBar.addClass('progress-bar-success')
                        progressBar.attr('aria-valuenow', 100)
                        progressBar.css('width', '100%')

                        timer = setTimeout(function(){
                            checkStatus(guid);
                        }, 2000);

                        if(result.text === 'Import complete!' ){
                            clearTimeout(timer);
                            loader.hide();
                        }

                    }
                    if(result.status === "error")
                    {
                        progressText.text(result.text);
                        progressBar.removeClass('progress-bar-warning')
                        progressBar.removeClass('progress-bar-success')
                        progressBar.removeClass('progress-bar-danger')

                        progressBar.addClass('progress-bar-danger')
                        progressBar.attr('aria-valuenow', result.percent)
                        progressBar.css('width', '25%')

                        errorList.classList.add('errorList');
                        result.errors.forEach((e) => {
                            let li = document.createElement('li');
                            let issue = "Upload Error: "+e.type +" in "+ e.section+
                                    " on row "+e.row+" in "+e.col+" column, "
                                     + "expected to find " + e.expected
                                    + " but didn't." ;
                            li.textContent = issue;
                            errorList.appendChild(li);
                        });

                        loader.hide();

                        clearTimeout(timer);


                    }
                },
                fail: function () {

                }
            });
        }


    </script>

@stop

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">

@endpush
