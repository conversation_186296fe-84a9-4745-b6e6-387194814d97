@extends('layouts.master')

@section('section-name') Import Files @stop

@section('content')

    <section>
        <div class="row">
            <div class="col-lg-12">
                <h1 class="page-header">File Import</h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>
        <section id="uploadFiles">
            <div class="row">

                @if($errors && count($errors) > 0)
                <div class="alert alert-warning">
                    @foreach($errors as $error)
                        <div class="row" style="padding: 10px; padding-left: 30px;"> <i></i>
                        @if($error['type'] === 'required')
                            The field {{$error['col']}} in {{$error['section']}} is required - missing on row {{$error['row']}}
                        @endif
                        @if($error['type'] === 'mismatch')
                            The {{$error['col']}} in {{$error['section']}} was not found in row {{$error['row']}} - expected {{$error['expected']}}
                        @endif
                        @if($error['type'] === 'missing')
                            The {{$error['section']}} section has no records!
                        @endif
                        @if($error['type'] === 'sectionblank')
                            The column {{$error['section']}} is missing!
                        @endif
                        </div>
                    @endforeach
                </div>
                @endif
                <form id="uploadform" method="POST" action="/import/upload"  enctype="multipart/form-data">
                    {{ csrf_field() }}
                    <div class="col-md-12 col-sm-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <strong>Import Product and Department Data from and Excel Spreadsheet</strong>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-sm-12">

                                        <div class="col-lg-6 col-sm-6 col-12">
                                            <h4>Import File</h4>
                                            <label class="">
                                                Choose the file to upload
                                            </label>
                                            <div class="input-group">
                                                <label class="input-group-btn">
                                                    <span class="btn btn-primary">
                                                        Browse… <input type="file" name="import" style="display: none;">
                                                    </span>
                                                </label>
                                                <input type="text" class="form-control file-name" readonly="">
                                            </div>
                                        </div>
                                        @if(Auth::user()->hasRole('newbridge'))
                                        <div class="col-lg-6 col-sm-6 col-12">
                                            <h4>Confirm Company Name</h4>
                                            <label class="">
                                                Enter <span class="text-warning"> <strong>{{Auth::user()->company->company_name}}</strong></span> in the box below
                                            </label>
                                            <input type="text" name="company_name" class="form-control" value="" placeholder="Enter the Company Name">
                                        </div>
                                        @endif

                                        {{--<div class="col-lg-6 col-sm-6 col-12">--}}
                                            {{--<h4>Department File</h4>--}}
                                            {{--<div class="input-group">--}}
                                                {{--<label class="input-group-btn">--}}
                                                    {{--<span class="btn btn-primary">--}}
                                                        {{--Browse… <input type="file" style="display: none;"--}}
                                                                       {{--multiple="false" name="departments">--}}
                                                    {{--</span>--}}
                                                {{--</label>--}}
                                                {{--<input type="text" class="form-control file-name" readonly="">--}}
                                            {{--</div>--}}
                                        {{--</div>--}}
                                        {{--<div class="col-lg-6 col-sm-6 col-12">--}}
                                            {{--<h4>Groups File</h4>--}}
                                            {{--<div class="input-group">--}}
                                                {{--<label class="input-group-btn">--}}
                                                    {{--<span class="btn btn-primary">--}}
                                                        {{--Browse… <input type="file" style="display: none;" multiple="false" name="groups">--}}
                                                    {{--</span>--}}
                                                {{--</label>--}}
                                                {{--<input type="text" class="form-control file-name" readonly="">--}}
                                            {{--</div>--}}
                                        {{--</div>--}}

                                    </div>

                                </div>
                                <!-- /.row (nested) -->
                            </div>
                            <div class="panel-footer">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-md btn-success" id="uploadbutton">
                                            Upload
                                        </button>
                                    </div>

                                </div>
                            </div>
                            <!-- /.panel-body -->
                        </div>
                        <!-- /.panel -->
                    </div>
                    <!-- /.col-lg-6 -->
            </div>
        </section>
    </section>

    <script>

        function loading(target) {
            $('<div id="loading"><i class="fa fa-spinner fa-spin" style="margin-top: 80px; font-size: 48px;"></i><br /><h3>Loading data please wait</h3></div>').css({
                position: "absolute",
                'min-height': '200px',
                width: "100%",
                height: "100%",
                "text-align": "center",
                top: 0,
                left: 0,
                background: '#ccc',
                background: 'rgba(999,999,999,1.0)',
                'z-index': 99999,
            }).appendTo($(target).css("position", "relative"));
        }

        $('#uploadform').submit(function( event ) {

            loading('.panel-body')

        });

        $(document).on('change', ':file', function () {
            var input = $(this),
                numFiles = input.get(0).files ? input.get(0).files.length : 1,
                label = input.val().replace(/\\/g, '/').replace(/.*\//, '');

            $(this).parent().parent().parent().find('.file-name').val(label)
        });


    </script>

@stop

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">

@endpush
