@extends('layouts.master')

@section('content')
    <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
        <h4>Create Revenue Center</h4>
        <hr/>
    </div>
    <form id="form" action="/company/revenue-centers/create" method="post">
        {{ csrf_field() }}
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Create Revenue Center
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label><strong>Name</strong></label>
                                <input type="text" class="form-control" name="name"/>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label><strong>Terminals</strong></label>
                                <select name="terminals[]" id="terminals" multiple>
                                    @foreach($sites as $site)
                                        <optgroup label="{{$site->site_name}}">
                                            @foreach($site->terminals as $terminal)

                                                <option value="{{$terminal->id}}">
                                                    Terminal {{$terminal->terminal_num}}</option>

                                            @endforeach
                                        </optgroup>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="row" style="padding-bottom: 50px;">
            <div class="col-md-12">
                <div class="col-md-6 pull-right">
                    <button type="submit" class="btn btn-md btn-success pull-right" id="submit">Create
                        <i class="fa fa-save"></i></button>
                </div>
            </div>
        </div>
    </form>
@stop


@section('js')
    @parent
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.15/js/bootstrap-multiselect.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5sortable/0.8.1/html5sortable.min.js"></script>

    <script>
        $(function () {
            $('select')
                .multiselect({
                    buttonWidth: '100%',
                    includeSelectAllOption: false,
                    enableClickableOptGroups: true,
                    enableCollapsibleOptGroups: true,
                    collapseOptGroupsByDefault: true,
                    enableCaseInsensitiveFiltering: true,
                    onDropdownShown: function (event) {
                        $(event.target).find('.filter input').focus();
                    }
                })
                .multiselect('selectAll', true)
                .multiselect('updateButtonText');


        })
    </script>

@stop
