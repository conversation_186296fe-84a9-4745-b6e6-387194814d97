@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header" style="width: 100%;">View {{$meta['title_list']}} Data ({{$summary['status']}})<div class="pull-right"><a href="{{$meta['url']}}" class="btn btn-md btn-primary"><i class="fa fa-arrow-left"></i> Back</a></h1>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 col-xs-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="btn-group pull-right">
                        @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']) && $summary['status'] === 'Completed')
                            <a class="reopen-form"
                               href="/stock/stocktake/completion/{{$summary['id']}}/0">Reopen {{ ucfirst(Request::segment(2)) }}</a>
                            <span class="vertical-line"></span>
                        @endif
                        <a class="export-form" target="_blank"
                               href="/stock/export/{{Request::segment(2)}}/{{$summary['id']}}/csv">Export CSV</a>
                    </div>
                    {{$meta['title_list']}} Data @if($meta === 3) ({{$summary['stocktake_date']}}) @else ({{$summary['created_at']}}) @endif
                </div>

            </div>
                <div class="panel-body">
                    @if(count($notStockedProducts))
                    <div class="row">
                        <div class="alert alert-warning">
                            We could not display data for the following products because you are no longer tracking them for stock.
                            <ul>
                                @foreach($notStockedProducts as $ns)
                                    <li>{{ $ns }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                    @endif
                    <div class="table-responsive">
                        <table id="stock-table" class="table table-striped table-condensed" width="100%">
                            @foreach($results as $k => $v)
                                <thead>
                                    <tr>
                                        <th id="row-{{$k}}" colspan="@if(Request::segment(2) == 'stocktake') 4 @else 2 @endif"><h4>{{$k}}</h4></th>
                                    </tr>
                                    <tr>
                                        <th>Product Name</th>
                                        @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']))
                                            <th>Expected Stock</th>
                                        @endif
                                        <th>{{$meta['value_title']}} (SKU)</th>
                                        <th>{{$meta['value_title']}} (Units)</th>
                                        @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']))
                                            <th>Variance Qty</th>
                                            <th>Variance @ Cost</th>
                                            <th>Variance @ Retail</th>
                                        @endif
                                        @if(Request::segment(2) == 'wastages')
                                            <th>Cost Price</th>
                                        @endif
                                        @if(Request::segment(2) == 'wastages' || Request::segment(2) == 'adjustments' || Request::segment(2) == 'transfers')
                                            <th>Reason</th>
                                        @endif
                                    </tr>
                                </thead>
                                <tbody>
                                @foreach($v as $t)
                                    <tr>
                                        <td>
                                            {{$t['product_name']}}
                                        </td>
                                        @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']))
                                            <td>
                                                {{$t['expected_stock']}}
                                            </td>
                                        @endif
                                        @if(Request::segment(2) == 'wastages')
                                            <td>
                                                {{$t['counted_stock']}}
                                            </td>
                                            <td>
                                                {{$t['wastage_amount']}}
                                            </td>
                                        @else
                                            <td>
                                                {{$t['counted_stock']}}
                                            </td>
                                            <td>
                                                {{$t['quantity']}}
                                            </td>
                                        @endif
                                        @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']))
                                            <td>
                                                {{$t['variance_qty']}}
                                            </td>
                                            <td>
                                                {!! $t['currency'] !!}{{$t['variance_cost']}}
                                            </td>
                                            <td>
                                               {!! $t['currency'] !!}{{$t['variance_retail']}}
                                            </td>
                                        @endif
                                        @if(Request::segment(2) == 'wastages')
                                        <td>
                                            {{$t['costprice']}}
                                        </td>
                                        @endif
                                        @if(Request::segment(2) == 'wastages' || Request::segment(2) == 'adjustments' || Request::segment(2) == 'transfers')
                                            <td>
                                                {{$t['reason']}}
                                            </td>
                                        @endif
                                    </tr>
                                @endforeach
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@push('scripts')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>

    <script>
        var meta = {!! json_encode($meta) !!};
        var module = '{{Request::segment(2)}}';
    </script>
    <style>
        .vertical-line {
            border-left: 1px solid #000;
            height: 20px;
            margin: 0 10px;
        }
    </style>
@endpush
