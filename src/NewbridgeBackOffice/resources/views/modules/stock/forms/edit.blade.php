@extends('layouts.master')

@section('section-name')
    Stock - {{ucfirst(Request::segment(2))}}
@stop

@section('content')

    <style>
        #OrderTable label.error {
            display: none;
        }

        input:read-only.negative {
            background: red !important;
            color: white !important;
        }

        input:read-only.positive {
            background: lightgreen !important;
            color: black !important;
        }

        input:read-only.neutral {
            background: gold !important;
            color: black !important;
        }
    </style>
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">{{$meta['title_create']}} <br/>
                <small> @if(\Session::get('current_site') != 0)
                        {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}}
                    @endif</small></h1>
        </div>
    </div>
    <form id="stock-form">
        {{ csrf_field() }}
        <div class="row">
            <div class="col-sm-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        {{$meta['title_create']}} Details
                    </div>
                    <div class="panel-body">
                        <input type="hidden" name="site" id="site" value="{{\Session::get('current_site')}}">
                        @if(Request::segment(2) == 'stocktake' || Request::segment(2) == 'spotchecks' || Request::segment(2) == 'wastages' || Request::segment(2) == 'adjustments')
                            <div class='form-group col-sm-12'>
                                <label><strong>{{$meta['title_list']}} Date</strong></label>
                                <div class='input-group date' id='datetimepicker1'>
                                    <input type='text' readonly class="form-control" id="stocktake_date"
                                           name="stocktake_date"
                                           value="{{\Carbon\Carbon::parse($details->stocktake_date)->setTimezone(\NewbridgeWeb\Http\Helpers\TimezoneHelper::getTimezone())->format('d/m/Y H:i:s')}}"/>
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        @endif

                        @if(Request::segment(2) == 'transfers' )
                            <div class="form-group col-sm-12">
                                <label><strong>Target Site</strong> <br/> <small>Please select the site you are
                                        transfering stock to.</small></label>
                                <select data-product="" class="form-control input-sm site_target required" type="text"
                                        required name="site_target">
                                    <option value="">Please select one</option>
                                    @foreach($sites as $k => $site)
                                        <option @if($details->site_target == $site->site_num) selected
                                                @endif value="{{$site->site_num}}">{{$site->site_name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        @if(Request::segment(2) == 'requisitions' )
                            <div class="form-group col-sm-12">
                                <label><strong>Source Site</strong> <br/> <small>Please select the site you are
                                        requesting stock from.</small></label>
                                <select data-product="" class="form-control input-sm site_source required" disabled
                                        type="text" required name="site_source">
                                    <option value="">Please select one</option>
                                    @foreach($allSites as $k => $rsite)
                                        <option @if($details->site_source == $rsite->site_num) selected
                                                @endif value="{{$rsite->site_num}}">{{$rsite->site_name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        @endif

                        <div class="form-group col-sm-12">
                            <label><strong>Notes</strong></label>
                            <textarea name="notes" class="form-control">{{$details->notes}}</textarea>
                        </div>
                    </div>
                </div>
            </div>
            @if(Request::segment(2) !== 'stocktake')
                <div class="col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <a href="#" onclick="switchAddTab('products')">Products</a> | <a href="#"
                                                                                             onclick="switchAddTab('suppliers')">Suppliers</a>
                            | <a href="#" onclick="switchAddTab('departments')">Departments</a> | <a href="#"
                                                                                                     onclick="switchAddTab('subdepartments')">Sub
                                Departments</a>
                        </div>
                        <div class="panel-body">
                            <div class="form-group col-sm-12">
                                <div id="products">
                                    <div class="row">
                                        <div class="col-lg-12 products" data-type="add-tab">
                                            <div class="form-group">
                                                <label><strong>Product</strong></label>
                                                <select class="form-control multiselect" id="product_data" multiple>
                                                    @foreach($departments as $dep)
                                                        @if($dep['products'] != null)
                                                            <optgroup label="{{$dep['displayname']}}"
                                                                      class="{{$dep['guid']}}">
                                                                @foreach($dep['products'] as $p)
                                                                    <option value="{{$p['guid']}}">{{$p['displayname']}}</option>
                                                                @endforeach
                                                            </optgroup>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="btn btn-primary btn-block add-product-button"
                                                 style="margin-top: 10px;">Add Product
                                            </div>
                                        </div>
                                        <div class="col-lg-12 departments" data-type="add-tab" style="display: none;">
                                            <div class="form-group">
                                                <label><strong>Department</strong></label>
                                                <select class="form-control multiselect" id="department_data" multiple>
                                                    @foreach($departments as $dep)
                                                        <option value="{{$dep['guid']}}">{{$dep['displayname']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="btn btn-primary btn-block add-department-button"
                                                 style="margin-top: 10px;">Add Department Products
                                            </div>
                                        </div>
                                        <div class="col-lg-12 subdepartments" data-type="add-tab"
                                             style="display: none;">
                                            <div class="form-group">
                                                <label><strong>Sub Department</strong></label>
                                                <select class="form-control multiselect" id="subdepartment_data"
                                                        multiple>
                                                    @foreach(json_decode($subdepartments, true) as $subdepartment)
                                                        <option value="{{$subdepartment['guid']}}">{{$subdepartment['displayname']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="btn btn-primary btn-block add-sub-department-button"
                                                 style="margin-top: 10px;">Add Sub Department Products
                                            </div>
                                        </div>
                                        <div class="col-lg-12 suppliers" data-type="add-tab" style="display: none;">
                                            <div class="form-group">
                                                <label><strong>Supplier</strong></label>
                                                <select class="form-control multiselect" id="supplier_data" multiple>
                                                    @foreach(json_decode($suppliers, true) as $supplier)
                                                        <option value="{{$supplier['guid']}}">{{$supplier['name']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="btn btn-primary btn-block add-supplier-button"
                                                 style="margin-top: 10px;">Add Supplier Products
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <div class="row" id="product-area">
            <div class="col-xs-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        {{$meta['title_create']}} Products
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <hr/>
                            <div class="col-sm-6">
                                <button class="btn btn-block btn-warning btn-sm" id="save-button">Save</button>
                            </div>
                            <div class="col-sm-6">
                                <button class="btn btn-block btn-success btn-sm createButtons"
                                        id="createButton">{{$meta['button_create']}}</button>
                            </div>
                        </div>
                        <table id="OrderTable" class="table-striped table" width="100%">
                            <tbody id="order-table-body">
                            <tr class="copy-example" style="display: none;" data-product="" data-priority="0.00">
                                <td>
                                    <label style="font-size: 10px"><strong>Product Name</strong></label>
                                    <input data-product="" class="form-control input-sm plu" type="hidden" name="plu[]"
                                           readonly value="">
                                    <input data-product="" class="form-control input-sm priority" type="hidden" name="priority[]"
                                           readonly value="">
                                    <input data-product="" class="form-control input-sm displayname input" type="text"
                                           name="name[]" placeholder="Product" readonly value="" disabled="true">
                                </td>
                                <td style="max-width: 150px;">
                                    <label style="font-size: 10px"><strong>Expected Stock</strong></label>
                                    <input data-product="" style="max-width: 90px;"
                                           class="form-control input-sm current_stock" type="text"
                                           name="current_stock[]" value="" readonly>
                                </td>
                                <td style="max-width: 100px;">
                                    <label style="font-size: 10px"><strong>Min Stock</strong></label>
                                    <input data-product="" class="form-control input-sm min_stock" type="text"
                                           name="min_stock[]" value="" readonly disabled="true">
                                </td>
                                <td style="max-width: 100px;">
                                    <label style="font-size: 10px"><strong>Max Stock</strong></label>
                                    <input data-product="" class="form-control input-sm max_stock" type="text"
                                           name="max_stock[]" value="" readonly disabled="true">
                                </td>
                                <td>
                                    <div class="form-group">
                                        <label style="font-size: 10px"><strong>SKU</strong></label>
                                        <select data-product="" class="form-control input-sm sku" type="text"
                                                name="sku[]" readonly>
                                            @foreach($skus as $k => $sku)
                                                <option data-qty="{{$sku['qty']}}"
                                                        value="{{$sku['guid']}}">{{$sku['displayname']}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </td>
                                <td style="max-width: 80px;">
                                    <label style="font-size: 10px"><strong>SKU Qty</strong></label>
                                    <input data-product="" class="form-control quantity" type="number"
                                           name="SkuQuantity[]" step="any" value="0"
                                           @if($meta['type'] != 6 && $meta['type'] != 4) min="0" @endif  data-price="">
                                </td>
                                <td style="max-width: 80px;">
                                    <label style="font-size: 10px"><strong>Unit Qty</strong></label>
                                    <input data-product="" class="form-control unitQuantity" type="number"
                                           name="UnitQuantity[]" step="any" value="0"
                                           @if($meta['type'] != 6 && $meta['type'] != 4) min="0" @endif  data-price="">
                                </td>
                                @if(Request::segment(2) == 'wastages')
                                    <td style="max-width: 120px;">
                                        <label style="font-size: 10px"><strong>Reason</strong></label>
                                        <input data-product="" class="form-control reason" type="text" name="reason[]"
                                               placeholder="e.g. Line Clean">
                                    </td>
                                @endif
                                @if(Request::segment(2) == 'adjustments')
                                    <td style="max-width: 120px;">
                                        <label style="font-size: 10px"><strong>Reason</strong></label>
                                        <input data-product="" class="form-control reason" type="text" name="reason[]"
                                               placeholder="e.g. Transfer To...">
                                    </td>
                                @endif
                                @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']))
                                    <td style="max-width: 100px;">
                                        <label style="font-size: 10px"><strong>Variance Qty</strong></label>
                                        <input data-product="" class="form-control variance" type="text"
                                               name="variance[]" readonly>
                                    </td>
                                    <td style="max-width: 100px;">
                                        <label style="font-size: 10px"><strong>Variance Value</strong></label>
                                        <input data-product="" class="form-control variance_total" type="text"
                                               name="variance_total[]" readonly>
                                    </td>
                                @endif
                                @if(Request::segment(2) !== 'stocktake')
                                    <td>
                                        <button class="btn btn-md btn-danger delete-button" data-product=""
                                                style="margin-top: 24px;"><i class="fa fa-trash"></i></button>
                                    </td>
                                @endif
                            </tr>
                            </tbody>
                        </table>
                        <div class="row">
                            <hr/>
                            <div class="col-sm-6">
                                <button class="btn btn-block btn-warning btn-sm" id="save-button">Save</button>
                            </div>
                            <div class="col-sm-6">
                                <button class="btn btn-block btn-success btn-sm createButtons"
                                        id="createButton">{{$meta['button_create']}}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    @include('modules.stock.modals.confirm-stock')
@stop


@push('scripts')
    <script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-autocomplete/1.0.7/jquery.auto-complete.min.js"></script>
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/jquery-autocomplete/1.0.7/jquery.auto-complete.min.css">

    <script>
        let permissions = {};
        permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
        permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
        permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};

        let products = {!! $products !!};
        let subdepartments = {!! $subdepartments !!};


        let module = '{{Request::segment(2)}}';

        let stock_date = moment($('#stocktake_date').val(), 'DD/MM/YYYY H:mm:ss').format('YYYY-MM-DD H:mm:ss');

        let productAddCount = 0;
        let productAddCurrent = 0;

        $('body').on('click', '.add-product-button', function (e) {
            e.preventDefault();

            var ref = $('#product_data').val();

            productAddCount = _.size(ref);
            productAddCurrent = 0;
            if (productAddCount == 0) {
                return;
            }

            loading('#product-area', 'Products loading, please wait...');

            _.forEach(ref, function (value) {
                setTimeout(function (){
                    newRow('product', value);
                }, 100);
            });

            clearSelection('products');

            $('#product-dropdown option:selected').each(function () {
                $(this).prop('selected', false);
            })
            $('#product-dropdown').multiselect('refresh');

        })

        $('body').on('click', '.add-supplier-button', function (e) {
            e.preventDefault();
            var ref = $('#supplier_data').val();

            productAddCount = countSelectedProducts(ref, 'supplier_guid');
            productAddCurrent = 0;

            if (productAddCount == 0) {
                return;
            }
            loading('#product-area', 'Products loading, please wait...');

            _.forEach(ref, function (value) {
                setTimeout(function (){
                    newRow('supplier', value);
                }, 100);
            });

            clearSelection('suppliers');

            $('#supplier-dropdown option:selected').each(function () {
                $(this).prop('selected', false);
            })
            $('#supplier-dropdown').multiselect('refresh');
        })

        $('body').on('click', '.add-department-button', function (e) {
            e.preventDefault();
            var ref = $('#department_data').val();

            productAddCount = countSelectedProducts(ref, 'department_guid');
            productAddCurrent = 0;

            if (productAddCount == 0) {
                return;
            }
            loading('#product-area', 'Products loading, please wait...');

            _.forEach(ref, function (value) {
                setTimeout(function (){
                    newRow('department', value);
                }, 100);
            });

            clearSelection('departments');

            $('#department-dropdown option:selected').each(function () {
                $(this).prop('selected', false);
            })
            $('#department-dropdown').multiselect('refresh');
        })

        $('body').on('click', '.add-sub-department-button', function (e) {
            e.preventDefault();
            var ref = $('#subdepartment_data').val();

            productAddCount = countSelectedProducts(ref, 'sub_department_guid');
            productAddCurrent = 0;

            if (productAddCount == 0) {
                return;
            }
            loading('#product-area', 'Products loading, please wait...');


            _.forEach(ref, function (value) {
                setTimeout(function (){
                    newRow('subdepartment', value);
                }, 100);
            });


            clearSelection('subdepartments');

            $('#subdepartment-dropdown option:selected').each(function () {
                $(this).prop('selected', false);
            })
            $('#subdepartment-dropdown').multiselect('refresh');
        })

        function countSelectedProducts(ref, keyColumnName) {
            var count = 0;
            _.forEach(ref, function (value) {
                var itemData = _.filter(products, {[keyColumnName]: value});
                count += _.size(itemData);
            });
            return count;
        }

        function clearSelection(divClass) {
            var node = $('.' + divClass);
            $(node).find('ul li.active').each(function () {
                $(this).removeClass('active');
                $(this).find('input').each(function () {
                    $(this).prop('checked', false);
                });
            });
            $(node).find('button.multiselect').prop('title', 'None selected');

            $(node).find('span.multiselect-selected-text').text('None selected');
        }

        var details = {!! json_encode($details) !!};
        var transactions = {!! json_encode($transactions) !!};

        @if(Request::segment(2) !== 'stocktake')


        let editItemCount = _.size(transactions);

        let currentEditItem = 0;

        loading('#product-area', 'Loading products, please wait....')
        loadEditRows();

        function loadEditRows()
        {
            _.each(transactions, function(transaction){
                product = products[transaction.product_id];

                var rowExists = $('tr[data-product="' + transaction.product_id + '"]').length;
                if (rowExists == 0) {

                    $('#message_loading').text('Loading Products ' + currentEditItem + '/' + editItemCount)
                    createEditRow(product, transaction)
                    currentEditItem++;
                    if (currentEditItem >= editItemCount) {
                        $('#loading').remove()

                        if(module !== 'stocktake') {

                            reOrderProducts();
                        }
                    }

                }
            });
        }

        @endif

        var row = 0;

        @if(Request::segment(2) === 'stocktake')
        loading('#product-area', 'Loading products, please wait....')

        getProductsCurrentStock();
        let current = 0;
        let count = 0;
        let productCurrentStockList = [];

        function getProductsCurrentStock() {
            // const start = Date.now();

            let subDeptCount = _.size(subdepartments);
            let currentSub = 1;
            let zeroPrioritySub = 101.00;
            subdepartments = $.map(subdepartments, function(value, index) { return [value]; });

            subdepartments.sort(function (a, b) {
                return a.stock_order - b.stock_order * -1;
            })

            _.each(subdepartments, function (subdepartment) {
                setTimeout(function(){
                    let productIdList = [];
                    let subProducts = _.filter(products, {'sub_department_guid': subdepartment.guid})
                    _.each(subProducts, function (product) {
                        productIdList.push(product.id)
                    })
                    if (productIdList.length > 0) {

                        $('#message_loading').text('Loading Products - ' + subdepartment.displayname);
                        if (parseFloat(subdepartment.stock_order) === 0.00) {
                            subdepartment.stock_order = (zeroPrioritySub).toString();
                            zeroPrioritySub++;
                            currentSub++;
                            addRowsForSubdepartment(subdepartment);
                        } else {
                            addRowsForSubdepartment(subdepartment);
                            currentSub++;
                        }

                        if (currentSub > subDeptCount) {
                            $('#loading').remove();
                        }
                    } else {
                        subDeptCount--;
                        if (currentSub >= subDeptCount) {
                            $('#loading').remove();
                        }
                    }
                }, 10)

            });
        }

        @endif

        function reOrderSubDepartments() {
            const start = Date.now();
            let $wrapper = $('#OrderTable tbody');

            $wrapper.find("tr[data-priority!='0.00']").sort(function (a, b) {
                return +a.dataset.priority - +b.dataset.priority;
            }).appendTo($wrapper);

            _.each(subdepartments, function (subdepartment) {

                $('.item-record[data-subdepartment="' + subdepartment.guid + '"]').sort(function (a, b) {
                    let A = $(a).find('input.displayname').val();
                    let B = $(b).find('input.displayname').val();
                    return (A < B) ? -1 : (A > B) ? 1 : 0;
                }).insertAfter('.subheading.' + subdepartment.guid);
            })
            const stop = Date.now();
        }

        function addRowsForSubdepartment(subdepartment) {
            let first = 1;
            let subProducts = _.filter(products, {'sub_department_guid': subdepartment.guid});
            subProducts.sort(function (a, b) {
                return a.displayname.localeCompare(b.displayname) * -1;
            });

            if (first === 1) {
                $('tbody tr').first().after('<tr class="subheading ' + subdepartment.guid + '" data-priority="' + subdepartment.stock_order + '"><td colspan="11" class="' + subdepartment.guid + '"><h2>' + subdepartment.displayname + '</h2></td></tr>');
                first = 0;
            }

            _.each(subProducts, function (prod) {
                if (prod.sku_guid != null) {
                    let transaction = _.find(transactions, {'product_id': prod.id})
                    var rowExists = $('tr[data-product="' + prod.id + '"]').length;

                    if (rowExists == 0) {
                        let prio = subdepartment.stock_order.toString().split('.')

                        prod.priority = prio[0] + '.' + prod.priority
                        createEditRow(prod, transaction, subdepartment.guid)
                    }
                }
            });
        }

        let xitemList = [];
        let xitemCount = 0;
        let xcurrentItem = 0;
        let productIndex = 100;
        let categoryIndex = 100;

        function newRow(type, ref) {
            xitemList = [];
            xitemCount = 0;
            xcurrentItem = 0;

            if (ref.length === 0) {
                notificationBar('error', 'Please choose a product, department or supplier to add to your order');
            }

            let keyColumnName = 'guid';
            switch (type) {
                case 'department':
                    keyColumnName = 'department_guid'
                    break;
                case 'subdepartment':
                    keyColumnName = 'sub_department_guid'
                    break;
                case 'supplier':
                    keyColumnName = 'supplier_guid'
                    break;
                default:
                    keyColumnName = 'guid';
                    break;
            }
            if(typeof ref === "string"){
                ref = [ref];
            }
            _.each(ref, function (guid, index) {
                let productList = _.filter(products, {[keyColumnName]: guid});

                productList = _.sortBy(productList, 'displayname');
                _.each(productList, function(product, oin){
                    product.priority = categoryIndex+'.'+productIndex;
                    productIndex++;
                    xitemList.push(product);
                })
                categoryIndex = categoryIndex+100;
            })

            xitemCount = xitemList.length
            processAddRows()
            row++
        }

        function processAddRows()
        {
            let product = xitemList[xcurrentItem];
            let rowExists = $('tr[data-product="' + product.id + '"]').length;
            if (product.sku_guid != null && rowExists === 0) {
                createRow(product);
                updateRow(product);

                $('#message_loading').text('Loading Products ' + xcurrentItem + '/' + xitemCount)
                if (xcurrentItem >= xitemCount) {
                    $('#loading').remove()
                    if(module !== 'stocktake') {
                        reOrderProducts();
                    }
                } else {
                    processAddRows()
                }

            } else {
                xcurrentItem++;
                if (xcurrentItem >= xitemCount) {
                    $('#loading').remove()
                    xcurrentItem = 0;
                    xitemList = [];
                    if(module !== 'stocktake') {
                        reOrderProducts();
                    }
                } else {
                    processAddRows()
                }
            }

        }

        $('body').on('keyup, change', '.quantity', function (e) {
            var id = $(this).data('product');

            if ($(this).val() == '') {
                $(this).val('0.00')
            }

            if (e.which !== 9 || e.which !== 13) {

                var costprice = $(this).data('price');
                var quantity = $(this).val();
                var stockQuantity = $('[data-product="' + id + '"].sku option:selected').data('qty');

                var units = (stockQuantity * quantity);

                // current stock value as shown in the view
                var current_stock = $('[data-product="' + id + '"].current_stock').val();

                @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']))
                // Calculate the variance between the current and counted

                var variance = difference(parseFloat(current_stock), parseFloat(units));

                if(variance === undefined) {
                    variance = 0;
                }

                if (variance < 0) {
                    $('[data-product="' + id + '"].variance').addClass('negative')
                    $('[data-product="' + id + '"].variance').removeClass('positive')
                    $('[data-product="' + id + '"].variance').removeClass('neutral')
                }
                if (variance > 0) {
                    $('[data-product="' + id + '"].variance').addClass('positive')
                    $('[data-product="' + id + '"].variance').removeClass('negative')
                    $('[data-product="' + id + '"].variance').removeClass('neutral')
                }
                if (variance == 0) {
                    $('[data-product="' + id + '"].variance').removeClass('positive')
                    $('[data-product="' + id + '"].variance').removeClass('negative')
                    $('[data-product="' + id + '"].variance').addClass('neutral')
                }

                var varianceTotal = variance > 0 ? Math.abs(variance * costprice) : (Math.abs(variance * costprice) * -1);
                $('[data-product="' + id + '"].variance').val(variance.toFixed(2))
                $('[data-product="' + id + '"].variance_total').val(varianceTotal.toFixed(2))
                @endif

                var units = (stockQuantity * quantity);
                var total = (costprice * units);

                $('[data-product="' + id + '"].unitQuantity').val(units.toFixed(2))
                $('[data-product="' + id + '"].stock_value').val(total.toFixed(2))

            }
        })

        $('body').on('keyup, change', '.unitQuantity', function (e) {
            if (e.which !== 9 || e.which !== 13) {
                var id = $(this).data('product');

                if ($(this).val() == '') {
                    $(this).val('0.00')
                }

                var costprice = $(this).data('price');
                var quantity = parseFloat($(this).val());
                var stockQuantity = $('[data-product="' + id + '"].sku option:selected').data('qty');
                var stockUnits = (quantity / stockQuantity);
                var total = (costprice * quantity);

                // current stock value as shown in the view
                var current_stock = $('[data-product="' + id + '"].current_stock').val();

                @if(in_array(Request::segment(2), ['stocktake', 'spotchecks']))

                    var variance = difference(current_stock, quantity);

                    if(variance === undefined) {
                        variance = 0;
                    }

                    if (variance < 0) {
                        $('[data-product="' + id + '"].variance').addClass('negative')
                        $('[data-product="' + id + '"].variance').removeClass('positive')
                        $('[data-product="' + id + '"].variance').removeClass('neutral')
                    }
                    if (variance > 0) {
                        $('[data-product="' + id + '"].variance').addClass('positive')
                        $('[data-product="' + id + '"].variance').removeClass('negative')
                        $('[data-product="' + id + '"].variance').removeClass('neutral')
                    }
                    if (variance == 0) {
                        $('[data-product="' + id + '"].variance').removeClass('positive')
                        $('[data-product="' + id + '"].variance').removeClass('negative')
                        $('[data-product="' + id + '"].variance').addClass('neutral')
                    }

                    var varianceTotal = variance > 0 ? parseFloat(Math.abs(variance * costprice)) : parseFloat(Math.abs(variance * costprice) * -1);
                    $('[data-product="' + id + '"].variance').val(variance.toFixed(2))
                    $('[data-product="' + id + '"].variance_total').val(varianceTotal.toFixed(2))

                @endif

                $('[data-product="' + id + '"].quantity').val(stockUnits.toFixed(2))
                $('[data-product="' + id + '"].stock_value').val(total.toFixed(2))

            }
        })

        function updateRow(itemData) {
            const current_stock = itemData.current_stock != null ? itemData.current_stock : 0;
            const variance = (current_stock * -1);
            const varianceTotal = variance > 0 ? Math.abs(variance * itemData.costprice) : (Math.abs(variance * itemData.costprice) * -1);

            let tr = $(`tr.item-record[data-product='${itemData.id}']`);
            if (tr) {
                tr.find('.current_stock').val(current_stock);
                tr.find('.variance_total').val(varianceTotal.toFixed(2));
                tr.find('.variance')
                    .val(current_stock * -1)
                    .removeClass('negative')
                    .removeClass('positive')
                    .removeClass('neutral')
                    .addClass(variance < 0 ? 'negative' : variance > 0 ? 'positive' : 'neutral');
            }
        }

        function createRow(itemData) {
            var $tr = $('tbody tr').first();
            var $clone = $tr.clone();

            var current_stock = itemData.current_stock != null ? itemData.current_stock : 0;
            var skuPrice = (itemData.qty * itemData.costprice).toFixed(2)
            var variance = (parseFloat(current_stock) * -1)

            var varianceTotal = variance > 0 ? Math.abs(variance * itemData.costprice) : (Math.abs(variance * itemData.costprice) * -1).toFixed(2);
            varianceTotal = parseFloat(varianceTotal)

            $clone.removeClass('copy-example');
            $clone.addClass('item-record');
            $clone.find('.plu').val(itemData.id);
            $clone.attr('data-priority', itemData.priority);
            $clone.find('.priority').val(itemData.priority);
            $clone.find('.displayname').val(itemData.displayname + ' (' + itemData.sku.displayname + ')');
            $clone.find('input').attr('id', 'r' + row);
            $clone.find('.min_stock').val(itemData.min_stock);
            $clone.find('.max_stock').val(itemData.max_stock);
            $clone.find('.current_stock').val(current_stock);
            $clone.find('.skuPrice').val(skuPrice);
            $clone.attr('data-product', itemData.id);
            $clone.find('input, select, button').attr('data-product', itemData.id);
            $clone.find('.quantity').attr('data-price', itemData.costprice);
            $clone.find('.unitQuantity').attr('data-price', itemData.costprice);
            $clone.find('.variance_total').val(varianceTotal.toFixed(2));
            $clone.find('.stock_value').val(0);
            $clone.css('display', '');

            var $lastRow = $('tbody#order-table-body tr').last();
            $lastRow.after($clone);

            $('.sku[data-product="' + itemData.id + '"]').val(itemData.sku_guid);

            if (variance < 0) {
                $clone.find('.variance').addClass('negative')
                $clone.find('.variance').removeClass('positive')
                $clone.find('.variance').removeClass('neutral')
            }

            if (variance > 0) {
                $clone.find('.variance').addClass('positive')
                $clone.find('.variance').removeClass('negative')
                $clone.find('.variance').removeClass('neutral')
            }

            if (variance == 0) {
                $clone.find('.variance').removeClass('positive')
                $clone.find('.variance').removeClass('negative')
                $clone.find('.variance').addClass('neutral')
            }

            $('.supplier[data-product="' + itemData.id + '"]').val(itemData.supplier_guid);
        }

        function createEditRow(itemData, transaction, class_guid) {
            if (transaction) {
                var $tr = $('tbody tr').first();
                var $clone = $tr.clone();

                var current_stock = itemData.current_stock != null ? itemData.current_stock : 0;
                var skuPrice = (transaction.quantity * itemData.costprice).toFixed(2);
                var variance = difference(current_stock, transaction.qty_entered);
                if (variance != undefined) {
                    variance = variance.toFixed(2);
                } else {
                    variance = 0
                }
                var variance_total = transaction.variance_cost;

                if (transaction.qty_entered > 0) {
                    var quantity = ((Math.abs(transaction.qty_entered) / itemData.sku.qty)).toFixed(2);
                } else {
                    var quantity = (((Math.abs(transaction.qty_entered) / itemData.sku.qty)).toFixed(2) * -1);
                }

                $clone.removeClass('copy-example');
                $clone.addClass('item-record');
                $clone.attr('data-priority', transaction.priority);
                $clone.find('.priority').val(transaction.priority);
                $clone.find('.plu').val(itemData.id);
                $clone.find('.displayname').val(itemData.displayname + ' (' + itemData.sku.displayname + ')');
                $clone.find('input').attr('id', 'r' + row);
                $clone.find('.min_stock').val(itemData.min_stock);
                $clone.find('.max_stock').val(itemData.max_stock);
                $clone.find('.current_stock').val(current_stock);
                $clone.find('.skuPrice').val(skuPrice);
                $clone.attr('data-product', itemData.id);
                $clone.attr('data-subdepartment', itemData.sub_department_guid);
                $clone.attr('data-supplier', itemData.supplier_guid);
                $clone.attr('data-department', itemData.department_guid);
                $clone.find('input, select, button').attr('data-product', itemData.id);
                $clone.find('.quantity').attr('data-price', itemData.costprice);
                $clone.find('.unitQuantity').attr('data-price', itemData.costprice);
                $clone.find('.quantity').val(quantity);
                $clone.find('.unitQuantity').val(parseFloat(transaction.quantity));
                $clone.find('.reason').val(transaction.reason);
                $clone.find('.stock_value').val(0);
                $clone.find('.variance').val(variance)

                if (variance < 0) {
                    $clone.find('.variance').addClass('negative')
                    $clone.find('.variance').removeClass('positive')
                    $clone.find('.variance').removeClass('neutral')
                }
                if (variance > 0) {
                    $clone.find('.variance').addClass('positive')
                    $clone.find('.variance').removeClass('negative')
                    $clone.find('.variance').removeClass('neutral')
                }

                if (variance == 0) {
                    $clone.find('.variance').removeClass('positive')
                    $clone.find('.variance').removeClass('negative')
                    $clone.find('.variance').addClass('neutral')
                }
                $clone.find('.variance_total').val(variance_total);
                $clone.css('display', '');

                $clone.find('.quantity').val(quantity);
                $clone.find('.unitQuantity').val(transaction.qty_entered);

                if (class_guid) {
                    $('.' + class_guid).closest('tr').after($clone);
                } else {
                    $tr.after($clone);
                }
                $('.sku[data-product="' + itemData.id + '"]').val(itemData.sku_guid);
                $('.supplier[data-product="' + itemData.id + '"]').val(itemData.supplier_guid);
            }
        }

        function reOrderProducts() {
            let $wrapper = $('#OrderTable tbody');

            $wrapper.find("tr.item-record").sort(function (a, b) {

                let aParts = a.dataset.priority.split(".", 2);
                let bParts = b.dataset.priority.split(".", 2);

                let aNum = parseInt(aParts[0]+'0'+aParts[1]);
                let bNum = parseInt(bParts[0]+'0'+bParts[1]);

                return +aNum - +bNum;

            }).appendTo($wrapper);

            setTimeout(function(){
                let currentPriority = $('tr.item-record').last().data('priority').toString().split('.', 2);
                productIndex = parseInt(currentPriority[1]) + 1;
                categoryIndex = parseInt(currentPriority[0]) + 100;
            }, 500)

        }

        $('body').on('click', '.delete-button', function () {
            var id = $(this).data('product');
            $('tr[data-product="' + id + '"]').remove()
        });

        $('body').on('change, blur, keyup', '.quantity', function () {

            var quantity = $(this).val();
            var productId = $(this).data('product');
            var skuPrice = $('[data-product="' + productId + '"].skuPrice').val();
            var price = skuPrice * quantity;

            $('.price[data-product="' + productId + '"]').val(price.toFixed(2));
        });

        $('body').on('change, blur', '.skuPrice', function () {

            var productId = $(this).data('product');
            var quantity = $('[data-product="' + productId + '"].quantity').val();
            var skuPrice = +$(this).val();
            $(this).val(skuPrice.toFixed(2));
            var price = skuPrice * quantity;

            $('.price[data-product="' + productId + '"]').val(price.toFixed(2))
        });

        var status = 0;

        $("form").submit(function (e) {
            e.preventDefault();

            var $input = $('input.quantity, input.unitQuantity, input.stock_value');

            if (e.which != 9 && e.which != 13) {
                var form = this;
                var errors = false

                _.each($input, function (input) {
                    if ($(input).val() == '') {
                        notificationBar('error', 'All fields must have a value');
                        $(input).focus();
                        errors = true;
                        return false;
                    }
                });

                if (!errors) {

                    $('#ConfirmStockModal .modal-body').html('If you would like to save your stock action for later, press <strong>save</strong> below. If you have completed your stock action' +
                        ' please press <strong>Complete</strong>. <br><br>(completed stock take can be reopened later)')

                    let newHtml = '<button type="button" class="btn btn-danger pull-left" data-dismiss="modal">Cancel</button>' +
                        '<button type="submit" class="btn btn-warning" id="save-modal-button">Save</button>';

                    var companySpotcheckOption = {{Auth::user()->company->spotchecks_affect_stock != '' ? Auth::user()->company->spotchecks_affect_stock : 0 }};

                    if (module !== 'spotchecks') {
                        newHtml = newHtml + '<button type="submit" class="btn btn-success" id="confirm-modal-button">Complete</button>';
                    }
                    if (module === 'spotchecks' && companySpotcheckOption === 0) {
                        newHtml = newHtml + '<button type="submit" class="btn btn-success" id="confirm-modal-button">Complete</button>';
                    }

                    $('#ConfirmStockModal .modal-footer').html(newHtml)

                    $('#ConfirmStockModal').modal('show');

                }
            }
        });

        $('body').on('click', '#existing-order-items li', function () {
            var id = $(this).data('product');
            $('tr.item-record[data-product="' + id + '"]').remove()
            $(this).remove();
        })

        $(document).on('click', '#confirm-modal-button', function () {
            if (module === 'stocktake') {
                $('#ConfirmStockModal .modal-body').html('<div class="alert alert-danger text-center"><h3>Are you sure?</h3><br/>' +
                    'Once complete, this stocktake can not be modified without reopening.</strong></div>')

                $('#ConfirmStockModal .modal-footer').html('<button type="button" class="btn btn-danger pull-left" data-dismiss="modal">Cancel</button>' +
                    '<button type="submit" class="btn btn-success" id="confirm-stocktake-button">Complete</button>')

            } else {
                complete(false)
            }
        })

        $(document).on('click', '#confirm-stocktake-button', function () {
            complete(false);
        });

        function complete(reset) {
            notificationBar('info', 'Saving your ' + module + ', please wait!', undefined, 'loading', true);

            $.ajax({
                type: "post",
                url: "/stock/" + module + "/edit/{{Request::segment(4)}}?site_num={{Request::input('site_num')}}&status=4&reset=" + reset,
                data: $('form').serialize(),
                success: function (data) {
                    $('#ConfirmStockModal').modal('hide');
                    notificationBar(data.status, data.message, '/stock/' + module)
                }
            }).fail(function (data) {
                $('#ConfirmStockModal').modal('hide');
                notificationBar(data.status, data.message)
            });
        }

        $(document).on('click', '#save-modal-button', function () {
            notificationBar('info', 'Saving your ' + module + ', please wait!', undefined, 'loading', true);
            $.ajax({
                type: "post",
                url: "/stock/" + module + "/edit/{{Request::segment(4)}}?site_num={{Request::input('site_num')}}&status=0",
                data: $('form').serialize(),
                success: function (data) {
                    $('#ConfirmStockTakeModal').modal('hide');
                    notificationBar(data.status, data.message)
                }
            }).fail(function (data) {
                $('#ConfirmStockTakeModal').modal('hide');
                notificationBar(data.status, data.message)
            });
        })

        $(document).on('click', '#save-button', function (e) {
            notificationBar('info', 'Saving', undefined, 'loading', true);
            e.preventDefault();
            $.ajax({
                type: "post",
                url: "/stock/" + module + "/edit/{{Request::segment(4)}}?site_num={{Request::input('site_num')}}&status=0",
                data: $('form').serialize(),
                success: function (data) {
                    notificationBar(data.status, data.message)
                }
            }).fail(function (data) {
                notificationBar(data.status, data.message)
            });
        })

        function switchAddTab(tab) {
            $('[data-type=add-tab]').hide();
            $('[data-type=add-tab].' + tab).show();

        }

        function difference(current_stock, new_stock) {

            current_stock = parseFloat(current_stock)
            new_stock = parseFloat(new_stock)

            if (current_stock < new_stock) {
                return Math.abs(current_stock - new_stock);
            }
            if (current_stock > new_stock) {
                return (Math.abs(current_stock - new_stock) * -1);
            }
            if (current_stock == new_stock) {
                return 0;
            }
        }

        $(document).ready(function () {
            $('#stock-form .multiselect').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true
            });
        });

        $(document).on('focus', '.quantity, .unitQuantity', function (e) {
            $(this).select();
        });

        $(document).on('keydown', '.quantity', function (e) {
            var keyCode = e.keyCode || e.which;

            if (keyCode == 9) {
                e.preventDefault();
                var element = $(this).parent().parent().next().find('.quantity').focus()
            }
        });

        $(document).on('keydown', '.unitQuantity', function (e) {
            var keyCode = e.keyCode || e.which;

            if (keyCode == 9) {
                e.preventDefault();
                var element = $(this).parent().parent().next().find('.unitQuantity').focus()
            }
        });


    </script>
@endpush
