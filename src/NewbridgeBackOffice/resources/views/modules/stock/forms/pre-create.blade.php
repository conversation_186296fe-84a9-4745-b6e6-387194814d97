@extends('layouts.master')

@section('section-name') Stock - {{ucfirst(Request::segment(2))}} @stop

@section('content')

    <style>
        #OrderTable label.error {
            display: none;
        }

        .prefix {
            margin-right: 10px;
            position: absolute;
            margin-top: 7px;
            font-size: 14px;
        }

        .my-input {
            padding-left: 5px;
        }

        input:read-only.negative {
            background: red !important;
            color: white !important;
        }

        input:read-only.positive {
            background: lightgreen !important;
            color: black !important;
        }

        input:read-only.neutral {
            background: gold !important;
            color: black !important;
        }
    </style>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">{{$meta['title_create']}}<br/>
                <small> @if(\Session::get('current_site') != 0) {{\NewbridgeWeb\Http\Helpers\SiteHelper::showSiteName(\Session::get('current_site'))}} @endif</small>
            </h1>
        </div>
    </div>
    <form id="pre-stock-form" action="/stock/stocktake/pre-create" method="POST">
        {{ csrf_field() }}
        <div class="row">
            <div class="col-sm-12">
                <div class="panel panel-default" id="meta">
                    <div class="panel-heading">
                        {{$meta['title_create']}} Details
                    </div>
                    <div class="panel-body">
                        <input type="hidden" name="site" id="site" value="{{\Session::get('current_site')}}">
                        <div class='form-group col-sm-12'>
                            <label><strong>{{$meta['title_list']}} Date</strong></label>
                            <div class='input-group date' id='datetimepicker1'>
                                <input type='text' class="form-control" id="stocktake_date" name="stocktake_date"
                                       value="{{$date->format('d/m/Y H:i:s')}}"/>
                                <span class="input-group-addon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>
                        <input type="hidden" name="notes">
                        <script type="text/javascript">;
                            $(function () {
                                $('#datetimepicker1').datetimepicker({
                                    format: 'DD/MM/YYYY H:mm:ss',
                                    useCurrent: true
                                });
                            });
                        </script>
                    </div>
                </div>
            </div>

            <div class="col-sm-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Start Stock Take
                    </div>
                    <div class="panel-body">
                        <div class="form-group col-sm-12">
                            <div id="products">
                                <div class="row">
                                    <div class="col-lg-12 all" data-type="add-tab">
                                        <button class="btn btn-success btn-block add-allproducts-button addButton" id="beginButton" type="submit"
                                             style="margin-top: 10px;">Begin Stock Take
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>



@stop


@push('scripts')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/css/bootstrap-multiselect.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-autocomplete/1.0.7/jquery.auto-complete.min.js"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/tether/1.4.0/js/tether.min.js"></script>
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/css/shepherd-theme-arrows.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/shepherd/1.8.1/js/shepherd.min.js"></script>
    <script>
        var permissions = {};
        permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
        permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
        permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,stock', '') == 1 ? 1 : 0 }};
        var module = '{{Request::segment(2)}}';
        let mode = '{{Request::segment(3)}}';
    </script>

    <script>
        $('#beginButton').on('click', function(e){
            let loader = notificationBar('info', 'Creating Stocktake', undefined, 'loading', true);
            $('#pre-stock-form').submit();
        })
    </script>

    <style>
        .shepherd-max-z {
            z-index: 999999999;
        }
    </style>


@endpush
