@extends('layouts.master')

@section('content')

    <div class="row col-md-12" style="padding-bottom: 20px">
        <h3>Booking Details</h3>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    customer Email
                </div>
                <div class="panel-body">
                    <label><strong>Email Address</strong></label>
                    <input type="text" class="form-control" maxlength="255" email name="emailChecks" placeholder="<EMAIL>"/>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-xs-2 pull-right text-right">
                            <button class="btn btn-success" id="emailCheck">Check</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form id="CreateBookingForm" style="display: none">
        {{ csrf_field() }}
    <div id="booking-form-area">

    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Booking Information
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label><strong>Arrival Date</strong></label><br>
                                    <input type="text" name="arrival_date" class="form-control" value="{{$bookingData['booking_details']['arrival_date']}}" readonly/>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label><strong>Duration Day(s)</strong></label><br>
                                    <input type="text" name="duration" class="form-control" value="{{$bookingData['booking_details']['duration']}}" readonly/>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label><strong>Location</strong></label><br>
                                    <input type="text" name="location" class="form-control" value="{{$bookingData['asset_group']['name']}}" readonly/>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label><strong>No. of Guests</strong></label><br>
                                    <input type="text" name="no_guests" class="form-control" value="{{$bookingData['booking_details']['num_guests']}}" readonly />
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12">
                        </div>
                    </div>
                </div>
                <div class="panel-footer">
                    <div class="row">
                        <div class="col-xs-2 pull-right text-right">
                            <button class="btn btn-success" id="createBooking">Create</button>
                            <input type="hidden" name="asset_group_id" value="{{$bookingData['asset_group']['id']}}">
                            <input type="hidden" name="site_num" value="{{$bookingData['asset_group']['site_num']}}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>




@endsection

@section('js')
    @parent

    <script>

        $('#emailCheck').on('click', function(e){
            e.preventDefault()

            let emailCheck = $("input[name=emailChecks]").val();

            $.ajax({
                type: "POST",
                url: "/booking/emailCheck/"+ emailCheck,
                success: function (data) {
                    $('#booking-form-area').html(data.html)
                    $('#CreateBookingForm').show();
                    // $('#booking-form-area').html(data.html)
                    // notificationBar('error', 'No Customer exists please create one below')
                },
                error: function () {
                }
            });
        });

        $('#createBooking').on('click', function(e){
            e.preventDefault()
            $('#CreateBookingForm').submit()
        });

        let dateForm = $('#CreateBookingForm')

        // validate signup form on keyup and submit
        $("#CreateBookingForm").validate({
            rules: {
                prefix: "required",
                first_name: "required",
                last_name: "required",
                email: "required",
                telephone: "required",
                address_line1: "required",
                address_line2: "required",
                city: "required",
                county: "required",
                postcode: "required",
                arrival_date: "required",

            },
            submitHandler: function (dateForm) {
                $.ajax({
                    type: "POST",
                    url: "/booking/create",
                    data: $(dateForm).serialize(),
                    success: function (data) {
                       notificationBar(data.status,data.message,'/booking',data.title);

                    },
                    error: function () {
                       // notificationBar('error', 'No available assets found with these options!')
                    }
                });
            }
        });




    </script>

@endsection
