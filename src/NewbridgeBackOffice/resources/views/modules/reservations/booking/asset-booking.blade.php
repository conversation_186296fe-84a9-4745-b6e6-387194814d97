@extends('layouts.master')

@section('content')

    <div class="row col-md-12" style="padding-bottom: 20px">
        <h3>Asset Booking</h3>
    </div>
    <form id="DataSelectionForm" action="" method="POST">
        {{ csrf_field() }}
        <div class="col-lg-12">
            <div class="form-group">
                <div class="panel panel-default">
                    <div class="panel-heading">Select a date</div>
                    <div class="panel-body">
                        <div class="row" style="padding-bottom: 10px;">
                            <div class="col-lg-6">
                                <label><strong>Arrival Date</strong></label>
                                <div class='input-group date' id='date'>
                                    <input type='text' class="form-control" name="arrival_date"
                                           value="{{\Carbon\Carbon::now()->format('d/m/Y')}}">
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-6">
                                <label><strong>Number of Guests</strong></label>
                                <select name="num_guests" id="num_guests" class="form-control">
                                    <option value="">Select Guests</option>
                                    {{$a = 1}}
                                    @while($a <= $maxGuests)
                                        <option value="{{$a}}">{{$a}}</option>
                                        {{$a++}}
                                    @endwhile
                                </select>
                            </div>
                            <div class="col-lg-6">
                                <label><strong>Duration of stay (days)</strong></label>
                                <select name="duration" id="duration" class="form-control">
                                    <option value="">Select duration</option>
                                    {{$b = 1}}
                                    @while($b <= $maxStay)
                                        <option value="{{$b}}">{{$b}}</option>
                                        {{$b++}}
                                    @endwhile
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="panel-footer">
                        <div class="row">
                            <div class="col-xs-2 pull-right">
                                <button class="btn btn-success" id="Submit">submit</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div id="availability-list-html-area"></div>


@endsection

@section('js')
    @parent

    <script>

        let assets = {!! json_encode($assetType) !!};

        $(function () {
            $('.date').datetimepicker({
                format: 'DD/MM/YYYY',
                toolbarPlacement: 'top',
                minDate: moment(carbonDate),
            });

        });


        $('#Submit').on('click', function (e) {
            e.preventDefault()
            $('#DataSelectionForm').submit()
        });

        let dateForm = $('#DataSelectionForm')
        // validate signup form on keyup and submit
        $("#DataSelectionForm").validate({
            rules: {
                date: "required",
                num_guests: "required",
                duration: "required",
            },
            submitHandler: function (dateForm) {

                if ($(dateForm).attr('action') == '') {
                    $.ajax({
                        type: "POST",
                        url: "/booking/data",
                        data: $(dateForm).serialize(),
                        success: function (data) {
                            $('#availability-list-html-area').html(data.html);
                        },
                        error: function () {
                            notificationBar('error', 'No available assets found with these options!', undefined, 'No Available Assets')
                            $('#availability-list-html-area').hide();
                        }
                    });
                } else {
                    dateForm.submit();
                }
            }
        });


        $(document).on('click', '.book', function (e) {
            e.preventDefault()

            let dateForm = $('#DataSelectionForm');
            let assetId = $(this).attr("data-id");
            $(dateForm).attr('action', '/booking/add/' + assetId);
            $(dateForm).submit();
            //
            // $.ajax({
            //     type: "POST",
            //     url: "/booking/add",
            //     data: dateForm.serialize(),
            //     success: function () {
            //
            //     },
            //     fail: function () {
            //
            //     }
            // });

        });


    </script>

@endsection
