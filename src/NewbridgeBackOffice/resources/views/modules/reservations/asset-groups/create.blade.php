@extends('layouts.master')

@section('content')
    {{--    Breadcrumbs --}}
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/asset-group" class="">Asset Groups</a></li>
        <li>Create</li>
    </ol>

    <div class="row col-md-12" style="padding-bottom: 20px">
        <h3>Create Asset Group</h3>
    </div>
    <form id="CreateForm">
        {{ csrf_field() }}
        <div class="col-lg-12">
            <div class="form-group">
                <div class="panel panel-default">
                    <div class="panel-heading">Select a site</div>
                    <div class="panel-body">
                        <select name="site_num" id="site_num" class="form-control">
                            <option value="">Please select a site</option>
                            @foreach($sites as $site)
                                <option value="{{$site->site_num}}">{{$site->site_name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-12">
            <div class="form-group" id="asset-group-info" style="display: none;">
                <div class="panel panel-default">
                    <div class="panel-heading">Asset Group Information</div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Name</strong></label>
                                    <input type="text" class="form-control" name="name"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="form-group">
                                    <label><strong>Description</strong></label>
                                    <input type="text" class="form-control" name="description"/>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label><strong>Minimum Stay (hours)</strong></label>
                                    <input type="number" class="form-control" name="min_stay"/>
                                </div>
                            </div>
                            <div class="col-lg-3">
                                <div class="form-group">
                                    <label><strong>Maximum Stay (hours)</strong></label>
                                    <input type="number" class="form-control" name="max_stay"/>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel-footer">
                        <div class="row">
                            <div class="col-xs-2 pull-right">
                                <button class="btn btn-success" id="Submit">Create</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>


@endsection
@section('js')
    @parent

<script>

    $('body').on('change', '[name="site_num"]', function(e){
        console.log('changed')

        let element = $(this)

        if(element.val() == 0){
            $('#asset-group-info').hide()
        } else {
            $('#asset-group-info').show()
        }
    })

    $('#Submit').on('click', function(e){
        e.preventDefault()
        $('#CreateForm').submit()
    })

    var form = $('#CreateForm')

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        rules: {
            name: "required",
            min_stay: "required",
            max_stay: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/asset-group/add",
                data: $(form).serialize(),
                success: function () {
                    notificationBar('success', 'Asset Group Created!', '/asset-group')
                },
                fail: function () {
                    notificationBar('error', 'There was an error in your entry, please try again.')
                }
            });
        }
    })
</script>
    @endsection
