@extends('layouts.master')

@section('content')
    <script>
         let table = null;
    </script>

    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Asset Groups</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Create and Manage Asset Groups<br />
                    <small>Click in the table to instantly edit records shown, use the search boxes to filter the list.</small>
                </div>
                <div class="panel-body">
                    <table class="table table-bordered table-striped table-responsive" id="table" width="100%">
                        <thead>
                        <tr>
                            <th>Group ID</th>
                            <th>Group Name</th>
                            <th>Group Description</th>
                            <th>Site Name</th>
                            <th>Asset Count</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>


@stop

@push('scripts')
    <script src="https://cdn.datatables.net/1.10.13/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.4/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/select/1.2.1/js/dataTables.select.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="https://cdn.datatables.net/keytable/2.2.0/js/dataTables.keyTable.min.js"></script>
    <script src="/js/dataTables.editor.min.js"></script>

    <script>
        var permissions = {};
        permissions.can_edit = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'edit_sites') == 1 ? 1 : 0 }};
        permissions.can_add = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'add_sites') == 1 ? 1 : 0 }};
        permissions.can_delete = {{\NewbridgeWeb\Http\Helpers\AbilityHelper::ability('newbridge,owner,reseller', 'delete_sites') == 1 ? 1 : 0 }};
    </script>

    <script src="/js/reservations/asset-groups.js?{{\Carbon\Carbon::now()->timestamp}}"></script>
@endpush
