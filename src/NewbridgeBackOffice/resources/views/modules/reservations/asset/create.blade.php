@extends('layouts.master')

@section('content')
    {{--    Breadcrumbs --}}
    <ol class="breadcrumb" style="width: 100%">
        <li class=""><a href="/"><i class="fa fa-home"></i> Dashboard</a></li>
        <li><a href="/asset-group" class="">Asset Groups</a></li>
        <li><a href="/asset-group/{{Request::segment(2)}}/assets" class="">Asset</a></li>
        <li>Create</li>
    </ol>
    <div class="row col-md-12" style="padding-bottom: 20px">
        <h3>Create Asset</h3>
    </div>
    <form id="CreateForm">
        {{ csrf_field() }}
        <div class="col-lg-12">
            <div class="form-group" id="asset-info">
                <div class="panel panel-primary">
                    <div class="panel-heading">Asset Information</div>
                    <div class="panel-body">
                        <div class="col-lg-6">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label><strong>Name</strong></label>
                                        <input type="text" class="form-control" name="name"/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label><strong>Description</strong></label>
                                        <input type="text" class="form-control" name="description"/>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Asset Number</strong></label>
                                        <input type="number" class="form-control" name="number"/>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label><strong>Maximum Guests</strong></label>
                                        <input type="number" class="form-control" name="max_guests"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group">
                                <lable><strong>Upload Image</strong></lable>
                                <div class="input-group">
                                        <span class="input-group-btn">
                                            <span class="btn btn-default btn-file">
                                                Browse… <input type="file" id="imgInp" name="image"/>
                                            </span>
                                        </span>
                                    <input type="text" class="form-control" readonly>
                                </div>
                                <div class="form-group">
                                    <label><strong>Image Preview</strong></label><br />
                                    <img id='img-upload' style="max-width: 350px;"/>
                                </div>
                                <input type="hidden" value="" id="img-string" name="image-base64" >
                            </div>
                        </div>
                    </div>
                    <div class="panel-footer">
                        <div class="row">
                            <div class="col-xs-2 pull-right text-right">
                                <button class="btn btn-success" id="Submit">Create</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class="clearfix"></div>


@endsection
@section('js')
    @parent

    <script>

        $(document).ready( function() {
            $(document).on('change', '.btn-file :file', function() {
                var input = $(this),
                    label = input.val().replace(/\\/g, '/').replace(/.*\//, '');
                input.trigger('fileselect', [label]);
            });

            $('.btn-file :file').on('fileselect', function(event, label) {

                var input = $(this).parents('.input-group').find(':text'),
                    log = label;

                if( input.length ) {
                    input.val(log);
                } else {
                    if( log ) alert(log);
                }

            });
            function readURL(input) {
                if (input.files && input.files[0]) {
                    var reader = new FileReader();

                    reader.onload = function (e) {
                        $('#img-upload').attr('src', e.target.result);
                        $('#img-string').attr('value', e.target.result);
                    }

                    reader.readAsDataURL(input.files[0]);
                }
            }

            $("#imgInp").change(function(){
                readURL(this);
            });
        });

        $('#Submit').on('click', function(e){
            e.preventDefault()
            $('#CreateForm').submit()
        })

        var form = $('#CreateForm')

        // validate signup form on keyup and submit
        $("#CreateForm").validate({
            rules: {
                name: "required",
                number: "required",
                max_guests: "required"
            },
            submitHandler: function (form) {
                $.ajax({
                    type: "POST",
                    url: "/asset-group/{{Request::segment(2)}}/assets/add",
                    data: $(form).serialize(),
                    success: function () {
                        notificationBar('success', 'Asset Created!', '/asset-group/{{Request::segment(2)}}/assets')
                    },
                    fail: function () {
                        notificationBar('error', 'There was an error in your entry, please try again.')
                    }
                });
            }
        })
    </script>
@endsection
