@if($availability > 0)
    <div class="row" id="email">
        <div class="col-lg-12">
            <label><strong>Email Address</strong></label>
            <input type="text" class="form-control" maxlength="255" email name="emailChecks"
                   placeholder="<EMAIL>"/>
            <br>
        </div>
        <div class="col-xs-2 pull-left text-right">
            <button class="btn btn-success" id="emailCheck">Check</button>
        </div>

    </div>
    <div id="booking-details-modal-area"></div>
    <input type="hidden" class="form-control" name="asset_id" value="{{$asset['id']}}"/>
    <input type="hidden" class="form-control" name="date" value="{{$date}}"/>


    <script>

        $('#emailCheck').on('click', function (e) {
            e.preventDefault()

            let emailCheck = $("input[name=emailChecks]").val();
            let asset_id = $("input[name=asset_id]").val();

            $.ajax({
                type: "POST",
                url: "/booking/emailCheck/modal",
                data: {email: emailCheck, asset: asset_id},
                success: function (data) {
                    $('#booking-details-modal-area').html(data.html)
                    $('#email').hide();

                },
                error: function () {
                    notificationBar('error', 'Please enter an email address!', undefined, 'No email entered!');
                }
            });
        });


    </script>

@else
    <div class="row">
        <div class="col-lg-12">
            <h2>No Availability!</h2>
            There are currently no available slots for today
        </div>
    </div>
@endif

