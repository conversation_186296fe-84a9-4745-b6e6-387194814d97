@extends('layouts.master')

@section('section-name')
    Guestline Migration
@stop

@section('content')

    <section>
        <div class="row" style="margin-bottom:40px;">
            <div class="col-lg-12">
                <h1 class="page-header">Guestline Migration </h1>
            </div>
            <!-- /.col-lg-12 -->
        </div>

        <div class="row" id="loading-area" style="display: none;">
            <div class="col-lg-12">
                <div class="panel panel-primary wizard">
                    <div class="panel-body text-center">
                        <div class="col-md-8 col-md-offset-2">
                            <h2>Running Migration</h2>
                            <div class="progress">
                                <div class="progress-bar progress-bar-danger progress-bar-striped  active" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 1%"></div>
                            </div>
                            <h4 id="loading-text">Starting Guestline Migration...</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="startForm">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <i class="fa fa-file-export"></i>
                    <span>Migration Details</span>
                </div>
                <div class="panel-body">
                    <form>
                        {{ csrf_field() }}
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><strong>Tenant ID</strong></label>
                            <input type="text" class="form-control" name="tenant_id"/>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><strong>API key</strong></label>
                            <input type="text" class="form-control" name="api_key"/>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-group">
                            <label><strong>Site ID</strong></label>
                            <input type="text" class="form-control" name="site_id"/>
                        </div>
                    </div>
                    </form>
                    <div class=" col-xs-12">
                        <button id="startMigration" class="btn btn-success btn-block">Start Migration</button>
                    </div>
                </div>
            </div>
        </div>

    </section>

    <section id="sections">

    </section>

@endsection

@push('scripts')
    <script>
        $(function () {

            let percent = 10;
            let timer = null;
            let checkTime = 5000;
            let migration_guid = null;

            $(document).on('click', '#startMigration', function(){
                createLoader([], ['#startForm'])
                let form = $('form');
                $.ajax({
                    type: "POST",
                    url: "/guestline-migration/start",
                    data: $(form).serialize(),
                    success: function (data) {
                        migration_guid = data.guid
                        checkStatus(migration_guid);
                    },
                    fail: function () {
                        clearTimeout(timer);
                        notificationBar('error', 'Migration Failed');
                    },
                    error: function(data) {
                        clearTimeout(timer);
                        notificationBar('error', data.responseJSON.message)
                        let loader = $('#loading-area');
                        loader.hide();
                        $('#startForm').show();
                    }
                });

            });

            function createLoader(show, hide)
            {

                _.each(show, function(s){
                    $(s).show();
                })
                _.each(hide, function(h){
                    $(h).hide();
                })

                let loader = $('#loading-area');
                loader.show();
                let progressBar = $('#loading-area .progress-bar');
                let progressText = $('#loading-area #loading-text');

                progressBar.show();

                progressText.text('Starting migration, please wait...')

                progressBar.removeClass('progress-bar-warning');
                progressBar.removeClass('progress-bar-success');
                progressBar.removeClass('progress-bar-danger');

                progressBar.addClass('progress-bar-danger');

                progressBar.attr('aria-valuenow', percent);
                progressBar.css('width', 10+'%');
            }

            function updateLoader(percent, text)
            {
                let progressBar = $('#loading-area .progress-bar');
                let progressText = $('#loading-area #loading-text');
                progressText.text(text);
                progressBar.attr('aria-valuenow', percent)
                progressBar.css('width', percent+'%')

                progressBar.removeClass('progress-bar-warning')
                progressBar.removeClass('progress-bar-success')
                progressBar.removeClass('progress-bar-danger')

                if(percent >= 10){
                    progressBar.addClass('progress-bar-danger');
                }

                if(percent > 45){
                    progressBar.addClass('progress-bar-warning');
                }

                if(percent > 80){
                    progressBar.addClass('progress-bar-success');
                }
            }

            function checkStatus(guid)
            {

                $.ajax({
                    type: "GET",
                    url: "/guestline-migration/check-status/"+migration_guid,
                    success: function (data) {
                        clearTimeout(timer);

                        timer = setTimeout(function(){
                            checkStatus(migration_guid);
                        }, checkTime);

                        if (typeof(data.percent) != "undefined"){
                            updateLoader(data.percent, data.current_step)
                        } else {
                            updateLoader(data.data.percent, data.data.current_step)
                        }
                        updateLoader(data.percent, data.current_step)
                        $('#sections').html(data.html)
                    },
                    fail: function () {
                        clearTimeout(timer);
                    }
                });

            }

            timer = setTimeout(function(){
                checkStatus(migration_guid);
            }, checkTime);
        })
    </script>
@endpush