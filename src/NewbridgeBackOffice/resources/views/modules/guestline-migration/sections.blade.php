<div id="startForm">
    <div class="panel panel-primary">
        <div class="panel-heading">
            <i class="fa fa-file-export"></i>
            <span>Migration Details</span>
        </div>
        <div class="panel-body">
            <table class="table table-striped table-condensed">
                <thead>
                    <tr>
                        <th style="width: 50%">Data</th>
                        <th>Quantity</th>
                        <th>Status</th>
                        <th>Errors</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data['sections'] as $k => $section)
                        @if(isset($section['name']))
                        <tr>
                            <td>{{$section['name']}}</td>
                            <td>{{$section['current']}}/{{$section['quantity']}}</td>
                            <td>
                                @if(isset($section['inprogress']) && $section['status'] < 2)
                                   <span class="text-warning" style="font-weight: bold;"> <span class="fa fa-spin fa-spinner"> </span> In Progress </span>
                                @else
                                    @if($section['status'] == 0)
                                        <span class="text-danger">Pending</span>
                                    @endif
                                    @if($section['status'] == 1)
                                        <span class="text-warning">Converted</span>
                                    @endif
                                    @if($section['status'] == 2)
                                        <span class="text-success">Import Complete</span>
                                    @endif
                                @endif
                            </td>
                            <td>{{count($section['errors'])}}</td>
                        </tr>
                        @endif
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
