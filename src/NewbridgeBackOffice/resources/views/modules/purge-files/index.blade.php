@extends('layouts.master')

@section('content')
    <div class="row">
        <div class="row col-md-12" style="padding-top: 30px; padding-bottom: 20px">
            <h4>Purge File Settings</h4>
            <hr/>
        </div>
    </div>
    <form id="EditForm">
        {{ csrf_field() }}
        <div class="row">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    Select a Site
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="form-group col-md-12">
                            <label>Select Site</label><br/>
                            <select name="site" id="site" class="form-control input">
                                <option value="">Please Select a site</option>
                                @foreach($sites as $site)
                                    <option value="{{$site->site_num}}">{{$site->site_name}}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="html-area">

        </div>

            <div class="col-md-6 pull-right">
                <button class="btn btn-md btn-success pull-right" id="button">Save <i class="fa fa-save"></i></button>
            </div>
    </form>

@stop


@section('js')
    @parent

    <script>

        $(document).on('click', '#button', function (e) {
            e.preventDefault();

            let form = $('#EditForm');

            $.ajax({
                type: "POST",
                url: "/purge/update",
                data: $(form).serialize(),
                success: function (data) {
                    notificationBar('success', 'Your changes have been saved!')
                }
            }).fail(function () {

            });

        });

        $(document).on('change', '#site', function (e) {
            e.preventDefault();

            let value = $(this).val();
            let form = $('#EditForm');

            getData(value);

        });

        function getData(site){
            // TODO: Loading Swal

            $.ajax({
                type: "GET",
                url: "/purge/get-form/" + site,
                success: function (data) {
                    $('#html-area').html(data.html)
                }
            }).fail(function () {
                notificationBar('error', 'unable to load data')
            });
        }
        // $(function(){
        //     getData();
        // })

    </script>



@stop