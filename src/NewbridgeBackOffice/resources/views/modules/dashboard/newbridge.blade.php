@extends('layouts.master')

@section('section-name') Dashboard @stop


@section('content')
    <style>

        .chart-legend {
            width: 100%;
        }

        .panel.fixed-height {
            height: 800px;
            overflow: auto;
        }

        .chart-legend span {
            display: inline-block;
            font-size: 9px;
            letter-spacing: 0.5px;
            line-height: 17.5px;
            word-spacing: 0.5px;
            text-align: center;
            float: left;
            color: white;
            padding: 5px;
            margin: 5px;
        }

        .reportrange {
            background-color: white;
            color: black!important;
        }

    </style>


    {{-- Dashboard Page title--}}
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Dashboard @if($manager != null) ({{$manager->name}}) @endif</h1>
        </div>
        <!-- /.col-lg-12 -->
    </div>

    <div class="row">
        <div class=" col-xs-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-12">
                            <div>Update All Results</div>
                        </div>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="col-xs-6">
                        <p>Change the date in this area to update all of the widgets below.</p>
                        <div id="reportrange-all"
                             style=" cursor: pointer; padding: 5px 10px; border: 1px solid #ccc;">
                            <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>&nbsp;
                            <span></span> <b class="caret"></b>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <p>Change the account manager to filter the list of companies.</p>
                        <select name="manager" id="manager" class="multiselect" multiselect="multiselect">
                            <option value="">Select Account Manager</option>
                            @foreach($managers as $manager)
                                <option @if(Request::segment(2) == $manager->id) selected @endif value="{{$manager->id}}">{{$manager->name}}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @ability('newbridge,owner,reseller', 'view_dashboard')
    <div class="row" id="widgets">

        <div class="col-lg-6 col-md-6">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-hashtag fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <div class="huge"></div>
                            <div># of Transactions</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6 col-md-6">
            <div class="panel panel-green">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-gbp fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <div class="huge"></div>
                            <div>Gross Sales</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="table-responsive">
            <table class="table table-condensed table-striped text-center">
                <thead>
                    <tr>
                        <th style="width: 300px; !important;" class="text-left">Company Name</th>
                        <th class="text-center">Stock Control</th>
                        <th class="text-center">Loyalty</th>
                        <th class="text-center">Gift Cards</th>
                        <th class="text-center">Reporting (Scheduled)</th>
                        <th class="text-center">Alarms</th>
                        <th class="text-center">Time & Attendance</th>
                        <th class="text-center">Discounts</th>
                        <th class="text-center">Promotions</th>
                        <th class="text-center">PMS Links</th>
                        <th class="text-center">Paymentsense</th>
                    </tr>
                    <tr>
                        @foreach($totals as $k => $total)

                            <th class="text-center">{{$total}} @if($k != 'blank')/{{$company_count}}@endif</th>

                        @endforeach

                    </tr>
                </thead>
                <tbody>
                @foreach($results as $result)
                    <tr>
                        <td class="text-left"><strong>{{$result['name']}}</strong></td>
                        @foreach($result as $k => $res)
                            @if($k != 'name')
                            <td>@if($res == true) <i class="fa fa-check text-success"></i> @else <i class="fa fa-times text-danger"></i> @endif </td>
                            @endif
                        @endforeach

                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <style>
        table thead tr{
            display:block !important;
        }

        table th,table td{
            width:125px !important;//fixed width
        }


        table  tbody{
            display:block !important;
            height:500px !important;
            overflow:auto !important;//set tbody to auto
        }
    </style>

    @endability

@endsection

@push('scripts')
    <script src="//cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.14/js/bootstrap-multiselect.min.js"></script>
    <script>


        $(function () {

            $('select').not('.swal2-select').multiselect({
                buttonWidth: '100%',
                includeSelectAllOption: true,
                enableCaseInsensitiveFiltering: true,
                onDropdownShown: function(event) {
                    $(event.target).find('.filter input').focus();
                },
                onChange: function(element, checked) {
                    let value = $(element).val();
                    notificationBar('success', 'Switching Account Manager');

                    window.location.href = '/newbridge/'+value;
                }
            })


            var start = moment(carbonDate).startOf('day');
            var end = moment(carbonDate).endOf('day');


            function allcb(start, end) {
                $('#reportrange-all span').html(start.format('YYYY-MM-DD HH:mm:ss') + ' - ' + end.format('YYYY-MM-DD HH:mm:ss'));
            }

            getWidgets(start, end);

            function getWidgets() {
                var parent = '#widgets'

                loading(parent)

                $.ajax({
                    type: "GET",
                    url: "/dashboard/widgets-newbridge",
                    data: {start: start.format('YYYY-MM-DD HH:mm:ss'), end: end.format('YYYY-MM-DD HH:mm:ss')},
                    success: function (data) {
                        $(parent + '#loading').remove()
                        $('#widgets').html(data)
                    },
                    fail: function () {
                        $(parent + '#loading').remove()
                        noData(parent)
                    }
                });
            }

            $('#reportrange-all').daterangepicker({
                startDate: start,
                endDate: end,
                autoApply: true,
                timePicker: true,
                ranges: {
                    'Today': [moment(carbonDate).startOf('day'), moment(carbonDate).endOf('day')],
                    'Yesterday': [moment(carbonDate).subtract(1, 'days').startOf('day'), moment(carbonDate).subtract(1, 'days').endOf('day')],
                    'Last 7 Days': [moment(carbonDate).subtract(6, 'days').startOf('day'), moment(carbonDate).endOf('day')],
                    'Last 30 Days': [moment(carbonDate).subtract(29, 'days').startOf('day'), moment(carbonDate).endOf('day')],
                    'This Month': [moment(carbonDate).startOf('month').startOf('day'), moment(carbonDate).endOf('month').endOf('day')],
                    'Last Month': [moment(carbonDate).subtract(1, 'month').startOf('month').startOf('day'), moment(carbonDate).subtract(1, 'month').endOf('month').endOf('day')]
                },
                format: 'YYYY-MM-DD HH:mm:ss'
            }, allcb);

            $('#reportrange-all').on('apply.daterangepicker', function (ev, picker) {

                start = picker.startDate
                end = picker.endDate

                getWidgets(start, end);

            });

            // $('#manager').on('click', function(e){
            //     e.preventDefault();
            //
            //     let value = $('#manager:selected').val();
            //
            // })


        })



    </script>
@endpush