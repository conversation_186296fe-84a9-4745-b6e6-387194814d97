/*!
FullCalendar Resource Time Grid Plugin v4.2.0
Docs & License: https://fullcalendar.io/scheduler
(c) 2019 Adam Shaw
*/
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@fullcalendar/core"),require("@fullcalendar/resource-common"),require("@fullcalendar/timegrid"),require("@fullcalendar/resource-daygrid")):"function"==typeof define&&define.amd?define(["exports","@fullcalendar/core","@fullcalendar/resource-common","@fullcalendar/timegrid","@fullcalendar/resource-daygrid"],r):(e=e||self,r(e.FullCalendarResourceTimeGrid={},e.FullCalendar,e.FullCalendarResourceCommon,e.FullCalendarTimeGrid,e.FullCalendarResourceDayGrid))}(this,function(e,r,t,i,o){"use strict";function s(e,r){function t(){this.constructor=e}c(e,r),e.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}function a(e,r,o,s){var a=i.buildDayTable(e,r);return s?new t.DayResourceTable(a,o):new t.ResourceDayTable(a,o)}var n="default"in t?t["default"]:t,l="default"in i?i["default"]:i,c=function(e,r){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)r.hasOwnProperty(t)&&(e[t]=r[t])})(e,r)},d=function(){return d=Object.assign||function(e){for(var r,t=1,i=arguments.length;t<i;t++){r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},d.apply(this,arguments)},u=function(e){function o(o,s){var a=e.call(this,o,s.el)||this;return a.buildDayRanges=r.memoize(i.buildDayRanges),a.splitter=new t.VResourceSplitter,a.slicers={},a.joiner=new p,a.timeGrid=s,o.calendar.registerInteractiveComponent(a,{el:a.timeGrid.el}),a}return s(o,e),o.prototype.destroy=function(){this.calendar.unregisterInteractiveComponent(this)},o.prototype.render=function(e){var t=this,o=this.timeGrid,s=e.dateProfile,a=e.resourceDayTable,n=this.dayRanges=this.buildDayRanges(a.dayTable,s,this.dateEnv),l=this.splitter.splitProps(e);this.slicers=r.mapHash(l,function(e,r){return t.slicers[r]||new i.TimeGridSlicer});var c=r.mapHash(this.slicers,function(e,r){return e.sliceProps(l[r],s,null,o,n)});o.allowAcrossResources=1===n.length,o.receiveProps(d({},this.joiner.joinProps(c,a),{dateProfile:s,cells:a.cells[0]}))},o.prototype.renderNowIndicator=function(e){var r=this.timeGrid,t=this.props.resourceDayTable,i=this.slicers[""].sliceNowDate(e,r,this.dayRanges),o=this.joiner.expandSegs(t,i);r.renderNowIndicator(o,e)},o.prototype.buildPositionCaches=function(){this.timeGrid.buildPositionCaches()},o.prototype.queryHit=function(e,r){var t=this.timeGrid.positionToHit(e,r);if(t)return{component:this.timeGrid,dateSpan:{range:t.dateSpan.range,allDay:t.dateSpan.allDay,resourceId:this.props.resourceDayTable.cells[0][t.col].resource.id},dayEl:t.dayEl,rect:{left:t.relativeRect.left,right:t.relativeRect.right,top:t.relativeRect.top,bottom:t.relativeRect.bottom},layer:0}},o}(r.DateComponent),p=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return s(r,e),r.prototype.transformSeg=function(e,r,t){return[d({},e,{col:r.computeCol(e.col,t)})]},r}(t.VResourceJoiner),y=function(e){function i(i,s,n,l){var c=e.call(this,i,s,n,l)||this;return c.flattenResources=r.memoize(t.flattenResources),c.buildResourceDayTable=r.memoize(a),c.resourceOrderSpecs=r.parseFieldSpecs(c.opt("resourceOrder")),c.opt("columnHeader")&&(c.header=new t.ResourceDayHeader(c.context,c.el.querySelector(".fc-head-container"))),c.resourceTimeGrid=new u(i,c.timeGrid),c.dayGrid&&(c.resourceDayGrid=new o.ResourceDayGrid(i,c.dayGrid)),c}return s(i,e),i.prototype.destroy=function(){e.prototype.destroy.call(this),this.header&&this.header.destroy(),this.resourceTimeGrid.destroy(),this.resourceDayGrid&&this.resourceDayGrid.destroy()},i.prototype.render=function(r){e.prototype.render.call(this,r);var t=this.splitter.splitProps(r),i=this.flattenResources(r.resourceStore,this.resourceOrderSpecs),o=this.buildResourceDayTable(this.props.dateProfile,this.dateProfileGenerator,i,this.opt("datesAboveResources"));this.header&&this.header.receiveProps({resources:i,dates:o.dayTable.headerDates,dateProfile:r.dateProfile,datesRepDistinctDays:!0,renderIntroHtml:this.renderHeadIntroHtml}),this.resourceTimeGrid.receiveProps(d({},t.timed,{dateProfile:r.dateProfile,resourceDayTable:o})),this.resourceDayGrid&&this.resourceDayGrid.receiveProps(d({},t.allDay,{dateProfile:r.dateProfile,resourceDayTable:o,isRigid:!1,nextDayThreshold:this.nextDayThreshold}))},i.prototype.renderNowIndicator=function(e){this.resourceTimeGrid.renderNowIndicator(e)},i.needsResourceData=!0,i}(i.AbstractTimeGridView),f=r.createPlugin({deps:[n,l],defaultView:"resourceTimeGridDay",views:{resourceTimeGrid:{"class":y,allDaySlot:!0,slotDuration:"00:30:00",slotEventOverlap:!0},resourceTimeGridDay:{type:"resourceTimeGrid",duration:{days:1}},resourceTimeGridWeek:{type:"resourceTimeGrid",duration:{weeks:1}}}});e.ResourceTimeGrid=u,e.ResourceTimeGridView=y,e["default"]=f,Object.defineProperty(e,"__esModule",{value:!0})});