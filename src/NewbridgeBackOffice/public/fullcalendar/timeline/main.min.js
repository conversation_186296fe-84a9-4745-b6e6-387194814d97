/*!
FullCalendar Timeline Plugin v4.2.0
Docs & License: https://fullcalendar.io/scheduler
(c) 2019 Adam Shaw
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@fullcalendar/core")):"function"==typeof define&&define.amd?define(["exports","@fullcalendar/core"],t):(e=e||self,t(e.FullCalendarTimeline={},e.FullCalendar))}(this,function(e,t){"use strict";function r(e,t){function r(){this.constructor=e}T(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function n(){return z||(z=i())}function i(){var e=t.htmlToElement('<div style=" position: absolute; top: -1000px; width: 1px; height: 1px; overflow: scroll; direction: rtl; font-size: 100px; ">A</div>');document.body.appendChild(e);var r;return e.scrollLeft>0?r="positive":(e.scrollLeft=1,r=e.scrollLeft>0?"reverse":"negative"),t.removeElement(e),r}function o(e,r){var n=r.dateEnv,i={labelInterval:c(r,"slotLabelInterval"),slotDuration:c(r,"slotDuration")};d(i,e,n),h(i,e,n),u(i,e,n);var o=r.opt("slotLabelFormat"),s=Array.isArray(o)?o:null!=o?[o]:p(i,e,n,r);i.headerFormats=s.map(function(e){return t.createFormatter(e)}),i.isTimeScale=Boolean(i.slotDuration.milliseconds);var v=null;if(!i.isTimeScale){var y=t.greatestDurationDenominator(i.slotDuration).unit;/year|month|week/.test(y)&&(v=y)}i.largeUnit=v,i.emphasizeWeeks=t.isSingleDay(i.slotDuration)&&f("weeks",e,n)>=2&&!r.opt("businessHours");var S,b,E=r.opt("snapDuration");E&&(S=t.createDuration(E),b=t.wholeDivideDurations(i.slotDuration,S)),null==b&&(S=i.slotDuration,b=1),i.snapDuration=S,i.snapsPerSlot=b;var D=t.asRoughMs(e.maxTime)-t.asRoughMs(e.minTime),w=l(e.renderRange.start,i,n),C=l(e.renderRange.end,i,n);i.isTimeScale&&(w=n.add(w,e.minTime),C=n.add(t.addDays(C,-1),e.maxTime)),i.timeWindowMs=D,i.normalizedRange={start:w,end:C};for(var T=[],R=w;R<C;)a(R,i,e,r)&&T.push(R),R=n.add(R,i.slotDuration);i.slotDates=T;var x=-1,M=0,z=[],k=[];for(R=w;R<C;)a(R,i,e,r)?(x++,z.push(x),k.push(M)):z.push(x+.5),R=n.add(R,i.snapDuration),M++;return i.snapDiffToIndex=z,i.snapIndexToDiff=k,i.snapCnt=x+1,i.slotCnt=i.snapCnt/i.snapsPerSlot,i.isWeekStarts=g(i,n),i.cellRows=m(i,n,r),i}function l(e,r,n){var i=e;return r.isTimeScale||(i=t.startOfDay(i),r.largeUnit&&(i=n.startOf(i,r.largeUnit))),i}function s(e,r,n){if(!r.isTimeScale&&(e=t.computeVisibleDayRange(e),r.largeUnit)){var i=e;e={start:n.startOf(e.start,r.largeUnit),end:n.startOf(e.end,r.largeUnit)},(e.end.valueOf()!==i.end.valueOf()||e.end<=e.start)&&(e={start:e.start,end:n.add(e.end,r.slotDuration)})}return e}function a(e,r,n,i){if(i.dateProfileGenerator.isHiddenDay(e))return!1;if(r.isTimeScale){var o=t.startOfDay(e),l=e.valueOf()-o.valueOf(),s=l-t.asRoughMs(n.minTime);return s=(s%864e5+864e5)%864e5,s<r.timeWindowMs}return!0}function c(e,r){var n=e.opt(r);if(null!=n)return t.createDuration(n)}function d(e,r,n){var i=r.currentRange;if(e.labelInterval){var o=n.countDurationsBetween(i.start,i.end,e.labelInterval);o>t.config.MAX_TIMELINE_SLOTS&&(console.warn("slotLabelInterval results in too many cells"),e.labelInterval=null)}if(e.slotDuration){var l=n.countDurationsBetween(i.start,i.end,e.slotDuration);l>t.config.MAX_TIMELINE_SLOTS&&(console.warn("slotDuration results in too many cells"),e.slotDuration=null)}if(e.labelInterval&&e.slotDuration){var s=t.wholeDivideDurations(e.labelInterval,e.slotDuration);(null===s||s<1)&&(console.warn("slotLabelInterval must be a multiple of slotDuration"),e.slotDuration=null)}}function h(e,r,n){var i=r.currentRange,o=e.labelInterval;if(!o){var l=void 0;if(e.slotDuration){for(var s=0,a=O;s<a.length;s++){l=a[s];var c=t.createDuration(l),d=t.wholeDivideDurations(c,e.slotDuration);if(null!==d&&d<=A){o=c;break}}o||(o=e.slotDuration)}else for(var h=0,u=O;h<u.length;h++){l=u[h],o=t.createDuration(l);var p=n.countDurationsBetween(i.start,i.end,o);if(p>=W)break}e.labelInterval=o}return o}function u(e,r,n){var i=r.currentRange,o=e.slotDuration;if(!o){for(var l=h(e,r,n),s=0,a=O;s<a.length;s++){var c=a[s],d=t.createDuration(c),u=t.wholeDivideDurations(l,d);if(null!==u&&u>1&&u<=A){o=d;break}}if(o){var p=n.countDurationsBetween(i.start,i.end,o);p>B&&(o=null)}o||(o=l),e.slotDuration=o}return o}function p(e,r,n,i){var o,l,s=e.labelInterval,a=t.greatestDurationDenominator(s).unit,c=i.opt("weekNumbers"),d=o=l=null;switch("week"!==a||c||(a="day"),a){case"year":d={year:"numeric"};break;case"month":f("years",r,n)>1&&(d={year:"numeric"}),o={month:"short"};break;case"week":f("years",r,n)>1&&(d={year:"numeric"}),o={week:"narrow"};break;case"day":f("years",r,n)>1?d={year:"numeric",month:"long"}:f("months",r,n)>1&&(d={month:"long"}),c&&(o={week:"short"}),l={weekday:"narrow",day:"numeric"};break;case"hour":c&&(d={week:"short"}),f("days",r,n)>1&&(o={weekday:"short",day:"numeric",month:"numeric",omitCommas:!0}),l={hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"};break;case"minute":t.asRoughMinutes(s)/60>=A?(d={hour:"numeric",meridiem:"short"},o=function(e){return":"+t.padStart(e.date.minute,2)}):d={hour:"numeric",minute:"numeric",meridiem:"short"};break;case"second":t.asRoughSeconds(s)/60>=A?(d={hour:"numeric",minute:"2-digit",meridiem:"lowercase"},o=function(e){return":"+t.padStart(e.date.second,2)}):d={hour:"numeric",minute:"2-digit",second:"2-digit",meridiem:"lowercase"};break;case"millisecond":d={hour:"numeric",minute:"2-digit",second:"2-digit",meridiem:"lowercase"},o=function(e){return"."+t.padStart(e.millisecond,3)}}return[].concat(d||[],o||[],l||[])}function f(e,r,n){var i=r.currentRange,o=null;return"years"===e?o=n.diffWholeYears(i.start,i.end):"months"===e?o=n.diffWholeMonths(i.start,i.end):"weeks"===e?o=n.diffWholeMonths(i.start,i.end):"days"===e&&(o=t.diffWholeDays(i.start,i.end)),o||0}function g(e,t){for(var r=e.slotDates,n=e.emphasizeWeeks,i=null,o=[],l=0,s=r;l<s.length;l++){var a=s[l],c=t.computeWeekNumber(a),d=n&&null!==i&&i!==c;i=c,o.push(d)}return o}function m(e,r,n){for(var i=e.slotDates,o=e.headerFormats,l=o.map(function(e){return[]}),s=o.map(function(e){return e.getLargestUnit?e.getLargestUnit():null}),a=0;a<i.length;a++)for(var c=i[a],d=e.isWeekStarts[a],h=0;h<o.length;h++){var u=o[h],p=l[h],f=p[p.length-1],g=o.length>1&&h<o.length-1,m=null;if(g){var y=r.format(c,u);f&&f.text===y?f.colspan+=1:m=v(c,y,s[h],n)}else if(!f||t.isInt(r.countDurationsBetween(e.normalizedRange.start,c,e.labelInterval))){var y=r.format(c,u);m=v(c,y,s[h],n)}else f.colspan+=1;m&&(m.weekStart=d,p.push(m))}return l}function v(e,r,n,i){var o=t.buildGotoAnchorHtml(i,{date:e,type:n,forceOff:!n},{"class":"fc-cell-text"},t.htmlEscape(r));return{text:r,spanHtml:o,date:e,colspan:1,isWeekStart:!1}}function y(e,r,n){e.forEach(function(e,i){var o=r[i].naturalBound;t.applyStyle(e,{position:"relative",left:n[i].left-o.left,top:n[i].top-o.top})})}function S(e,r,n){e.forEach(function(e,i){var o=0;"center"===r[i].intendedTextAlign&&(o=(n-r[i].elWidth)/2,"center"===r[i].computedTextAlign&&(e.setAttribute("data-sticky-center",""),e.parentNode.style.textAlign="left")),t.applyStyle(e,{position:_,left:o,right:0,top:0})})}function b(){var e=t.htmlToElement('<div style="position:-webkit-sticky;position:sticky"></div>'),r=e.style.position;return r.indexOf("sticky")!==-1?r:null}function E(e){for(var t=0,r=0,n=e;r<n.length;r++){var i=n[r];t=Math.max(t,D(i))}return t}function D(e){return null==e.top&&(e.top=E(e.above)),e.top+e.height}function w(e,t){return e.left<t.right&&e.right>t.left}function C(e,t){var r=e.resourceEditable;if(null==r){var n=e.sourceId&&t.state.eventSources[e.sourceId];n&&(r=n.extendedProps.resourceEditable),null==r&&(r=t.opt("eventResourceEditable"),null==r&&(r=!0))}return r}/*! *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
    ***************************************************************************** */
var T=function(e,t){return(T=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},R=function(){return R=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},R.apply(this,arguments)},x=function(){function e(){this.gutters={},this.el=t.htmlToElement('<div class="fc-scroller-canvas"> <div class="fc-content"></div> <div class="fc-bg"></div> </div>'),this.contentEl=this.el.querySelector(".fc-content"),this.bgEl=this.el.querySelector(".fc-bg")}return e.prototype.setGutters=function(e){e?R(this.gutters,e):this.gutters={},this.updateSize()},e.prototype.setWidth=function(e){this.width=e,this.updateSize()},e.prototype.setMinWidth=function(e){this.minWidth=e,this.updateSize()},e.prototype.clearWidth=function(){this.width=null,this.minWidth=null,this.updateSize()},e.prototype.updateSize=function(){var e=this,r=e.gutters,n=e.el;t.forceClassName(n,"fc-gutter-left",r.left),t.forceClassName(n,"fc-gutter-right",r.right),t.forceClassName(n,"fc-gutter-top",r.top),t.forceClassName(n,"fc-gutter-bottom",r.bottom),t.applyStyle(n,{paddingLeft:r.left||"",paddingRight:r.right||"",paddingTop:r.top||"",paddingBottom:r.bottom||"",width:null!=this.width?this.width+(r.left||0)+(r.right||0):"",minWidth:null!=this.minWidth?this.minWidth+(r.left||0)+(r.right||0):""}),t.applyStyle(this.bgEl,{left:r.left||"",right:r.right||"",top:r.top||"",bottom:r.bottom||""})},e}(),M=function(e){function i(r,n){var i=e.call(this,r,n)||this;return i.reportScroll=function(){i.isScrolling||i.reportScrollStart(),i.trigger("scroll"),i.isMoving=!0,i.requestMovingEnd()},i.reportScrollStart=function(){i.isScrolling||(i.isScrolling=!0,i.trigger("scrollStart",i.isTouching))},i.reportTouchStart=function(){i.isTouching=!0},i.reportTouchEnd=function(){i.isTouching&&(i.isTouching=!1,i.isTouchScrollEnabled&&i.unbindPreventTouchScroll(),i.isMoving||i.reportScrollEnd())},i.isScrolling=!1,i.isTouching=!1,i.isMoving=!1,i.isTouchScrollEnabled=!0,i.requestMovingEnd=t.debounce(i.reportMovingEnd,500),i.canvas=new x,i.el.appendChild(i.canvas.el),i.applyOverflow(),i.bindHandlers(),i}return r(i,e),i.prototype.destroy=function(){e.prototype.destroy.call(this),this.unbindHandlers()},i.prototype.disableTouchScroll=function(){this.isTouchScrollEnabled=!1,this.bindPreventTouchScroll()},i.prototype.enableTouchScroll=function(){this.isTouchScrollEnabled=!0,this.isTouching||this.unbindPreventTouchScroll()},i.prototype.bindPreventTouchScroll=function(){this.preventTouchScrollHandler||this.el.addEventListener("touchmove",this.preventTouchScrollHandler=t.preventDefault)},i.prototype.unbindPreventTouchScroll=function(){this.preventTouchScrollHandler&&(this.el.removeEventListener("touchmove",this.preventTouchScrollHandler),this.preventTouchScrollHandler=null)},i.prototype.bindHandlers=function(){this.el.addEventListener("scroll",this.reportScroll),this.el.addEventListener("touchstart",this.reportTouchStart,{passive:!0}),this.el.addEventListener("touchend",this.reportTouchEnd)},i.prototype.unbindHandlers=function(){this.el.removeEventListener("scroll",this.reportScroll),this.el.removeEventListener("touchstart",this.reportTouchStart,{passive:!0}),this.el.removeEventListener("touchend",this.reportTouchEnd)},i.prototype.reportMovingEnd=function(){this.isMoving=!1,this.isTouching||this.reportScrollEnd()},i.prototype.reportScrollEnd=function(){this.isScrolling&&(this.trigger("scrollEnd"),this.isScrolling=!1)},i.prototype.getScrollLeft=function(){var e=this.el,t=window.getComputedStyle(e).direction,r=e.scrollLeft;if("rtl"===t)switch(n()){case"positive":r=r+e.clientWidth-e.scrollWidth;break;case"reverse":r=-r}return r},i.prototype.setScrollLeft=function(e){var t=this.el,r=window.getComputedStyle(t).direction;if("rtl"===r)switch(n()){case"positive":e=e-t.clientWidth+t.scrollWidth;break;case"reverse":e=-e}t.scrollLeft=e},i.prototype.getScrollFromLeft=function(){var e=this.el,t=window.getComputedStyle(e).direction,r=e.scrollLeft;if("rtl"===t)switch(n()){case"negative":r=r-e.clientWidth+e.scrollWidth;break;case"reverse":r=-r-e.clientWidth+e.scrollWidth}return r},i}(t.ScrollComponent);t.EmitterMixin.mixInto(M);var z,k=function(){function e(e,r,n){this.isHScrollbarsClipped=!1,this.isVScrollbarsClipped=!1,"clipped-scroll"===e&&(e="scroll",this.isHScrollbarsClipped=!0),"clipped-scroll"===r&&(r="scroll",this.isVScrollbarsClipped=!0),this.enhancedScroll=new M(e,r),n.appendChild(this.el=t.createElement("div",{className:"fc-scroller-clip"})),this.el.appendChild(this.enhancedScroll.el)}return e.prototype.destroy=function(){t.removeElement(this.el)},e.prototype.updateSize=function(){var e=this.enhancedScroll,r=e.el,n=t.computeEdges(r),i={marginLeft:0,marginRight:0,marginTop:0,marginBottom:0};this.isVScrollbarsClipped&&(i.marginLeft=-n.scrollbarLeft,i.marginRight=-n.scrollbarRight),this.isHScrollbarsClipped&&(i.marginBottom=-n.scrollbarBottom),t.applyStyle(r,i),!this.isHScrollbarsClipped&&"hidden"!==e.overflowX||!this.isVScrollbarsClipped&&"hidden"!==e.overflowY||n.scrollbarLeft||n.scrollbarRight||n.scrollbarBottom?r.classList.remove("fc-no-scrollbars"):r.classList.add("fc-no-scrollbars")},e.prototype.setHeight=function(e){this.enhancedScroll.setHeight(e)},e.prototype.getScrollbarWidths=function(){var e=this.enhancedScroll.getScrollbarWidths();return this.isVScrollbarsClipped&&(e.left=0,e.right=0),this.isHScrollbarsClipped&&(e.bottom=0),e},e}(),I=function(){function e(e,t){this.axis=e,this.scrollers=t;for(var r=0,n=this.scrollers;r<n.length;r++){var i=n[r];this.initScroller(i)}}return e.prototype.initScroller=function(e){var t=this,r=e.enhancedScroll,n=function(){t.assignMasterScroller(e)};"wheel mousewheel DomMouseScroll MozMousePixelScroll".split(" ").forEach(function(e){r.el.addEventListener(e,n)}),r.on("scrollStart",function(){t.masterScroller||t.assignMasterScroller(e)}).on("scroll",function(){if(e===t.masterScroller)for(var n=0,i=t.scrollers;n<i.length;n++){var o=i[n];if(o!==e)switch(t.axis){case"horizontal":o.enhancedScroll.el.scrollLeft=r.el.scrollLeft;break;case"vertical":o.enhancedScroll.setScrollTop(r.getScrollTop())}}}).on("scrollEnd",function(){e===t.masterScroller&&t.unassignMasterScroller()})},e.prototype.assignMasterScroller=function(e){this.unassignMasterScroller(),this.masterScroller=e;for(var t=0,r=this.scrollers;t<r.length;t++){var n=r[t];n!==e&&n.enhancedScroll.disableTouchScroll()}},e.prototype.unassignMasterScroller=function(){if(this.masterScroller){for(var e=0,t=this.scrollers;e<t.length;e++){var r=t[e];r.enhancedScroll.enableTouchScroll()}this.masterScroller=null}},e.prototype.update=function(){for(var e,t,r=this.scrollers.map(function(e){return e.getScrollbarWidths()}),n=0,i=0,o=0,l=0,s=0,a=r;s<a.length;s++)e=a[s],n=Math.max(n,e.left),i=Math.max(i,e.right),o=Math.max(o,e.top),l=Math.max(l,e.bottom);for(t=0;t<this.scrollers.length;t++){var c=this.scrollers[t];e=r[t],c.enhancedScroll.canvas.setGutters("horizontal"===this.axis?{left:n-e.left,right:i-e.right}:{top:o-e.top,bottom:l-e.bottom})}},e}(),P=function(){function e(e,t,r){this.headerScroller=new k("clipped-scroll","hidden",e),this.bodyScroller=new k("auto",r,t),this.scrollJoiner=new I("horizontal",[this.headerScroller,this.bodyScroller])}return e.prototype.destroy=function(){this.headerScroller.destroy(),this.bodyScroller.destroy()},e.prototype.setHeight=function(e,t){var r;r=t?"auto":e-this.queryHeadHeight(),this.bodyScroller.setHeight(r),this.headerScroller.updateSize(),this.bodyScroller.updateSize(),this.scrollJoiner.update()},e.prototype.queryHeadHeight=function(){return this.headerScroller.enhancedScroll.canvas.contentEl.offsetHeight},e}(),H=function(e){function n(r,n){var i=e.call(this,r)||this;return n.appendChild(i.tableEl=t.createElement("table",{className:i.theme.getClass("tableGrid")})),i}return r(n,e),n.prototype.destroy=function(){t.removeElement(this.tableEl),e.prototype.destroy.call(this)},n.prototype.render=function(e){this.renderDates(e.tDateProfile)},n.prototype.renderDates=function(e){for(var r=this,n=r.dateEnv,i=r.theme,o=e.cellRows,l=o[o.length-1],s=t.asRoughMs(e.labelInterval)>t.asRoughMs(e.slotDuration),a=t.isSingleDay(e.slotDuration),c="<colgroup>",d=e.slotCnt-1;d>=0;d--)c+="<col/>";c+="</colgroup>",c+="<tbody>";for(var h=0,u=o;h<u.length;h++){var p=u[h],f=p===l;c+="<tr"+(s&&f?' class="fc-chrono"':"")+">";for(var g=0,m=p;g<m.length;g++){var v=m[g],y=[i.getClass("widgetHeader")];v.isWeekStart&&y.push("fc-em-cell"),a&&(y=y.concat(t.getDayClasses(v.date,this.props.dateProfile,this.context,!0))),c+='<th class="'+y.join(" ")+'" data-date="'+n.formatIso(v.date,{omitTime:!e.isTimeScale,omitTimeZoneOffset:!0})+'"'+(v.colspan>1?' colspan="'+v.colspan+'"':"")+'><div class="fc-cell-content">'+v.spanHtml+"</div></th>"}c+="</tr>"}c+="</tbody>",this.tableEl.innerHTML=c,this.slatColEls=t.findElements(this.tableEl,"col"),this.innerEls=t.findElements(this.tableEl.querySelector("tr:last-child"),"th .fc-cell-text"),t.findElements(this.tableEl.querySelectorAll("tr:not(:last-child)"),"th .fc-cell-text").forEach(function(e){e.classList.add("fc-sticky")})},n}(t.Component),L=function(e){function n(r,n){var i=e.call(this,r)||this;return n.appendChild(i.el=t.createElement("div",{className:"fc-slats"})),i}return r(n,e),n.prototype.destroy=function(){t.removeElement(this.el),e.prototype.destroy.call(this)},n.prototype.render=function(e){this.renderDates(e.tDateProfile)},n.prototype.renderDates=function(e){for(var r=this,n=r.theme,i=r.view,o=r.dateEnv,l=e.slotDates,s=e.isWeekStarts,a='<table class="'+n.getClass("tableGrid")+'"><colgroup>',c=0;c<l.length;c++)a+="<col/>";a+="</colgroup>",a+="<tbody><tr>";for(var c=0;c<l.length;c++)a+=this.slatCellHtml(l[c],s[c],e);a+="</tr></tbody></table>",this.el.innerHTML=a,this.slatColEls=t.findElements(this.el,"col"),this.slatEls=t.findElements(this.el,"td");for(var c=0;c<l.length;c++)i.publiclyTrigger("dayRender",[{date:o.toDate(l[c]),el:this.slatEls[c],view:i}]);this.outerCoordCache=new t.PositionCache(this.el,this.slatEls,(!0),(!1)),this.innerCoordCache=new t.PositionCache(this.el,t.findChildren(this.slatEls,"div"),(!0),(!1))},n.prototype.slatCellHtml=function(e,r,n){var i,o=this,l=o.theme,s=o.dateEnv;return n.isTimeScale?(i=[],i.push(t.isInt(s.countDurationsBetween(n.normalizedRange.start,e,n.labelInterval))?"fc-major":"fc-minor")):(i=t.getDayClasses(e,this.props.dateProfile,this.context),i.push("fc-day")),i.unshift(l.getClass("widgetContent")),r&&i.push("fc-em-cell"),'<td class="'+i.join(" ")+'" data-date="'+s.formatIso(e,{omitTime:!n.isTimeScale,omitTimeZoneOffset:!0})+'"><div></div></td>'},n.prototype.updateSize=function(){this.outerCoordCache.build(),this.innerCoordCache.build()},n.prototype.positionToHit=function(e){var r=this.outerCoordCache,n=this.props.tDateProfile,i=r.leftToIndex(e);if(null!=i){var o=r.getWidth(i),l=this.isRtl?(r.rights[i]-e)/o:(e-r.lefts[i])/o,s=Math.floor(l*n.snapsPerSlot),a=this.dateEnv.add(n.slotDates[i],t.multiplyDuration(n.snapDuration,s)),c=this.dateEnv.add(a,n.snapDuration);return{dateSpan:{range:{start:a,end:c},allDay:!this.props.tDateProfile.isTimeScale},dayEl:this.slatColEls[i],left:r.lefts[i],right:r.rights[i]}}return null},n}(t.Component),W=18,A=6,B=200;t.config.MAX_TIMELINE_SLOTS=1e3;var O=[{years:1},{months:1},{days:1},{hours:1},{minutes:30},{minutes:15},{minutes:10},{minutes:5},{minutes:1},{seconds:30},{seconds:15},{seconds:10},{seconds:5},{seconds:1},{milliseconds:500},{milliseconds:100},{milliseconds:10},{milliseconds:1}],N=function(){function e(e,t){this.headParent=e,this.bodyParent=t}return e.prototype.render=function(e,r){var n=r?{right:-e}:{left:e};this.headParent.appendChild(this.arrowEl=t.createElement("div",{className:"fc-now-indicator fc-now-indicator-arrow",style:n})),this.bodyParent.appendChild(this.lineEl=t.createElement("div",{className:"fc-now-indicator fc-now-indicator-line",style:n}))},e.prototype.unrender=function(){this.arrowEl&&t.removeElement(this.arrowEl),this.lineEl&&t.removeElement(this.lineEl)},e}(),_=b(),F=/Edge/.test(navigator.userAgent),q="-webkit-sticky"===_,U="fc-sticky",j=function(){function e(e,t,r){var n=this;this.usingRelative=null,this.updateSize=function(){var e=Array.prototype.slice.call(n.scroller.canvas.el.querySelectorAll("."+U)),t=n.queryElGeoms(e),r=n.scroller.el.clientWidth;if(n.usingRelative){var i=n.computeElDestinations(t,r);y(e,t,i)}else S(e,t,r)},this.scroller=e,this.usingRelative=!_||F&&t||(F||q)&&r,this.usingRelative&&e.on("scrollEnd",this.updateSize)}return e.prototype.destroy=function(){this.scroller.off("scrollEnd",this.updateSize)},e.prototype.queryElGeoms=function(e){for(var r=this.scroller.canvas.el.getBoundingClientRect(),n=[],i=0,o=e;i<o.length;i++){var l=o[i],s=t.translateRect(l.parentNode.getBoundingClientRect(),-r.left,-r.top),a=l.getBoundingClientRect(),c=window.getComputedStyle(l),d=window.getComputedStyle(l.parentNode).textAlign,h=d,u=null;"sticky"!==c.position&&(u=t.translateRect(a,-r.left-(parseFloat(c.left)||0),-r.top-(parseFloat(c.top)||0))),l.hasAttribute("data-sticky-center")&&(h="center"),n.push({parentBound:s,naturalBound:u,elWidth:a.width,elHeight:a.height,computedTextAlign:d,intendedTextAlign:h})}return n},e.prototype.computeElDestinations=function(e,t){var r=this.scroller.getScrollFromLeft(),n=this.scroller.getScrollTop(),i=r+t;return e.map(function(e){var t,o,l=e.elWidth,s=e.elHeight,a=e.parentBound,c=e.naturalBound;switch(e.intendedTextAlign){case"left":t=r;break;case"right":t=i-l;break;case"center":t=(r+i)/2-l/2}return t=Math.min(t,a.right-l),t=Math.max(t,a.left),o=n,o=Math.min(o,a.bottom-s),o=Math.max(o,c.top),{left:t,top:o}})},e}(),V=function(e){function n(t,r,n){var i=e.call(this,t)||this,o=i.layout=new P(r,n,"auto"),l=o.headerScroller.enhancedScroll,s=o.bodyScroller.enhancedScroll;return i.headStickyScroller=new j(l,i.isRtl,(!1)),i.bodyStickyScroller=new j(s,i.isRtl,(!1)),i.header=new H(t,l.canvas.contentEl),i.slats=new L(t,s.canvas.bgEl),i.nowIndicator=new N(l.canvas.el,s.canvas.el),i}return r(n,e),n.prototype.destroy=function(){this.layout.destroy(),this.header.destroy(),this.slats.destroy(),this.nowIndicator.unrender(),this.headStickyScroller.destroy(),this.bodyStickyScroller.destroy(),e.prototype.destroy.call(this)},n.prototype.render=function(e){var t=this.tDateProfile=o(e.dateProfile,this.view);this.header.receiveProps({dateProfile:e.dateProfile,tDateProfile:t}),this.slats.receiveProps({dateProfile:e.dateProfile,tDateProfile:t})},n.prototype.getNowIndicatorUnit=function(e){var r=this.tDateProfile=o(e,this.view);if(r.isTimeScale)return t.greatestDurationDenominator(r.slotDuration).unit},n.prototype.renderNowIndicator=function(e){t.rangeContainsMarker(this.tDateProfile.normalizedRange,e)&&this.nowIndicator.render(this.dateToCoord(e),this.isRtl)},n.prototype.unrenderNowIndicator=function(){this.nowIndicator.unrender()},n.prototype.updateSize=function(e,t,r){this.applySlotWidth(this.computeSlotWidth()),this.layout.setHeight(t,r),this.slats.updateSize()},n.prototype.updateStickyScrollers=function(){this.headStickyScroller.updateSize(),this.bodyStickyScroller.updateSize()},n.prototype.computeSlotWidth=function(){var e=this.opt("slotWidth")||"";return""===e&&(e=this.computeDefaultSlotWidth(this.tDateProfile)),e},n.prototype.computeDefaultSlotWidth=function(e){var r=0;this.header.innerEls.forEach(function(e,t){r=Math.max(r,e.getBoundingClientRect().width)});var n=Math.ceil(r)+1,i=t.wholeDivideDurations(e.labelInterval,e.slotDuration),o=Math.ceil(n/i),l=window.getComputedStyle(this.header.slatColEls[0]).minWidth;return l&&(l=parseInt(l,10),l&&(o=Math.max(o,l))),o},n.prototype.applySlotWidth=function(e){var t=this,r=t.layout,n=t.tDateProfile,i="",o="",l="";if(""!==e){e=Math.round(e),i=e*n.slotDates.length,o="",l=e;var s=r.bodyScroller.enhancedScroll.getClientWidth();s>i&&(o=s,i="",l=Math.floor(s/n.slotDates.length))}r.headerScroller.enhancedScroll.canvas.setWidth(i),r.headerScroller.enhancedScroll.canvas.setMinWidth(o),r.bodyScroller.enhancedScroll.canvas.setWidth(i),r.bodyScroller.enhancedScroll.canvas.setMinWidth(o),""!==l&&this.header.slatColEls.slice(0,-1).concat(this.slats.slatColEls.slice(0,-1)).forEach(function(e){e.style.width=l+"px"})},n.prototype.computeDateSnapCoverage=function(e){var r=this,n=r.dateEnv,i=r.tDateProfile,o=n.countDurationsBetween(i.normalizedRange.start,e,i.snapDuration);if(o<0)return 0;if(o>=i.snapDiffToIndex.length)return i.snapCnt;var l=Math.floor(o),s=i.snapDiffToIndex[l];return t.isInt(s)?s+=o-l:s=Math.ceil(s),s},n.prototype.dateToCoord=function(e){var t=this.tDateProfile,r=this.computeDateSnapCoverage(e),n=r/t.snapsPerSlot,i=Math.floor(n);i=Math.min(i,t.slotCnt-1);var o=n-i,l=this.slats,s=l.innerCoordCache,a=l.outerCoordCache;return this.isRtl?a.rights[i]-s.getWidth(i)*o-a.originClientRect.width:a.lefts[i]+s.getWidth(i)*o},n.prototype.rangeToCoords=function(e){return this.isRtl?{right:this.dateToCoord(e.start),left:this.dateToCoord(e.end)}:{left:this.dateToCoord(e.start),right:this.dateToCoord(e.end)}},n.prototype.computeDateScroll=function(e){var r=this.dateEnv,n=this.props.dateProfile,i=0;return n&&(i=this.dateToCoord(r.add(t.startOfDay(n.activeRange.start),t.createDuration(e))),!this.isRtl&&i&&(i+=1)),{left:i}},n.prototype.queryDateScroll=function(){var e=this.layout.bodyScroller.enhancedScroll;return{left:e.getScrollLeft()}},n.prototype.applyDateScroll=function(e){this.layout.bodyScroller.enhancedScroll.setScrollLeft(e.left||0),this.layout.headerScroller.enhancedScroll.setScrollLeft(e.left||0)},n}(t.Component),G=function(e){function n(t,r,n){var i=e.call(this,t)||this;return i.masterContainerEl=r,i.timeAxis=n,i}return r(n,e),n.prototype.renderSegHtml=function(e,r){var n=e.eventRange,i=n.def,o=n.ui,l=o.startEditable||C(i,this.timeAxis.calendar),s=e.isStart&&o.durationEditable&&this.context.options.eventResizableFromStart,a=e.isEnd&&o.durationEditable,c=this.getSegClasses(e,l,s||a,r);c.unshift("fc-timeline-event","fc-h-event");var d=this.getTimeText(n);return'<a class="'+c.join(" ")+'" style="'+t.cssToStr(this.getSkinCss(o))+'"'+(i.url?' href="'+t.htmlEscape(i.url)+'"':"")+'><div class="fc-content">'+(d?'<span class="fc-time">'+t.htmlEscape(d)+"</span>":"")+'<span class="fc-title fc-sticky">'+(i.title?t.htmlEscape(i.title):"&nbsp;")+"</span></div>"+(s?'<div class="fc-resizer fc-start-resizer"></div>':"")+(a?'<div class="fc-resizer fc-end-resizer"></div>':"")+"</a>"},n.prototype.computeDisplayEventTime=function(){return!this.timeAxis.tDateProfile.isTimeScale},n.prototype.computeDisplayEventEnd=function(){return!1},n.prototype.computeEventTimeFormat=function(){return{hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"}},n.prototype.attachSegs=function(e,r){if(!this.el&&this.masterContainerEl&&(this.el=t.createElement("div",{className:"fc-event-container"}),r&&this.el.classList.add("fc-mirror-container"),this.masterContainerEl.appendChild(this.el)),this.el)for(var n=0,i=e;n<i.length;n++){var o=i[n];this.el.appendChild(o.el)}},n.prototype.detachSegs=function(e){for(var r=0,n=e;r<n.length;r++){var i=n[r];t.removeElement(i.el)}},n.prototype.computeSegSizes=function(e){for(var r=this.timeAxis,n=0,i=e;n<i.length;n++){var o=i[n],l=r.rangeToCoords(o);t.applyStyle(o.el,{left:o.left=l.left,right:-(o.right=l.right)})}},n.prototype.assignSegSizes=function(e){if(this.el){for(var r=0,n=e;r<n.length;r++){var i=n[r];i.height=t.computeHeightAndMargins(i.el)}this.buildSegLevels(e);var o=E(e);t.applyStyleProp(this.el,"height",o);for(var l=0,s=e;l<s.length;l++){var i=s[l];t.applyStyleProp(i.el,"top",i.top)}}},n.prototype.buildSegLevels=function(e){var t=[];e=this.sortEventSegs(e);for(var r=0,n=e;r<n.length;r++){var i=n[r];i.above=[];for(var o=0;o<t.length;){for(var l=!1,s=0,a=t[o];s<a.length;s++){var c=a[s];w(i,c)&&(i.above.push(c),l=!0)}if(!l)break;o+=1}for((t[o]||(t[o]=[])).push(i),o+=1;o<t.length;){for(var d=0,h=t[o];d<h.length;d++){var u=h[d];w(i,u)&&u.above.push(i)}o+=1}}return t},n}(t.FgEventRenderer),X=function(e){function n(t,r,n){var i=e.call(this,t)||this;return i.masterContainerEl=r,i.timeAxis=n,i}return r(n,e),n.prototype.attachSegs=function(e,r){if(r.length){var n=void 0;n="businessHours"===e?"bgevent":e.toLowerCase();var i=t.createElement("div",{className:"fc-"+n+"-container"});this.masterContainerEl.appendChild(i);for(var o=0,l=r;o<l.length;o++){var s=l[o];i.appendChild(s.el)}return[i]}},n.prototype.computeSegSizes=function(e){for(var t=this.timeAxis,r=0,n=e;r<n.length;r++){var i=n[r],o=t.rangeToCoords(i);i.left=o.left,i.right=o.right}},n.prototype.assignSegSizes=function(e){for(var r=0,n=e;r<n.length;r++){var i=n[r];t.applyStyle(i.el,{left:i.left,right:-i.right})}},n}(t.FillRenderer),Z=function(e){function n(r,n,i,o){var l=e.call(this,r,i)||this;l.slicer=new J,l.renderEventDrag=t.memoizeRendering(l._renderEventDrag,l._unrenderEventDrag),l.renderEventResize=t.memoizeRendering(l._renderEventResize,l._unrenderEventResize);var s=l.fillRenderer=new X(r,i,o),a=l.eventRenderer=new G(r,n,o);return l.mirrorRenderer=new G(r,n,o),l.renderBusinessHours=t.memoizeRendering(s.renderSegs.bind(s,"businessHours"),s.unrender.bind(s,"businessHours")),l.renderDateSelection=t.memoizeRendering(s.renderSegs.bind(s,"highlight"),s.unrender.bind(s,"highlight")),l.renderBgEvents=t.memoizeRendering(s.renderSegs.bind(s,"bgEvent"),s.unrender.bind(s,"bgEvent")),l.renderFgEvents=t.memoizeRendering(a.renderSegs.bind(a),a.unrender.bind(a)),l.renderEventSelection=t.memoizeRendering(a.selectByInstanceId.bind(a),a.unselectByInstanceId.bind(a),[l.renderFgEvents]),l.timeAxis=o,l}return r(n,e),n.prototype.render=function(e){var t=this.slicer.sliceProps(e,e.dateProfile,this.timeAxis.tDateProfile.isTimeScale?null:e.nextDayThreshold,this,this.timeAxis);this.renderBusinessHours(t.businessHourSegs),this.renderDateSelection(t.dateSelectionSegs),this.renderBgEvents(t.bgEventSegs),this.renderFgEvents(t.fgEventSegs),this.renderEventSelection(t.eventSelection),this.renderEventDrag(t.eventDrag),this.renderEventResize(t.eventResize)},n.prototype.destroy=function(){e.prototype.destroy.call(this),this.renderBusinessHours.unrender(),this.renderDateSelection.unrender(),this.renderBgEvents.unrender(),this.renderFgEvents.unrender(),this.renderEventSelection.unrender(),this.renderEventDrag.unrender(),this.renderEventResize.unrender()},n.prototype._renderEventDrag=function(e){e&&(this.eventRenderer.hideByHash(e.affectedInstances),this.mirrorRenderer.renderSegs(e.segs,{isDragging:!0,sourceSeg:e.sourceSeg}))},n.prototype._unrenderEventDrag=function(e){e&&(this.eventRenderer.showByHash(e.affectedInstances),this.mirrorRenderer.unrender(e.segs,{isDragging:!0,sourceSeg:e.sourceSeg}))},n.prototype._renderEventResize=function(e){if(e){var t=e.segs.map(function(e){return R({},e)});this.eventRenderer.hideByHash(e.affectedInstances),this.fillRenderer.renderSegs("highlight",t),this.mirrorRenderer.renderSegs(e.segs,{isDragging:!0,sourceSeg:e.sourceSeg})}},n.prototype._unrenderEventResize=function(e){e&&(this.eventRenderer.showByHash(e.affectedInstances),this.fillRenderer.unrender("highlight"),this.mirrorRenderer.unrender(e.segs,{isDragging:!0,sourceSeg:e.sourceSeg}))},n.prototype.updateSize=function(e){var t=this,r=t.fillRenderer,n=t.eventRenderer,i=t.mirrorRenderer;r.computeSizes(e),n.computeSizes(e),i.computeSizes(e),r.assignSizes(e),n.assignSizes(e),i.assignSizes(e)},n}(t.DateComponent),J=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return r(n,e),n.prototype.sliceRange=function(e,r){var n=r.tDateProfile,i=r.props.dateProfile,o=s(e,n,r.dateEnv),l=[];if(r.computeDateSnapCoverage(o.start)<r.computeDateSnapCoverage(o.end)){var c=t.intersectRanges(o,n.normalizedRange);c&&l.push({start:c.start,end:c.end,isStart:c.start.valueOf()===o.start.valueOf()&&a(c.start,n,i,r.view),isEnd:c.end.valueOf()===o.end.valueOf()&&a(t.addMs(c.end,-1),n,i,r.view)})}return l},n}(t.Slicer),Y=function(e){function t(t,r,n,i){var o=e.call(this,t,r,n,i)||this;return o.el.classList.add("fc-timeline"),o.opt("eventOverlap")===!1&&o.el.classList.add("fc-no-overlap"),o.el.innerHTML=o.renderSkeletonHtml(),o.timeAxis=new V(o.context,o.el.querySelector("thead .fc-time-area"),o.el.querySelector("tbody .fc-time-area")),o.lane=new Z(o.context,o.timeAxis.layout.bodyScroller.enhancedScroll.canvas.contentEl,o.timeAxis.layout.bodyScroller.enhancedScroll.canvas.bgEl,o.timeAxis),t.calendar.registerInteractiveComponent(o,{el:o.timeAxis.slats.el}),o}return r(t,e),t.prototype.destroy=function(){this.timeAxis.destroy(),this.lane.destroy(),e.prototype.destroy.call(this),this.calendar.unregisterInteractiveComponent(this)},t.prototype.renderSkeletonHtml=function(){var e=this.theme;return'<table class="'+e.getClass("tableGrid")+'"> <thead class="fc-head"> <tr> <td class="fc-time-area '+e.getClass("widgetHeader")+'"></td> </tr> </thead> <tbody class="fc-body"> <tr> <td class="fc-time-area '+e.getClass("widgetContent")+'"></td> </tr> </tbody> </table>'},t.prototype.render=function(t){e.prototype.render.call(this,t),this.timeAxis.receiveProps({dateProfile:t.dateProfile}),this.lane.receiveProps(R({},t,{nextDayThreshold:this.nextDayThreshold}))},t.prototype.updateSize=function(e,t,r){this.timeAxis.updateSize(e,t,r),this.lane.updateSize(e)},t.prototype.getNowIndicatorUnit=function(e){return this.timeAxis.getNowIndicatorUnit(e)},t.prototype.renderNowIndicator=function(e){this.timeAxis.renderNowIndicator(e)},t.prototype.unrenderNowIndicator=function(){this.timeAxis.unrenderNowIndicator()},t.prototype.computeDateScroll=function(e){return this.timeAxis.computeDateScroll(e)},t.prototype.applyScroll=function(t,r){e.prototype.applyScroll.call(this,t,r);var n=this.calendar;(r||n.isViewUpdated||n.isDatesUpdated||n.isEventsUpdated)&&this.timeAxis.updateStickyScrollers()},t.prototype.applyDateScroll=function(e){this.timeAxis.applyDateScroll(e)},t.prototype.queryScroll=function(){var e=this.timeAxis.layout.bodyScroller.enhancedScroll;return{top:e.getScrollTop(),left:e.getScrollLeft()}},t.prototype.buildPositionCaches=function(){this.timeAxis.slats.updateSize()},t.prototype.queryHit=function(e,t,r,n){var i=this.timeAxis.slats.positionToHit(e);if(i)return{component:this,dateSpan:i.dateSpan,rect:{left:i.left,right:i.right,top:0,bottom:n},dayEl:i.dayEl,layer:0}},t}(t.View),K=t.createPlugin({defaultView:"timelineDay",views:{timeline:{"class":Y,eventResizableFromStart:!0},timelineDay:{type:"timeline",duration:{days:1}},timelineWeek:{type:"timeline",duration:{weeks:1}},timelineMonth:{type:"timeline",duration:{months:1}},timelineYear:{type:"timeline",duration:{years:1}}}});e.HeaderBodyLayout=P,e.ScrollJoiner=I,e.StickyScroller=j,e.TimeAxis=V,e.TimelineLane=Z,e.TimelineView=Y,e["default"]=K,Object.defineProperty(e,"__esModule",{value:!0})});