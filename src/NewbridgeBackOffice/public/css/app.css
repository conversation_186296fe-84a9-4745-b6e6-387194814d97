@import url(https://fonts.googleapis.com/css?family=Raleway:300,400,600);@import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css);@import url(https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/3.3.7/css/bootstrap.min.css);@import url(https://cdnjs.cloudflare.com/ajax/libs/metisMenu/2.6.2/metisMenu.css);@import url(https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.13/css/jquery.dataTables.min.css);@import url(https://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.13/css/dataTables.bootstrap.min.css);@import url(https://fonts.googleapis.com/css?family=Roboto:400,500,700,900);body {
  background-color: #f8f8f8;
  font-family: "Roboto", sans-serif !important;
  font-weight: 400 !important;
}

p {
  font-family: sans-serif !important;
  font-weight: 400 !important;
}

h1 {
  font-weight: bold !important;
  font-family: 'Raleway', sans-serif !important;
}

h2 {
  font-family: "Raleway", sans-serif !important;
  font-weight: 500 !important;
}

.swal2-container {
  z-index: 9999999 !important;
}

#wrapper {
  width: 100%;
}

#page-wrapper {
  padding: 0 15px;
  min-height: 568px;
  background-color: white;
}

@media (min-width: 768px) {
  #page-wrapper {
    position: inherit;
    margin: 0 0 0 250px;
    padding: 0 30px;
    border-left: 1px solid #e7e7e7;
  }
}

.navbar-top-links {
  margin-right: 0;
}

.navbar-top-links li {
  display: inline-block;
}

.navbar-top-links li:last-child {
  margin-right: 15px;
}

.navbar-top-links li a {
  padding: 15px;
  min-height: 50px;
}

.navbar-top-links .dropdown-menu li {
  display: block;
}

.navbar-top-links .dropdown-menu li:last-child {
  margin-right: 0;
}

.navbar-top-links .dropdown-menu li a {
  padding: 3px 20px;
  min-height: 0;
}

.navbar-top-links .dropdown-menu li a div {
  white-space: normal;
}

.navbar-top-links .dropdown-messages,
.navbar-top-links .dropdown-tasks,
.navbar-top-links .dropdown-alerts {
  width: 310px;
  min-width: 0;
}

.navbar-top-links .dropdown-messages {
  margin-left: 5px;
}

.navbar-top-links .dropdown-tasks {
  margin-left: -59px;
}

.navbar-top-links .dropdown-alerts {
  margin-left: -123px;
}

.navbar-top-links .dropdown-user {
  right: 0;
  left: auto;
}

.sidebar .sidebar-nav.navbar-collapse {
  padding-left: 0;
  padding-right: 0;
}

.sidebar ul li {
  border-bottom: 1px solid #e7e7e7;
}

.sidebar ul li a.active {
  background-color: #eeeeee;
}

.sidebar .arrow {
  float: right;
}

.sidebar .fa.arrow:before {
  content: "\F104";
}

.sidebar .active > a > .fa.arrow:before {
  content: "\F107";
}

.sidebar .nav-second-level li,
.sidebar .nav-third-level li {
  border-bottom: none !important;
}

.sidebar .nav-second-level li a {
  padding-left: 37px;
}

.sidebar .nav-third-level li a {
  padding-left: 52px;
}

@media (min-width: 768px) {
  .sidebar {
    z-index: 1;
    position: absolute;
    width: 250px;
    margin-top: 51px;
  }

  .navbar-top-links .dropdown-messages,
  .navbar-top-links .dropdown-tasks,
  .navbar-top-links .dropdown-alerts {
    margin-left: auto;
  }
}

.btn-outline {
  color: inherit;
  background-color: transparent;
  -webkit-transition: all .5s;
  transition: all .5s;
}

.btn-primary.btn-outline {
  color: #428bca;
}

.btn-success.btn-outline {
  color: #5cb85c;
}

.btn-info.btn-outline {
  color: #5bc0de;
}

.btn-warning.btn-outline {
  color: #f0ad4e;
}

.btn-danger.btn-outline {
  color: #d9534f;
}

.btn-primary.btn-outline:hover,
.btn-success.btn-outline:hover,
.btn-info.btn-outline:hover,
.btn-warning.btn-outline:hover,
.btn-danger.btn-outline:hover {
  color: white;
}

.chat {
  margin: 0;
  padding: 0;
  list-style: none;
}

.chat li {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dotted #999999;
}

.chat li.left .chat-body {
  margin-left: 60px;
}

.chat li.right .chat-body {
  margin-right: 60px;
}

.chat li .chat-body p {
  margin: 0;
}

.panel .slidedown .glyphicon,
.chat .glyphicon {
  margin-right: 5px;
}

.chat-panel .panel-body {
  height: 350px;
  overflow-y: scroll;
}

.login-panel {
  margin-top: 25%;
}

.flot-chart {
  display: block;
  height: 400px;
}

.flot-chart-content {
  width: 100%;
  height: 100%;
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc_disabled {
  background: transparent;
}

table.dataTable thead .sorting_asc:after {
  content: "\F0DE";
  float: right;
  font-family: fontawesome;
}

table.dataTable thead .sorting_desc:after {
  content: "\F0DD";
  float: right;
  font-family: fontawesome;
}

table.dataTable thead .sorting:after {
  content: "\F0DC";
  float: right;
  font-family: fontawesome;
  color: rgba(50, 50, 50, 0.5);
}

.btn-circle {
  width: 30px;
  height: 30px;
  padding: 6px 0;
  border-radius: 15px;
  text-align: center;
  font-size: 12px;
  line-height: 1.428571429;
}

.btn-circle.btn-lg {
  width: 50px;
  height: 50px;
  padding: 10px 16px;
  border-radius: 25px;
  font-size: 18px;
  line-height: 1.33;
}

.btn-circle.btn-xl {
  width: 70px;
  height: 70px;
  padding: 10px 16px;
  border-radius: 35px;
  font-size: 24px;
  line-height: 1.33;
}

.show-grid [class^="col-"] {
  padding-top: 10px;
  padding-bottom: 10px;
  border: 1px solid #ddd;
  background-color: #eee !important;
}

.show-grid {
  margin: 15px 0;
}

.huge {
  font-size: 40px;
}

.panel-green {
  border-color: #5cb85c;
}

.panel-green > .panel-heading {
  border-color: #5cb85c;
  color: white;
  background-color: #5cb85c;
}

.panel-green > a {
  color: #5cb85c;
}

.panel-green > a:hover {
  color: #3d8b3d;
}

.panel-red {
  border-color: #d9534f;
}

.panel-red > .panel-heading {
  border-color: #d9534f;
  color: white;
  background-color: #d9534f;
}

.panel-red > a {
  color: #d9534f;
}

.panel-red > a:hover {
  color: #b52b27;
}

.panel-yellow {
  border-color: #f0ad4e;
}

.panel-yellow > .panel-heading {
  border-color: #f0ad4e;
  color: white;
  background-color: #f0ad4e;
}

.panel-yellow > a {
  color: #f0ad4e;
}

.panel-yellow > a:hover {
  color: #df8a13;
}

.page-header {
  font-size: 2.0em;
}

.timeline {
  position: relative;
  padding: 20px 0 20px;
  list-style: none;
}

.timeline:before {
  content: " ";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 3px;
  margin-left: -1.5px;
  background-color: #eeeeee;
}

.timeline > li {
  position: relative;
  margin-bottom: 20px;
}

.timeline > li:before,
.timeline > li:after {
  content: " ";
  display: table;
}

.timeline > li:after {
  clear: both;
}

.timeline > li:before,
.timeline > li:after {
  content: " ";
  display: table;
}

.timeline > li:after {
  clear: both;
}

.timeline > li > .timeline-panel {
  float: left;
  position: relative;
  width: 46%;
  padding: 20px;
  border: 1px solid #d4d4d4;
  border-radius: 2px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
}

.timeline > li > .timeline-panel:before {
  content: " ";
  display: inline-block;
  position: absolute;
  top: 26px;
  right: -15px;
  border-top: 15px solid transparent;
  border-right: 0 solid #ccc;
  border-bottom: 15px solid transparent;
  border-left: 15px solid #ccc;
}

.timeline > li > .timeline-panel:after {
  content: " ";
  display: inline-block;
  position: absolute;
  top: 27px;
  right: -14px;
  border-top: 14px solid transparent;
  border-right: 0 solid #fff;
  border-bottom: 14px solid transparent;
  border-left: 14px solid #fff;
}

.timeline > li > .timeline-badge {
  z-index: 100;
  position: absolute;
  top: 16px;
  left: 50%;
  width: 50px;
  height: 50px;
  margin-left: -25px;
  border-radius: 50% 50% 50% 50%;
  text-align: center;
  font-size: 1.4em;
  line-height: 50px;
  color: #fff;
  background-color: #999999;
}

.timeline > li.timeline-inverted > .timeline-panel {
  float: right;
}

.timeline > li.timeline-inverted > .timeline-panel:before {
  right: auto;
  left: -15px;
  border-right-width: 15px;
  border-left-width: 0;
}

.timeline > li.timeline-inverted > .timeline-panel:after {
  right: auto;
  left: -14px;
  border-right-width: 14px;
  border-left-width: 0;
}

.timeline-badge.primary {
  background-color: #2e6da4 !important;
}

.timeline-badge.success {
  background-color: #3f903f !important;
}

.timeline-badge.warning {
  background-color: #f0ad4e !important;
}

.timeline-badge.danger {
  background-color: #d9534f !important;
}

.timeline-badge.info {
  background-color: #5bc0de !important;
}

.timeline-title {
  margin-top: 0;
  color: inherit;
}

.timeline-body > p,
.timeline-body > ul {
  margin-bottom: 0;
}

.timeline-body > p + p {
  margin-top: 5px;
}

@media (max-width: 767px) {
  ul.timeline:before {
    left: 40px;
  }

  ul.timeline > li > .timeline-panel {
    width: calc(100% - 90px);
    width: -webkit-calc(100% - 90px);
  }

  ul.timeline > li > .timeline-badge {
    top: 16px;
    left: 15px;
    margin-left: 0;
  }

  ul.timeline > li > .timeline-panel {
    float: right;
  }

  ul.timeline > li > .timeline-panel:before {
    right: auto;
    left: -15px;
    border-right-width: 15px;
    border-left-width: 0;
  }

  ul.timeline > li > .timeline-panel:after {
    right: auto;
    left: -14px;
    border-right-width: 14px;
    border-left-width: 0;
  }
}

.example-button {
  height: 200px;
  background: #74787E;
  background: -webkit-gradient(#FFFFFF, #74787E);
  background: -webkit-linear-gradient(#FFFFFF, #74787E);
  background: linear-gradient(#FFFFFF, #74787E);
  border-radius: 10px;
  box-shadow: #555 2px 2px;
  display: table;
}

.example-button.small {
  height: 150px;
}

.example-button.active {
  background: #e3e3e3;
  background: -webkit-gradient(#74787E, #FFFFFF);
  background: -webkit-linear-gradient(#74787E, #FFFFFF);
  background: linear-gradient(#74787E, #FFFFFF);
}

.spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 60px;
  width: 60px;
  margin: 0px auto;
  -webkit-animation: rotation .6s infinite linear;
  animation: rotation .6s infinite linear;
  border-left: 6px solid rgba(0, 174, 239, 0.15);
  border-right: 6px solid rgba(0, 174, 239, 0.15);
  border-bottom: 6px solid rgba(0, 174, 239, 0.15);
  border-top: 6px solid rgba(0, 174, 239, 0.8);
  border-radius: 100%;
}

@-webkit-keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes rotation {
  from {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}

/**
  Resizable elements
 */

.resizable {
  width: 100px;
  height: 50px;
  background: -webkit-linear-gradient(#2ca02c, #e1faea);
  background: linear-gradient(#2ca02c, #e1faea);
  /* Standard syntax */
  position: absolute !important;
  text-align: center;
  display: table;
  vertical-align: middle;
  overflow: hidden;
}

.resizable span {
  display: table-cell;
  vertical-align: middle;
  overflow: hidden;
  position: absolute;
  top: 10px;
  width: 100%;
}

.resizable .edit {
  z-index: 100 !important;
}

.resizable-area {
  width: 100px;
  height: 50px;
  background: lightpink;
  position: absolute !important;
  text-align: center;
  vertical-align: center;
}

.resizable-area:hover,
.resizable:hover {
  background: lightblue;
}

label {
  font-weight: normal !important;
}

.form-control.error {
  color: red;
  border: 1px solid red;
}

label.error {
  color: red;
}

.dt-button {
  background: #2E6DA4 !important;
  /* For browsers that do not support gradients */
  color: white !important;
  border: none !important;
  font-size: 16px !important;
}

.searchCol {
  font-weight: normal !important;
}

.searchCol input {
  font-weight: normal !important;
}

.searchCol select {
  font-weight: normal !important;
}

.dataTables_length {
  float: right !important;
}

.example-button {
  height: 200px;
  background: #74787E;
  background: -webkit-gradient(#FFFFFF, #74787E);
  background: -webkit-linear-gradient(#FFFFFF, #74787E);
  background: linear-gradient(#FFFFFF, #74787E);
  border-radius: 10px;
  box-shadow: #555 2px 2px;
  display: table;
  text-align: center;
}

.example-button.active {
  background: #e3e3e3;
  background: -webkit-gradient(#74787E, #FFFFFF);
  background: -webkit-linear-gradient(#74787E, #FFFFFF);
  background: linear-gradient(#74787E, #FFFFFF);
}

.example-button span {
  position: absolute;
  top: 0;
  left: 0;
  display: table-cell;
  vertical-align: middle;
  text-align: center;
  width: 100%;
  text-align: center;
}

.colorpicker {
  z-index: 999999 !important;
}

#notificationBar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: center;
  display: none;
  background-color: #cbb956;
  z-index: 99999999;
  color: white;
}

#notificationBar.success {
  background-color: green;
  color: white;
  font-weight: 900;
}

#notificationBar.error {
  background-color: red;
  color: white;
  font-weight: 900;
}

#notificationBar.warning {
  background-color: yellow;
  color: black;
  font-weight: 900;
}

input:-moz-read-only {
  background: white !important;
  background-color: white !important;
}

input:read-only {
  background: white !important;
  background-color: white !important;
}

input:disabled {
  background: #F5F5F5 !important;
  background-color: #F5F5F5 !important;
}

.multiselect-group label {
  font-weight: bold !important;
}

.panel-heading {
  font-family: 'Raleway', sans-serif;
  font-weight: bold !important;
  font-size: 16px !important;
}

.panel-heading small {
  font-weight: normal !important;
  font-family: 'Raleway', sans-serif;
}

.dt-button {
  font-family: 'Raleway', sans-serif;
  font-weight: normal !important;
  font-size: 16px !important;
}

.dt-button:hover{
  background: #367db5 !important;
}

.modal.left .modal-dialog,
.modal.right .modal-dialog {
  position: fixed;
  margin: auto;
  width: 320px;
  height: 100%;
  -webkit-transform: translate3d(0%, 0, 0);
  -ms-transform: translate3d(0%, 0, 0);
  -o-transform: translate3d(0%, 0, 0);
  transform: translate3d(0%, 0, 0);
}

.modal.left .modal-content,
.modal.right .modal-content {
  height: 100%;
  overflow-y: auto;
}

.modal.left .modal-body,
.modal.right .modal-body {
  padding: 15px 15px 80px;
}

/*Left*/
.modal.left.fade .modal-dialog{
  left: -320px;
  -webkit-transition: opacity 0.3s linear, left 0.3s ease-out;
  -moz-transition: opacity 0.3s linear, left 0.3s ease-out;
  -o-transition: opacity 0.3s linear, left 0.3s ease-out;
  transition: opacity 0.3s linear, left 0.3s ease-out;
}

.modal.left.fade.in .modal-dialog{
  left: 0;
}

/*Right*/
.modal.right.fade .modal-dialog {
  right: -320px;
  -webkit-transition: opacity 0.3s linear, right 0.3s ease-out;
  -moz-transition: opacity 0.3s linear, right 0.3s ease-out;
  -o-transition: opacity 0.3s linear, right 0.3s ease-out;
  transition: opacity 0.3s linear, right 0.3s ease-out;
}

.modal.right.fade.in .modal-dialog {
  right: 0;
}

/* ----- MODAL STYLE ----- */
.modal-content {
  border-radius: 0;
  border: none;
}

.modal-header {
  border-bottom-color: #EEEEEE;
  background-color: #FAFAFA;
}

.modal.left.wide .modal-dialog,
.modal.right.wide .modal-dialog {
  width: 450px;
}

.dt-buttons{
 position: -webkit-sticky !important; /* Safari */
 position: sticky !important;
 top: 0 !important;
 background: white;
}

.modal {
  z-index: 2005;
}

.modal-open {
  z-index: 2000;
}

td {
  word-wrap:break-word!important;
}