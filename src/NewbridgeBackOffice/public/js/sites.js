$(function() {

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/company/sites/create'
            },
            edit: {
                type: 'PUT',
                url:  '/company/sites/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/company/sites/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Description:",
                name: "site_name"
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        ajax: '/company/sites/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'site_num', name: 'Site Num', editField: "site_num", type: "string"},
            { data: 'site_name', name: 'Display Name', editField: "site_name", type: "string" },
            { data: 'terminal_count', name: 'Terminal Count', editField: "terminal_count", type: "number" },
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        $('#CreateModal').modal('toggle')
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function (e, dt, node, config) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error','Please select a single row to edit')
                        } else {
                            window.location.href = '/company/sites/edit/'+id;
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Manage Terminals',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single site to manage terminals')
                        } else {
                           window.location.href = '/company/sites/'+id+'/terminals/'
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Printer Names',
                action: function (e, dt, node, config) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single site to manage terminals')
                        } else {
                            window.location.href = "/company/printers/" + id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Colour Settings',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single site to manage terminals')
                        } else {
                            window.location.href = '/company/sites/'+id+'/colours'
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Shared Paths',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error','Please select a single row to edit')
                        } else {
                            window.location.href = '/newbridge/company-network-paths/'+company
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            }
        ]
    });


    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $('#createSubmit').on('click', function(){
        $('#CreateForm').submit()
    })

    var form = $('#CreateForm')

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        rules: {
            ID: "required",
            DisplayName: "required",
            Min: "required",
            Max: "required"
        },
        submitHandler: function (form) {

            $('#CreateModal').modal('toggle')

            $.ajax({
                type: "POST",
                url: "/company/sites/create",
                data: $(form).serialize(),
                success: function () {
                    notificationBar('success', "Site created successfully, it may take a few minutes for all default data to be populated.", '/company/sites', 'Site Created');
                },
                fail: function () {
                    notificationBar('error', "The site failed to create, please try again", '', 'Create Failed');
                },
                error: function () {
                    notificationBar('error', "The site failed to create, please try again", '', 'Create Failed');
                }
            });
        }
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search Name"/></th>')
        $("#table .searchCol").append('<th></th>');
    }

    $(document).on('click', '.manage-terminals-button', function(){
        var site_num = $(this).data('sitenum')
        window.location.href = '/company/sites/'+site_num+'/terminals'
    })
});