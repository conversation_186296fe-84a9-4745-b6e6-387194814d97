$(function () {

    if (permissions.can_edit == 0) {
        editor.disable()
    }

    let url = null;

    if (transactions != null) {
        url = '/transactions/data?transactions=' + transactions;
    } else {
        url = '/transactions/data/' + start1.format('YYYY-MM-DD HH:mm:ss') + '/' + end1.format('YYYY-MM-DD HH:mm:ss') + '/' + site;

        let department = $('#department').val();
        let subdepartment = $('#subdepartment').val();
        let product = $('#product').val();

        url = url + '?department='+department+'&subdepartment='+subdepartment+'&product='+product;
    }

    table = $('#table').on('processing.dt', function (e, settings, processing) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if (processing) {
            if ($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    }).DataTable({
        lengthMenu: [[10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"]],
        dom: "BlrtBip",
        processing: true,
        serverSide: false,
        pageLength: 50,
        ajax: url,
        initComplete: function (settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            {data: 'id', defaultContent: '', className: 'select-checkbox', orderable: true},
            {data: 'finalised_date', name: 'finalised_date', type: "date",
                render: function(data, type, row) {
                    if (!data) return '';
                    return moment.utc(data).tz(userTimezone).format('YYYY-MM-DD HH:mm:ss');
                }},
            {data: 'clerk.full_name', name: 'full_name', type: "string", defaultContent: "Newbridge Admin"},
            {data: 'subtotal', name: 'subtotal', type: "num"},
            {data: 'total_discount_value', name: 'total_discount_value', type: "num"},
            {data: 'total', name: 'total', type: "num"},
            {data: 'order_number', name: 'order_number', type: "string"},
            {data: 'payment_methods', name: 'payment_methods', type: "string"},
            {data: 'table_number_location', name: 'table_number_location', type: "string"},
            {
                "aTargets": [9],
                "mData": "room_number",
                "orderable": true,
                "mRender": function (data, type, full) {

                    if(data == 0){
                        return "None";
                    } else {
                        return data;
                    }
                }
            },
        ],
        order: [1, 'desc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [10]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'View Transaction',
                action: function (e, dt, node, config) {
                    if (permissions.can_view == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to view')
                        } else {
                            viewTransaction(id)
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            }
        ]
    });

    if (openTransaction) {
        viewTransaction(openTransaction)
    }

    function decodeEntities(encodedString) {
        var textArea = document.createElement('textarea');
        textArea.innerHTML = encodedString;
        return '<span>' + textArea.value + '</span>';
    }

    function viewTransaction(id) {
        $.ajax({
            type: "GET",
            url: "/transactions/view/" + id,
            success: function (data) {

            },
            fail: function () {
                flashMessage('error', 'There was an error in your product entry, please try again.')
            }
        }).done(function (data) {
            $('#view-modal-area').html(data)
            $('#ViewModal').modal('toggle')
        });
    }

    $(document).on('keyup', '#idSearch', function () {
        table.column(0).search(this.value).draw();
    });
    $(document).on('keyup', '#dateSearch', function () {
        table.column(1).search(this.value).draw();
    });
    $(document).on('keyup', '#clerkSearch', function () {
        table.column(2).search(this.value).draw();
    });
    $(document).on('keyup', '#subTotalSearch', function () {
        table.column(3).search(this.value).draw();
    });
    $(document).on('keyup', '#discountAmountSearch', function () {
        table.column(4).search(this.value).draw();
    });
    $(document).on('keyup', '#totalSearch', function () {
        table.column(5).search(this.value).draw();
    });
    $(document).on('keyup', '#orderNumberSearch', function () {
        table.column(6).search(this.value).draw();
    });
    $(document).on('keyup', '#searchPaymentMethod', function () {
        table.column(7).search(this.value).draw();
    });
    $(document).on('keyup', '#locationTableSearch', function () {
        table.column(8).search(this.value).draw();
    });
    $(document).on('keyup', '#roomSearch', function () {
        table.column(9).search(this.value).draw();
    });

    function buildSearchColumns() {
        $("#table .searchCol").append('<th><input id="idSearch" class="form-control" placeholder="Search By ID"/></th>');
        $("#table .searchCol").append('<th><input id="dateSearch" class="form-control" placeholder="Search Date"/></th>')
        $("#table .searchCol").append('<th><input id="clerkSearch" class="form-control" placeholder="Search Clerk Name"/></th>')
        $("#table .searchCol").append('<th><input id="subTotalSearch" class="form-control" placeholder="Search Subtotal"/></th>')
        $("#table .searchCol").append('<th><input id="discountAmountSearch" class="form-control" placeholder="Search Discount"/></th>')
        $("#table .searchCol").append('<th><input id="totalSearch" class="form-control" placeholder="Search Total"/></th>')
        $("#table .searchCol").append('<th><input id="orderNumberSearch" class="form-control" placeholder="Search Order #"/></th>')
        $("#table .searchCol").append('<th><input id="searchPaymentMethod" class="form-control" placeholder="Search Payment"/></th>')
        $("#table .searchCol").append('<th><input id="locationTableSearch" class="form-control" placeholder="Search Table #"/></th>')
        $("#table .searchCol").append('<th><input id="roomSearch" class="form-control" placeholder="Search Room #"/></th>')
    }

});
