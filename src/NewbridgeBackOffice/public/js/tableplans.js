$(function() {

    var boolOption = { "Yes":1, "No":0 };

    let buttons = [
        {
            text: 'New',
            action: function ( e, dt, node, config ) {
                if (permissions.can_add == 1) {
                    let site_num = $('#site').val();
                    window.location.href = '/table-plans/create/'+site_num;
                } else {
                    notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                }
            }
        },
        {
            text: 'Delete',
            action: function ( e, dt, node, config ) {
                if (permissions.can_delete == 1) {

                    var id = table.$('tr.selected').attr('id')

                    if (id == undefined) {
                        confirm('Please select a single row to edit')
                    } else {

                        var r = confirm("Are you sure you would like to delete this location and all tables associated with it??");
                        if (r == true) {
                            $.ajax({
                                type: "DELETE",
                                url: "/table-plans/delete/"+id,

                                success: function (data) {

                                },
                                fail: function () {
                                    flashMessage('error', 'There was an error in your request,  please try again.')
                                }
                            }).done(function (data) {
                                table.ajax.reload();
                            });
                        } else {
                            return false;
                        }

                    }
                } else {
                    notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                }
            }
        },
    ];

    let takeawayButton = {
            text: 'Print QR Codes',
            action: function ( e, dt, node, config ) {
                let site_num = $('#site').val();
                notificationBar('info', 'Your QR Codes are being generated, please wait for the download to start');
                window.location.href = '/company/sites/table-qr-codes/' + site_num;
                setTimeout(function() {
                    notificationBar('success', 'Your QR Codes have been generated and are downloading.');
                }, 6000);
            }
        };

    if(takeawayEnabled > 0){
        buttons.push(takeawayButton);
    }

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/table-plans/create'
            },
            edit: {
                type: 'PUT',
                url:  '/table-plans/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/table-plans/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Location Name:",
                name: "displayname"
            },
            {
                label: "App Availability:",
                name: "app_available",
                type: "select",
                options: boolOption
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });


    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Brtip",
        processing: true,
        serverSide: false,
        ajax: '/table-plans/data/'+parseFloat(site),
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'displayname', name: 'Location Name', editField: "displayname", type: "string" },
            { data: 'Segment_Name', name: 'Period Name', editField: "Segment_name", type: "string", editable: false},
            { data: 'table_location_id',  editable: false},
            {
                "aTargets": [5],
                "mData": "app_available",
                "orderable": true,
                "mRender": function (data, type, full) {

                    if(data === 1){
                        return "Yes";
                    } else {
                        return "No";
                    }
                }
            },
            {"mRender": function ( data, type, row ) {
                    return '<a href="/table-plans/edit/'+row.id+'" class="edit-screen-button btn btn-sm btn-primary" data-id="'+row.id+'" data-site="'+$('#site').val()+'">Update Tables</a>';}
            }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: buttons
    });

    $(document).on('change', '#site', function(){

        //TODO reload the table with the selected site as data

        let value = $(this).val();

        window.location.href = '/table-plans/'+value;
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });


    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#shortNameSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#pinSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );
    $(document).on('change', '#numberSearch', function () {
        table.column( 4 ).search( this.value ).draw();
    } );
    $(document).on('change', '#screenSearch', function () {
        table.column( 5 ).search( this.value ).draw();
    } );

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');

        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search Name"/></th>')

        $("#table .searchCol").append('<th></th>');

        $("#table .searchCol").append('<th></th>');

        $("#table .searchCol").append('<th></th>');

        $("#table .searchCol").append('<th></th>');


    }
});
