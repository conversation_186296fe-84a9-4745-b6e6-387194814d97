$(function() {

    var boolOption = { "yes":1, "No":0 };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/sub-departments/create'
            },
            edit: {
                type: 'PUT',
                url:  '/sub-departments/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/sub-departments/delete'
            }
        },
        table: "#sub-department-table",
        fields: [{
            label: "Description:",
            name: "displayname"
        },{
            label: "Account Code:",
            name: "acc_code"
        },{
            label: "Print Priority",
            name: "print_priority"
        }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#sub-department-table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('table')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        initComplete: function(settings, json) {
            $('#sub-department-table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        ajax: '/sub-departments/data',
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'displayname', name: 'displayname', editField: "displayname", type: "string" },
            { data: 'acc_code', name: 'Account Code', editField: "acc_code", type: "string" },
            { data: 'print_priority', name: 'Print Priority', editField: "print_priority", type: "number" },
            {
                "aTargets": [5],
                "mData": "priority",
                "orderable": false,
                edit: false,
                editable: false,
                "mRender": function (data, type, full) {

                        return '<a href="/sub-departments/product-priority/'+full.id+'" title="Re-Order Products"><i class="fa fa-list pull-right" style="cursor: pointer; z-index: 9999;"></i></a>';

                }
            },

        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        $('#CreateModal').modal('toggle')
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {

                            $.ajax({
                                type: "GET",
                                url: "/sub-departments/edit-modal/" + id,
                                success: function (data) {

                                },
                                fail: function () {
                                    notificationBar('error', 'There was an error in your product entry, please try again.')
                                }
                            }).done(function (data) {
                                $('#edit-modal-area').html(data)
                                $('#EditModal').modal('toggle')
                            });
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {

                            notificationBar('delete', 'Are you sure you would like to delete the sub department?', '/sub-departments/delete/'+id, 'Confirm Delete', false, true );

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });

    // Inline editing on click
    $('#sub-department-table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $('#createSubmit').on('click', function(){
        $('#CreateForm').submit()
    })

    var form = $('#CreateForm')

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        rules: {
            description: "required",
            acc_code: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/sub-departments/create",
                data: $(form).serialize(),
                success: function () {
                    $('#CreateModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    notificationBar('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    })

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#descriptionSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#accountSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    function buildSearchColumns()
    {
        $("#sub-department-table .searchCol").append('<th></th>');
        $("#sub-department-table .searchCol").append('<th><input id="descriptionSearch" class="form-control" placeholder="Search Description"/></th>')
        $("#sub-department-table .searchCol").append('<th><input id="accountSearch" class="form-control" placeholder="Search Account Code"/></th>')
        $("#sub-department-table .searchCol").append('<th></th>');
        $("#sub-department-table .searchCol").append('<th></th>');
    }
});