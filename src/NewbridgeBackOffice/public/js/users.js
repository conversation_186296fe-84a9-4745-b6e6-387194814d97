$(function() {

    $('[data-toggle="tooltip"]').tooltip();

    var boolOption = { "yes":1, "No":0 };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/users/create'
            },
            edit: {
                type: 'PUT',
                url:  '/users/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/users/delete'
            }
        },
        table: "#table",
        fields: [{
            label: "Name:",
            name: "name"
        },
        {
            label: "Email:",
            name: 'email'
        },
            {
                label: "Username:",
                name: 'username'
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('table')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        ajax: '/users/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'name', name: 'Name', editField: "name", type: "string" },
            { data: 'username', name: 'Username', editField: "username", type: "string" },
            { data: 'email', name: 'Email', editField: "email", type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        $('#CreateModal').modal('toggle')
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error','Please select a single row to edit')
                        } else {

                            $.ajax({
                                type: "GET",
                                url: "/users/edit-modal/" + id,
                                success: function (data) {
                                },
                                fail: function () {
                                    notificationBar('error', 'There was an error in your product entry, please try again.')
                                }
                            }).done(function (data) {
                                $('#edit-modal-area').html(data)
                                $('#EditModal').modal('toggle')
                                $('#EditModal select').multiselect({
                                    buttonWidth: '100%',
                                    includeSelectAllOption: true,
                                    enableClickableOptGroups: true,
                                    enableCollapsibleOptGroups: true,
                                    collapseOptGroupsByDefault: true,
                                    enableCaseInsensitiveFiltering: true,
                                    dropup: true,
                                    onChange: function (option, checked, select) {
                                        // if (checked == true) {
                                        //     $('#DisplayName').val($(option).text())
                                        // }
                                    },
                                    onDropdownShown: function(event) {
                                        $(event.target).find('.filter input').focus();
                                    }
                                });

                            });
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {

                           // var r = confirm("Are you sure you would like to delete the user?");
                            var r = notificationBar('delete', 'Are you sure you would like to delete the user?', '/users/delete/'+id, 'Confirm Delete', false, true );

                            if (r == true) {
                                $.ajax({
                                    type: "DELETE",
                                    url: "/users/delete",
                                    data: {id: id},
                                    success: function (data) {

                                    },
                                    fail: function () {
                                        flashMessage('error', 'There was an error in your product entry, please try again.')
                                    }
                                }).done(function (data) {
                                    table.ajax.reload();
                                });
                            } else {
                                return false;
                            }
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $('#createSubmit').on('click', function(){
        $('#CreateForm').submit()
    })

    $('body').on('focus', 'input', function(){
        $(this).removeAttr('readonly')
    })
    $('body').on('click', 'input', function(){
        $(this).removeAttr('readonly')
    })

    $('body').on('keyup', '[name="password"]', function(){
        var el = $(this)

        if(el.val() != ''){
            $('[name="confirm_password"]').attr('disabled', false)
        } else {
            $('[name="confirm_password"]').attr('disabled', true)
        }

    })

    var form = $('#CreateForm')

    $('body').on('blur', '[name="username"]', function(){
        var val = $(this).val().split('@')
        $(this).val(val[0]+companyExt)
    })

    jQuery.validator.addMethod("usernameTaken", function(value, element) {
        var isSuccess = false;

        var val = value.split('@');
        var username = val[0]+companyExt;

        $.ajax({
            type: "GET",
            url: "/users/check-username/"+username,
            async: false,
            success: function(data){
                isSuccess = data.taken == 0;
            }
        })
        return isSuccess;
    }, "Username taken, please choose an alternative");

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        onfocusout: function(element) {
            // "lazy" validation by default
            if (!this.checkable(element) && (element.name in this.submitted || !this.optional(element))) {
                this.element(element);
            }
        },
        onkeyup: false,
        rules: {
            first_name: "required",
            last_name: "required",
            username: {
                required: true,
                usernameTaken: true
            },
            email: "required",
            password: "required",
            confirm_new_password: {
                required: function(element){
                    if($('#CreateForm [name="password"]').val() != ''){ return true }
                    return false;
                },
                equalTo: '[name="password"]'
            }
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/users/create",
                data: $(form).serialize(),
                success: function () {
                    $('#CreateModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#unameSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#emailSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );
    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search First Name"/></th>')
        $("#table .searchCol").append('<th><input id="unameSearch" class="form-control" placeholder="Search Username"/></th>')
        $("#table .searchCol").append('<th><input id="emailSearch" class="form-control" placeholder="Search Email"/></th>')
    }
});