$(function() {

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/products/create'
            },
            edit: {
                type: 'PUT',
                url:  '/products/edit'
            },
            remove: {
                type: 'DELETE',
                url:  'products/delete?id=_id_'
            }
        },
        table: "#table",
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    editor.disable()

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "BlrtBip",
        processing: true,
        serverSide: true,
        responsive: true,
        pageLength: 20,
        ajax: '/reports/schedules/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', className: 'select-checkbox', orderable: true},
            { data: 'name', name: 'name', type: "string"},
            { data: 'frequency_name', name: 'frequency_name', type: "string", orderable: false},
            { data: 'time', name: 'time', type: "string"},
            { data: 'last_run', defaultContent: '', name: 'last_run', editField: "last_run", type: "datetime" },

        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    window.location.href = '/reports/schedules/create'
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {

                    var id = table.$('tr.selected').attr('id')
                    if(id == null)
                        notificationBar('error', 'Please select a row to edit');
                    else
                        window.location.href = '/reports/schedules/edit/'+id

                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    var id = table.$('tr.selected').attr('id')
                    if (permissions.can_delete == 1) {

                        if (id == undefined) {
                            confirm('Please select a single row to edit')
                        } else {

                            $.ajax({
                                type: "DELETE",
                                url: "/reports/schedules/delete/"+id,
                            }).fail(function(data) {
                                notificationBar(data.responseJSON.status, data.responseJSON.message)
                            }).done(function (data) {
                                table.ajax.reload();
                            });
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Results',
                action: function ( e, dt, node, config ) {
                    var id = table.$('tr.selected').attr('id')
                    if (id == undefined) {
                        confirm('Please select a single row to view results for')
                    } else {
                        window.location.href = '/reports/schedules/results/' + id
                    }
                }
            },
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    function debounce(fn, delay) {
        var timer = null;
        return function () {
            var context = this, args = arguments;
            clearTimeout(timer);
            timer = setTimeout(function () {
                fn.apply(context, args);
            }, delay);
        };
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');

        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search Name"/></th>')
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th></th>');
    }

});