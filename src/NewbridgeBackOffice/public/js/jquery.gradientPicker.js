// /**
// <AUTHOR> (tantaman)
// */
// (function( $ ) {
// 	if (!$.event.special.destroyed) {
// 		$.event.special.destroyed = {
// 		    remove: function(o) {
// 		    	if (o.handler) {
// 		    		o.handler();
// 		    	}
// 		    }
// 		}
// 	}
//
// 	function ctrlPtComparator(l,r) {
// 		return l.position - r.position;
// 	}
//
// 	function bind(fn, ctx) {
// 		if (typeof fn.bind === "function") {
// 			return fn.bind(ctx);
// 		} else {
// 			return function() {
// 				fn.apply(ctx, arguments);
// 			}
// 		}
// 	}
//
// 	var browserPrefix = "";
// 	var agent = window.navigator.userAgent;
// 	if (agent.indexOf('WebKit') >= 0)
// 		browserPrefix = "-webkit-"
// 	else if (agent.indexOf('Mozilla') >= 0)
// 		browserPrefix = "-moz-"
// 	else if (agent.indexOf('Microsoft') >= 0)
// 		browserPrefix = "-ms-"
// 	else
// 		browserPrefix = ""
//
// 	function GradientSelection($el, opts) {
// 		this.$el = $el;
// 		this.$el.css("position", "relative");
// 		this.opts = opts;
//
// 		var $preview = $("<canvas class='gradientPicker-preview'></canvas>");
// 		this.$el.append($preview);
// 		var canvas = $preview[0];
// 		canvas.width = 100;
// 		canvas.height = 10;
// 		this.g2d = canvas.getContext("2d");
//
// 		var $ctrlPtContainer = $("<div class='gradientPicker-ctrlPts'></div>");
// 		this.$el.append($ctrlPtContainer)
// 		this.$ctrlPtContainer = $ctrlPtContainer;
//
// 		this.updatePreview = bind(this.updatePreview, this);
// 		this.controlPoints = [];
// 		this.ctrlPtConfig = new ControlPtConfig(this.$el, opts);
// 		for (var i = 0; i < opts.controlPoints.length; ++i) {
// 			var ctrlPt = this.createCtrlPt(opts.controlPoints[i]);
// 			this.controlPoints.push(ctrlPt);
// 		}
//
// 		this.docClicked = bind(this.docClicked, this);
// 		this.destroyed = bind(this.destroyed, this);
// 		$(document).bind("click", this.docClicked);
// 		this.$el.bind("destroyed", this.destroyed);
// 		this.previewClicked = bind(this.previewClicked, this);
// 		$preview.click(this.previewClicked);
//
// 		this.updatePreview();
// 	}
//
// 	GradientSelection.prototype = {
// 		docClicked: function() {
// 			this.ctrlPtConfig.hide();
// 		},
//
// 		createCtrlPt: function(ctrlPtSetup) {
// 			return new ControlPoint(this.$ctrlPtContainer, ctrlPtSetup, this.opts.orientation, this, this.ctrlPtConfig)
// 		},
//
// 		destroyed: function() {
// 			$(document).unbind("click", this.docClicked);
// 		},
//
// 		updateOptions: function(opts) {
// 			$.extend(this.opts, opts);
// 			this.updatePreview();
// 		},
//
// 		updatePreview: function() {
// 			var result = [];
// 			this.controlPoints.sort(ctrlPtComparator);
// 			if (this.opts.orientation == "horizontal") {
// 				var grad = this.g2d.createLinearGradient(0, 0, this.g2d.canvas.width, 0);
// 				for (var i = 0; i < this.controlPoints.length; ++i) {
// 					var pt = this.controlPoints[i];
// 					grad.addColorStop(pt.position, pt.color);
// 					result.push({
// 						position: pt.position,
// 						color: pt.color
// 					});
// 				}
// 			} else {
//
// 			}
//
// 			this.g2d.fillStyle = grad;
// 			this.g2d.fillRect(0, 0, 100, this.g2d.canvas.height);
//
// 			if (this.opts.generateStyles)
// 				var styles = this._generatePreviewStyles();
//
// 			this.opts.change(result, styles);
// 		},
//
// 		removeControlPoint: function(ctrlPt) {CanvasGradient
//
// 			if (cpidx != -1) {
// 				this.controlPoints.splice(cpidx, 1);
// 				ctrlPt.$el.remove();
// 			}
// 		},
//
// 		previewClicked: function(e) {
// 			var offset = $(e.target).offset();
// 			var x = e.pageX - offset.left;
// 			var y = e.pageY - offset.top;
//
// 			var imgData = this.g2d.getImageData(x,y,1,1);
// 			var colorStr = "rgb(" + imgData.data[0] + "," + imgData.data[1] + "," + imgData.data[2] + ")";
//
// 			var cp = this.createCtrlPt({
// 				position: x / this.g2d.canvas.width,
// 				color: colorStr
// 			});
//
// 			this.controlPoints.push(cp);
// 			this.controlPoints.sort(ctrlPtComparator);
// 		},
//
// 		_generatePreviewStyles: function() {
// 			//linear-gradient(top, rgb(217,230,163) 86%, rgb(227,249,159) 9%)
// 			var str = this.opts.type + "-gradient(" + ((this.opts.type == "linear") ? (this.opts.fillDirection + ", ") : "");
// 			var first = true;
// 			for (var i = 0; i < this.controlPoints.length; ++i) {
// 				var pt = this.controlPoints[i];
// 				if (!first) {
// 					str += ", ";
// 				} else {
// 					first = false;
// 				}
// 				str += pt.color + " " + ((pt.position*100)|0) + "%";
// 			}
//
// 			str = str + ")"
// 			var styles = [str, browserPrefix + str];
// 			return styles;
// 		}
// 	};
//
// 	function ControlPoint($parentEl, initialState, orientation, listener, ctrlPtConfig) {
// 		this.$el = $("<div class='gradientPicker-ctrlPt'></div>");
// 		$parentEl.append(this.$el);
// 		this.$parentEl = $parentEl;
// 		this.configView = ctrlPtConfig;
//
// 		if (typeof initialState === "string") {
// 			initialState = initialState.split(" ");
// 			this.position = parseFloat(initialState[1])/100;
// 			this.color = initialState[0];
// 		} else {
// 			this.position = initialState.position;
// 			this.color = initialState.color;
// 		}
//
// 		this.listener = listener;
// 		this.outerWidth = this.$el.outerWidth();
//
// 		this.$el.css("background-color", this.color);
// 		if (orientation == "horizontal") {
// 			var pxLeft = ($parentEl.width() - this.$el.outerWidth()) * (this.position);
// 			this.$el.css("left", pxLeft);
// 		} else {
// 			var pxTop = ($parentEl.height() - this.$el.outerHeight()) * (this.position);
// 			this.$el.css("top", pxTop);
// 		}
//
// 		this.drag = bind(this.drag, this);
// 		this.stop = bind(this.stop, this);
// 		this.clicked = bind(this.clicked, this);
// 		this.colorChanged = bind(this.colorChanged, this);
// 		this.$el.draggable({
// 			axis: (orientation == "horizontal") ? "x" : "y",
// 			drag: this.drag,
// 			stop: this.stop,
// 			containment: $parentEl
// 		});
// 		this.$el.css("position", 'absolute');
// 		this.$el.click(this.clicked);
// 	}
//
// 	ControlPoint.prototype = {
// 		drag: function(e, ui) {
// 			// convert position to a %
// 			var left = ui.position.left;
// 			this.position = (left / (this.$parentEl.width() - this.outerWidth));
// 			this.listener.updatePreview();
// 		},
//
// 		stop: function(e, ui) {
// 			this.listener.updatePreview();
// 			this.configView.show(this.$el.position(), this.color, this);
// 		},
//
// 		clicked: function(e) {
// 			this.configView.show(this.$el.position(), this.color, this);
// 			e.stopPropagation();
// 			return false;
// 		},
//
// 		colorChanged: function(c) {
// 			this.color = c;
// 			this.$el.css("background-color", this.color);
// 			this.listener.updatePreview();
// 		},
//
// 		removeClicked: function() {
// 			this.listener.removeControlPoint(this);
// 			this.listener.updatePreview();
// 		}
// 	};
//
// 	function ControlPtConfig($parent, opts) {
// 		//color-chooser
// 		this.$el = $('<div class="gradientPicker-ptConfig" style="visibility: hidden"></div>');
// 		$parent.append(this.$el);
// 		var $cpicker = $('<div class="color-chooser"></div>');
// 		this.$el.append($cpicker);
// 		var $rmEl = $("<div class='gradientPicker-close'></div>");
// 		this.$el.append($rmEl);
//
// 		this.colorChanged = bind(this.colorChanged, this);
// 		this.removeClicked = bind(this.removeClicked, this);
// 		$cpicker.ColorPicker({
// 			onChange: this.colorChanged
// 		});
// 		this.$cpicker = $cpicker;
// 		this.opts = opts;
// 		this.visible = false;
//
// 		$rmEl.click(this.removeClicked);
// 	}
//
// 	ControlPtConfig.prototype = {
// 		show: function(position, color, listener) {
// 			this.visible = true;
// 			this.listener = listener;
// 			this.$el.css("visibility", "visible");
// 			this.$cpicker.ColorPickerSetColor(color);
// 			this.$cpicker.css("background-color", color);
// 			if (this.opts.orientation === "horizontal") {
// 				this.$el.css("left", position.left);
// 			} else {
// 				this.$el.css("top", position.top);
// 			}
// 			//else {
// 			//	this.visible = false;
// 				//this.$el.css("visibility", "hidden");
// 			//}
// 		},
//
// 		hide: function() {
// 			if (this.visible) {
// 				this.$el.css("visibility", "hidden");
// 				this.visible = false;
// 			}
// 		},
//
// 		colorChanged: function(hsb, hex, rgb) {
// 			hex = "#" + hex;
// 			this.listener.colorChanged(hex);
// 			this.$cpicker.css("background-color", hex)
// 		},
//
// 		removeClicked: function() {
// 			this.listener.removeClicked();
// 			this.hide();
// 		}
// 	};
//
// 	var methods = {
// 		init: function(opts) {
// 			opts = $.extend({
// 				controlPoints: ["#FFF 0%", "#000 100%"],
// 				orientation: "horizontal",
// 				type: "linear",
// 				fillDirection: "left",
// 				generateStyles: true,
// 				change: function() {}
// 			}, opts);
//
// 			this.each(function() {
// 				var $this = $(this);
// 				var gradSel = new GradientSelection($this, opts);
// 				$this.data("gradientPicker-sel", gradSel);
// 			});
// 		},
//
// 		update: function(opts) {
// 			this.each(function() {
// 				var $this = $(this);
// 				var gradSel = $this.data("gradientPicker-sel");
// 				if (gradSel != null) {
// 					gradSel.updateOptions(opts);
// 				}
// 			});
// 		}
// 	};
//
// 	$.fn.gradientPicker = function(method, opts) {
// 		if (typeof method === "string" && method !== "init") {
// 			methods[method].call(this, opts);
// 		} else {
// 			opts = method;
// 			methods.init.call(this, opts);
// 		}
// 	};
// })( jQuery );


(function(a){a.gradientCreator=function(d,g){var n={gradient:"0% #02CDE8,100% #000000",width:a(d).parent().innerWidth(),height:18,pointSize:8,orientation:"vertical",target:"",tooltipGradient:"0% #feffff,100% #ededed",onChange:function(){},onInit:function(){},noSupport:function(){alert("Sorry, your browser doesn't support HTML5. Please upgrade it.")}};var h=this;h.settings={};var t=a(d),d=d;var x,u,A,f,F;var D,p,o,q;var z,B,b=new Array(),E;h.init=function(){h.settings=a.extend({},n,g);if(i()){h.update();h.settings.onInit()}else{h.settings.noSupport()}};h.update=function(){m();s();r()};h.getCssGradient=function(){var J="";var Q="0%";var P="100%";var I="left bottom";var M="top";var N="0";if(h.settings.orientation=="horizontal"){Q="100%";P="0%";I="right top";M="left";N="1"}var L='<svg xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="gradient" x1="0%" y1="0%" x2="'+Q+'" y2="'+P+'">';var H="progid:DXImageTransform.Microsoft.gradient( startColorstr='"+b[0][1]+"', endColorstr='"+b[b.length-1][1]+"',GradientType="+N+")";var K="-webkit-gradient(linear, left top, "+I;var O="";a.each(b,function(R,S){K+=", color-stop("+S[0]+", "+S[1]+")";O+=","+S[1]+" "+S[0]+"";L+='<stop offset="'+S[0]+'" style="stop-color:'+S[1]+';" />'});K+=")";O=O.substr(1);L+='</linearGradient></defs><rect fill="url(#gradient)" height="100%" width="100%" /></svg>';L=G(L);J+="background: url(data:image/svg+xml;base64,"+L+");\n";J+="background: "+K+";\n";J+="background: -moz-linear-gradient("+M+","+O+");\n";J+="background: -webkit-linear-gradient("+M+","+O+");\n";J+="background: -o-linear-gradient("+M+","+O+");\n";J+="background: -ms-linear-gradient("+M+","+O+");\n";J+="background: linear-gradient("+M+","+O+");";return J};h.getArrayGradient=function(){return b};h.getStringGradient=function(){var H="";a.each(b,function(I,J){H+=J[0]+" "+J[1]+","});H=H.substr(0,H.length-1);return H};h.setOrientation=function(H){h.settings.orientation=H;e()};var m=function(){b=new Array();if(a.isArray(h.settings.gradient)){b=h.settings.gradient}else{b=v(h.settings.gradient)}};var s=function(){t.html("");x=a('<div class="gcGradientCreator"></div>');u=a('<canvas class="gcGradient" width="'+h.settings.width+'" height="'+h.settings.height+'"></canvas>');x.append(u);z=u.get(0).getContext("2d");A=a('<div class="gcPoints"></div>');A.css("width",h.settings.width+"px");var H=a('<div class="gcPoint"></div>');A.css("width",(h.settings.width)+"px");x.append(A);f=a('<div class="gcPointsInfos"></div>');f.append('<div class="gcPointsInfosArrow"></div>');x.append(f);F=a('<div class="gcPointsInfosContent"></div>');f.append(F);E=v(h.settings.tooltipGradient);w(F,E);F.css("color",h.settings.tooltipTextColor);f.find(".gcPointsInfosArrow").css("borderColor","transparent transparent "+E[0][1]+" transparent");t.hover(function(){t.addClass("hover")},function(){t.removeClass("hover")});D=a('<div class="gcPointColor"><div style="background-color: #00ff00"></div></div>');p=a('<span class="gcPointPosition">%</span>');q=a('<a href="javascript:" class="gcBtnPointDel"></a>');F.append(D,p,q);t.append(x);D.ColorPicker({color:"#00ff00",onSubmit:function(I,K,J){t.find(".gcPointColor div").css("backgroundColor","#"+K);B.css("backgroundColor","#"+K);l();e()},onChange:function(I,K,J){t.find(".gcPointColor div").css("backgroundColor","#"+K);B.css("backgroundColor","#"+K);l();e()}});a(document).bind("click",function(){if(!t.is(".hover")){f.hide("fast")}});u.unbind("click");u.bind("click",function(L){var M=u.offset();var J=L.pageX-M.left;J=Math.round((J*100)/h.settings.width);var K="#000000";var I=9999999999999;a.each(b,function(N,O){if((parseInt(O[0])<J)&&(J-parseInt(O[0])<I)){I=J-parseInt(O[0]);K=O[1]}else{if((parseInt(O[0])>J)&&(parseInt(O[0])-J<I)){I=parseInt(O[0])-J;K=O[1]}}});b.push([J+"%",K]);b.sort(k);r();a.each(b,function(N,O){if(O[0]==J+"%"){C(A.find(".gcPoint:eq("+N+")"))}})})};var r=function(){c();l();e()};var c=function(){A.html("");a.each(b,function(H,I){A.append('<div class="gcPoint" style="background-color: '+I[1]+"; left:"+(parseInt(I[0])*h.settings.width)/100+"px; top:-"+(H*(h.settings.pointSize+2))+'px"><div class="gcPointArrow"></div></div>')});A.find(".gcPoint").css("width",h.settings.pointSize+"px");A.find(".gcPoint").css("height",h.settings.pointSize+"px");A.find(".gcPoint").mouseup(function(){C(this)});A.find(".gcPoint").draggable({axis:"x",containment:"parent",drag:function(){if(parseInt(jQuery(this).css("left"))>A.width()){jQuery(this).css("left",A.width())}if(parseInt(jQuery(this).css("left"))<0){jQuery(this).css("left",0)}C(this);l();e()},stop:function(){if(parseInt(jQuery(this).css("left"))>A.width()){jQuery(this).css("left",A.width())}if(parseInt(jQuery(this).css("left"))<0){jQuery(this).css("left",0)}C(this);l();e()}})};var C=function(K){B=a(K);var J=a(K).css("backgroundColor");var I=parseInt(a(K).css("left"));I=Math.round((I/h.settings.width)*100);J=J.substr(4,J.length);J=J.substr(0,J.length-1);D.ColorPickerSetColor(y(J.split(",")));D.find("div").css("backgroundColor",y(J.split(",")));p.html("Position: "+I+"%");q.unbind("click");q.bind("click",function(){if(b.length>1){b.splice(B.index(),1);r();t.find(".gcPointsInfos").hide("fast")}});var H=parseInt(a(K).css("left"))-30;t.find(".gcPointsInfos").css("marginLeft",H+"px");t.find(".gcPointsInfos").show("fast")};var l=function(){b=new Array();t.find(".gcPoint").each(function(J,K){var H=Math.round((parseInt(a(K).css("left"))/h.settings.width)*100);if(H>100){H=100}var I=a(K).css("backgroundColor").substr(4,a(K).css("backgroundColor").length-5);I=y(I.split(","));b.push([H+"%",I])});b.sort(k);j();h.settings.onChange(h.getStringGradient(),h.getCssGradient(),h.getArrayGradient())};var w=function(J,N){var S="0%";var R="100%";var I="left bottom";var M="top";var O="0";if((J==u)||(h.settings.orientation=="horizontal")){S="100%";R="0%";I="right top";M="left";O="1"}var L='<svg xmlns="http://www.w3.org/2000/svg"><defs><linearGradient id="gradient" x1="0%" y1="0%" x2="'+S+'" y2="'+R+'">';var H="progid:DXImageTransform.Microsoft.gradient( startColorstr='"+N[0][1]+"', endColorstr='"+N[N.length-1][1]+"',GradientType="+O+")";var K="-webkit-gradient(linear, left top, "+I;var Q="";a.each(N,function(T,U){K+=", color-stop("+U[0]+", "+U[1]+")";Q+=","+U[1]+" "+U[0]+"";L+='<stop offset="'+U[0]+'" style="stop-color:'+U[1]+';" />'});K+=")";Q=Q.substr(1);L+="</linearGradient></defs>";if(J==F){var P=parseInt(F.css("borderRadius"));L+='<rect fill="url(#gradient)" height="100%" width="100%" rx="'+P+'" ry="'+P+'" />'}else{L+='<rect fill="url(#gradient)" height="100%" width="100%" />'}L+="</svg>";L=G(L);J.css("background","url(data:image/svg+xml;base64,"+L+")");J.css("background",K);J.css("background","-moz-linear-gradient("+M+","+Q+")");J.css("background","-webkit-linear-gradient("+M+","+Q+")");J.css("background","-o-linear-gradient("+M+","+Q+")");J.css("background","-ms-linear-gradient("+M+","+Q+")");J.css("background","linear-gradient("+M+","+Q+")")};var e=function(){if(h.settings.target!=""){var H=a(h.settings.target);w(H,b)}};var j=function(){var H=z.createLinearGradient(0,0,h.settings.width,0);a.each(b,function(I,J){H.addColorStop(parseInt(J[0])/100,J[1])});z.clearRect(0,0,h.settings.width,h.settings.height);z.fillStyle=H;z.fillRect(0,0,h.settings.width,h.settings.height);h.settings.onChange(h.getStringGradient(),h.getCssGradient(),h.getArrayGradient())};var v=function(I){var J=new Array();var H=I.split(",");a.each(H,function(M,N){var K;if((N.substr(N.indexOf("%")-3,N.indexOf("%"))=="100")||(N.substr(N.indexOf("%")-3,N.indexOf("%"))=="100%")){K="100%"}else{if(N.indexOf("%")>1){K=parseInt(N.substr(N.indexOf("%")-2,N.indexOf("%")));K+="%"}else{K=parseInt(N.substr(N.indexOf("%")-1,N.indexOf("%")));K+="%"}}var L=N.substr(N.indexOf("#"),7);J.push([K,L])});return J};var y=function(N){var L=N[0];var O=N[1];var H=N[2];function J(Q){Q=parseInt(Q,10);if(isNaN(Q)){return"00"}Q=Math.max(0,Math.min(Q,255));return"0123456789ABCDEF".charAt((Q-Q%16)/16)+"0123456789ABCDEF".charAt(Q%16)}function I(Q){return(Q.charAt(0)=="#")?Q.substring(1,7):Q}function P(Q){return parseInt((I(Q)).substring(0,2),16)}function K(Q){return parseInt((I(Q)).substring(2,4),16)}function M(Q){return parseInt((I(Q)).substring(4,6),16)}return"#"+J(L)+J(O)+J(H)};var k=function(I,H){if(parseInt(I[0])<parseInt(H[0])){return -1}if(parseInt(I[0])>parseInt(H[0])){return 1}return 0};var G=function(K){var I="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var H="";var R,P,N;var Q,O,M,L;var J=0;while(J<K.length){R=K.charCodeAt(J++);P=K.charCodeAt(J++);N=K.charCodeAt(J++);Q=R>>2;O=((R&3)<<4)|(P>>4);M=((P&15)<<2)|(N>>6);L=N&63;if(isNaN(P)){M=L=64}else{if(isNaN(N)){L=64}}H+=I.charAt(Q)+I.charAt(O)+I.charAt(M)+I.charAt(L)}return H};var i=function(){var H=document.createElement("canvas");return !!(H.getContext&&H.getContext("2d"))};h.init()};a.fn.gradientCreator=function(b){return this.each(function(){if(undefined==a(this).data("gradientCreator")){var c=new a.gradientCreator(this,b);a(this).data("gradientCreator",c)}})}})(jQuery);