/*!
 Bootstrap integration for DataTables' Editor
 ©2015 SpryMedia Ltd - datatables.net/license
*/
(function(c){"function"===typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-editor"],function(a){return c(a,window,document)}):"object"===typeof exports?module.exports=function(a,d){a||(a=window);if(!d||!d.fn.dataTable)d=require("datatables.net-bs4")(a,d).$;d.fn.dataTable.Editor||require("datatables.net-editor")(a,d);return c(d,a,a.document)}:c(jQuery,window,document)})(function(c,a,d){a=c.fn.dataTable;a.Editor.defaults.display="semanticui";c.extend(!0,c.fn.dataTable.Editor.classes,
{header:{wrapper:"DTE_Header header"},body:{wrapper:"DTE_Body content"},footer:{wrapper:"DTE_Footer actions"},form:{tag:"ui form",button:"ui button",content:"DTE_Form_Content"},field:{wrapper:"DTE_Field inline fields",label:"right aligned five wide field",input:"eight wide field DTE_Field_Input",error:"error has-error","msg-labelInfo":"ui small","msg-info":"ui small","msg-message":"ui message small","msg-error":"ui error message small",multiValue:"ui message multi-value",multiInfo:"small",multiRestore:"ui message multi-restore"},
inline:{wrapper:"DTE DTE_Inline ui form"},bubble:{table:"DTE_Bubble_Table ui form",bg:"ui dimmer modals page transition visible active"}});c.extend(!0,a.ext.buttons,{create:{formButtons:{className:"primary"}},edit:{formButtons:{className:"primary"}},remove:{formButtons:{className:"negative"}}});var b;a.Editor.display.semanticui=c.extend(!0,{},a.Editor.models.displayController,{init:function(){if(!b._dom.modal){b._dom.modal=c('<div class="ui modal"></div>');b._dom.close=c('<i class="close icon"/>').click(function(){b._dte.close("icon");
return false});c(d).on("click","div.ui.dimmer.modals",function(a){c(a.target).hasClass("modal")&&b._shown&&b._dte.background()})}return b},open:function(a,d,e){if(b._shown)e&&e();else{b._dte=a;b._shown=true;var a=b._dom.modal,f=c(d).children();a.children().detach();a.append(f).prepend(a.children(".header")).addClass(d.className).prepend(b._dom.close);c(b._dom.modal).appendTo("body").modal("setting",{dimmerSettings:{closable:false},onVisible:function(){b._dte.s.setFocus&&b._dte.s.setFocus.focus();
e&&e()},onHidden:function(){c(d).append(f);c(this).detach();b._shown=false}}).modal("show")}},close:function(a,c){var d=b._dom.modal;if(b._shown){d.modal("hide");b._dte=a;b._shown=false}c&&c()},node:function(){return b._dom.modal[0]},_shown:!1,_dte:null,_dom:{}});b=a.Editor.display.semanticui;return a.Editor});
