$(function() {

    var boolOption = { "yes":1, "No":0 };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/discounts/create'
            },
            edit: {
                type: 'PUT',
                url:  '/discounts/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/discounts/delete?id=_id_'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Name:",
                name: "displayname"
            },
            {
                label: "Discount Amount:",
                name: "value"
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "BlrtBip",
        processing: true,
        serverSide: true,
        responsive: true,
        pageLength: 20,
        ajax: '/discounts/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', className: 'select-checkbox', orderable: true},
            { data: 'displayname', name: 'displayname', type: "string"},
            { data: 'discount_method_name', name: 'type', type: "string"},
            { data: 'value', name: 'value', type: "string"}
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {

                        $('#CreateModal').modal('toggle')
                        $('#CreateModal select').multiselect({
                            buttonWidth: '100%',
                            includeSelectAllOption: true,
                            enableClickableOptGroups: true,
                            enableCollapsibleOptGroups: true,collapseOptGroupsByDefault: true,
                            enableCaseInsensitiveFiltering: true,
                            onChange: function (option, checked, select) {
                                // if (checked == true) {
                                //     $('#DisplayName').val($(option).text())
                                // }
                            },
                            onDropdownShown: function(event) {
                                $(event.target).find('.filter input').focus();
                            }
                        });
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {

                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error','Please select a single row to edit')
                        }

                        // have we selected more than one item?

                        var rows = table.rows('.selected').data().length

                        if(rows == 1) {

                            var id = table.$('tr.selected').attr('id')

                            if (id == undefined) {
                                notificationBar('error','Please select a single row to edit')
                            } else {

                                $.ajax({
                                    type: "GET",
                                    url: "/discounts/edit-modal/" + id,
                                    success: function (data) {

                                    },
                                    fail: function () {
                                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                                    }
                                }).fail(function (data) {
                                    notificationBar(data.responseJSON.status, data.responseJSON.message)
                                }).done(function (data) {
                                    $('#edit-modal-area').html(data)
                                    $('#EditModal').modal('toggle')
                                    $('#EditModal select').multiselect({
                                        buttonWidth: '100%',
                                        includeSelectAllOption: true,
                                        enableClickableOptGroups: true,
                                        enableCollapsibleOptGroups: true,collapseOptGroupsByDefault: true,
                                        enableCaseInsensitiveFiltering: true,
                                        onChange: function (option, checked, select) {
                                            // if (checked == true) {
                                            //     $('#DisplayName').val($(option).text())
                                            // }
                                        },
                                        onDropdownShown: function(event) {
                                            $(event.target).find('.filter input').focus();
                                        }
                                    });
                                });
                            }
                        }

                        if(rows > 1)
                        {
                            var rowIds = table.rows('.selected').ids().toArray();

                            $.ajax({
                                type: "POST",
                                url: "/discounts/mass-edit-modal",
                                data: { ids: rowIds},
                                success: function (data) {

                                },
                                fail: function () {
                                    notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                                }
                            }).fail(function (data) {
                                notificationBar(data.responseJSON.status, data.responseJSON.message)
                            }).done(function (data) {
                                $('#edit-modal-area').html(data)
                                $('#EditPromotionModal').modal('toggle')
                            });
                        }

                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    var id = table.$('tr.selected').attr('id')
                    if (permissions.can_delete == 1) {

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to delete')
                        } else {

                            notificationBar('delete', 'Are you sure you would like to delete the discount?', '/discounts/delete/'+id, 'Confirm Delete', false, true );

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            }
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $('#createForm').on('click', function(){
        $('#CreateForm').submit()
    });

    var form = $('#CreateForm');

    $.validator.addMethod(
        "startEndCheck",
        function(value, element) {

            if($(element).attr('name') == 'start_date'){
                var start = moment($(element).val(), 'DD-MM-YYYY');
                var end = moment($('[name="end_date"]').val(), 'DD-MM-YYYY')

                return start.isBefore(end);
            }

            if($(element).attr('name') == 'end_date'){
                var start = moment($('[name="start_date"]').val(), 'DD-MM-YYYY');
                var end = moment($(element).val(), 'DD-MM-YYYY')

                return start.isBefore(end);
            }

            if($(element).attr('name') == 'start_time'){

                var start = moment($(element).val(), 'hh:mm:ss');
                var end = moment($('[name="end_time"]').val(), 'hh:mm:ss')

                return start.isBefore(end);
            }

            if($(element).attr('name') == 'end_time'){
                var start = moment($('[name="start_time"]').val(), 'hh:mm:ss');
                var end = moment($(element).val(), 'hh:mm:ss')

                return start.isBefore(end);
            }
        },
        "We are ending before we have begun!"
    );

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        rules: {
            display_name: "required",
            receipt_text: "required",
            type: "required",
            discount_method: "required",
            value: {
                required: true,
                number: true
            },
            start_date: {
                required: true,
                startEndCheck: true
            },
            end_date: {
                required: true,
                startEndCheck: true
            },
            start_time: {
                required: true,
                startEndCheck: true
            },
            end_time: {
                required: true,
                startEndCheck: true
            },
            days: "required"
        },
        messages: {

        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/discounts/create",
                data: $(form).serialize(),
                success: function () {
                    $('#CreateModal').modal('toggle')
                    // flashMessage('success', 'Your product has been added successfully!')
                    table.ajax.reload();

                    $('#CreateForm').each(function(){
                        this.reset();
                    });
                }

            }).fail(function(data) {
                $('#CreateModal').modal('toggle')
                notificationBar(data.responseJSON.status, data.responseJSON.message)
            });
        }
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999
        }).appendTo($(target).css("position", "relative"));
    }

    function debounce(fn, delay) {
        var timer = null;
        return function () {
            var context = this, args = arguments;
            clearTimeout(timer);
            timer = setTimeout(function () {
                fn.apply(context, args);
            }, delay);
        };
    }

    $(document).on('keyup', '#descriptionSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#priceSearch', function () {
        table.column( 6 ).search( this.value ).draw();
    } );
    $(document).on('change', '#skuSearch', function () {
        table.column( 5 ).search( this.value ).draw();
    } );
    $(document).on('change', '#supplierSearch', function () {
        table.column( 4 ).search( this.value ).draw();
    } );

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');

        $("#table .searchCol").append('<th><input id="descriptionSearch" class="form-control" placeholder="Search Description"/></th>')

        $("#table .searchCol").append('<th><select id="departmentSearch" class="form-control"></select></th>')

        $("#table .searchCol").append('<th><select id="subDepartmentSearch" class="form-control"></select></th>')


    }

    $(function() {
        $('.btn').button();
    })

});