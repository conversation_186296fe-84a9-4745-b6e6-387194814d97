$(function() {

    var boolOption = { "yes":1, "No":0 };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/asset-group/create'
            },
            edit: {
                type: 'PUT',
                url:  '/asset-group/inline-edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/asset-group/delete'
            }
        },
        table: "#table",
        fields: [{
            label: "Name:",
            name: "name"
        },
            {
                label: "Description:",
                name: "description"
            }

        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        // editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }

    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        // initComplete: function(settings, json) {
        //     $('#table thead').append('<tr role="row" class="searchCol"></tr>')
        //     buildSearchColumns()
        // },
        ajax: '/asset-group/data',
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'name', name: 'Group Name', type: "string", editField: "name" },
            { data: 'description', name: 'Group Description', editField: "description", type: "string" },
            { data: 'site.site_name', name: 'Site Name', type: "string" },
            { data: 'asset_count', name: 'Asset Count', type: "string", editable: false }

        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/asset-group/create';
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {
                            window.location.href = "/asset-group/edit/" + id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {

                            notificationBar('delete', 'Are you sure you would like to delete the Asset Group?', '/asset-group/delete/'+id, 'Confirm Delete', false, true );

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Manage Asset Group',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {
                            window.location.href = "/asset-group/" + id +"/assets"
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });


    // Inline editing on click
    $('#department-table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

});