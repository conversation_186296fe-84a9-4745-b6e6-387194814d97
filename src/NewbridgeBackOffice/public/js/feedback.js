$(function() {

    var boolOption = { "yes":1, "No":0 };
    let statuses = {
        "Newly submitted by customer": 0,
        "Planned for development": 1,
        "Known bug": 2,
        "Approved for development": 3,
        "Currently in development": 4,
        "In post-development testing": 5,
        "Ready for deployment": 6,
        "Released feature" : 7

    };
    let bugTypes = {
        "Bug" : 1,
        "Enhancement" : 2
    }

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url: '/newbridge/bug-reports/store'
            },
            edit: {
                type: 'PUT',
                url: '/newbridge/bug-reports/edit'
            },
            remove: {
                type: 'DELETE',
                url: '/newbridge/bug-reports/delete?id=_id_'
            }
        },
        table: "#feedback-table",
        fields: [{
            label: "Id:",
            name: "id"
        }, {
                label: "Type",
                name: "type",
                type:"select",
                options: bugTypes
        },{
            label: "Status:",
            name: "status",
            type: "select",
            options: statuses
        },{
            label: "User",
            name: "user"
        }, {
            label: "Subject",
            name : "details"
        }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#feedback-table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('table')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "BlrtBip",
        processing: true,
        serverSide: false,
        ajax: '/newbridge/bug-reports/data',
        initComplete: function(settings, json) {
            $('#feedback-table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', className: 'select-checkbox', orderable: true},
            { data: 'type_text', name: 'type', editField: "type", type: "string" },
            { data: 'status_text', name: 'Status', editField: "status", type: "string" },
            { data: 'user.username', name: 'User', editable: false, type: "string", defaultContent: 'Unknown' },
            { data: 'detail_summary', name: 'Details', editable: false, type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'View',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            confirm('Please select a single row to view')
                        } else {

                            window.location.href = '/newbridge/bug-reports/view/'+id

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },{
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            confirm('Please select a single row first')
                        } else {

                            notificationBar('delete',
                                'Are you sure you would like to delete this issue?',
                                '/newbridge/bug-reports/delete/' + id, 'Confirm Delete', false, true);


                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }

            }
        ]
    });

    // Inline editing on click
    $('#feedback-table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('change', '#typeSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('change', '#statusSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#nameSearch', function () {
        if(this.value.length > 2){
            table.column( 3 ).search( this.value ).draw();
        }

    } );

    function buildSearchColumns()
    {
        $("#feedback-table .searchCol").append('<th></th>');
        $("#feedback-table .searchCol").append('<th><select class="form-control" name="typeSearch" id="typeSearch">' +
            '<option value="">Any type</option>' +
            '<option value="Bug">Bug</option>' +
            '<option value="Enhancement">Enhancement</option>' +
            '</select>' +
            '</th>')
        $("#feedback-table .searchCol").append('<th><select class="form-control" name="statusSearch" id="statusSearch">' +
            '<option value="">Any status</option>' +
            '<option value="Newly submitted by customer">Newly submitted by customer</option>' +
            '<option value="Planned for development">Planned for development</option>' +
            '<option value="Known bug">Known bug</option>' +
            '<option value="Approved for development">Approved for development</option>' +
            '<option value="Currently in development">Currently in development</option>' +
            '<option value="In post-development testing">In post-development testing</option>' +
            '<option value="Ready for deployment">Ready for deployment</option>' +
            '<option value="Released feature">Released feature</option>' +
            '</select></th>')
        $("#feedback-table .searchCol").append('<th>' +
            '<input class="form-control" name="nameSearch" id="nameSearch" ' +
            'type="text" placeholder="Type 2 or more letters to search">' +
            '</th>');
        $("#feedback-table .searchCol").append('<th></th>');
    }
});