$(document).on('click', '#ButtonEditModal #save-products', function(){

    if(addItem){

        linkProducts(addItem);

    } else {
        notificationBar('error', 'Please choose an action to add to this button!')
    }
})

$(document).on('click', '#ButtonEditModal #save-name', function(){
    saveName();
});

function linkProducts(item)
{
    var vals = item.CommandUID;
    var name = item.displayname;

    $('#_'+buttonStyles.id +' span').html(name)

    var elms = $('.resizable span')

    $('#DisplayName').val(name)

    _.each(elms, function(e){
        var height = $(e).parent().height()/2
        var elHeight = $(e).height()
        var diff = height-(elHeight/2);
        $(e).css('top', diff+'px')
    })

    let types = {Products: 0, Functions: 1, Pages: 1, "Payment Methods": 2, "Discounts": 3}

    addLink(types[item.type], vals, name);
}

function addLink(type, vals, name) {

    var count = $('#ButtonEditModal #sortable2 li').length;
    count++;

    $.ajax({
        type: 'POST',
        url: '/buttons/link/'+buttonGuid,
        data: {
            guids: vals,
            type: type,
            name: name
        },
        dataType: 'json',
        success: function(data) {
            _.each(data.links, function(link){
                $('#ButtonEditModal #sortable2').append(JSON.parse(link));
            })
            addItem = undefined;
            $('.js-typeahead-user_v1').val('')
        },
        error: function() {
            $('#info').html('<p>An error has occurred</p>');
        }
    });
}

function saveLinkOrder(ordered) {

    $.ajax({
        type: 'POST',
        url: '/buttons/reorder-links',
        data: {data: ordered},
        dataType: 'json',
        success: function(data) {
            //notificationBar('success', 'Button link order updated')
        },
        error: function() {
            notificationBar('error', 'Link order not updated, please try again')
        }
    });
}

function saveName()
{
    var name = $('#ButtonEditModal #DisplayName').val();

    $('#_'+buttonStyles.id +' span').html(name);

    $.ajax({
        url: '/buttons/save-name/'+buttonGuid,
        error: function() {
            $('#info').html('<p>An error has occurred</p>');
        },
        data: {
            name: name
        },
        dataType: 'json',
        success: function(data) {
            $('#ButtonEditModal').modal('toggle');
        },
        type: 'POST'
    });
}

$(document).on('click', '#ButtonEditModal .removelink', function () {
    var element = $(this)
    var id = $(this).data('id');

    $.ajax({
        url: '/buttons/delete-link/'+id,
        error: function() {
            $('#info').html('<p>An error has occurred</p>');
        },
        dataType: 'json',
        success: function(data) {
            element.remove()
        },
        type: 'DELETE'
    });
})

$(document).on('click', '#ButtonEditModal #copy-button', function (e) {
    e.preventDefault();

    $(this).hide();
    $('#ButtonEditModal #copy-confirm').show();
})

$(document).on('click', '#ButtonEditModal #copy-confirm', function (e) {
    e.preventDefault();

    copyButton(buttonId)
})

function copyButton(id)
{

    $('#ButtonEditModal #copy-confirm').hide();
    $('#ButtonEditModal #copy-loading').show();

    var pages = $('#ButtonEditModal #pages').val()

    if(pages.length > 0) {

        $.ajax({
            url: '/buttons/copy-button/'+id,
            data: {pages: pages},
            error: function() {
                notificationBar('error', 'Failed to copy this button')
                $('#ButtonEditModal #copy-loading').hide();
                $('#ButtonEditModal #copy-button').show();
            },
            dataType: 'json',
            success: function(data) {
                notificationBar('success', 'Button copied to all pages')
                $('#ButtonEditModal #copy-loading').hide();
                $('#ButtonEditModal #copy-button').show();
            },
            type: 'POST'
        });
    } else {

        $('#ButtonEditModal #copy-confirm').hide();
        $('#ButtonEditModal #copy-loading').hide();
        $('#ButtonEditModal #copy-button').show();

        notificationBar('error', 'Please select one or more pages to copy this button to.')
    }

}