$(function() {

    var boolOption = { "yes":1, "No":0 };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/clerks/create'
            },
            edit: {
                type: 'PUT',
                url:  '/clerks/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/clerks/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Full Name:",
                name: "full_name"
            },
            {
                label: "Pin:",
                name: 'pin'
            },
            {
                label: "Employee Number:",
                name: 'employee_no'
            },
            {
                label: "Short Name:",
                name: 'short_name'
            },
            {
                label: "Permission Group",
                name: 'permission_group_guid',
                type: 'select',
                options: permissionGroups
            },
            {
                label: "Default Page",
                name: 'default_screen_guid',
                type: 'select',
                options: pages
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('table')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Brtip",
        processing: true,
        serverSide: false,
        ajax: '/clerks/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'full_name', name: 'full_name', editField: "full_name", defaultContent: "UNKNOWN", type: "string" },
            { data: 'short_name', name: 'short_name', editField: "short_name", type: "string" },
            { data: 'pin', name: 'Pin', editField: "pin", type: "string" },
            { data: 'employee_no', name: 'employee_no', editField: "employee_no", type: "string" },
            { data: 'permission_group.name', name: 'permission_group.name', defaultContent: "None Selected", editField: "permission_group_guid", type: "select", orderable: false },
            { data: 'screen.DisplayName', name: 'screen.DisplayName', defaultContent: "Default", editField: "default_screen_guid", type: "select", orderable: false }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/clerks/create';
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Copy',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single clerk to copy')
                        } else {
                            window.location.href = '/clerks/copy/'+id;
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {
                            window.location.href = '/clerks/edit/'+id;
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {

                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to delete')
                        } else {

                            notificationBar('delete', 'Are you sure you would like to delete the clerk?', '/clerks/delete/'+id, 'Confirm Delete', false, true );

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });


    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#shortNameSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#pinSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );
    $(document).on('change', '#numberSearch', function () {
        table.column( 4 ).search( this.value ).draw();
    } );

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');

        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search Name"/></th>')
        $("#table .searchCol").append('<th><input id="shortNameSearch" class="form-control" placeholder="Search Short Name"/></th>')

        $("#table .searchCol").append('<th><input id="pinSearch" class="form-control" placeholder="Search Pin"/></th>')
        $("#table .searchCol").append('<th><input id="numberSearch" class="form-control" placeholder="Search Employee Number"/></th>')

        $("#table .searchCol").append('<th></th>')

        $("#table .searchCol").append('<th></th>')
    }
});
