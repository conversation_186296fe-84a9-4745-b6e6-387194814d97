$(function() {

    $('body').on('change', '[name="group_type"]', function(){

        var val = $(this).val();

        if(val == ''){
            $('.discounttype').hide();
        } else {
            $('.discounttype').hide();
            $('#' + val).show();
        }
    })

    $('body').on('change', '[name="earn_points"]', function(){

        var val = $(this).val();

        if(val == 'no'){
            $('#points').hide();
        } else {
            $('#points').show();
        }
    })

    $('body').on('change', '#discountmethod', function(){
        var val = $(this).val()

        if(val < 3){
            $('#pricelevel').hide()
            $('#discountvalue').show()
        } else {
            $('#pricelevel').show()
            $('#discountvalue').hide()
        }
    })


    var boolOption = { "yes":1, "No":0 };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/customers/groups/create'
            },
            edit: {
                type: 'PUT',
                url:  '/customers/groups/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/customers/groups/delete'
            }
        },
        table: "#table",
        fields: [{
            label: "Group Name:",
            name: "displayname"
        },{
            label: "Points Group:",
            name: "gives_points",
            type: "select",
            options: boolOption
        },{
            label: "Points Earned:",
            name: "points_per_amount"
        },{
            label: "Point Value:",
            name: "points_spending_value"
        }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('.panel-body')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "BlrtBip",
        processing: true,
        serverSide: false,
        ajax: '/customers/groups/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            //buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', className: 'select-checkbox', orderable: true},
            { data: 'displayname', name: 'Name', editField: "first_name", type: "string" },
            { data: 'group_type', name: 'Name', editField: "group_type", type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = "/customers/groups/create"
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {
                            window.location.href = "/customers/groups/edit/" + id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to delete')
                        } else {
                            notificationBar('delete', 'Are you sure you would like to delete the loyalty scheme?', '/customers/groups/delete/'+id, 'Confirm Delete', false, true );
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

});