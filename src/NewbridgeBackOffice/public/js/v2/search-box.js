$(document).ready(function () {
    const $searchContainer = $('.search-bar-container');
    const $sidebarSearch = $searchContainer.find('.sidebar-search');
    const $searchInput = $sidebarSearch.find('.search-input');
    const $searchIcon = $sidebarSearch.find('.fa-search');
    const $closeIcon = $sidebarSearch.find('.fa-times-circle');
    const $searchResults = $('#search-output-box');
    const $searchTags = $('.search-tag');

    let activeResultIndex = -1;
    let $results = [];

    let tagsSelected = [];
    let searchResults = [];
    let debounceTimeout;

    function openSearch() {
        $searchContainer.addClass('expanded');
        $sidebarSearch.addClass('expanded');
    }

    function closeSearch() {
        $searchContainer.removeClass('expanded');
        $sidebarSearch.removeClass('expanded');
        $searchInput.blur();
    }

    $searchIcon.on('click', openSearch);
    $searchInput.on('focus', openSearch);
    $closeIcon.on('click', closeSearch);

    $(document).on('keydown', function (event) {
        if (event.key === "Escape" || event.keyCode === 27) {
            if ($sidebarSearch.hasClass('expanded')) {
                closeSearch();
            }
        }

        if ($sidebarSearch.hasClass('expanded')) {
            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    if ($results.length > 0) {
                        activeResultIndex = (activeResultIndex + 1) % $results.length;
                        updateActiveResult();
                    }
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    if ($results.length > 0) {
                        activeResultIndex = (activeResultIndex - 1 + $results.length) % $results.length;
                        updateActiveResult();
                    }
                    break;
                case 'Enter':
                    event.preventDefault();
                    if (activeResultIndex >= 0 && activeResultIndex < $results.length) {
                        $results.eq(activeResultIndex).click();
                    }
                    break;
            }
        }
    });

    $searchTags.on('click', function () {
        const $tag = $(this);
        const tagKey = Number.parseInt($tag.data('tag-key'));

        if ($tag.hasClass('selected')) {
            // Tag is already selected, deselect it
            $tag.removeClass('selected');
            tagsSelected = tagsSelected.filter(function (key) {
                return key !== tagKey;
            });
        } else {
            // Tag is not selected, select it
            $tag.addClass('selected');
            tagsSelected.push(tagKey);
        }

        triggerSearch();
    });

    // Handle input event with debouncing
    $searchInput.on('input', function () {
        triggerSearch();
    });

    function triggerSearch() {
        const query = $searchInput.val().trim();

        clearTimeout(debounceTimeout);

        if (query.length > 0) {
            debounceTimeout = setTimeout(function () {
                $.ajax({
                    url: '/search/get',
                    method: 'POST',
                    data: {search: query, search_tags: tagsSelected, user_url: window.location.href},
                    dataType: 'json',
                    success: function (response) {
                        searchResults = response;
                        renderSearchResults();
                    },
                    error: function (xhr, status, error) {
                        $searchResults.empty();
                        $searchResults.append('<p class="error center-block">Sorry, we did not find correct results for your query.</p>');
                    }
                });
            }, 300); // Debounce time in milliseconds
        } else {
            // If query is empty, clear results
            $searchResults.empty();
        }
    }

    function renderSearchResults() {
        $searchResults.empty();
        let results = searchResults.results;

        if (results.length === 0) {
            $searchResults.append('<p class="no-results center-block">Sorry, there is no results for you query.</p>');
            return;
        }

        results.forEach(function (tagResults) {
            const tagName = tagResults.tagName;
            const tagId = tagResults.tagId;
            const records = tagResults.results;

            if (records.length === 0) {
                // Skip this type if there are no records
                return;
            }

            // Add a header for this result type using the key
            const count = records.length;
            const heading = `<div class="header">
                                <span class="title">${tagName}</span><span class="count">${count}</span>
                             </div>`;
            $searchResults.append(heading);

            // Create a UL with data-type attribute
            const $ul = $(`<ul class="results" data-type="${tagName}"></ul>`);

            // Render the first 3 results
            records.slice(0, 3).forEach(function (record) {
                const $li = createResultListItem(tagId, tagName, record);
                $ul.append($li);
            });

            // If there are more than 3 records, add a "Show more" button
            if (records.length > 3) {
                const $showMore = $(`<span class="more" data-type="${tagName}">Show more <i class="fas fa-chevron-down"></i></span>`);
                $ul.append($showMore);
            }

            $searchResults.append($ul);
        });

        $results = $searchResults.find('.result');
        activeResultIndex = -1;
    }

    function createResultListItem(tagId, tagName, record) {
        const $li = $(`<li class="result ${tagName.toLowerCase()}"></li>`);

        // Create a title element based on the type
        let parsedResult = '';
        switch (tagName) {
            case 'LINK':
                parsedResult = `<div class="link-first-row">
                                    <i class="fa fa-link"></i>
                                    <div class="details-1">
                                        <span class="title"><strong>Navigate to:</strong> ${record.title}</span>
                                    </div>
                                </div>
                                <div class="link-extensions">
                                    <span class="extension-info">${record.info}</span>
                                </div>
                               `;
                break;
            case 'TRANSACTIONS':
                parsedResult = `<i class="fa fa-wallet"></i>
                                <div class="details-1">
                                    <span class="title">ID: ${record.id}</span>
                                    <span class="site-name">Site: ${record.siteName}</span>
                                </div>
                                <div class="details-2">
                                    <span class="terminal-name">Terminal: ${record.terminalNum}</span>
                                    <span class="employee-name">Employee: ${record.clerkFullName}</span>
                                </div>
                                <div class="details-3">
                                    <span class="date">${record.transDatetime}</span>
                                    <span class="amount">Total: ${record.total}</span>
                                </div>`;
                break;
            case 'CUSTOMERS':
                parsedResult = `<i class="fa fa-user"></i>
                                <div class="details-1">
                                    <span class="title">${record.fullName}</span>
                                    <span class="site-name">Membership: ${record.membershipNo}</span>
                                </div>
                                <div class="details-2">
                                    <span class="terminal-name">Group: ${record.groupName}</span>
                                    <span class="employee-name">Type: ${record.typeName}</span>
                                </div>
                                <div class="details-3">
                                    <span class="date">${record.telephone}</span>
                                    <span class="amount">${record.email}</span>
                                </div>`;
                break;
            case 'PRODUCTS':
                parsedResult = `<i class="fa fa-wallet"></i>
                                <div class="details-1">
                                    <span class="title">${record.displayName}</span>
                                    <span class="site-name">ID: ${record.id}</span>
                                </div>
                                <div class="details-2">
                                    <span class="terminal-name">Barcode: ${record.barcode}</span>
                                    <span class="employee-name">EAN13: ${record.ean13}</span>
                                </div>
                                <div class="details-3">
                                    <span class="date">${record.departmentName}</span>
                                    <span class="amount">${record.subDepartmentName}</span>
                                </div>`;
                break;
            // Add cases for other types as needed
            default:
                break;
        }

        // Append title and details to list item
        $li.append(parsedResult);

        // Handle click event on each result
        $li.on('click', function () {
            $.ajax({
                url: '/search/clicked',
                method: 'POST',
                data: {
                    search_request_uuid: searchResults.searchRequestUUID,
                    clicked_tag: tagId,
                    clicked_id: record.id
                },
                dataType: 'json',
                error: function (xhr, status, error) {
                    console.error('Failed to record click data.');
                }
            });

            switch (tagName.toLowerCase()) {
                case 'transactions':
                    console.log('Transaction clicked:', record);
                    // Handle transaction click
                    break;
                case 'customers':
                    window.location.href = `/customers/edit/${record.id}`;
                    break;
                case 'products':
                    window.location.href = `/products/edit/${record.id}`;
                    break;
                case 'link':
                    window.location.href = `${record.url}`;
                    break;
            }
        });

        return $li;
    }

    function updateActiveResult() {
        $results.removeClass('active');
        if (activeResultIndex >= 0 && activeResultIndex < $results.length) {
            const $activeResult = $results.eq(activeResultIndex);
            $activeResult.addClass('active');
            $activeResult[0].scrollIntoView({ block: 'nearest' });
        }
    }

    function showAllResultsForType(tagName) {
            const records = searchResults.results.find(function (tagResults) {
            return tagResults.tagName === tagName;
        });

        const $ul = $searchResults.find(`ul[data-type="${tagName}"]`);

        // Clear existing list items (except the "Show more" button)
        $ul.find('li.result').remove();

        // Render all records
        records.results.forEach(function (record) {
            const $li = createResultListItem(records.tagId, tagName, record);
            $ul.append($li);
        });
    }

    // Event delegation for "Show more" buttons
    $searchResults.on('click', '.more', function () {
        const tagName = $(this).data('type');
        showAllResultsForType(tagName);
        $(this).remove();
    });
});
