$(function() {

    let boolOption = { "Yes":1, "No":0 };

    let type = $('#reasonType option:selected').val();

    editor = new $.fn.dataTable.Editor({
        ajax: {
            edit: {
                type: 'PUT',
                url: `/reasons/edit`
            }
        },
        table: "#table",
        fields: [{
            label: "Reason:",
            name: "reason"
        },{
            label: "Stock:",
            name: "stock",
            type: 'select',
            options: boolOption
        }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "BlrtBip",
        processing: true,
        serverSide: false,
        ajax: '/reasons/data/'+type,
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns();
        },
        columns: [
            { data: 'id', defaultContent: '', classReason: 'select-checkbox', orderable: true},
            { data: 'reason', name: 'Reason', editField: "reason", type: "string" },
            { data: 'stock', name: 'Reason', editField: "stock", type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/reasons/create/'+type
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error','Please select a single row to edit')
                        } else {
                            window.location.href = '/reasons/edit/'+id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to delete')
                        } else {
                            notificationBar('delete', 'Are you sure you would like to delete the reason?', '/reasons/delete/'+id, 'Confirm Delete', false, true );


                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $(document).on('change', '#reasonType', function (e) {
        e.preventDefault();

        type = $(this).val();

        table.ajax.url( '/reasons/data/'+type ).load();
        // table.ajax.reload();
    });

    function buildSearchColumns() {
        $('#table .searchCol').append('<th></th>');
        $('#table .searchCol').append('<th><input class="form-control" name="reasonSearch" id="reasonSearch" placeholder="Search reasons"/></th>');
        $('#table .searchCol').append('<th>' +
            '<select class="form-control" name="takesStockSearch" id="takesStockSearch">' +
            '<option value="">Any</option>' +
            '<option value="0">No</option>' +
            '<option value="1">Yes</option>' +
            '</select>' +
            '</th>');
    }

    $(document).on('keyup', '#reasonSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('change', '#takesStockSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }


});