
$(function () {

    var aggregates = [];

    function saveAggregates(){
        aggregates = [];

        var typeElements = $('#aggregates [name="aggregate_type[]"]')
        var columnElements = $('#aggregates [name="aggregate_column[]"]')
        var aggregateNames = $('#aggregates [name="aggregate_as[]"]')

        var count = typeElements.length;

        for($i = 0; $i < count; $i++){

            var type = $(typeElements[$i]).val()
            var column = $(columnElements[$i]).val()
            var name = $(aggregateNames[$i]).val()
            //var column_pretty = $(columnElements[$i]).data('pretty')

            if(type !== '' || column !== '') {
                var result = {type: type, column: column, name: name}
            }

            aggregates.push(result)
        }

        // now populate the aggregate dropdown
        $('.condition_column #aggregate_group').html('')

        _.each(aggregates, function(ag){
            $('.condition_column #aggregate_group').append('<option value="'+ag.name+'">'+ag.name+'</option>')
        })

    }

    $('.next-button').on('click', function(e){
        e.preventDefault();

        var $step = $(this).data('current-step')
        var $next = $step+1;

        if($next === 2){
            populateColumns()
        }

        if($step === 3){
            saveAggregates();
        }

        $('[data-step="'+$step+'"]').hide()
        $('[data-step="'+$next+'"]').show()
    })

    $('.prev-button').on('click', function(e){
        e.preventDefault();

        var $step = $(this).data('current-step')
        var $next = $step-1;

        if($step === 3){
            saveAggregates();
        }

        $('[data-step="'+$step+'"]').hide()
        $('[data-step="'+$next+'"]').show()
    })


    function loadButtons() {

        $('.button-checkbox').each(function () {

            // Settings
            var $widget = $(this),
                $button = $widget.find('button'),
                $checkbox = $widget.find('input:checkbox'),
                color = $button.data('color'),
                settings = {
                    on: {
                        icon: 'glyphicon glyphicon-check'
                    },
                    off: {
                        icon: 'glyphicon glyphicon-unchecked'
                    }
                };

            // Event Handlers
            $button.on('click', function (e) {
                e.preventDefault();
                $checkbox.prop('checked', !$checkbox.is(':checked'));
                $checkbox.triggerHandler('change');
                updateDisplay();
            });
            $checkbox.on('change', function () {
                updateDisplay();
            });

            // Actions
            function updateDisplay() {
                var isChecked = $checkbox.is(':checked');

                // Set the button's state
                $button.data('state', (isChecked) ? "on" : "off");

                // Set the button's icon
                $button.find('.state-icon')
                    .removeClass()
                    .addClass('state-icon ' + settings[$button.data('state')].icon);

                // Update the button's color
                if (isChecked) {
                    $button
                        .removeClass('btn-default')
                        .addClass('btn-' + color + ' active');
                }
                else {
                    $button
                        .removeClass('btn-' + color + ' active')
                        .addClass('btn-default');
                }
            }

            // Initialization
            function init() {

                updateDisplay();

                // Inject the icon if applicable
                if ($button.find('.state-icon').length === 0) {
                    $button.prepend('<i class="state-icon ' + settings[$button.data('state')].icon + '"></i> ');
                }
            }

            init();
        })
    }



    $('body').on('click', '#add-aggregate', function(e){
        e.preventDefault();
        copyAggregate()
    })

    $('body').on('click', '#add-condition', function(e){
        e.preventDefault();
        copyCondition()
    })

    $('body').on('click', '.delete-aggregate', function(e){
        e.preventDefault();

        var element = $(this);
        element.parent().parent().remove()
    })

    function copyAggregate(){
        var $copy = $('#copy-aggregate .aggregate-primary').clone();
        $copy.removeClass('aggregate-primary');
        $copy.find('select').val('');
        $('#aggregates').append($copy);
    }

    function copyCondition(){
        var $copy = $('#copy-conditions .form-group').clone();
        $copy.find('select').val('');
        $copy.find('input').val('');
        $('#conditions').append($copy);
    }

    $('body').on('change', '[name="condition_type[]"]', function(){
        var conditionType =  $(this).val();

        $('.condition-input').not('.'+conditionType).hide()
        $('.'+conditionType).show()

        if(conditionType === 'having'){

            $(this).parent().parent().find('.condition_column #base_group').hide()
            $(this).parent().parent().find('.condition_column #join_group').hide()
            $(this).parent().parent().find('.condition_column #aggregate_group').show()
        } else {

            $(this).parent().parent().find('.condition_column #base_group').show()
            $(this).parent().parent().find('.condition_column #join_group').show()
            $(this).parent().parent().find('.condition_column #aggregate_group').hide()
        }

        if(conditionType === 'between'){
            $(this).parent().parent().find('[name="condition_operator[]"]').hide()
        } else {
            $(this).parent().parent().find('[name="condition_operator[]"]').show()
        }
    })

    /**
     * When a base table is changes, populate the joins and reset all fields after this.
     */
    $('#base_table').on('change', function () {

        // TODO Alert confirm that this will clear all other selections
        var table = $(this).val();

        populateJoins(table)
    })

    /**
     * Populate the colum selection checkboxes with
     * values from the table json
     *
     * @param tables
     * @param section
     */
    function populateColumns() {

        var section = '#base_columns';

        $('#join_columns').html('');
        $('#base_columns').html('');

        var base_table = $('#base_table').val();
        var join_tables = $('#joins').val();

        var tableArray = [];

        tableArray.push(base_table)

        _.each(join_tables, function(jt){
            tableArray.push(jt)
        })

        if(section === '#base_columns'){
            $('.aggregate_column #base_group option').remove()
            $('.aggregate_column #join_group option').remove()

            $('.condition_column #base_group option').remove()
            $('.condition_column #join_group option').remove()
        } else {
            $('.aggregate_column #join_group option').remove()
            $('.condition_column #join_group option').remove()
        }

        _.each(tableArray, function(t){
            var tc = _.find(tables, {name: t});

            if(tc){

                $(section).append('<div class="col-md-12 '+tc.name+'" style="padding: 20px; border: 1px solid #e2e2e2; margin-top: 25px;"><legend>'+tc.pretty_name+'</legend>');

                _.each(tc.columns, function(c, i){


                    $(section+' .'+tc.name).append('<span class="button-checkbox"> <button type="button" class="btn" data-color="primary">'+i+'</button> <input type="checkbox" name="base_columns[]" class="hidden" value="'+tc.name+'.'+c+' AS '+ i +'"/> </span>');

                    if(section === '#base_columns'){
                        $('.aggregate_column #base_group').append('<option value="'+tc.name+'.'+c+'">'+i+'</option>')
                        $('.condition_column #base_group').append('<option value="'+tc.name+'.'+c+'">'+i+'</option>')
                    }
                    if(section === '#join_columns'){
                        $('.aggregate_column #join_group').append('<option value="'+tc.name+'.'+c+'">'+i+'</option>')
                        $('.condition_column #join_group').append('<option value="'+tc.name+'.'+c+'">'+i+'</option>')
                    }

                })

                $(section).append('</div>')

            }

        })

        $(section).append('</div>');

        loadButtons()
    }

    /**
     * Populate the available joins for the selected base table
     * @param searchTable
     */
    function populateJoins(searchTable) {

        $('#joins option').remove();

        var table = _.find(tables, {name: searchTable});

        _.each(table.joins, function (j, i) {
            $('#joins').append('<option value="' + i + '">' + j.name + '</option>')
        })

    }

    $( "#report-wizard-form" ).submit(function( e ) {
        e.preventDefault();

        // validation goes here
        // and on each step to ensure we have good data

        $.ajax({
            type: "POST",
            url: "/reports/create",
            data: $(this).serialize(),
            success: function (data) {
                var data = JSON.parse(data)
                var str = JSON.stringify(data, undefined, 4);
                var res = syntaxHighlight(str);
                $('#result').html('');
                output(res)
            }
        }).fail(function(data) {
            $('#CreateProductModal').modal('toggle')
            notificationBar(data.responseJSON.status, data.responseJSON.message)
        });
    });

    function output(inp) {

        document.getElementById('result').appendChild(document.createElement('pre')).innerHTML = inp;
    }

    function syntaxHighlight(json) {
        json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
            var cls = 'number';
            if (/^"/.test(match)) {
                if (/:$/.test(match)) {
                    cls = 'key';
                } else {
                    cls = 'string';
                }
            } else if (/true|false/.test(match)) {
                cls = 'boolean';
            } else if (/null/.test(match)) {
                cls = 'null';
            }
            return '<span class="' + cls + '">' + match + '</span>';
        });
    }

    var obj = {a:1, 'b':'foo', c:[false,'false',null, 'null', {d:{e:1.3e5,f:'1.3e5'}}]};
    var str = JSON.stringify(obj, undefined, 4);


});