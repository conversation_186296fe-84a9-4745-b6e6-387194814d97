$(function() {

    var boolOption = { "yes":1, "No":0 };
    var settingToCopy = null;

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/pos-settings/create'
            },
            edit: {
                type: 'PUT',
                url:  '/pos-settings/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/pos-settings/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Description:",
                name: "description"
            },
            {
                label: "Value 1:",
                name: "value1"
            },
            {
                label: "Value 2:",
                name: "value2"
            },
            {
                label: "Value 3:",
                name: "value3"
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('table')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "BlrtBip",
        processing: true,
        serverSide: false,
        ajax: '/company/sites/'+site_num+'/terminals/'+terminal_num+'/settings/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', className: 'select-checkbox', orderable: true},
            { data: 'label', name: 'Label', type: "string" },
            { data: 'description', name: 'Description', editField: "description", type: "string" },
            { data: 'value1', name: 'Value', editField: "value1", type: "string" },
            { data: 'value2', name: 'Value 2', editField: "value2", type: "string" },
            { data: 'value3', name: 'Value 3', editField: "value3", type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'Copy to terminals',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        settingToCopy = table.$('tr.selected').attr('id');
                        $('#copy_modal').modal('toggle')
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#labelSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#valueSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="labelSearch" class="form-control" placeholder="Search Label"/></th>')
        $("#table .searchCol").append('<th><input id="valueSearch" class="form-control" placeholder="Search Value"/></th>')
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th></th>');
    }
});