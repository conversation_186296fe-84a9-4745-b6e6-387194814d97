$(function() {

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('table')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({
        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        ajax: '/newbridge/terminals/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'company.company_name', name: 'Company', orderable: true },
            { data: 'site_name', name: 'Site', orderable: true },
            { data: 'terminal_num', name: 'Name', orderable: true },
            { data: 'version', name: 'Version', orderable: true},
            { data: 'last_ping', name: 'Last Ping', orderable: true}
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        "pageLength": 50,
        select: {
            style: 'os',
            selector: 'td:first-child'
        }
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#companySearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#siteSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#terminalSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#versionSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#pingSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="companySearch" class="form-control" placeholder="Company Name"/></th>')
        $("#table .searchCol").append('<th><input id="siteSearch" class="form-control" placeholder="Site Name"/></th>')
        $("#table .searchCol").append('<th><input id="terminalSearch" class="form-control" placeholder="Terminal"/></th>')
        $("#table .searchCol").append('<th><input id="versionSearch" class="form-control" placeholder="Version Search"/></th>')
        $("#table .searchCol").append('<th><input id="pingSearch" class="form-control" placeholder="Date/Time Search"/></th>')
    }
});
