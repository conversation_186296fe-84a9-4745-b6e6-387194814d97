$(function() {

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
            $('#processingIndicator').css('display', processing ? 'none' : 'none');
            if(processing) {
                if($('#loading').length == 0) {
                    loading('table')
                }
            } else {
                $('body #loading').remove()
            }
        } ).DataTable({
            lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
            dom: "Brtip",
            processing: true,
            serverSide: false,
            ajax: '/alarms/data',
            initComplete: function(settings, json) {
        },
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'displayname', name: 'Name', editField: "full_name", defaultContent: "UNKNOWN", type: "string" },
            { data: 'type.name', name: 'Type Name', editField: "short_name", type: "string" },
            { data: 'period.name', name: 'Period', type: "string" },
            { data: 'alarm_period_count', name: 'Period Count', type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/alarms/create';
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error','Please select a single row to edit')
                        } else {

                           window.location.href = '/alarms/edit/'+id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {

                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error','Please select a single row to delete')
                        } else {

                            var r = notificationBar('delete', 'Are you sure you want to delete this alarm?', '/alarms/delete/'+id, 'Confirm Delete', false, true );
                            if (r == true) {
                                $.ajax({
                                    type: "DELETE",
                                    url: "/alarms/delete/"+id,
                                    success: function (data) {
                                        notificationBar('success', 'Alarm has been deleted.');
                                        table.ajax.reload();
                                    },
                                    fail: function () {
                                        notificationBar('error', 'Alarm has not been deleted, Please try again.');
                                    }
                                })
                            } else {
                                return false;
                            }

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'View Alarms',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {

                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to view results')
                        } else {

                            window.location.href = '/alarms/results/'+id

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            }
        ]
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }
});