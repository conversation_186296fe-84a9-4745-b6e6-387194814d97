if (suggestions) {
    let firstSuggestion = 0;
    _.each(suggestions, function (suggestion) {

        let itemData = _.find(master_products, {'id': suggestion.product});
        let rowExists = $('tr[data-product="' + itemData.id + '"]').length;

        if (rowExists == 0) {
            if(firstSuggestion === 0) {
                firstSuggestion = _.find(suppliers, {'guid': itemData.supplier_guid}).id;
            }

            $.ajax({
                type: "get",
                url: "/stock/current-stock/" + itemData.id,
                success: function (data) {
                    itemData.current_stock = data
                    createSuggestedRow(itemData, suggestion)
                    updateTotal();

                    $('ul.multiselect-container input[type="radio"]').each(function () {
                        if ($(this).val() == firstSuggestion) {
                            $(this).prop('checked', true).trigger('change');
                        }
                    });

                    $('.multiselect-native-select .btn-group').removeClass('open');
                }
            });
        }

        $('#createButton').removeAttr('disabled');

    });
}


_.each(master_products, function (prod) {
    $('#product-dropdown').append($('<option>', {
        value: prod.guid,
        text: prod.displayname
    }));
})

_.each(departments, function (dep) {
    $('#department-dropdown').append($('<option>', {
        value: dep.guid,
        text: dep.displayname
    }));
})

_.each(subdepartments, function (dep) {

    $('#subdepartment-dropdown').append($('<option>', {
        value: dep.guid,
        text: dep.displayname
    }));

})

_.each(suppliers, function (sup) {

    $('#supplier-dropdown').append($('<option>', {
        value: sup.guid,
        text: sup.name
    }));

})

$('body').on('click', '.add-product-button', function (e) {
    e.preventDefault();
    var ref = $('#product-dropdown').val();

    _.forEach(ref, function (value) {
        newRow('product', value);
    });

    $('#product-dropdown option:selected').each(function () {
        $(this).prop('selected', false);
    })
    $('#product-dropdown').multiselect('refresh');

})

$('body').on('click', '.add-supplier-button', function (e) {
    e.preventDefault();
    var ref = $('#supplier-dropdown').val();
    _.forEach(ref, function (value) {
        newRow('supplier', value);
    });

    $('#supplier-dropdown option:selected').each(function () {
        $(this).prop('selected', false);
    })
    $('#supplier-dropdown').multiselect('refresh');
})

$('body').on('click', '.add-department-button', function (e) {
    e.preventDefault();
    var ref = $('#department-dropdown').val();
    _.forEach(ref, function (value) {
        newRow('department', value);
    });

    $('#department-dropdown option:selected').each(function () {
        $(this).prop('selected', false);
    })
    $('#department-dropdown').multiselect('refresh');
})

$('body').on('click', '.add-subdepartment-button', function (e) {
    e.preventDefault();
    var ref = $('#subdepartment-dropdown').val();

    _.forEach(ref, function (value) {
        newRow('subdepartment', value);
    });

    $('#subdepartment-dropdown option:selected').each(function () {
        $(this).prop('selected', false);
    })
    $('#subdepartment-dropdown').multiselect('refresh');
})

$('body').on('click', '.add-allproducts-button', function (e) {
    e.preventDefault();

    newRow('allproducts', null);
})

var row = 0;

function newRow(type, ref) {

    var itemData = {};

    $('#createButton').removeAttr('disabled');

    if (ref !== null && ref.length === 0) {
        notificationBar('error', 'Please choose a product, department or supplier to add to your order');
    }

    if (type === 'product') {
        var itemData = _.find(master_products, {'guid': ref});

        var rowExists = $('tr[data-product="' + itemData.id + '"]').length;

        if (rowExists > 0) {
            notificationBar('error', 'This product already exists in the order list, please update the record or remove it.');
            return false;
        }

        // get current stock

        $.ajax({
            type: "get",
            url: "/stock/current-stock/" + itemData.id,
            success: function (data) {
                itemData.current_stock = data
                createRow(itemData);
            }
        });

    }

    if (type === 'supplier') {
        var itemData = _.filter(master_products, {'supplier_guid': ref});

        _.each(itemData, function (item) {
            if (item.sku_guid != null) {
                var rowExists = $('tr[data-product="' + item.id + '"]').length;

                if (rowExists == 0) {
                    $.ajax({
                        type: "get",
                        url: "/stock/current-stock/" + item.id,
                        success: function (data) {
                            item.current_stock = data
                            createRow(item)
                        }
                    });
                }


            }

        })

    }

    if (type === 'department') {
        var itemData = _.filter(master_products, {'department_guid': ref});

        _.each(itemData, function (item) {

            if (item.sku_guid != null) {
                var rowExists = $('tr[data-product="' + item.id + '"]').length;

                if (rowExists == 0) {
                    $.ajax({
                        type: "get",
                        url: "/stock/current-stock/" + item.id,
                        success: function (data) {
                            item.current_stock = data
                            createRow(item)
                        }
                    });
                }
            }

        })

    }

    if (type === 'subdepartment') {
        var itemData = _.filter(master_products, {'sub_department_guid': ref});

        _.each(itemData, function (item) {

            if (item.sku_guid != null) {
                var rowExists = $('tr[data-product="' + item.id + '"]').length;

                if (rowExists == 0) {
                    $.ajax({
                        type: "get",
                        url: "/stock/current-stock/" + item.id,
                        success: function (data) {
                            item.current_stock = data
                            createRow(item)
                        }
                    });
                }
            }

        })

    }

    if (itemData.length == 0) {
        notificationBar('error', 'The ' + type + ' selected cannot be found in the product information, please try again.');
        return false;
    }

    row++
};

function createRow(itemData) {
    var $tr = $('tbody tr').first();
    var $clone = $tr.clone();

    var current_stock = itemData.current_stock != null ? itemData.current_stock : 0;

    var skuPrice = (+itemData.sku.qty * itemData.costprice).toFixed(2)

    $clone.removeClass('copy-example');
    $clone.addClass('item-record');
    $clone.find('.plu').val(itemData.id);
    $clone.find('.displayname').val(itemData.displayname);
    $clone.find('.pcode').val(itemData.stock_code);
    $clone.find('input').attr('id', 'r' + row);
    $clone.find('.min_stock').val(itemData.min_stock);
    $clone.find('.max_stock').val(itemData.max_stock);
    $clone.find('.current_stock').val(current_stock);
    $clone.find('.skuPrice').val(skuPrice);
    $clone.attr('data-product', itemData.id);
    $clone.find('input, select, button').attr('data-product', itemData.id);
    $clone.css('display', '');

    $clone.find('input').removeClass('dontSend');
    $clone.find('select').removeClass('dontSend');

    // find the last tr in list
    $('#OrderTable tbody').append($clone);
    // $tr.after($clone);
    $('.sku[data-product="' + itemData.id + '"]').val(itemData.sku_guid);
    $('.supplier[data-product="' + itemData.id + '"]').val(itemData.supplier_guid);
}


function createSuggestedRow(itemData, suggestion) {
    var $tr = $('tbody tr').first();
    var $clone = $tr.clone();

    var current_stock = itemData.current_stock != null ? itemData.current_stock : 0;

    var skuPrice = (+itemData.sku.qty * itemData.costprice).toFixed(2)

    $clone.removeClass('copy-example');
    $clone.addClass('item-record');
    $clone.find('.plu').val(itemData.id);
    $clone.find('.displayname').val(itemData.displayname);
    $clone.find('.pcode').val(itemData.stock_code);
    $clone.find('input').attr('id', 'r' + row);
    $clone.find('.min_stock').val(itemData.min_stock);
    $clone.find('.max_stock').val(itemData.max_stock);
    $clone.find('.current_stock').val(current_stock);
    $clone.find('.skuPrice').val(skuPrice);
    $clone.find('.quantity').val(suggestion.order_quantity);
    $clone.find('.price').val((suggestion.order_quantity * skuPrice).toFixed(2));
    $clone.attr('data-product', itemData.id);
    $clone.find('input, select, button').attr('data-product', itemData.id);
    $clone.css('display', '');

    $clone.find('input').removeClass('dontSend');
    $clone.find('select').removeClass('dontSend');

    // find the last tr in list
    $('#OrderTable tbody').append($clone);
    // $tr.after($clone);
    $('.sku[data-product="' + itemData.id + '"]').val(itemData.sku_guid);
    $('.supplier[data-product="' + itemData.id + '"]').val(itemData.supplier_guid);
}

$('body').on('click', '.delete-button', function () {
    var id = $(this).data('product');
    $('tr[data-product="' + id + '"]').remove()

    updateTotal()
});

$('body').on('change', '.quantity', function () {
    var quantity = $(this).val();
    var productId = $(this).data('product');
    var skuPrice = $('[data-product="' + productId + '"].skuPrice').val()

    let splits = $(this).val().split(".");

    if(splits.length > 1 && splits[1].length > 2) {
        if( isNaN( parseFloat( this.value ) ) ) return;
        this.value = parseFloat(this.value).toFixed(2);
        quantity = this.value
    } else {
        quantity = this.value
    }
    var price = skuPrice * quantity;

    $('.price[data-product="' + productId + '"]').val(price.toFixed(2))

    updateTotal(price);
})


$('body').on('blur,keyup', '.quantity', function () {
    var quantity = $(this).val();
    var productId = $(this).data('product');
    var skuPrice = $('[data-product="' + productId + '"].skuPrice').val()
    var price = skuPrice * quantity;

    let splits = $(this).val().split(".");

    if(splits.length > 1 && splits[1].length > 2) {
        if( isNaN( parseFloat( this.value ) ) ) return;
        this.value = parseFloat(this.value).toFixed(2);
        quantity = this.value
    } else {
        quantity = this.value
    }

    $('.price[data-product="' + productId + '"]').val(price.toFixed(2))

    updateTotal(price);
});

$('body').on('change, blur', '.skuPrice', function () {
    var productId = $(this).data('product');
    var quantity = $('[data-product="' + productId + '"].quantity').val();
    var skuPrice = +$(this).val()
    $(this).val(skuPrice.toFixed(2))
    var price = skuPrice * quantity;

    $('.price[data-product="' + productId + '"]').val(price.toFixed(2))

    updateTotal(price);
});

$(document).ready(function () {
    statusChange($('#status').val());
})

$('body').on('change', '#status', function () {
    statusChange($(this).val());
})

function statusChange(status) {
    if (status < 2) {
        $('#delivery_date').parent('div').hide()
        $('#delivery_no').parent('div').hide()
        $('#invoice_no').parent('div').hide()
    }
    if (status == 2) {
        $('#delivery_date').parent('div').show()
        $('#delivery_no').parent('div').show()
        $('#invoice_no').parent('div').hide()
    }
    if (status == 3) {
        $('#delivery_date').parent('div').show()
        $('#delivery_no').parent('div').show()
        $('#invoice_no').parent('div').show()
    }
}

$(document).ready(function () {
    $('#product_data, #status, #send_method, #department-dropdown, #product-dropdown, #supplier-dropdown, #subdepartment-dropdown, #supplier').multiselect({
        buttonWidth: '100%',
        includeSelectAllOption: true,
        enableCaseInsensitiveFiltering: true,
        onDropdownShown: function (event) {
            $(event.target).find('.filter input').focus();
        },
        onChange: function (option, checked, select) {
            selectedDataId = $('#product_data').val();
        }
    });
});
var submitClicked = false;
$("body #OrderForm").on('click', function () {
    submitClicked = true;
})

$('[name="supplier_id"]').on('change', function (e) {
    e.preventDefault();

    let id = $(this).val();

    $.ajax({
        type: "get",
        url: "/suppliers/get-email/" + id,
        success: function (data) {
            $('[name="supplier_emails"]').val(data.email)
        }
    });

})

$("body #OrderForm").validate({
    errorPlacement: function (error, element) {
    },
    checkForm: function () {
        this.prepareForm();
        for (var i = 0, elements = (this.currentElements = this.elements()); elements[i]; i++) {
            if (this.findByName(elements[i].name).length != undefined && this.findByName(elements[i].name).length > 1) {
                for (var cnt = 0; cnt < this.findByName(elements[i].name).length; cnt++) {
                    this.check(this.findByName(elements[i].name)[cnt]);
                }
            } else {
                this.check(elements[i]);
            }
        }
        return this.valid();
    },
    rules: {
        delivery_date: {
            required: function (element) {
                return $("#status").val() >= 2;
            }
        },
        delivery_no: {
            required: function (element) {
                return $("#status").val() >= 2;
            }
        },
        invoice_no: {
            required: function (element) {
                return $("#status").val() >= 2;
            }
        },
        "min_stock[]": {
            required: true,
            number: true
        },
        "max_stock[]": {
            required: true,
            number: true
        },
        "quantity[]": {
            required: true,
            min: 0.01,
            max: 9999999.00
        },
        "price[]": {
            required: true,
            number: true
        },
        "skuPrice[]": {
            required: true,
            number: true
        }
    },
    messages: {
        name: "Please enter a contact name",
        username: {
            required: "Please enter a username",
            minlength: "Your username must consist of at least 5 characters"
        },
        email: "Please enter a valid email address",
        department_guid: {
            required: "Please choose which department this product belongs to"
        },
        supplier_guid: {
            required: "Please select this product's supplier"
        },
        selling_price_1: {
            required: "Please enter a price for this product"
        },
        cost_price: {
            required: "Please enter a cost price per serving/unit for this product"
        },
        sku_price: {
            required: "Please provide a price for a full case of this product"
        }

    },
    showErrors: function (errorsObj) {
        this.defaultShowErrors();
        if (submitClicked && $.isEmptyObject(errorsObj) == false) {
            submitClicked = false;
            notificationBar('error', 'There are errors in your order, please check and try again.')
        }
    },
    submitHandler: function (form) {
        var count = $('.item-record').length
        if (count < 1) {
            notificationBar('error', 'Please add items to the order before continuing')

            return false;

        } else {

            let orderStatus = $('#status').val();
            let orderEmails = $('.supplier_emails').val();
            let orderMethod = $('#send_method').val();

            if (parseInt(orderStatus) == 1 && orderEmails !== '' && parseInt(orderMethod) === 2) {
                Swal.fire({
                    title: 'Send Order Email?',
                    text: 'Saving this order will send the email to supplier, if you do not wish to send the email press cancel and change the order status to new or change the delivery method.',
                    type: 'warning',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: 'green',
                    confirmButtonText: 'Send It!'
                }).then((result) => {
                    if (result.value) {
                        orderSubmit()
                    }
                })
            } else {
                orderSubmit();
            }

        }
    }

});

function orderSubmit() {

    let form = $('#OrderForm input, #OrderForm select, #OrderForm textarea').not('.dontSend')

    let loader = notificationBar('info', 'Saving your order, please wait!', undefined, 'loading', true);
    $.ajax({
        type: "post",
        url: "/stock/orders/check-matches",
        data: $(form).serialize(),
        success: function (data) {
            if (data.status == false) {
                $.ajax({
                    type: "post",
                    url: "/stock/orders/create",
                    data: $(form).serialize(),
                    success: function (data) {
                        deleteEntireHashMap();

                        notificationBar(data.status, data.message, '/stock/orders')

                    }
                }).fail(function (data) {
                    data = JSON.parse(data.responseText);
                    if (data.errors) {
                        notificationBar('error', data.message)
                    } else {
                        notificationBar(data.status, data.message)
                    }

                    if(data.meta.errors){
                        let errorList = '';
                        _.each(data.meta.errors, function (errors) {
                            errorList = errors.join(', ');
                        })

                        notificationBar('error', errorList)
                    }
                });

            } else {

                $('#ConfirmOrderModal ul li').remove();

                _.each(data.matches, function (match) {
                    $('#ConfirmOrderModal ul').append('<li data-product="' + match.product_id + '">' + match.product.displayname + ' <span class="pull-right">&times;</span></li>')
                })

                $('#ConfirmOrderModal').modal('show');
            }
        },
    }).fail(function (data) {
        notificationBar(data.status, data.responseJSON.message)
    });

}

$('body').on('click', '#existing-order-items li', function () {
    var id = $(this).data('product');
    $('tr.item-record[data-product="' + id + '"]').remove()
    $(this).remove();
});

$('body').on('click', '#confirm-order', function () {

    var form = $('#OrderForm');
    var count = $('.item-record').length
    if (count < 1) {
        notificationBar('error', 'Please add items to the order before continuing')
        $('#ConfirmOrderModal').modal('hide');
        return false;

    } else {

        $.ajax({
            type: "post",
            url: "/stock/orders/create",
            data: $(form).serialize(),
            success: function (data) {
                $('#ConfirmOrderModal').modal('hide');
                notificationBar(data.status, data.message, '/stock/orders')
            }
        }).fail(function (data) {
            $('#ConfirmOrderModal').modal('hide');
            if (data.errors) {
                notificationBar('error', data.message, '/stock/orders')
            } else {
                notificationBar(data.status, data.message)
            }
        });
    }
});

function switchAddTab(tab) {
    $('[data-type=add-tab]').hide();
    $('[data-type=add-tab].' + tab).show();
}

$('#site').on('change', function () {

    var r = confirm("Changing the site will reload this page, all entries will be lost. Are you sure?");
    if (r == true) {
        window.location.href = '/stock/orders/{{Request::segment(3)}}?site_num=' + $(this).val()
    } else {
        return false;
    }

});

function updateTotal() {

    let prices = $('.price');
    let total = 0.00;

    setTimeout(function () {

        _.each(prices, function (price) {
            let priceVal = $(price).val();
            if (priceVal !== '') {

                total = (parseFloat(total) + parseFloat(priceVal));

            }
        });

        $('#orderTotal').text(total.toFixed(2));

    }, 400);

}

$(document).on('keydown', 'input', function (e) {
    var keyCode = e.keyCode || e.which;

    if (keyCode == 9 || keyCode == 13) {
        e.preventDefault();
        var element = $(this).parent().parent().next().find('.quantity').focus()
        return false;

    }
});
