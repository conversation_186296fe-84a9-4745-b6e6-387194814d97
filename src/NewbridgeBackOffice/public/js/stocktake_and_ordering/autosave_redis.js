/*
        Init
 */

window.addEventListener('DOMContentLoaded', async function () {

    let restore = false;
    const newReportButton = document.querySelector('.addButton');
    if (await checkIfSaved() > 0) {
        Swal.fire({
            title: "Pick up where you left off?",
            text: "You have an autosaved " + module
                + " draft. Would you like to go to your saved draft?",
            type: "info",
            showCancelButton: true,
            cancelButtonText: "No, I'll start fresh",
            allowOutsideClick: false,
        }).then((res) => {
            if (res.value == true) {
                restore = true;
                Swal.fire({
                    title: 'Draft restored!',
                    type: 'success'
                });
                layoutDom(restore);
                restore = false;
            } else {
                restore = false;
                deleteEntireHashMap();
                Swal.fire({
                    title: 'Taking you to a fresh page!',
                    type: 'success'
                });
                layoutDom(restore);
            }
        });
    }

    restore = false;

})

/*
        Functions
 */

function sleep(delay){
    return new Promise((resolve)=> setTimeout(resolve, delay))
}

async function layoutDom(restore) {
    // const sleep = (delay) => new Promise((resolve) => setTimeout(resolve, delay));
    await sleep(500);
    const orderTable = document.querySelector('#OrderTable');
    let fields = orderTable.querySelectorAll('input'); // changed from input#r0
    for (let i = 0; i < fields.length; i++) {
        fields[i].addEventListener('change', updateDraftInRedis);
    }
    await sleep(500);
    if (restore !== false) {
        await updateWithAutoSavedValues();
    }
    await sleep(5000);
    await saveDraftAsHashMap()
}

async function saveDraftAsHashMap() {

    let itemRows = document.querySelectorAll('.item-record');
    let stockDate = document.querySelector('[name="stocktake_date"]')
    let notes = document.querySelector('[name="notes"]')
    let data = [];

    for (let i = 0; i < itemRows.length; i++) {
        let productId = itemRows[i].dataset.product;
        let plu = itemRows[i].querySelector('[name="plu[]"]');
        let name = itemRows[i].querySelector('[name="name[]"]');
        let supplier = itemRows[i].querySelector('[name="supplier[]"]');
        let currentStock = itemRows[i].querySelector('[name="current_stock[]"]');
        let minStock = itemRows[i].querySelector('[name="min_stock[]"]');
        let maxStock = itemRows[i].querySelector('[name="max_stock[]"]');
        let sku = itemRows[i].querySelector('[name="sku[]"]');
        let skuPrice = itemRows[i].querySelector('[name="skuPrice[]"]');
        let skuQuantity = itemRows[i].querySelector('[name="SkuQuantity[]"]');
        let quantity = itemRows[i].querySelector('[name="quantity[]"]');
        let price = itemRows[i].querySelector('[name="price[]"]');
        let unitQuantity = itemRows[i].querySelector('[name="UnitQuantity[]"]');
        let variance = itemRows[i].querySelector('[name="variance[]"]');
        let variance_total = itemRows[i].querySelector('[name="variance_total[]"]');
        let reason = itemRows[i].querySelector('[name="reason[]"]');


        // post as array of items
        data[i] = {
            id: productId ? productId : '',
            node_index: i ? i : 0,
            product_id: productId ? productId : '',
            plu: plu ? plu.value : '',
            name: name ? name.value : '',
            supplier: supplier ? supplier.value : '',
            current_stock: currentStock ? currentStock.value : '',
            min_stock: minStock ? minStock.value : '',
            max_stock: maxStock ? maxStock.value : '',
            sku: sku ? sku.value : '',
            sku_price: skuPrice ? skuPrice.value : '',
            sku_quantity: skuQuantity ? skuQuantity.value : '',
            unit_quantity: unitQuantity ? unitQuantity.value : '',
            quantity: quantity ? quantity.value : '',
            price: price ? price.value : '',
            variance: variance ? variance.value : '',
            variance_total: variance_total ? variance_total.value : '',
            reason: reason ? reason.value : '',
        }

    }

    let response = await fetch('/stock/drafts', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        body: JSON.stringify({
            data: data,
            module: module
        })
    })
    let res = await response.json();
    return res;
}

async function updateRedisHashMapMeta() {
    let stockDate = document.querySelector('[name="stocktake_date"]');
    let siteSource = document.querySelector('[name="site_source"]');
    let siteTarget = document.querySelector('[name="site_target"]');
    let notes = document.querySelector('[name="notes"]');
    let supplierId = document.querySelector('[name="supplier_id"]');
    let supplierEmails = document.querySelector('[name="supplier_emails"]');
    let status = document.querySelector('[name="status"]');
    let sendMethod = document.querySelector('[name="send_method"]');
    let deliveryDate = document.querySelector('[name="delivery_date"]');
    let deliveryNo = document.querySelector('[name="delivery_no"]');
    let invoiceNo = document.querySelector('[name="invoice_no"]');

    let request = {
        meta : {
            module: module,
            date: stockDate ? stockDate.value : '',
            notes: notes ? notes.value : '',
            supplier_id: supplierId ? supplierId.value : '',
            supplier_emails: supplierEmails ? supplierEmails.value : '',
            status: status ? status.value : '',
            send_method: sendMethod ? sendMethod.value : '',
            delivery_date: deliveryDate ? deliveryDate.value : '',
            delivery_no: deliveryNo ? deliveryNo.value : '',
            invoice_no: invoiceNo ? invoiceNo.value : '',
            site_source: siteSource ? siteSource.value : '',
            site_target: siteTarget ? siteTarget.value: ''
        }

    }

    let response = await fetch('/stock/drafts/meta', {
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        body: JSON.stringify(request)
    });
    let data = await response.json();
    return data;
}

async function updateDraftInRedis() {
    const sleep = (delay) => new Promise((resolve) => setTimeout(resolve, delay));
    let itemRows = document.querySelectorAll('.item-record'); // get nodeList of doc for reference
    let row = event.target.closest('tr'); // get current row
    let rowPosition = null;
    itemRows.forEach((r, n) => { // finding node in nodeList
        if (r === row) {
            rowPosition = n;
        }
    })

    if (rowPosition === null) { // if node is not in list, terminate function
        return "Couldn't locate row within the document";
    }

    let productId = row.dataset.product;
    let plu = row.querySelector('[name="plu[]"]');
    let name = row.querySelector('[name="name[]"]');
    let supplier = row.querySelector('[name="supplier[]"]');
    let currentStock = row.querySelector('[name="current_stock[]"]');
    let minStock = row.querySelector('[name="min_stock[]"]');
    let maxStock = row.querySelector('[name="max_stock[]"]');
    let sku = row.querySelector('[name="sku[]"]');
    let skuPrice = row.querySelector('[name="skuPrice[]"]');
    let skuQuantity = row.querySelector('[name="SkuQuantity[]"]');
    let unitQuantity = row.querySelector('[name="UnitQuantity[]"]');
    let quantity = row.querySelector('[name="quantity[]"]');
    let price = row.querySelector('[name="price[]"]');
    let variance = row.querySelector('[name="variance[]"]');
    let variance_total = row.querySelector('[name="variance_total[]"]');
    let reason = row.querySelector('[name="reason[]"]');

    await sleep(500);
    // post as array of items
    let data = {
        id: productId ? productId : '',
        node_index: rowPosition ? rowPosition : 0,
        product_id: productId ? productId : '',
        plu: plu ? plu.value : '',
        name: name ? name.value : '',
        supplier: supplier ? supplier.value : '',
        current_stock: currentStock ? currentStock.value : '',
        min_stock: minStock ? minStock.value : '',
        max_stock: maxStock ? maxStock.value : '',
        sku: sku ? sku.value : '',
        sku_price: skuPrice ? skuPrice.value : '',
        sku_quantity: skuQuantity ? skuQuantity.value : '',
        unit_quantity: unitQuantity ? unitQuantity.value : '',
        quantity: quantity ? quantity.value : '',
        price: price ? price.value : '',
        variance: variance ? variance.value : '',
        variance_total: variance_total ? variance_total.value : '',
        reason: reason ? reason.value : ''
    }

    let response = await fetch('/stock/drafts', {
        method: 'PUT',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        body: JSON.stringify({
            data: data,
            module: module,
            row: rowPosition
        })
    })
    let res = await response.json();
    return res;

}

async function deleteRowFromHashMap(clear = false) {
    let row = event.target.closest('tr');
    let product = row.dataset.product;

    let response = await fetch('/stock/drafts/remove', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        body: JSON.stringify({
            product: product,
            module: module,
            clear: clear
        })
    })
    let data = await response.json();
}

async function checkIfSaved() {
    let response = await fetch('/stock/drafts/check', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        body: JSON.stringify({
            module: module
        })
    });
    let data = await response.json();
    return data;
}

async function updateWithAutoSavedValues() {

    let itemRows = document.querySelectorAll('.item-record');
    const submitButtons = document.querySelectorAll('.createButtons');
    let response = await fetch('/stock/drafts/restore', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        body: JSON.stringify({
            module: module,
            total_rows: itemRows.length // this doesn't work where rows can be added so check on server side
        })
    });
    let res = await response.json();
    let data = res['data'];
    let meta = JSON.parse(res['meta']);
    data.sort((a, b) => {
        return a.node_index - b.node_index;
    });

    for (let i = 0; i < data.length; i++) {
        if (data[i]) {
            createRow(data[i]);
        }
    }

    if(meta !== null) {

        let stockDate = document.querySelector('[name="stocktake_date"]');
        let notes = document.querySelector('[name="notes"]');
        let siteSource = document.querySelector('[name="site_source"]');
        let siteTarget = document.querySelector('[name="site_target"]');
        let supplierId = document.querySelector('[name="supplier_id"]');
        let supplierEmails = document.querySelector('[name="supplier_emails"]');
        let status = document.querySelector('[name="status"]');
        let sendMethod = document.querySelector('[name="send_method"]');
        let deliveryDate = document.querySelector('[name="delivery_date"]');
        let deliveryNo = document.querySelector('[name="delivery_no"]');
        let invoiceNo = document.querySelector('[name="invoice_no"]');

        if (stockDate != null && meta['date'] != null) {
            stockDate.value = meta['date'];
        }

        if (notes != null && meta['notes'] != null) {
            notes.value = meta['notes'];
        }

        if (supplierId != null && meta['supplier_id'] != null) {
            supplierId.value = meta['supplier_id'];
            $('[name="supplier_id"]').multiselect('refresh');
        }

        if (supplierEmails != null && meta['supplier_emails'] != null) {
            supplierEmails.value = meta['supplier_emails'];
        }

        if (status != null && meta['status'] != null) {
            status.value = Number(meta['status']);
            $('[name="status"]').multiselect('refresh');
            statusChange(meta['status']);
        }

        if (sendMethod != null && meta['send_method'] != null) {
            sendMethod.value = meta['send_method'];
            $('[name="send_method"]').multiselect('refresh');
        }

        if (deliveryDate != null && meta['delivery_date'] != null) {
            deliveryDate.value = meta['delivery_date'];
        }

        if (deliveryNo != null && meta['delivery_no'] != null) {
            deliveryNo.value = meta['delivery_no'];
        }

        if (invoiceNo != null && meta['invoice_no'] != null) {
            invoiceNo.value = meta['invoice_no'];
        }

        if(siteSource != null && meta['site_source'] != null){
            siteSource.value = meta['site_source'];
        }

        if(siteTarget != null && meta['site_target'] != null){
            siteTarget.value = meta['site_target'];
        }
    }
    itemRows = document.querySelectorAll('.item-record');

    if(itemRows.length > 0)
        submitButtons.forEach((b) => b.removeAttribute('disabled'));

    for (let i = 0; i < itemRows.length; i++) {

        if (data[i] != null) {

            let plu = itemRows[i].querySelector('[name="plu[]"]');
            let name = itemRows[i].querySelector('[name="name[]"]');
            let supplier = itemRows[i].querySelector('[name="supplier[]"]');
            let currentStock = itemRows[i].querySelector('[name="current_stock[]"]');
            let minStock = itemRows[i].querySelector('[name="min_stock[]"]');
            let maxStock = itemRows[i].querySelector('[name="max_stock[]"]');
            let sku = itemRows[i].querySelector('[name="sku[]"]');
            let skuPrice = itemRows[i].querySelector('[name="skuPrice[]"]');
            let skuQuantity = itemRows[i].querySelector('[name="SkuQuantity[]"]');
            let quantity = itemRows[i].querySelector('[name="quantity[]"]');
            let price = itemRows[i].querySelector('[name="price[]"]');
            let unitQuantity = itemRows[i].querySelector('[name="UnitQuantity[]"]');
            let variance = itemRows[i].querySelector('[name="variance[]"]');
            let variance_total = itemRows[i].querySelector('[name="variance_total[]"]');
            let reason = itemRows[i].querySelector('[name="reason[]"]');

            let deleteButton = itemRows[i].querySelector('.delete-button');

            /*
                    Rebuild Rows
             */

            // Add values
            if (plu != null && data[i]['plu'] != null) {
                plu.value = data[i]['plu'];
            }

            if (name != null && data[i]['name'] != null) {
                name.value = data[i]['name'];
            }

            if (supplier != null && data[i]['supplier'] != null) {
                supplier.value = data[i]['supplier'];
            }

            if (currentStock != null && data[i]['current_stock'] != null) {
                currentStock.value = data[i]['current_stock'];
            }
            if (minStock != null && data[i]['min_stock'] != null) {
                minStock.value = data[i]['min_stock'];
            }
            if (maxStock != null && data[i]['max_stock'] != null) {
                maxStock.value = data[i]['max_stock'];
            }
            if (sku != null && data[i]['sku'] != null) {
                sku.value = data[i]['sku'];
            }

            if (skuPrice != null && data[i]['sku_price'] != null) {
                skuPrice.value = data[i]['sku_price'];
            }

            if (unitQuantity != null && data[i]['unit_quantity'] != null) {
                unitQuantity.value = data[i]['unit_quantity'];
            }
            if (skuQuantity != null && data[i]['sku_quantity'] != null) {
                skuQuantity.value = data[i]['sku_quantity'];
            }

            if (quantity != null && data[i]['quantity'] != null) {
                quantity.value = data[i]['quantity'];
            }

            if (price != null && data[i]['price'] != null) {
                price.value = data[i]['price'];
            }

            if (variance != null && data[i]['variance'] != null) {
                variance.value = data[i]['variance'];
            }
            if (variance_total != null && data[i]['variance_total'] != null) {
                variance_total.value = data[i]['variance_total'];
            }
            if (reason != null && data[i]['reason'] != null) {
                reason.value = data[i]['reason'];
            }


            if (data[i]['product_id'] != null) {

                itemRows[i].dataset.product = data[i]['product_id'];
                if (plu != null) {
                    plu.dataset.product = data[i]['product_id'];
                }
                if (name != null) {
                    name.dataset.product = data[i]['product_id'];
                }
                if (supplier != null) {
                    supplier.dataset.product = data[i]['product_id'];
                }
                if (currentStock != null) {
                    currentStock.dataset.product = data[i]['product_id'];
                }
                if (minStock != null) {
                    minStock.dataset.product = data[i]['product_id'];
                }
                if (maxStock != null) {
                    maxStock.dataset.product = data[i]['product_id'];
                }
                if (sku != null) {
                    sku.dataset.product = data[i]['product_id'];
                }
                if (skuPrice != null) {
                    skuPrice.dataset.product = data[i]['product_id'];
                }
                if (unitQuantity != null) {
                    unitQuantity.dataset.product = data[i]['product_id'];
                }
                if (skuQuantity != null) {
                    skuQuantity.dataset.product = data[i]['product_id'];
                }
                if (quantity != null) {
                    quantity.dataset.product = data[i]['product_id'];
                }
                if (price != null) {
                    price.dataset.product = data[i]['product_id'];
                }
                if (variance != null) {
                    variance.dataset.product = data[i]['product_id'];
                }
                if (variance_total != null) {
                    variance_total.dataset.product = data[i]['product_id'];
                }

                if (deleteButton != null) {
                    deleteButton.dataset.product = data[i]['product_id'];
                }

            }

            if (variance) {

                if (variance.value < 0) {
                    variance.classList.add('negative');
                    variance.classList.remove('positive', 'neutral');
                } else if (variance.value > 0) {
                    variance.classList.add('positive');
                    variance.classList.remove('negative', 'neutral');
                } else {
                    variance.classList.add('neutral');
                    variance.classList.remove('negative', 'positive');
                }

            }

            if(quantity)
                updateTotal()

        }
    }


}

async function deleteEntireHashMap() {

    let response = await fetch('/stock/drafts/clear', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        body: JSON.stringify({
            row: null,
            module: module,
            clear: true
        })
    })

    let data = await response.json();
}

/*
        Event Listeners
 */

$('body').on('change, blur', '#OrderTable input, #OrderTable select', function () {
    updateDraftInRedis(); //
})

$('body').on('click', '.delete-button', function(){
    deleteRowFromHashMap();
})

$('body').on('change, blur', '#meta input, #meta select, #meta textarea', async function () {
    await sleep(500);
    updateRedisHashMapMeta();
})


