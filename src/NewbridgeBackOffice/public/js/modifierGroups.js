$(function() {
    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/modifier-groups/create'
            },
            edit: {
                type: 'PUT',
                url:  '/modifier-groups/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/modifier-groups/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Name:",
                name: "DisplayName"
            },{
                label: "Min Selection:",
                name: "<PERSON>"
            },{
                label: "Max Selection:",
                name: "<PERSON>"
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "BlrtBip",
        processing: true,
        serverSide: false,
        responsive: true,
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        ajax: '/modifier-groups/data',
        columns: [
            { data: 'id', defaultContent: ''},
            { data: 'DisplayName', name: 'DisplayName', editField: "DisplayName", type: "string" },
            { data: 'Min', name: 'Min', editField: "Min", type: "number" },
            { data: 'Max', name: 'Max', editField: "Max", type: "number" },
            {"mRender": function ( data, type, row ) {
                return '<button class="edit-screen-button btn btn-sm btn-primary" data-id="'+row.id+'">Update Screen</button>';}
            }

        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        $('#CreateModal').modal('toggle')
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            confirm('Please select a single row to edit')
                        } else {
                            $.ajax({
                                type: "GET",
                                url: "/modifier-groups/edit-modal/" + id,
                                success: function (data) {

                                },
                                fail: function () {
                                    flashMessage('error', 'There was an error in your product entry, please try again.')
                                }
                            }).done(function (data) {
                                $('#edit-modal-area').html(data)
                                $('#EditModal').modal('toggle')
                            });
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {
                            var r = notificationBar('delete', 'Are you sure you want to delete this modifier group?', "/modifier-groups/delete/"+id, 'Confirm Delete', false, true );
                            if (r == true) {
                                $.ajax({
                                    type: "DELETE",
                                    url: "/modifier-groups/delete/"+id,
                                    success: function (data) {

                                    },
                                    fail: function () {
                                        notificationBar('error', 'There was an error in your product entry, please try again.')
                                    }
                                }).done(function (data) {
                                    table.ajax.reload();
                                });

                            } else {
                                return false;
                            }
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            }
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $('#createSubmit').on('click', function(){
        $('#CreateForm').submit()
    })

    var form = $('#CreateForm')

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        rules: {
            id: "required",
            DisplayName: "required",
            Min: "required",
            Max: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/modifier-groups/create",
                data: $(form).serialize(),
                success: function () {
                    $('#CreateModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );

    $(document).on('keyup', '#minSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );

    $(document).on('keyup', '#maxSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search Name"/></th>')
        $("#table .searchCol").append('<th><input id="minSearch" class="form-control" placeholder="Search Min"/></th>')
        $("#table .searchCol").append('<th><input id="maxSearch" class="form-control" placeholder="Search Max"/></th>')
        $("#table .searchCol").append('<th></th>');
    }

    $(document).on('click', '.edit-screen-button', function(){

        var pageId = $(this).data('id');

        openWindow('/screens/manage20/'+pageId)

    })


    function openWindow(window_src)
    {
        var config = 'height=900, width=1300, '
            + 'toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, '
            + 'directories=no, status=no'
        window.open(window_src, 'POS Screen Editor', config);
    }
});