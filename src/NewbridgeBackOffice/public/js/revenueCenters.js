$(function() {
    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/company/revenue-centers/create'
            },
            edit: {
                type: 'PUT',
                url:  '/company/revenue-centers/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/company/revenue-centers/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Name:",
                name: "name"
            },{
                label: "Terminals:",
                name: "terminals"
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        ajax: '/company/revenue-centers/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            // buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: ''},
            { data: 'name', name: 'Name', editField: "name", type: "string", defaultContent: '' },
            { data: 'terminals', name: 'Terminals',
                render: function (data) {
                var decoded = $("<div/>").html(data).text();
                let parsed  = JSON.parse(decoded)
                let string = '';
                    $.each(parsed, function(p, val){
                        string = string + val+' '
                    })

                    return string;
                }},
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/company/revenue-centers/create'
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id');

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {
                            window.location.href = '/company/revenue-centers/edit/'+id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')
                        if (id == undefined) {
                            notificationBar('error','Please select a single row to delete')
                        } else {

                            var r = notificationBar('delete', 'Are you sure you would like to delete the Day Part?', '/company/revenue-centers/delete/'+id, 'Confirm Delete', false, true );
                            if (r == true) {
                                $.ajax({
                                    type: "DELETE",
                                    url: "/company/revenue-centers/delete"+id,
                                    success: function (data) {

                                    },
                                    fail: function () {
                                        notificationBar('error', 'There was an error in your entry, please try again.')
                                    }
                                }).done(function (data) {
                                    table.ajax.reload();
                                });
                            } else {
                                return false;
                            }

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            }
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $('#createSubmit').on('click', function(){
        $('#CreateForm').submit()
    })

    var form = $('#CreateForm')

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        rules: {
            name: "required",
            terminals: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/company/revenue-centers/create",
                data: $(form).serialize(),
                success: function () {
                    $('#CreateModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#descriptionSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#qtySearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="descriptionSearch" class="form-control" placeholder="Search Name"/></th>')
        $("#table .searchCol").append('<th><input id="qtySearch" class="form-control" placeholder="Search Email"/></th>')
        $("#table .searchCol").append('<th></th>');
    }
});