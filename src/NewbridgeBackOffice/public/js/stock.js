$(function() {
    let columns = [];

    if(module == 'transfers'){
         columns = [
            { data: 'summary_id', name: 'summary_id', defaultContent: '', orderable: true},
            { data: 'stocktake_date', name: 'stocktake_date', render: function(data, type, row){
                    return moment.utc(data).tz(userTimezone).format("YYYY-MM-DD HH:mm:ss");
                }},
             { data: 'status', name: 'Status', type: "string" },
             { data: 'name', name: 'name', type: "string" },
             { data: 'source_site_name', name: 'source_site_name', type: "string" },
             { data: 'target_site_name', name: 'target_site_name', type: "string" }
         ]
    } else {
         columns = [
             { data: 'summary_id', name: 'summary_id', defaultContent: '', orderable: true},
            { data: 'stocktake_date', name: 'stocktake_date', render: function(data, type, row){
                    return moment.utc(data).tz(userTimezone).format('YYYY-MM-DD HH:mm:ss');
                }},
            { data: 'status', name: 'Status', type: "string" },
            { data: 'name', name: 'name', type: "string" }
        ];
    }

    table =  $('#stock-table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('#stock-table')
            }
        } else {
            $('body #loading').remove()
        }
    }).DataTable({
        dom: "Blrtip",
        processing: true,
        serverSide: true,
        ajax: '/stock/'+module+'/data',
        initComplete: function(settings, json) {
            $('#stock-table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        createdRow: function ( row, data, index ) {
            $(row).attr('data-options',data['DT_RowData']);
        },
        columns: columns,
        order: [1, 'desc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        pageLength: 25,
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        if(module != 'stocktake') {
                            window.location.href = '/stock/' + module + '/create'
                        } else {
                            window.location.href = '/stock/' + module + '/pre-create'
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'View',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_view == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to view')
                        } else {
                            window.location.href = '/stock/'+module+'/view/'+id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')
                        var status = table.$('tr.selected').data('status')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                            return false;
                        }
                        if(status == 'Completed'){
                            notificationBar('error', 'Sorry you can not edit a completed stock record.')
                            return false;
                        }
                        if(status == 'Pending'){
                            notificationBar('error', 'Sorry this record has not finished being updated or created yet.')
                            return false;
                        }
                        else {
                            window.location.href = '/stock/'+module+'/edit/'+id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single record to delete')
                        } else {
                            var r = notificationBar('delete', 'Are you sure you would like to delete the '+module, '/stock/'+module+'/delete/'+id, 'Confirm Delete', false, true );
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Reset Filters',
                action: function ( e, dt, node, config ) {
                    window.location.reload();
                }
            },
        ]
    });

    $('#createSubmit').on('click', function(){
        $('#CreateForm').submit()
    });

    var form = $('#CreateForm')

    // validate signup form on keyup and submit
    $("#CreateForm").validate({
        rules: {
            description: "required",
            acc_code: "required"
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                ajax: '/stock/'+module+'/create',
                data: $(form).serialize(),
                success: function () {
                    $('#CreateModal').modal('toggle')
                    table.ajax.reload();
                },
                fail: function () {
                    flashMessage('error', 'There was an error in your product entry, please try again.')
                }
            });
        }
    })

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup, change', '#dateSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('keyup, change', '#statusSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#siteSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );
    $(document).on('keyup', '#userSearch', function () {
        table.column( 4 ).search( this.value ).draw();
    } );

    function buildSearchColumns() {
        $("#stock-table .searchCol").append('<th></th>')
        $("#stock-table .searchCol").append('<th><input id="dateSearch" class="form-control datepicker" data-date-format="dd/mm/yyyy" placeholder="Search Date"/></th>')
        $("#stock-table .searchCol").append('<th><select id="statusSearch" class="form-control"><option value="">All</option><option value="0">New</option><option value="4">Completed</option></select></th>')
        $("#stock-table .searchCol").append('<th></th>')
        if (module === 'transfers'){
            $("#stock-table .searchCol").append('<th></th>')
            $("#stock-table .searchCol").append('<th></th>')
        }

        $('.datepicker').datepicker({
            dateFormat: 'yy-mm-dd'
        });
    }

});
