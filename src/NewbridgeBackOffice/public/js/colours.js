$(function() {
    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/company/sites/create'
            },
            edit: {
                type: 'PUT',
                url:  '/company/sites/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/company/sites/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Setting Value:",
                name: "value1",
                type: "select",
                options: {"Option One": '#FF0000'}
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    editor.on('initEdit', function() {

        var colours = $('select option')

        _.each(colours, function(c){
            var value = $(c).attr('value')
            $(c).css('background-color', value)
        })
    });

    // if (permissions.can_edit === 1) {
    //     editor.disable();
    // }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        ajax: '/company/sites/'+site_num+'/colours/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', name: 'Site Num', editField: "site_num", type: "string"},
            { data: 'setting_id', name: 'Setting Name', editField: "setting_name", type: "string" },
            { data: 'value1', name: 'Setting Value', editField: "value1", type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
        ]
    });


    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#settingSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    });

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="settingSearch" class="form-control" placeholder="Search Settings"/></th>')
        $("#table .searchCol").append('<th></th>');
    }

});