$(function() {
    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/customers/gift-cards/create'
            },
            edit: {
                type: 'PUT',
                url:  '/customers/gift-cards/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/customers/gift-cards/delete'
            }
        },
        table: "#table",
        fields: [{
            label: "Gift Card Number:",
            name: "membership_no"
        },{
            label: "Balance:",
            name: "cash_value"
        }],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('.panel-body')
            }
        } else {
            $('body #loading').remove()
        }
    }).DataTable({
        dom: "BlrtBip",
        processing: true,
        serverSide: true,
        ajax: '/customers/gift-cards/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', className: 'select-checkbox', orderable: true},
            { data: 'membership_no', name: 'membership_no', editField: "membership_no", type: "string" },
            { data: "cash_value", render: $.fn.dataTable.render.number( ',', '.', 2, currencyChar ), orderable: false},
            { data: 'created_at', name: 'created_at', type: "date",
                render: function(data, type, row) {
                    if (!data) return '';
                    return moment.utc(data).tz(userTimezone).format('YYYY-MM-DD HH:mm:ss');
                }},
            { data: 'expires', name: 'expires', type: "date",
                render: function(data, type, row) {
                    if (!data) return '';
                    return moment.utc(data).tz(userTimezone).format('YYYY-MM-DD');
                }}
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        $('#CreateModal').modal('toggle')
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {
                           window.location.href = '/customers/gift-cards/edit/'+id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to delete')
                        } else {
                            notificationBar('delete', 'Are you sure you would like to delete the gift card?', '/customers/gift-cards/delete/'+id, 'Confirm Delete', false, true );
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    $('#createSubmit').on('click', function(e){
        e.preventDefault();
        $('#CreateForm').submit()
    })

    var form = $('#CreateForm')

    // validate signup form on keyup and submit
    form.validate({
        rules: {
            cash_value: {
                required: true,
                number: true
            },
            membership_no: {
                required: true
            }
        },
        submitHandler: function (form) {
            $.ajax({
                type: "POST",
                url: "/customers/gift-cards/create",
                data: $(form).serialize(),
                success: function () {
                    $('#CreateModal').modal('toggle')
                    table.ajax.reload();
                },
                error: function (data) {
                    $('#CreateModal').modal('toggle')
                    if(data.responseJSON){
                        notificationBar('error', data.responseJSON.message)
                    } else {
                        notificationBar('error', 'There was an error in your gift card entry, please try again.')
                    }
                }
            });
        }
    })

    function loading(target) {
        $('<div id="loading"><i class="fa fa-spinner fa-spin" style="margin-top: 80px; font-size: 48px;"></i><br /><h3>Loading data please wait</h3></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            "text-align": "center",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(999,999,999,1.0)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#membershipSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    } );
    $(document).on('change', '#dateSearch', function () {
        table.column( 3 ).search( this.value ).draw();
    } );
    $(document).on('change', '#dateSearch2', function () {
        table.column( 4 ).search( this.value ).draw();
    } );
    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="membershipSearch" class="form-control" placeholder="Search Gift Card No"/></th>')
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="dateSearch" class="form-control" placeholder="Search Date"/></th>')
        $("#table .searchCol").append('<th><input id="dateSearch2" class="form-control" placeholder="Search Date"/></th>')

    }
});