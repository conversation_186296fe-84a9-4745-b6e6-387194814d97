$(function() {

    var boolOption = { "yes":1, "No":0 };

    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/recipes/create'
            },
            edit: {
                type: 'PUT',
                url:  '/recipes/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/recipes/delete'
            }
        },
        table: "#table",
        fields: [{
            label: "Name:",
            name: "name"
        }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        ajax: '/recipes/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'id', defaultContent: '', orderable: true},
            { data: 'name', name: 'Name', editField: "name", type: "string" }
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: [
            {
                text: 'New',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/recipes/create'
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Edit',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_edit == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to edit')
                        } else {

                            window.location.href = '/recipes/edit/'+id
                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Delete',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_delete == 1) {
                        var id = table.$('tr.selected').attr('id')

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to delete')
                        } else {

                            notificationBar('delete', 'Are you sure you would like to delete the recipe?', '/recipes/delete/'+id, 'Confirm Delete', false, true );

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            },
            {
                text: 'Import',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/recipes/import'
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }

                }
            },
            {
                text: 'Export',
                action: function ( e, dt, node, config ) {
                    if (permissions.can_add == 1) {
                        window.location.href = '/recipes/export'
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }

                }
            }
        ]
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 1 ).search( this.value ).draw();
    });

    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search Name"/></th>')
    }
});
