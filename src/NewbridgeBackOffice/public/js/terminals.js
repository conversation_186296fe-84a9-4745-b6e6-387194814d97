$(function() {

    let pageButtons = [
        {
            text: 'Edit Terminal Settings',
            action: function ( e, dt, node, config ) {
                if (permissions.can_edit == 1) {
                    var id = table.$('tr.selected').attr('id')

                    if (id == undefined) {
                        notificationBar('error', 'Please select a single terminal to manage settings')
                    } else {
                        window.location.href = '/company/sites/'+site_num+'/terminals/'+id+'/settings'
                    }
                } else {
                    notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                }
            }
        },
        {
            text: 'View Pending Updates',
            action: function ( e, dt, node, config ) {
                if (permissions.can_edit == 1) {
                    var id = table.$('tr.selected').attr('id')

                    if (id == undefined) {
                        notificationBar('error', 'Please select a single terminal to manage settings')
                    } else {

                        $.ajax({
                            type: "GET",
                            url: '/company/sites/'+site_num+'/terminals/'+id+'/updates',
                            success: function (data) {

                                $('#dataModal .modal-body').html('');
                                $('#dataModal .modal-body').html(data.html);
                                $('#dataModal').modal('toggle')
                            },
                            fail: function () {
                                flashMessage('error', 'There was an error, please try again.')
                            }
                        });

                        // window.location.href = '/company/sites/'+site_num+'/terminals/'+id+'/updates'
                    }
                } else {
                    notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                }
            }
        },
        {
            text: 'Update Functions',
            action: function ( e, dt, node, config ) {
                if (permissions.can_edit == 1) {
                    var id = table.$('tr.selected').attr('id')

                    $.ajax({
                        type: "GET",
                        url: '/company/sites/'+site_num+'/terminals/send-functions',
                        success: function (data) {
                            notificationBar('success', 'Functions added to the update queue, reload data on the POS to use.')
                        },
                        fail: function () {
                            notificationBar('error', 'There was an error, please try again.')
                        }
                    });

                } else {
                    notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                }
            }
        }
    ];

    if (permissions.can_terminal ==1){
        pageButtons.push(
            // add this to the above array only if permermission
            {
                className: 'add-button',
                text: 'Add Terminal',
                    action: function ( e, dt, node, config ) {

                        Swal.fire({
                            type: 'info',
                            title: "Are you sure you'd like to add a new Terminal?",
                            text: "New terminals are billed from their date of creation",
                            showCancelButton: true,
                            confirmButtonText: 'Yes, add the terminal!',
                            cancelButtonColor: 'red',
                            confirmButtonColor: 'green'
                        }).then(async (res) => {
                                if(res.value) {
                                    let { value: choice } = await Swal.fire({
                                      html: `
                                        <input type="number" value="1" id="terminals-number">
                                        </br>
                                        <select style="margin-top: 20px" id="terminal-type">
                                            <option value="till">Till</option>
                                            <option value="tablet">Tablet</option>
                                        </select>
                                      `,
                                      focusConfirm: false,
                                      preConfirm: () => {
                                        return [
                                            document.getElementById("terminals-number").value,
                                            document.getElementById("terminal-type").value
                                        ];
                                      },
                                        type: 'question',
                                        title: 'How many and which type of terminals would you like to add?',
                                        showCancelButton: true,
                                        confirmButtonText: 'Add!',
                                        cancelButtonColor: 'red',
                                        confirmButtonColor: 'green'
                                    })

                                    if(choice) {
                                        $.ajax({
                                            type: "GET",
                                            url: "/companies/add-terminal/company/" + company_id + "/site/" + site_num +"?type=" + choice[1] + '&terminals=' + choice[0],
                                            success: function () {
                                                notificationBar('success', 'Terminals added to this company');
                                                table.ajax.reload();
                                                $('#loading').remove();
                                            },
                                            fail: function () {
                                                notificationBar('error', 'There was an error processing this request, please try again.')
                                            }
                                        });

                                    }



                                }
                            });

                    }
            }
        )

        pageButtons.push(
            // add this to the above array only if permermission
            {
                className: 'warning-button',
                text: 'Delete Terminal',
                action: function ( e, dt, node, config ) {
                    var id = table.$('tr.selected').attr('id')
                    if (permissions.can_delete == 1) {

                        if (id == undefined) {
                            notificationBar('error', 'Please select a single row to delete')
                        } else {

                            notificationBar('delete', 'Are you sure you would like to delete the terminal?', '/companies/delete-terminal/' + company_id + '/' + site_num +'/' + id, 'Confirm Delete', false, true);

                        }
                    } else {
                        notificationBar('error', 'Sorry, you have insufficient permissions to perform this action');
                    }
                }
            }
        )
    }


    editor = new $.fn.dataTable.Editor({
        ajax: {
            create: {
                type: 'POST',
                url:  '/company/sites/'+site_num+'/terminals/create'
            },
            edit: {
                type: 'PUT',
                url:  '/company/sites/'+site_num+'/terminals/edit'
            },
            remove: {
                type: 'DELETE',
                url:  '/company/sites/'+site_num+'/terminals/delete'
            }
        },
        table: "#table",
        fields: [
            {
                label: "Name:",
                name: "name"
            }
        ],
        formOptions: {
            inline: {
                onBlur: 'submit'
            }
        }
    });

    if(permissions.can_edit == 0) {
        editor.disable()
    }

    table =  $('#table').on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css('display', processing ? 'none' : 'none');
        if(processing) {
            if($('#loading').length == 0) {
                loading('table')
            }
        } else {
            $('body #loading').remove()
        }
    } ).DataTable({        lengthMenu: [ [10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "All"] ],
        dom: "Blrtip",
        processing: true,
        serverSide: false,
        ajax: '/company/sites/'+site_num+'/terminals/data',
        initComplete: function(settings, json) {
            $('#table thead').append('<tr role="row" class="searchCol"></tr>')
            buildSearchColumns()
        },
        columns: [
            { data: 'terminal_num', name: 'Terminal Num', editField: "site_num", type: "string"},
            { data: 'name', name: 'Terminal Name', editField: "name", type: "string" },
            { data: 'terminal_version', name: 'Terminal Version', editable: false, sortable: false},
            { data: 'last_ping_datetime', name: 'Terminal Ping', editable: false, sortable: false}
        ],
        order: [0, 'asc'],
        keys: {
            columns: ':not(:first-child)',
            keys: [9]
        },
        select: {
            style: 'os',
            selector: 'td:first-child'
        },
        buttons: pageButtons
    });

    // Inline editing on click
    $('#table').on('click', 'tbody td:not(:first-child)', function (e) {
        editor.inline(this);
    });

    // Inline editing on tab focus
    table.on('key-focus', function (e, datatable, cell) {
        editor.inline(cell.index());
    });

    function loading(target) {
        $('<div id="loading"><div class="spinner"></div></div>').css({
            position: "absolute",
            'min-height': '200px',
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            background: '#ccc',
            background: 'rgba(192,192,192,0.5)',
            'z-index': 99999,
        }).appendTo($(target).css("position", "relative"));
    }

    $(document).on('keyup', '#nameSearch', function () {
        table.column( 2 ).search( this.value ).draw();
    } );
    function buildSearchColumns()
    {
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th><input id="nameSearch" class="form-control" placeholder="Search Name"/></th>')
        $("#table .searchCol").append('<th></th>');
        $("#table .searchCol").append('<th></th>');
    }
});
