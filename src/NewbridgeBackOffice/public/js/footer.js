function loading(target, message) {

    if (!message) {
        message = 'Loading data, please wait...'
    }

    $('<div id="loading"><i class="fa fa-spinner fa-spin" style="margin-top: 80px; font-size: 48px;"></i><br /><h3 id="message_loading">' + message + '</h3></div>').css({
        position: "absolute",
        'min-height': '200px',
        width: "100%",
        height: "100%",
        "text-align": "center",
        top: 0,
        left: 0,
        background: '#ccc',
        background: 'rgba(999,999,999,1.0)',
        'z-index': 99999,
    }).appendTo($(target).css("position", "relative"));
}


function notificationBar(type, message, url = null, title = null, showLoading = false, confirm = false, site = null, element = null, timer = null) {

    let btnColor = '';
    let confirmText = 'Ok';
    if (typeof title == 'undefined') {
        if (type == 'error') {
            title = 'Something went wrong!'
            // btnColor = '#ff0000'
        }
        if (type == 'success') {
            title = 'Done!'
            //btnColor = '#00ff00'
        }
        if (type == 'delete') {
            title = 'Delete?!'
            confirmText = 'Yes, delete it!';
            // type = 'warning'
            //btnColor = '#00ff00'
        }
    }

    Swal.fire({
        title: title,
        text: message,
        timer: timer,
        type: type === 'delete' ? 'warning' : type,
        confirmButtonText: confirmText,
        cancelButtonText: 'No, cancel!',
        reverseButtons: true,
        cancelButtonColor: 'red',
        confirmButtonColor: 'green',
        showCancelButton: confirm,
        showConfirmButton: timer == null,
        onBeforeOpen: () => {
            if (showLoading)
                Swal.showLoading()
        }
    }).then(function (result) {
        if (result.value && url == 'reload-page') {
            location.reload()
            return;
        }

        if (result.value && url !== undefined &&  url !== null && type !== 'delete') {
            setTimeout(function () {
                window.location.href = url;
            }, 500);
            return;
        }

        if (result.value && type === 'delete' && url !== undefined &&  url !== null) {
            loading('#button-edits', 'Deleting', 'We are deleting the item, please wait.');
            $.ajax({
                type: "DELETE",
                url: url,
                success: function (data) {
                    if (url.indexOf('pages') > -1) {
                        getPages(site, 'delete');
                    }

                    if (url.indexOf('table-plans') > -1) {
                        console.log(element)
                        $('.grid-snap[data-id="' + element + '"]').remove();
                    }

                    if (url.indexOf('pages') === -1 && url.indexOf('table-plans') === -1) {
                        table.ajax.reload();
                        notificationBar('success', 'Item Deleted', undefined, 'Delete');
                    }
                },
                fail: function (data) {
                    notificationBar('error', 'The selected item has not been deleted, Please try again.');
                },
                error: function (data) {
                    notificationBar(data.responseJSON.status, data.responseJSON.message)
                }
            })
        }

        return false;
    })
}

function viewTransactionFromAnywhere(id) {
    $.ajax({
        type: "GET",
        url: "/transactions/view/" + id,
        success: function (data) {
            $('#transactionViewerArea').html(data)
            $('#ViewModal').modal('toggle')
        },
        fail: function () {
            flashMessage('error', 'There was an error in your product entry, please try again.')
        }
    })
}

// Update the live clock in the navbar
function updateClock() {
    let now = moment(carbonDate);
    $('#live-clock').text(now.format('HH:mm:ss'));
    setTimeout(updateClock, 1000);
}

// Initialize the clock when the document is ready
$(document).ready(function() {
    updateClock();
});

setTimeout(function () {
    window.location.href = '/';
}, sessionLifetime);

$(document).on('change', '#bug_type', function () {
    updateQuestions();
});

function updateQuestions() {
    let bug_type = parseInt(document.getElementById('bug_type').value);
    let q1 = document.getElementById('questionOne');
    let q2 = document.getElementById('questionTwo');

    if (bug_type === 1) {
        q1.innerText = "What is the symptom of the reported bug?";
        q2.innerText = "Please explain how we can re-create the issue";
    } else if (bug_type === 2) {
        q1.innerText = "What is the current problem this enhancement will resolve?";
        q2.innerText = "Is there something you are currently doing to work around the problem?";
    }
}

$(document).on('click', '#bug-report-submit', function () {
    submitBugReport();
});

async function submitBugReport() {
    let mType = document.querySelector('#bug_type').value;
    let mQuestionOne = document.querySelector('#questionOne');
    let mQuestionTwo = document.querySelector('#questionTwo');
    let answerOne = document.querySelector('#bq1');
    let answerTwo = document.querySelector('#bq2');

    let req = {
        bug_type: mType,
        q1: mQuestionOne.innerText,
        a1: answerOne.value,
        q2: mQuestionTwo.innerText,
        a2: answerTwo.value
    };

    console.log(req)

    $.ajax({
        type: "POST",
        url: "/bug-report/submit",
        data: req,
        success: function (data) {
            $('.feedback-modal').modal('hide');
            answerOne.value = "";
            answerTwo.value = "";
            notificationBar('success',
                data);
        },
        fail: function (data) {
            notificationBar('error',
                data);
        }
    })


}

function viewTransactionFromAnywhere(id) {
    $.ajax({
        type: "GET",
        url: "/transactions/view/" + id,
        success: function (data) {
            $('#transactionViewerArea').html(data)
            $('#ViewModal').modal('toggle')
        },
        fail: function () {
            flashMessage('error', 'There was an error in your product entry, please try again.')
        }
    })
}

if (sessionMessage !== null) {
    notificationBar(sessionMessage.status, sessionMessage.message)
}

$('#company_select').multiselect({
    buttonWidth: '100%',
    includeSelectAllOption: false,
    enableCaseInsensitiveFiltering: true,
    maxHeight: 400,
    onChange: function (option, checked, select) {
        if (checked == true) {
            var company_id, site;
            if (!this.$select.val().includes(":")) {
                company_id = this.$select.val()
                site = null;
            } else {
                company_id = this.$select.val().split(":")[0];
                site = this.$select.val().split(":")[1];
            }

            var company_name = this

            var r = confirm("Are you sure you want to switch company?");
            if (r == true) {
                $.ajax({
                    type: "POST",
                    url: "/company/switch",
                    data: {id: company_id, site: site}
                }).done(function (data) {
                    notificationBar(data.status, data.message);
                    if (data.status === 'success') {
                        window.location.href = '/dashboard';
                    }
                })
            } else {
                return false;
            }
        }
    },
    onDropdownShown: function (event) {
        $(event.target).find('.filter input').focus();
    }
});

$('.company-select').on('click', function () {
    var company_id = $(this).data('id');

    var r = confirm("Are you sure you want to switch company?");
    if (r == true) {
        $.ajax({
            type: "POST",
            url: "/company/switch",
            data: {id: company_id}
        }).done(function (data) {

            notificationBar(data.status, data.message);
            if (data.status === 'success') {
                window.location.href = '/dashboard';
            }
        })
    } else {
        return false;
    }
})

var sitenum = null;
var buttonStyles = {};

$('#side-menu').metisMenu();

$(window).bind("load resize", function () {
    var topOffset = 50;
    var width = (this.window.innerWidth > 0) ? this.window.innerWidth : this.screen.width;
    if (width < 768) {
        $('div.navbar-collapse').addClass('collapse');
        topOffset = 100; // 2-row-menu
    } else {
        $('div.navbar-collapse').removeClass('collapse');
    }

    var height = ((this.window.innerHeight > 0) ? this.window.innerHeight : this.screen.height) - 1;
    height = height - topOffset;
    if (height < 1) height = 1;
    if (height > topOffset) {
        $("#page-wrapper").css("min-height", (height) + "px");
    }
});

var url = window.location;

var element = $('ul.nav a').filter(function () {
    return this.href == url;
}).addClass('active').parent();

while (true) {
    if (element.is('li')) {
        element = element.parent().addClass('in').parent();
    } else {
        break;
    }
}

$('#side-menu').metisMenu();

function noData(target) {
    $('<div id="loading"><h3><i class="fa fa-warning"></i><br />We have no data yet!</h3>Please wait for more transactions to be recorded.<br /><br /> <button class="btn btn-success"><i class="fa fa-refresh"></i> Reload</button></div>').css({
        position: "absolute",
        width: "100%",
        height: "100%",
        top: 0,
        left: 0,
        background: '#FFF',
        'z-index': 999,
        "padding-top": "40%",
        "text-align": "center"
    }).appendTo($(target).css("position", "relative"));
}
