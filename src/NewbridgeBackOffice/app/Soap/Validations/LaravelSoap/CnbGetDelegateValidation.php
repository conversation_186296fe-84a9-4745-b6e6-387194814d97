<?php

namespace NewbridgeWeb\Soap\Validations\LaravelSoap;

use Illuminate\Support\Facades\Validator;

/**
 * cnb_GetDelegate Validation
 *
 * cnb_GetDelegate
 */
class CnbGetDelegateValidation
{
    public static function validator($parameters)
    {
        return Validator::make($parameters, [
            '0.cnbDelegate.FunctionRef' => 'string|nullable',
            '0.cnbDelegate.RoomPickID' => 'integer|nullable',
            '0.cnbDelegate.FolioID' => 'integer|nullable',
            '0.cnbDelegate.Surname' => 'string|nullable',
            '0.cnbDelegate.ProfileRef' => 'string|nullable',
            '0.cnbDelegate.CompanyRef' => 'string|nullable',
            '0.cnbDelegate.NoOfGuests' => 'integer|nullable',
            '0.cnbDelegate.Table' => 'string|nullable',
            '0.cnbDelegate.TableTime' => 'string|nullable',
            '0.cnbDelegate.Token' => 'string|nullable',
            '0.cnbDelegate.TokenSource' => 'in:Auric, Worldpay, SecureTrading, PaymentExpress',
            '0.cnbDelegate.Notes' => 'string|nullable',
            '0.cnbDelegate.DepositPayType' => 'string|nullable',
            '0.cnbDelegate.DepositAmount' => 'string|nullable',
            '0.cnbDelegate.DepositComments' => 'string|nullable',
        ]);
    }
}
