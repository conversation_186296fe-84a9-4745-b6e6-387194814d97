<?php

namespace NewbridgeWeb\Notifications;

use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Notifications\Messages\MailMessage;

class ResetPasswordNotification extends ResetPassword
{
    /**
     * Create a new notification instance.
     *
     * @param  string  $token
     * @return void
     */
    public function __construct($token)
    {
        parent::__construct($token);
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url(config('app.url').route('password.reset', $this->token, false));
        return (new MailMessage)
            ->subject('Reset Password Notification')
            ->view('emails.template', [
                'data' => [
                    'preHeaderText' => 'You are receiving this email because we received a password reset request for your account.',
                    'mainTitle' => 'Reset Your Password',
                    'textBlock1' => 'Click the button below to reset your password:',
                    'buttonUrl' => $url,
                    'buttonText' => 'Reset Password',
                    'lowerBlocks' => [
                        [
                            'background-color' => 'ffffff',
                            'text-color' => '666666',
                            'content' => 'This password reset link will expire in ' . config('auth.passwords.' . config('auth.defaults.passwords') . '.expire') . ' minutes.',
                            'additional-css' => ''
                        ],
                        [
                            'background-color' => 'ffffff',
                            'text-color' => '666666',
                            'content' => 'If you did not request a password reset, no further action is required.',
                            'additional-css' => ''
                        ]
                    ]
                ]
            ]);
    }
}
