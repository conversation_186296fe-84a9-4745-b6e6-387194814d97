<?php

namespace NewbridgeWeb\Listeners;

use NewbridgeWeb\Events\TransactionCreatedEvent;
use NewbridgeWeb\Jobs\ProcessCustomerPoints;
use NewbridgeWeb\Jobs\TransactionEventGridJob;
use NewbridgeWeb\Repositories\PosTransaction;

class TransactionCreatedEventListener
{
    public $transaction;

    /**
     * TransactionCreatedEventListener constructor.
     * @param PosTransaction $transaction
     */
    public function __construct(PosTransaction $transaction)
    {
        $this->transaction = $transaction->toArray();
    }

    /**
     * @param TransactionCreatedEvent $event
     */
    public function handle(TransactionCreatedEvent $event)
    {
        $job = new ProcessCustomerPoints($event->transactionSummary);

        dispatch($job)->delay(config('newbridge.worker_delay'));

        if(config('newbridge.send_to_event_grid')) {
            $eventJob = new TransactionEventGridJob($event->transactionSummary);

            dispatch($eventJob)->delay(10);
        }
    }
}
