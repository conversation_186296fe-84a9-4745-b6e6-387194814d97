<?php

namespace NewbridgeWeb\Listeners;

use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Helpers\EventGridHelper;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\RedisRepository;
use NewbridgeWeb\Repositories\RedisRepositoryLists;

class UpdaterUpdateCrudEventListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  UpdaterUpdateCrudEvent $event
     * @return void
     */
    public function handle(UpdaterUpdateCrudEvent $event)
    {
        $object = $event->event;

        $table = $object->getTable();

        $company_id = $object->company_id;

        $terminals = Pos::where('company_id', $company_id);

        // check if the object has a site num?
        if ($object->site_num && $object->site_num !== null && $object->site_num != 0) {
            $terminals = $terminals->where('site_num', $object->site_num);
        }

        $terminals = $terminals->get();

        $allTerminals = true;

        // put condition here to make
        if (isset($object->Command) && $object->Command == 6) {
            $table = 'mod_groups';
        }

        if ($table == 'pos_settings' && $object->type !== 4) {
            $allTerminals = false;
        }

        if ($table == 'sys_int_commands' && $object->Command == 1) {
            $table = 'pos_screens';
        }

        if ($table == 'site_table_tables') {
            $table = 'site_tables';
        }

        if ($table == 'pos_button') {
            if ($object->lnk_buttonlinks && !empty($object->lnk_buttonlinks)) {
                foreach ($object->lnk_buttonlinks as $k => $v) {
                    $object->lnk_buttonlinks[$k]->id = 0;
                }
            }
        }

        if ($allTerminals == true) {
            foreach ($terminals as $term) {
                $hash = 'c-'.$company_id;

                // dont queue if we just received this record, queue for all other terminals
                if ($term->terminal_num != $object->received_from) {
                    /**
                     * set the t-id and c-id here
                     */
                    $hash .= ':t-'.$term->id;
                    $key = $hash . ':updates:' . $table;

                    RedisRepositoryLists::store($key, $object->id);

                    self::addTableToAvailableUpdates((int) $company_id, (int) $term->id, $table);
                }
            }
        } else {
            $hash = $company_id;

            // don't queue if we just received this record, queue for all other terminals
            if ($object->received_from == 0) {
                $terminal = Pos::where('company_id', $company_id)
                    ->where('site_num', $object->site_num)
                    ->where('terminal_num', $object->terminal_num)
                    ->get();

                if (!empty($terminals)) {
                    foreach ($terminal as $terminal) {
                        $hash = 'c-' . $company_id . ':t-' . $terminal['id'];
                        $key = $hash . ':updates:' . $table;

                        RedisRepositoryLists::store($key, $object->id);

                        $checkKey = $hash . ':check-updates';
                        self::addTableToAvailableUpdates((int) $company_id, (int) $terminal['id'], $table);
                    }
                }
            }
        }
    }

    public static function addTableToAvailableUpdates(int $company, int $terminal, string $table): void
    {
        $hash = 'c-' . $company . ':t-' . $terminal;
        $key = $hash . ':check-updates';

        if(!Redis::connection('cache')->exists($key)) {
            RedisRepository::store($key, [$table]);
        } else {
            $data = Redis::connection('cache')->get($key);
            $decoded = json_decode($data, true);

            if($data == null) {
                $array = [$table];
                RedisRepository::store($key, $array);
            } else {

                if (is_array($decoded) && !in_array($table, $decoded)) {
                    $decoded[] = $table;
                    RedisRepository::store($key, $decoded);
                }

            }
        }
    }
}
