<?php

namespace NewbridgeWeb\Listeners;

use Laravel\Horizon\Events\MasterSupervisorOutOfMemory;
use Log;

class HorizonMemoryEventListener
{
    /**
     * HorizonMemoryEventListener constructor.
     */
    public function __construct()
    {
        //
    }

    /**
     * @param MasterSupervisorOutOfMemory $event
     */
    public function handle(MasterSupervisorOutOfMemory $event)
    {
        Log::warning('Horizon Master memory limit reached', json_decode(json_encode($event), true));
    }
}