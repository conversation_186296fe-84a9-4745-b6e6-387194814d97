<?php

namespace NewbridgeWeb\Listeners;

use Laravel\Horizon\Events\SupervisorOutOfMemory;
use Log;

class HorizonSupervisorMemoryEventListener
{
    /**
     * HorizonMemoryEventListener constructor.
     */
    public function __construct()
    {
        //
    }

    /**
     * @param SupervisorOutOfMemory $event
     */
    public function handle(SupervisorOutOfMemory $event)
    {
        Log::warning('Horizon supervisor memory limit reached', json_decode(json_encode($event), true));
    }
}