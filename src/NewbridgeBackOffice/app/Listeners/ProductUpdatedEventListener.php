<?php

namespace NewbridgeWeb\Listeners;

use NewbridgeWeb\Events\ProductUpdatedEvent;
use NewbridgeWeb\Jobs\ProductUpdateJob;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\Products;

class ProductUpdatedEventListener
{
    public $product;

    /**
     * TransactionCreatedEventListener constructor.
     * @param PosTransaction $transaction
     * @param $product
     */
    public function __construct(Products $product)
    {
        $this->product = $product;
    }

    /**
     * @param ProductUpdatedEvent $event
     */
    public function handle(ProductUpdatedEvent $event)
    {
        if ($event->product->sku_guid != null || $event->product->plu_parent_guid != null || $event->product->recipe_guid != null) {
            $job = new ProductUpdateJob($event->product->id);
            dispatch($job)->delay(config('newbridge.worker_delay'));
        }
    }
}
