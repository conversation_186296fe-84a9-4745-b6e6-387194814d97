<?php

namespace NewbridgeWeb\Listeners;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Events\BackgroundImportEvent;
use NewbridgeWeb\Mail\ImportCompletedEmail;

class BackgroundImportEventListener
{
    public function __construct()
    {
        //
    }

    public function handle(BackgroundImportEvent $event)
    {
        $data = $event->import;
        Redis::set('import-' . $event->import->guid . '-status', json_encode(['status' => 'success', 'text' => 'Import complete!', 'percent' => '100']));
        Mail::to($event->user->email)->send(new ImportCompletedEmail($data)); // mail still needs to be made
    }
}
