<?php

namespace NewbridgeWeb\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TerminalSettingUpdatedVersion
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;
    public $data;

    /**
     * TerminalSettingUpdatedVersion constructor.
     * @param $setting
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
