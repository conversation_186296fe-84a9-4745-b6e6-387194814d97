<?php

namespace NewbridgeWeb\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TransactionCreatedEvent implements ShouldDispatchAfterCommit
{
    use Dispatchable;
    use InteractsWithQueue;
    use SerializesModels;
    public $transactionSummary;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($transactionSummary)
    {
        $this->transactionSummary = $transactionSummary;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
