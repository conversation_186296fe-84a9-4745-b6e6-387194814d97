<?php

namespace NewbridgeWeb\Events;

use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TransactionDetailCreatedEvent implements ShouldDispatchAfterCommit
{
    use Dispatchable;
    use InteractsWithQueue;
    use SerializesModels;
    public $transactionDetail;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($transactionDetail)
    {
        $this->transactionDetail = [
            'id' => $transactionDetail['id'],
            'company_id' => $transactionDetail['company_id'],
            'site_num' => $transactionDetail['site_num'],
            'terminal_num' => $transactionDetail['terminal_num'],
            'command' => $transactionDetail['command'],
            'command_type' => $transactionDetail['command_type'],
            'product_guid' => $transactionDetail['product_guid'],
            'trans_id' => $transactionDetail['trans_id'],
            'qty' => $transactionDetail['qty'],
            'void_reason_guid' => $transactionDetail['void_reason_guid'],
            'splitparentreference' => $transactionDetail['splitparentreference'],
            'datetime' => $transactionDetail['datetime']
        ];
    }
}
