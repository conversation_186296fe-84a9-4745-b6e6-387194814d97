<?php

namespace NewbridgeWeb\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class BackgroundImportEvent
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;
    public $user;
    public $import;

    /**
     * Create a new event instance.
     *
     * @param $import
     */
    public function __construct($import)
    {
        $this->user = session('user');
        $this->import = $import;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
