<?php

namespace NewbridgeWeb\Repositories;

use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerMarketing extends NewbridgeRepository
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    
    protected $table = 'cus_customer_marketing';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    public string $sourceField = 'id';

    /**
     * Get all Departments for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompany()
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::exists()->where('company_id', $user->company_id)->get();

            return $data;
        } else {
            return null;
        }
    }

    public function customer()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\Customer', 'guid', 'customer_guid');
    }
}
