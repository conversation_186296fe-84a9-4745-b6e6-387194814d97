<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PromotionBins extends NewbridgeRepository
{
    use SoftDeletes;
    use LogsActivity;
    
    protected $table = 'plu_promotion_bin';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    public string $sourceField = 'id';

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * @return string
     */
    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $bins = self::select('guid', 'displayname')->where('company_id', $company_id)->get();

        $result = [];
        $result['Please select one...'] = '';
        foreach ($bins as $k => $v) {
            $result[$v['displayname']] = $v['guid'];
        }

        return json_encode($result);
    }

    /**
     * Get all Departments for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompany()
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::exists()->where('company_id', $user->company_id)->get();

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get all departments for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompanyDataTables()
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::exists()->where('company_id', $user->company_id);

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get all departments for the current company, by ID
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompanyById($id)
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::exists()->where('company_id', $user->company_id)->where('id', $id);

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get the parent promotion
     */
    public function promotion()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\Promotions', 'plu_promotion_guid', 'Guid');
    }

    /**
     * Get the products in the bin
     */
    public function items()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PromotionItems', 'plu_promotion_bin_guid', 'guid');
    }

    public function discount()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Discounts', 'CommandUID', 'discount_guid');
    }

    /**
     * Get the products in the bin
     */
    public function promotion_items()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PromotionItems', 'plu_promotion_bin_guid', 'guid');
    }
}
