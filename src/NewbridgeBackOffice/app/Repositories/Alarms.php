<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Alarms extends NewbridgeRepository
{
    use SoftDeletes;

    protected $table = 'sys_alarms';
    protected $guarded = ['id'];
    public $timestamps = true;
    protected $appends = ['new_count', 'alarm_period_count'];

    /**
     * Get the product records associated with the product.
     */
    public function type()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\AlarmTypes', 'id', 'type_id');
    }

    /**
     * Get the product records associated with the product.
     */
    public function results()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\AlarmResults', 'alarm_id', 'id')->orderBy('created_at', 'DESC');
    }

    public function getNewCountAttribute()
    {
        return AlarmResults::where('alarm_id', $this->id)->where('status', 0)->count();
    }

    public function period()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\AlarmPeriod', 'id', 'alarm_period');
    }

    public function getAlarmPeriodCountAttribute()
    {
        $key = 'alarm_' . $this->id;

        $check = RedisRepository::checkExists($key);

        if ($check === true) {
            $alarm_details = RedisRepository::getByKey($key);

            return $alarm_details['trigger_count'];
        }
    }
}
