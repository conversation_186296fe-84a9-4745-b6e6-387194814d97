<?php

namespace NewbridgeWeb\Repositories;

use Carbon\Carbon;
use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Laravel\Scout\Searchable;
use NewbridgeWeb\Events\ProductUpdatedEvent;
use NewbridgeWeb\Http\Helpers\StockPeriodHelper;
use NewbridgeWeb\Repositories\Abstracts\Replicable;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use Ramsey\Uuid\Uuid;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;

class Products extends NewbridgeModel implements Replicable
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    use LogsActivity;
    use Searchable;

    protected $table = 'plu_products';
    protected $dates = ['deleted_at'];
    public $guarded = ['id'];
    protected $appends = ['gross_profit', 'is_wasteable', 'is_modifier_text'];
    protected $cascadeDeletes = ['groups', 'links'];
    public string $sourceField = 'guid';

    public function searchableAs(): string
    {
        return 'products';
    }

    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'virtual_id' => 'product ' . $this->id,
            'short_desc' => $this->short_desc,
            'virtual_short_desc' => 'product ' . $this->short_desc,
            'displayname' => $this->displayname,
            'virtual_displayname' => 'product ' . $this->displayname,
            'department' => $this->department?->displayname,
            'subdepartment' => $this->subdepartment?->displayname,
            'bardcode' => $this->barcode,
            'ean13' => $this->ean13,
            'company_id' => $this->company_id,
            'site_num' => $this->site_num,
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logExcept(['productinfo'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
    protected $dispatchesEvents = [
        'updated' => ProductUpdatedEvent::class
    ];
    protected $casts = [
        'selling_price_1' => 'decimal:2',
        'selling_price_2' => 'decimal:2',
        'selling_price_3' => 'decimal:2',
        'selling_price_4' => 'decimal:2',
        'selling_price_5' => 'decimal:2'
    ];

    public function activity_log()
    {
        return $this->hasMany(Activity::class, 'subject_id', 'id')->where('subject_type', 'NewbridgeWeb\Repositories\Products');
    }

    public function sku()
    {
        return $this->hasOne(SKU::class, 'guid', 'sku_guid');
    }

    public function department()
    {
        return $this->hasOne(Departments::class, 'guid', 'department_guid');
    }

    public function subdepartment()
    {
        return $this->hasOne(SubDepartment::class, 'guid', 'sub_department_guid');
    }

    public function supplier()
    {
        return $this->hasOne(Suppliers::class, 'guid', 'supplier_guid');
    }

    public function groups()
    {
        return $this->hasMany(LinkModifierGroups::class, 'plu_guid', 'guid');
    }

    public function links()
    {
        return $this->hasMany(PosButtonLink::class, 'CommandUID', 'guid');
    }

    public function parent()
    {
        return $this->belongsTo(Products::class, 'plu_parent_guid', 'guid')
            ->withTrashed();
    }

    public function children()
    {
        return $this->hasMany(Products::class, 'plu_parent_guid', 'guid');
    }

    public function recipe()
    {
        return $this->belongsTo(Recipes::class, 'recipe_guid', 'guid');
    }

    public function stock()
    {
        return $this->hasMany(StockTransactions::class, 'product_id', 'id')
            ->where('status', 1);
    }

    public function tax()
    {
        return $this->hasOne(TaxRates::class, 'guid', 'tax_guid');
    }

    public function rule()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Tax\TaxRules', 'guid', 'tax_rule_guid');
    }

    public function stocktakedata()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\StockTransactions', 'product_id', 'id')->where('status', 0);
    }

    public function oldStock()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\StockTransactions', 'product_id', 'id')->where('status', 1);
    }

    public function transactions()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosTransactionDetail', 'product_guid', 'guid');
    }

    public function wastages()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\StockTransactions', 'product_id', 'id')->where('summary_type', 5);
    }

    public function deliveries()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\StockTransactions', 'product_id', 'id')->where('summary_type', 2);
    }

    public function recipeLinks()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\RecipeLinks', 'product_guid', 'guid');
    }

    public function appAvailability()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\AppAvailableProduct', 'product_id', 'id');
    }

    public function mapping()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Integrations\IntegrationLinks', 'subject_id', 'id');
    }

    public function currentStock(int|null $siteNum = null): float
    {
        $siteNum = $siteNum ?? session('current_site');
        return StockTransactions::where('product_id', $this->id)
            ->whereIn('site_num', [0, $siteNum])
            ->where('status', 1)
            ->whereNull('deleted_at')
            ->select(['product_quantity'])
            ->orderBy('created_at', 'desc')
            ->orderBy('id', 'desc')
            ->first()?->product_quantity ?? 0.0;
    }

    protected function productQuantity(): Attribute
    {
        return Attribute::make(
            get: fn () => StockTransactions::where('product_id', $this->id)
                ->whereIn('site_num', [0, session('current_site')])
                ->where('status', 1)
                ->whereNull('deleted_at')
                ->select(['product_quantity'])
                ->orderBy('created_at', 'desc')
                ->orderBy('id', 'desc')
                ->first()?->product_quantity ?? 0.0,
        );
    }

    public static function relations()
    {
        return ['supplier', 'department', 'sku', 'subdepartment', 'groups', 'parent', 'children', 'recipe', 'tax', 'rule'];
    }

    public static function forCurrentCompanyDataTables()
    {
        $user = session('user');
        $company = Company::find($user->company_id);

        if ($user->company_id) {
            if ($company->site_specific_products === 1) {
                $data = self::where('site_num', \Session::get('current_site'))->exists()
                    ->with(self::relations())->where('company_id', $user->company_id);
            } else {
                $data = self::exists()->with(self::relations())->where('company_id', $user->company_id);
            }

            return $data;
        } else {
            return null;
        }
    }

    /**
     * These products can be parents
     *
     * @param $company_id
     * @return mixed
     */
    public static function assignableProductsCompany($company_id)
    {
        $company = Company::find($company_id);
        if ($company->site_specific_products === 1) {
            return Products::where('company_id', $company_id)
                ->where('site_num', \Session::get('current_site'))
                ->has('sku')
                ->get();
        } else {
            return Products::where('company_id', $company_id)
                ->has('sku')->get();
        }
    }

    public static function forCurrentCompanyById($id): ?\Illuminate\Database\Eloquent\Builder
    {
        $user = session('user');
        $company = Company::find($user->company_id);

        if ($user->company_id) {
            if ($company->site_specific_products == 1) {
                $data = self::where('site_num', \Session::get('current_site'))->exists()->with(self::relations())->where('company_id', $user->company_id)->where('id', $id);
            } else {
                $data = self::exists()->with(self::relations())->where('company_id', $user->company_id)->where('id', $id);
            }

            return $data;
        } else {
            return null;
        }
    }

    public static function getWithStockData($company_id, $input)
    {
        $lastStocktake = StockPeriodHelper::availableStartDate();

        $stockResults = StockTransactions::select(DB::raw('id, product_id, sum(quantity) as current_stock'));

        if ($input['module'] === 'spotchecks' && Auth::user()->company->spotchecks_affect_stock === 1) {
            $wheres = [['company_id', $company_id], ['status', 1], ['created_at', '>', $lastStocktake]];
            $orWhere = [['company_id', $company_id], ['status', 0], ['summary_type', 6], ['created_at', '>', $lastStocktake]];
        } else {
            $wheres = [['company_id', $company_id], ['status', 1], ['created_at', '>=', $lastStocktake]];
            $orWhere = [];
        }

        $wheres[] = ['site_num', \Session::get('current_site')];
        if (!empty($orWhere)) {
            $orWhere[] = ['site_num', \Session::get('current_site')];
        }

        if (isset($input['before_date']) && $input['before_date'] != '') {
            $before = Carbon::parse($input['before_date']);
            $wheres[] = ['created_at', '<', $before->toDateTimeString()];

            if (!empty($orWhere)) {
                $orWhere[] = ['created_at', '<', $before->toDateTimeString()];
            }
        }

        if (isset($input['date']) && $input['date'] != '') {
            $date = Carbon::parse($input['date']);
            $stockResults = $stockResults->where('created_at', '<', $date->toDateTimeString());

            $wheres[] = ['created_at', '<', $date->toDateTimeString()];
            if (!empty($orWhere)) {
                $orWhere[] = ['created_at', '<', $date->toDateTimeString()];
            }
        }

        if (!empty($orWhere)) {
            $stockResults = $stockResults->where($wheres)->orWhere($orWhere);
        } else {
            $stockResults = $stockResults->where($wheres);
        }

        $stockResults = $stockResults->groupBy('product_id')->get()->keyBy('product_id');

        $products = Products::with('supplier', 'department', 'sku')
            ->has('sku')
            ->where('company_id', Auth::user()->company_id);

        if (isset($input['supplier']) && $input['supplier'] != '') {
            $products = $products->where('supplier_guid', $input['supplier']);
        }

        if (isset($input['department']) && $input['department'] != '') {
            $products = $products->where('department_guid', $input['department']);
        }

        $company = Company::find(Auth::user()->company_id);

        if ($company->site_specific_products == 1) {
            $products = $products->where('site_num', \Session::get('current_site'));
        }

        $products = $products->orderBy('displayname', 'DESC');

        $result = $products->get();

        foreach ($result as $r) {
            $r->current_stock = isset($stockResults[$r->id]) ? $stockResults[$r->id]->current_stock : 0;
        }

        return $result;
    }

    public function getVatAmountAttribute()
    {

        $vat_rate = $this->tax_guid != null ? TaxRates::where('guid', $this->tax_guid)->withTrashed()->first() : null;

        if ($vat_rate != null) {
            return $this->calculateVat($this->selling_price_1, $vat_rate->rate);
        } else {
            return 0.00;
        }
    }

    public function getIsWasteableAttribute()
    {
        if ($this->sku_guid == null && $this->recipe_guid == null && $this->plu_parent_guid == null) {
            return false;
        } else {
            return true;
        }
    }

    public function getAgeRestrictedAttribute($value): bool
    {
        if ($value == 0 || $value == null) {
            return false;
        } else {
            return true;
        }
    }

    public function getFollowCourseSeparatorAttribute($value): bool
    {
        if ($value == 0) {
            return false;
        } else {
            return true;
        }
    }

    public function getIsModifierAttribute($value)
    {
        if ($value == 0) {
            return false;
        } else {
            return true;
        }
    }

    public function getSoldbyweightAttribute($value)
    {
        if ($value == 0) {
            return false;
        } else {
            return true;
        }
    }

    public function getIsmanualweightAttribute($value)
    {
        if ($value == 0) {
            return false;
        } else {
            return true;
        }
    }

    public function getShowChildListAttribute($value)
    {
        if ($value == 0) {
            return false;
        } else {
            return true;
        }
    }

    public function getIsModifierTextAttribute()
    {
        if ($this->is_modifier == 1) {
            return 'Modifier';
        } else {
            return 'Product';
        }
    }

    public function getGrossProfitAttribute()
    {
        if ($this->costprice > 0 && $this->selling_price_1 > 0) {
            $net = ($this->selling_price_1 - $this->vat_amount);

            $result = (($net - (float) $this->costprice) / $net) * 100;

            return number_format($result, 2) . '%';
        } else {
            return '100%';
        }
    }

    private function calculateVat($gross, $rate)
    {
        $vatAmount = number_format(($gross - ($gross / (($rate / 100) + 1))), 2);

        return (float) $vatAmount;
    }

    public function getCostpriceAttribute($val)
    {
        if ($val === null) {
            return 0;
        } else {
            return $val;
        }
    }

    public function getReplicableRelationships()
    {
        return ['supplier', 'department', 'sku', 'subdepartment', 'groups', 'parent', 'children', 'recipe', 'tax'];
    }

    public function getRequiredAttributeReplacements()
    {
        $guid = Uuid::uuid4();

        return [
            'guid' => $guid,
            'CommandUID' => $guid,
            'barcode' => null,
            'selling_price_2' => null,
            'selling_price_3' => null,
            'selling_price_4' => null,
            'selling_price_5' => null,
        ];
    }
}
