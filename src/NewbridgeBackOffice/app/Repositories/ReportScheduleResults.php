<?php

namespace NewbridgeWeb\Repositories;

use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReportScheduleResults extends NewbridgeRepository
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    protected $table = 'report_results';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function schedule()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\ReportSchedules', 'schedule_id', 'id');
    }
}
