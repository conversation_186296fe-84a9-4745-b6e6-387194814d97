<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Http\Helpers\HasManyKeyByTrait;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class SubDepartment extends NewbridgeModel
{
    use SoftDeletes;
    use HasManyKeyByTrait;
    use LogsActivity;
    
    protected $table = 'plu_subdepartment';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    public string $sourceField = 'id';

    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $subdepartments = self::select('guid', 'displayname')->where('company_id', $company_id)->get();

        $result = [];
        $result['Please select one...'] = '';
        foreach ($subdepartments as $k => $v) {
            $result[$v['displayname']] = $v['guid'];
        }

        return json_encode($result);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function getLogNameToUse(string $eventName = ''): string
    {
        return 'activity_customer';
    }

    /**
     * Get the products record associated with the department.
     */
    public function products()
    {
        return $this->hasManyKeyBy('guid', 'NewbridgeWeb\Repositories\Products', 'sub_department_guid', 'guid');
    }

    public function sales()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosTransactionDetail', 'sub_department_guid', 'guid');
    }

    public function mapping()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Integrations\IntegrationLinks', 'subject_id', 'id');
    }
}
