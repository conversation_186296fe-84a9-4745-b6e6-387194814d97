<?php

namespace NewbridgeWeb\Repositories\JobStatus;

use Illuminate\Support\Collection;

class JobStatuses
{
    public Collection $jobStatuses;

    public function __construct()
    {
        $this->jobStatuses = new Collection();
    }

    public function addJobStatus(JobStatus $jobStatus): void
    {
        $this->jobStatuses->add($jobStatus);
    }

    public function removeJobStatus(JobStatus $jobStatus): void
    {
        $this->jobStatuses = $this->jobStatuses->reject(static fn ($item) => $item->getUuid() === $jobStatus->getUuid());
    }

    public function getJobStatuses(): Collection
    {
        return $this->jobStatuses;
    }

    public function toJson(): string
    {
        return $this->jobStatuses->map(static fn ($item) => json_decode($item->toJson(), true))->toJson();
    }

    public static function fromJson(string $jsonString): self
    {
        $data = json_decode($jsonString, true);
        $jobStatusCollection = new self();
        foreach ($data as $jobStatusData) {
            $jobStatus = JobStatus::fromJson(json_encode($jobStatusData));
            $jobStatusCollection->addJobStatus($jobStatus);
        }

        return $jobStatusCollection;
    }
}
