<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\CausesActivity;

class UserSites extends NewbridgeRepository
{
    use SoftDeletes;
    use CausesActivity;

    protected $table = 'user_sites';
    protected $guarded = ['id'];
    public $timestamps = true;
    protected $dates = ['deleted_at'];
    public string $sourceField = 'id';

    public function user()
    {
        return $this->belongsTo('NewbridgeWeb\User', 'id', 'user_id');
    }
}
