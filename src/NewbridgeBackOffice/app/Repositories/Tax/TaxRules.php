<?php

namespace NewbridgeWeb\Repositories\Tax;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Repositories\NewbridgeRepository;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\Traits\LogsActivity;

class TaxRules extends NewbridgeRepository
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivity;
    
    protected $table = 'sys_tax_rules';
    public string $sourceField = 'id';

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * @return HasMany
     */
    public function links(): HasMany
    {
        return $this->hasMany(TaxRuleLinks::class, 'rule_guid', 'guid');
    }

    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $skus = self::select('id', 'guid', 'name')->where('company_id', $company_id)->get();

        $result = [];

        $result['Please select one...'] = '';
        foreach ($skus as $k => $v) {
            $result[$v['name']] = $v['guid'];
        }

        return json_encode($result);
    }
}
