<?php

namespace NewbridgeWeb\Repositories;

class DashboardRepository extends NewbridgeRepository
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'web_dashboard_layout';

    /**
     *
     * Get dashboard options for the user as previously stored in the database
     *
     */
    public function layout()
    {
        $user = Auth::id();

        return ''; // this will be an aeeay of data based on the selections the user has made previously
    }
}
