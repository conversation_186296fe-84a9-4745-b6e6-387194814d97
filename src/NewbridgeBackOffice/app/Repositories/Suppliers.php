<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Suppliers extends NewbridgeModel
{
    use SoftDeletes;
    
    protected $table = 'plu_suppliers';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    public string $sourceField = 'id';

    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $supplers = self::select('id', 'guid', 'name')->where('company_id', $company_id)->get();

        $result = [];

        $result['Please select one...'] = '';
        foreach ($supplers as $k => $v) {
            $result[$v['name']] = $v['guid'];
        }

        return json_encode($result);
    }

    /**
     * Get all suppliers for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompany()
    {
        if (session('user.company_id')) {
            $data = self::where('company_id', session('user.company_id'))->get();

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get the transaction record associated with the stock summaries.
     */
    public function orders()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\StockSummary', 'supplier_id', 'id')
            ->where('summary_type', 2)
            ->where('status', '>=', 2);
    }
}
