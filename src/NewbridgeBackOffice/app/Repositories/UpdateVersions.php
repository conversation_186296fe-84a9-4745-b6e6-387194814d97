<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UpdateVersions extends Model
{
    use SoftDeletes;
    
    public $table = 'update_versions';
    protected $guarded = ['id'];
    public $version;
    public $description;
    public $changelog;
    public $minimum_version;
    public $filepath;
    public string $sourceField = 'id';
}
