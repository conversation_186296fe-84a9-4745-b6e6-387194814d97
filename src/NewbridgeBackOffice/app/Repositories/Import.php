<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Import extends Model
{
    use SoftDeletes;
    protected $table = 'imports';
    protected $guarded = ['id'];
    public $timestamps = true;
    public const FAILED = 0;
    public const IN_PROGRESS = 1;
    public const COMPLETE = 2;
    public const PRODUCT_IMPORT = 1;
    public const CUSTOMER_IMPORT = 2;

    public function findByGUID($guid)
    {
        $import = $this->where('guid', '=', $guid)->first();

        return $import;
    }
}
