<?php

namespace NewbridgeWeb\Repositories;

use Auth;
use <PERSON><PERSON>ynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Events\TransactionDetailCreatedEvent;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Tax\TaxRates;

class PosTransactionDetail extends NewbridgeRepository implements ShouldHandleEventsAfterCommit
{
    use SoftDeletes;
    use CascadeSoftDeletes;

    protected $dispatchesEvents = [
        'created' => TransactionDetailCreatedEvent::class
    ];
    protected $table = 'pos_transaction_details';
    protected $guarded = ['id'];
    protected $appends = ['tax_value_calculation'];
    public $timestamps = true;
    public string $sourceField = 'id';
    protected $casts = [
        'id' => 'int',
        'net_value' => 'float',
        'tax_value' => 'float',
        'tax_rate' => 'float',
        'discount_value' => 'float',
        'sale_value' => 'float'
    ];
    protected $cascadeDeletes = ['stockTransactions'];

    public function transaction()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\PosTransaction', 'trans_id', 'id');
    }

    public function detail()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\PosTransactionDetail', 'detail_id', 'id');
    }

    public function clerk()
    {
        return $this->hasOne(Clerks::class, 'guid', 'employee_guid')->withTrashed();
    }

    public function reason()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\VoidReasons', 'guid', 'void_reason_guid');
    }

    public function details()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosTransactionDetail', 'id', 'detail_id');
    }

    public function siblings()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosTransactionDetail', 'trans_id', 'trans_id')->where('discount_guid', 'cc91ab9e-64ea-4cc1-ae96-433ae171e4b1')->where('command_type', '!=', 5);
    }

    public function taxes()
    {
        return $this->hasMany(PosTransactionTaxDetails::class, 'detail_id', 'id');
    }

    public function department()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Departments', 'guid', 'department_guid');
    }

    public function sub_department()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\SubDepartment', 'guid', 'sub_department_guid');
    }

    public function product()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Products', 'guid', 'product_guid');
    }

    public function discount()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Discounts', 'CommandUID', 'discount_guid');
    }

    public function promotion()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Promotions', 'CommandUID', 'promotion_guid');
    }

    public function stockTransactions()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\StockTransactions', 'transaction_detail_id', 'id')->where(function ($query) {
            $query->where('isTransaction', 1);
            $query->orWhere('summary_type', 1);
        });
    }

    public function roomcharge()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosTransactionPayment', 'trans_id', 'trans_id')->where('method_type', 2);
    }

    public function terminal()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Pos', 'terminal_num', 'terminal_num');
    }

    /**
     * Get the Discount method as text.
     *
     * @param  string  $value
     * @return string
     */
    public function getDiscountStringAttribute()
    {
        return match ($this->discount_method) {
            0 => $this->discount_amount . ' %',
            1 => CurrencyHelper::symbol(Auth::user()->company_id) . '' . $this->discount_amount . ' Off',
            2 => 'Fixed Price',
            default => 'Unknown',
        };
    }

    public function getTaxValueCalculationAttribute()
    {
        if ($this->tax_id && $this->tax_id > 0) {
            $rate = TaxRates::find($this->tax_id);

            return ($this->net_value * ($rate->rate / 100));
        }

        return 0;
    }

    public function getTerminalNameAttribute()
    {
        $terminal = Pos::where('company_id', $this->company_id)
            ->where('site_num', $this->site_num)
            ->where('terminal_num', $this->terminal_num)
            ->withTrashed()
            ->first();

        if (!empty($terminal)) {
            return $terminal['name'];
        } else {
            return 'Unknown';
        }
    }
}
