<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Promotions extends NewbridgeModel
{
    use SoftDeletes;
    use LogsActivity;
    
    protected $table = 'plu_promotion';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    public string $sourceField = 'id';

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $promotions = self::select(['Guid', 'DisplayName'])->where('company_id', $company_id)->get();

        $result = [];
        $result['Please select one...'] = '';
        foreach ($promotions as $k => $v) {
            $result[$v['DisplayName']] = $v['Guid'];
        }

        return json_encode($result);
    }

    /**
     * Get all Promotions for the current company.
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompany()
    {
        $user = session('user');

        if ($user->company_id) {
            return self::exists()->where('company_id', $user->company_id)->get();
        } else {
            return null;
        }
    }

    /**
     * Get all promotions for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompanyDataTables()
    {
        $user = session('user');

        if ($user->company_id) {
            return self::exists()->where('company_id', $user->company_id);
        } else {
            return null;
        }
    }

    /**
     * Get all Promotions for the current company, by ID
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompanyById($id)
    {
        $user = session('user');

        if ($user->company_id) {
            return self::exists()->where('company_id', $user->company_id)->where('id', $id);
        } else {
            return null;
        }
    }

    /**
     * Get the discount related to the promotion
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function discount()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Discounts', 'CommandUID', 'discount_guid');
    }

    /**
     * Get the transactions related to the promotion
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function transactions()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosTransactionDetail', 'promotion_guid', 'CommandUID');
    }

    /**
     * get the bins related to this promotion
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function promotion_bins()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PromotionBins', 'plu_promotion_guid', 'CommandUID');
    }

    /**
     * get all products related to this promotion
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PromotionItems', 'plu_promotion_guid', 'CommandUID');
    }

    public function getUpsellAttribute($value)
    {
        return $value == 1;
    }

    public function getIsactiveAttribute($value)
    {
        return $value == 1;
    }
}
