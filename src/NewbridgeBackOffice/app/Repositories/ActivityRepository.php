<?php

namespace NewbridgeWeb\Repositories;

use NewbridgeWeb\User;

class ActivityRepository extends NewbridgeRepository
{
    protected $table = 'activity_log';
    protected $appends = ['activity_name', 'user_name', 'activity_data_new', 'activity_data_old'];

    public function getUserNameAttribute()
    {
        $user = User::find($this->causer_id);

        if ($user !== null) {
            return $user->name;
        }

        return 'POS Terminal';
    }

    public function getActivityNameAttribute()
    {
        return match ($this->subject_type) {
            'NewbridgeWeb\Repositories\Clerks\ClerkClocking' => 'Clerk Clocking ' . $this->description,
            'NewbridgeWeb\Repositories\Customer' => 'Customer Record ' . $this->description,
            'NewbridgeWeb\Repositories\CustomerGroup' => 'Customer Group ' . $this->description,
            'NewbridgeWeb\Repositories\PosButtons' => 'Pos Button ' . $this->description,
            'NewbridgeWeb\Repositories\Products' => 'Product ' . $this->description,
            'NewbridgeWeb\Repositories\Promotions' => 'Promotion ' . $this->description,
            'NewbridgeWeb\Repositories\PromotionItems' => 'Promotion Button ' . $this->description,
            'NewbridgeWeb\Repositories\PromotionBins' => 'Promotion Item Group ' . $this->description,
            '' => 'Unknown ' . $this->description,
            default => 'Unknown ' . $this->description,
        };
    }

    public function getActivityDataNewAttribute()
    {
        return json_decode($this->properties)->attributes;
    }

    public function getActivityDataOldAttribute()
    {
        if (!in_array($this->description, ['created', 'deleted'])) {
            return json_decode($this->properties)->old;
        }

        return null;
    }

    public function getActivityCompanyAttribute()
    {
        return null;
    }

    public function causer()
    {
        return $this->belongsTo('NewbridgeWeb\User', 'causer_id', 'id');
    }
}
