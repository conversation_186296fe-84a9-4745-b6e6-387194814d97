<?php

namespace NewbridgeWeb\Repositories;

class PosTransactionDetailExport extends PosTransactionDetail
{
    protected $table = 'pos_transaction_details';
    protected $appends = ['command_text', 'command_type_text'];
    protected $casts = [];

    public function getCommandTextAttribute()
    {
        return match ((int)$this->command) {
            Commands::PAGE => "Page",
            Commands::PRODUCT_SALE => "Product Sale",
            Commands::MODIFIER_GROUP => "Modifier group",
            Commands::MODIFIER_SALE => "Modifier Sale",
            Commands::CHECK_LEVEL_PROMOTION => "Check Level Promotion",
            Commands::CHECK_LEVEL_DISCOUNT => "Check Level Discount",
            Commands::SIGN_ON => "Sign On",
            Commands::SIGN_OFF => "Sign Off",
            Commands::NO_SALE => "No Sale",
            Commands::EXIT => "Exit",
            Commands::SET_UP => "Set Up",
            Commands::EDIT_DATA => "Edit Data",
            Commands::EDIT_LAYOUT => "Edit Layout",
            Commands::VOID => "Void",
            Commands::CLONE_PAGE => "Clone Page",
            Commands::EDIT_PAGE => "Edit Page",
            Commands::DELETE_PAGE => "Delete Page",
            Commands::CREATE_PAGE => "Create Page",
            Commands::EDIT_MODIFIER_GROUP => "Edit Modifier Group",
            Commands::DONE => "Done",
            Commands::TABLE_STORE => "Table Store",
            Commands::TABLE_RECALL => "Table Recall",
            Commands::CREDIT => "Credit",
            Commands::TABLE_PLAN => "Table Plan",
            Commands::SPLIT_BILL => "Split Bill",
            Commands::TRANSFER_BILL => "Transfer",
            Commands::EDIT_USERS => "Edit Users",
            Commands::CASHBACK => "Cashback",
            Commands::RELOAD_TRANSACTION => "Reload Transaction",
            Commands::SETTINGS => "Settings",
            Commands::PRINT_RECEIPT => "Print Receipt",
            Commands::OPEN_CASH_DRAWER => "Open Cash Drawer",
            Commands::PRINT_KITCHEN_RECEIPT => "Print Kitchen Receipt",
            Commands::CLEAR_CHECK => "Clear Check",
            Commands::SERVICE_CHARGE => "Service Charge",
            Commands::DOWNLOAD_DATA => "Download Data",
            Commands::GIFT_CARD_COMMAND => "Gift Card Command",
            Commands::LOYALTY_CARD => "Loyalty",
            Commands::RESET_CHECK => "Reset Check",
            Commands::STOCK_QUANTITY => "Stock Quantity",
            Commands::REPEAT_SELECTED_ITEMS => "Repeat Selected Items",
            Commands::X_REPORT => "X Report",
            Commands::Z_REPORT => "Z Report",
            Commands::RUN_NEXT_COMMAND => "Run Next Command",
            Commands::RETRIEVE_TRANSACTION => "Retrieve Transaction",
            Commands::CLOCK_ON => "Clock On",
            Commands::CLOCK_OFF => "Clock Off",
            Commands::CHIP_AND_PIN_REQUEST => "Chip And Pin",
            Commands::REFUND => "Refund",
            Commands::GRATUITY => "Gratuity",
            Commands::PDQ_END_OF_DAY => "PDQ End Of Day",
            Commands::PDQ_BANKING => "PDQ Banking",
            Commands::PDQ_X_BALANCE => "PDQ X Balance",
            Commands::PDQ_Z_BALANCE => "PDQ Z Balance",
            Commands::DEBIT => "Debit",
            Commands::ROOM_CHARGE => "Room Charge",
            Commands::EXPENSES => "Expenses",
            Commands::WASTAGE => "Wastage",
            default => "Undefined Command",
        };
    }

    public function getCommandTypeTextAttribute()
    {
        switch((int) $this->command_type) {
            case Commands::TYPE_REFUND:
                return "Refund";
            case Commands::TYPE_VOID:
                return "Void";
            case Commands::TYPE_OTHER_SALES:
            case Commands::TYPE_NORMAL_SALES:
                return "Sales";
            default:
                return "Undefined Command Type";
        }
    }
}
