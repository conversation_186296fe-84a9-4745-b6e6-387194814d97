<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Redis;
use function VeeWee\Xml\Dom\Loader\load;

class RedisRepositoryLists
{
    public static function store($key, $object, $overwrite = false): bool
    {
        if ($overwrite) {
            self::removeFromList($key, $object);
        }

        Redis::connection('cache')->rpush($key, json_encode($object));
        Redis::connection('cache')->expire($key, (60 * 60 * 24 * 5));

        return true;
    }

    public static function has($key, $object): bool
    {
        $items = Redis::connection('cache')->lrange($key, 0, -1);

        foreach ($items as $item) {
            if ($item == json_encode($object)) {
                return true;
            }
        }

        return false;
    }

    public static function hasKey($key): bool
    {
        return Redis::connection('cache')->exists($key);
    }

    public static function get($key): Collection
    {
        $items = Redis::connection('cache')->lrange($key, 0, -1);

        foreach($items as $k => $item) {
            $items[$k] = json_decode($item);
        }

        return collect($items);
    }

    public static function removeFromList($key, $object): bool
    {
        $items = Redis::connection('cache')->lrange($key, 0, -1);

        foreach ($items as $item) {
            if ($item == json_encode($object)) {
                Redis::connection('cache')->lrem($key, 0, $item);
                return true;
            }
        }

        return false;
    }

    public static function retrieve($key, $update_id, $delete = false)
    {
        $data = [];

        $maxCount = config('newbridge.button_update_limit');

        $items = Redis::connection('cache')->lrange($key, 0, $maxCount);

        $length = count($items);

        $trim = Redis::connection('cache')->ltrim($key, $length, -1);

        foreach ($items as $item) {
            $data[] = json_decode($item);
        }

        // populate a collected list with update ID and pop it upon ok

        foreach ($items as $item) {
            Redis::connection('cache')->rpush($update_id.':'.$key, $item);
        }

        Redis::connection('cache')->expire($update_id.':'.$key, (60 * 60 * 24 * 5));

        return $data;
    }

    public static function reQueue($update)
    {
        foreach ($update['keys'] as $key) {
            $items = Redis::connection('cache')->lrange($update['update_id'].':'.$key, 0, -1);
            foreach ($items as $item) {
                Redis::connection('cache')->rpush($key, $item);
            }

            Redis::connection('cache')->expire($key, (60 * 60 * 24 * 5));

            Redis::connection('cache')->del($update['update_id'].':'.$key);
        }

        Redis::connection('cache')->lrem('updatelog', 0, json_encode($update));
    }
}
