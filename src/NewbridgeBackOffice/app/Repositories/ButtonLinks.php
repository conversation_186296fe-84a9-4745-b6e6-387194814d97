<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ButtonLinks extends NewbridgeRepository
{
    use SoftDeletes;
    public string $sourceField = 'id';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'pos_button_link';
    protected $guarded = ['id'];
    protected $appends = ['display_name', 'indexer'];
    public $timestamps = true;

    /**
     * Get the product records associated with the product.
     */
    public function product()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Products', 'guid', 'commandUID');
    }

    /**
     * Get the product records associated with the product.
     */
    public function button()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\PosButtons', 'button_guid', 'guid');
    }

    /**
     * Get the intcommand records associated with the product.
     */
    public function intcommand()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Commands', 'CommandUID', 'commandUID');
    }

    /**
     * Get the payment records associated with the product.
     */
    public function payment()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Payments', 'CommandUID', 'commandUID');
    }

    /**
     * Get the payment records associated with the product.
     */
    public function discount()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Discounts', 'CommandUID', 'commandUID');
    }

    public function getTypeStringAttribute()
    {
        switch($this->command_type) {
            case 0:
                return 'Product';

                break;
            case 1:
                return 'Function';

                break;
            case 2:
                return 'Payment';

                break;
            case 3:
                return 'Discount';

                break;
            default:
                return 'Other';

                break;
        }
    }

    public function getTypeColorAttribute()
    {
        switch($this->command_type) {
            case 0:
                return '#e62e00';

                break;
            case 1:
                return '#e62e00';

                break;
            case 2:
                return '#e62e00';

                break;
            case 3:
                return '#e62e00';

                break;
            default:
                return '#e62e00';

                break;
        }
    }

    public function getDisplayNameAttribute()
    {
        switch($this->command_type) {
            case 0:
                if ($this->product) {
                    return $this->product->displayname;
                } else {
                    return '';
                }

                break;
            case 1:
                if ($this->intcommand) {
                    return $this->intcommand->DisplayName;
                } else {
                    return '';
                }

                break;
            case 2:
                if ($this->payment) {
                    return $this->payment->DisplayName;
                } else {
                    return '';
                }

                break;
            case 3:
                if ($this->discount) {
                    return $this->discount->displayname;
                } else {
                    return '';
                }

                break;
            default:
                return 'Unknown';

                break;
        }
    }

    public function getIdAttribute()
    {
        return 0;
    }

    /**
     * @return mixed
     */
    public function getIndexerAttribute()
    {
        return $this->position == null ? 0 : $this->position;
    }
}
