<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Support\Facades\Redis;

class RedisRepository
{
    public $maxElemnents = 1500;

    public static function getByKey($key)
    {
        return json_decode(Redis::connection('cache')->get($key), true);
    }

    public static function delete($key): void
    {
        Redis::connection('cache')->del($key);
    }

    public static function increment($key): void
    {
        Redis::connection('cache')->incr($key);
    }

    public static function count($key)
    {
        $redisScan = new RedisScan();

        $keys = $redisScan->scanAllForMatch($key);

        return count($keys);
    }

    public static function store(string $key, mixed $object, int $hours = 120): bool
    {
        Redis::connection('cache')->set($key, json_encode($object));
        Redis::connection('cache')->expire($key, ($hours * 60 * 60));

        return true;
    }

    public static function checkExists($key)
    {
        return Redis::connection('cache')->exists($key);
    }

    public static function replace($key, $object)
    {
        Redis::connection('cache')->del($key);

        self::store($key, $object);
        Redis::connection('cache')->expire($key, (60 * 60 * 24 * 5));
    }

    public static function retrieve($key, $update_id, $delete = false)
    {
        $redisScan = new RedisScan();

        $keys = $redisScan->scanAllForMatch($key.'*');

        $data = [];

        $maxCount = config('newbridge.button_update_limit');
        $currentCount = 0;

        foreach ($keys as $k) {
            if ($currentCount <= $maxCount) {
                $value = json_decode(Redis::connection('cache')->get($k));

                $data[] = $value;

                $newKey = $update_id . ':' . $k;
                Redis::connection('cache')->del($k);

                if ($delete == false) {
                    Redis::connection('cache')->set($newKey, json_encode($value));
                    Redis::connection('cache')->expire($newKey, (60 * 60 * 24 * 5));
                }

                $currentCount++;
            }
        }

        return $data;
    }
}
