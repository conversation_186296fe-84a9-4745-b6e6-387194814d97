<?php

namespace NewbridgeWeb\Repositories;

use Auth;
use <PERSON><PERSON><PERSON>da\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Commands extends NewbridgeModel
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    use LogsActivity;

    public string $sourceField = 'CommandUID';
    protected $table = 'sys_int_commands';
    protected $dates = ['deleted_at'];
    public $guarded = ['id'];
    protected $cascadeDeletes = ['buttons', 'links', 'group_links'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
    public const PAGE = 1;
    public const PRODUCT_SALE = 3;
    public const MODIFIER_GROUP = 6;
    public const MODIFIER_SALE = 7;
    public const CHECK_LEVEL_PROMOTION = 9;
    public const CHECK_LEVEL_DISCOUNT = 10;
    public const SIGN_ON = 14;
    public const SIGN_OFF = 15;
    public const NO_SALE = 16;
    public const EXIT = 17;
    public const SET_UP = 18;
    public const EDIT_DATA = 19;
    public const EDIT_LAYOUT = 20;
    public const VOID = 21;
    public const CLONE_PAGE = 22;
    public const EDIT_PAGE = 23;
    public const DELETE_PAGE = 24;
    public const CREATE_PAGE = 25;
    public const EDIT_MODIFIER_GROUP = 26;
    public const DONE = 27;
    public const TABLE_STORE = 29;
    public const TABLE_RECALL = 30;
    public const CREDIT = 31;
    public const TABLE_PLAN = 32;
    public const SPLIT_BILL = 33;
    public const TRANSFER_BILL = 34;
    public const EDIT_USERS = 35;
    public const CASHBACK = 37;
    public const RELOAD_TRANSACTION = 38;
    public const SETTINGS = 39;
    public const PRINT_RECEIPT = 40;
    public const OPEN_CASH_DRAWER = 41;
    public const PRINT_KITCHEN_RECEIPT = 42;
    public const CLEAR_CHECK = 43;
    public const SERVICE_CHARGE = 44;
    public const DOWNLOAD_DATA = 45;
    public const GIFT_CARD_COMMAND = 46;
    public const LOYALTY_CARD = 47;
    public const RESET_CHECK = 48;
    public const STOCK_QUANTITY = 50;
    public const REPEAT_SELECTED_ITEMS = 51;
    public const X_REPORT = 53;
    public const Z_REPORT = 54;
    public const RUN_NEXT_COMMAND = 55;
    public const RETRIEVE_TRANSACTION = 56;
    public const CLOCK_ON = 57;
    public const CLOCK_OFF = 58;
    public const CHIP_AND_PIN_REQUEST = 59;
    public const REFUND = 60;
    public const GRATUITY = 61;
    public const PDQ_END_OF_DAY = 62;
    public const PDQ_BANKING = 63;
    public const PDQ_X_BALANCE = 64;
    public const PDQ_Z_BALANCE = 65;
    public const DEBIT = 66;
    public const ROOM_CHARGE = 67;
    public const EXPENSES = 88;
    public const WASTAGE = 107;
    public const CUSTOMTEXT = 69;

    ///COMMAND TYPES

    public const TYPE_REFUND = 6;
    public const TYPE_VOID = 5;
    public const TYPE_NORMAL_SALES = 0;
    public const TYPE_OTHER_SALES = 1;

    /**
     * @return string
     */
    public static function getAllCompanyJson($types = [])
    {
        $company_id = Auth::user()->company_id;
        $results = self::select('id', 'DisplayName')->where('company_id', $company_id);

        if (!empty($types)) {
            $results->whereIn('Command', $types);
        }

        $results = $results->get();

        $result = [];
        $result['Please select one...'] = '';
        foreach ($results as $k => $v) {
            $result[$v['DisplayName']] = $v['id'];
        }

        return json_encode($result);
    }

    /**
     * @return string
     */
    public static function getAllCompanyJsonGuid($types = [])
    {
        $company_id = Auth::user()->company_id;
        $results = self::select('CommandUID', 'DisplayName')->where('company_id', $company_id)->where('RestrictedCommand', 0);

        if (!empty($types)) {
            $results->whereIn('Command', $types);
        }

        $results = $results->get();

        $result = [];
        $result['Please select one...'] = '';
        foreach ($results as $k => $v) {
            $result[$v['DisplayName']] = $v['CommandUID'];
        }

        return json_encode($result);
    }

    /**
     * Get the product records associated with the product.
     */
    public function buttons()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosButtons', 'plu_product_page_guid', 'CommandUID');
    }

    public function links()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosButtonLink', 'commandUID', 'CommandUID');
    }

    public function group_links()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\LinkModifierGroups', 'CommandUID', 'CommandUID');
    }
}
