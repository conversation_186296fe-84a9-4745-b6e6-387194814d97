<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Giftcards extends NewbridgeRepository
{
    use SoftDeletes;
    public string $sourceField = 'id';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'cus_customer';
    protected $guarded = ['id'];
    public $timestamps = true;
    protected $appends = ['balance'];


    public function getBalanceAttribute()
    {
        $balance = CustomerTransactions::where('customer_guid', $this->guid)->sum('value');

        return $balance;
    }
}
