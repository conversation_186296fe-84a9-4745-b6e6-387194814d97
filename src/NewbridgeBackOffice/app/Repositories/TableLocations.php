<?php

namespace NewbridgeWeb\Repositories;

use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;

class TableLocations extends NewbridgeRepository
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    use LogsActivity;
    
    protected $table = 'site_table_plans';
    protected $dates = ['deleted_at'];
    public $guarded = ['id'];
    protected $cascadeDeletes = ['tables'];
    public string $sourceField = 'guid';

    /**
     * Get the transaction record associated with the stock summaries.
     */
    public function tables()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\Tables', 'plan_guid', 'guid');
    }
}
