<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\TerminalSettingUpdatedVersion;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PosSettings extends NewbridgeRepository
{
    use SoftDeletes;
    use LogsActivity;
    
    protected $table = 'pos_settings';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    public string $sourceField = 'setting_id';

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $departments = self::select('label', 'setting_id')->where('company_id', $company_id)->get();

        $result = [];
        $result['Please select one...'] = '';
        foreach ($departments as $k => $v) {
            $result[$v['label']] = $v['setting_id'];
        }

        return json_encode($result);
    }

    /**
     * Get all Departments for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompany()
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::exists()->where('company_id', $user->company_id)->get();

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get all departments for the current company.
     *
     */
    public static function forCurrentCompanyDataTables()
    {
        $user = session('user');

        if ($user->company_id) {
            return self::exists()->where('company_id', $user->company_id);
        }

        return null;
    }

    public static function forCurrentCompanyById($id)
    {
        $user = session('user');

        if ($user->company_id) {
            return self::exists()->where('company_id', $user->company_id)->where('id', $id);
        }

        return null;
    }

    public function getIsreadonlyAttribute($value)
    {
        return $value == 1;
    }

    public function getIshiddenAttribute($value)
    {
        return $value == 1;
    }

    /**
     * Get the product records associated with the product.
     */
    public function header()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\PosSettingHeader', 'category', 'category');
    }
}
