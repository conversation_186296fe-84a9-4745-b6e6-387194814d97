<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentTypes extends NewbridgeRepository
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sys_payment_types';
    protected $guarded = ['id'];
    public $timestamps = true;
    public string $sourceField = 'id';

    public function xerolinks()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\CompanyAccountsPaymentLinks', 'payment_method', 'id');
    }
}
