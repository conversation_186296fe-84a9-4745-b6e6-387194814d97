<?php

namespace NewbridgeWeb\Repositories;

use Auth;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Permission\Models\Role;

class UserRole extends Role
{
    use SoftDeletes;
    
    public $guarded = ['id'];
    public string $sourceField = 'id';

    protected static function booted()
    {
        static::addGlobalScope('roleRestriction', function (Builder $query) {
            if (!Auth::user()->hasRole('super-admin')) {
                $query->whereNot('name', 'super-admin');
            }
        });
    }
}
