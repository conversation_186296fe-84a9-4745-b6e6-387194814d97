<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PosButtonStyle extends NewbridgeRepository
{
    use SoftDeletes;
    

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'pos_button_style';
    protected $guarded = ['id'];
    public $timestamps = true;
    public string $sourceField = 'guid';

    /**
     * Get the site records associated with the product.
     */
    public function button()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\PosButtons', 'pos_button_style_guid', 'guid');
    }
}
