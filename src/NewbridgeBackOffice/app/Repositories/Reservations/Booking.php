<?php

namespace NewbridgeWeb\Repositories\Reservations;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\NewbridgeRepository;

class Booking extends NewbridgeRepository
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'res_room_booking';
    protected $guarded = ['id'];
    public $timestamps = true;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\Customer', 'customer_id', 'id');
    }
}
