<?php

namespace NewbridgeWeb\Repositories\Reservations;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\NewbridgeRepository;

class Asset extends NewbridgeRepository
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'res_room_asset';
    protected $guarded = ['id'];
    public $timestamps = true;

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function assetGroup()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\Reservations\AssetGroups', 'asset_group', 'id');
    }
}
