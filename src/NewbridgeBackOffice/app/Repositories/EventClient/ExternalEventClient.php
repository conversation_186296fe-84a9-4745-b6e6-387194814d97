<?php

namespace NewbridgeWeb\Repositories\EventClient;

use GuzzleHttp\Client;
use Ramsey\Uuid\Uuid;

class ExternalEventClient
{
    public string $apiKey;
    public string $baseUrl;

    public function __construct(array $attributes = [])
    {
        $this->apiKey = config('newbridge.event_grid_key');
        $this->baseUrl = config('newbridge.event_grid_url').'?api-version=2018-01-01';
    }

    /**
     * @param string $type
     * @param string $subject
     * @param string $topic
     * @param array $data
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function send(string $type, string $subject, string $topic, array $data)
    {
        $client = new Client();
        $url = $this->baseUrl . '&topic=' . urlencode($topic);
        $response = $client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
                'aeg-sas-key' => $this->apiKey,
                'ce-id' => Uuid::uuid4()->toString(),
                'ce-type' => $type,
                'ce-source' => config('newbridge.event_grid_source'),
                'ce-specversion' => '1.0',
                'ce-datacontenttype' => 'application/json',
                'ce-subject' => $subject
            ],
            'body' => json_encode($data)
        ]);

        return $response;
    }
}
