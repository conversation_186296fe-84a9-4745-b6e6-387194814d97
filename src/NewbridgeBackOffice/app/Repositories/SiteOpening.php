<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;

class SiteOpening extends NewbridgeRepository
{
    use SoftDeletes;
    
    protected $table = 'sys_sites_opening';
    protected $dates = ['deleted_at'];
    public $guarded = ['id'];
    public string $sourceField = 'id';

    /**
     * Get the clerk records associated with the product.
     */
    public function times()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\SiteOpeningTimes', 'opening_id', 'id');
    }
}
