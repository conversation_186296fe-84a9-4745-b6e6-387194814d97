<?php

namespace NewbridgeWeb\Repositories;

use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PosButtons extends NewbridgeRepository
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    use LogsActivity;
    

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'pos_button';
    protected $guarded = ['id'];
    public $timestamps = true;
    protected $cascadeDeletes = ['style', 'links', 'lnk_buttonlinks'];
    public string $sourceField = 'guid';

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * Get the site records associated with the product.
     */
    public function style()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\PosButtonStyle', 'guid', 'pos_button_style_guid');
    }

    /**
     * Get the site records associated with the product.
     */
    public function pos_button_style()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\PosButtonStyle', 'guid', 'pos_button_style_guid');
    }

    /**
     * Get the site records associated with the product.
     */
    public function links()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\PosButtonLink', 'button_guid', 'guid')->where('type', 0)->orderBy('position', 'ASC');
    }

    /**
     * Get the site records associated with the product.
     */
    public function lnk_buttonlinks()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\ButtonLinks', 'button_guid', 'guid')->orderBy('position', 'ASC');
    }

    /**
     * Get the site records associated with the product.
     */
    public function plu_product_page()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\Commands', 'plu_product_page_guid', 'CommandUID');
    }
}
