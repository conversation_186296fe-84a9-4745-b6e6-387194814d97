<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Export extends Model
{
    use SoftDeletes;
    protected $table = 'exports';
    protected $guarded = ['id'];
    public $timestamps = true;
    protected $appends = ['exportable_name'];

    /** Status **/
    public const FAILED = 0;
    public const IN_PROGRESS = 1;
    public const COMPLETE = 2;

    /** Class **/
    public const POS_TRANSACTION_DETAIL_EXPORT = 1;
    public const POS_TRANSACTION_DETAIL_PAYMENTS_EXPORT = 2;

    public function findByGUID($guid)
    {
        return $this->where('guid', '=', $guid)->first();
    }

    public static function getExportById(int $id)
    {
        switch ($id) {
            case self::POS_TRANSACTION_DETAIL_EXPORT :
                return "NewbridgeWeb\Exports\TransactionDetailsExport";
            case self::POS_TRANSACTION_DETAIL_PAYMENTS_EXPORT:
                return "NewbridgeWeb\Exports\TransactionDetailPaymentsExport";
            default:
                return "Class not found";
        }
    }

    public function getExportableNameAttribute()
    {
        switch($this->exportable_class_id) {
            case self::POS_TRANSACTION_DETAIL_EXPORT :
                return "Transaction Details Export";
            case self::POS_TRANSACTION_DETAIL_PAYMENTS_EXPORT:
                return "Transaction Detail Payments Export";
            default:
                return "Class not found";
        }
    }
}
