<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ReceiptTypes extends NewbridgeRepository
{
    use SoftDeletes;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'pos_receipt_types';
    protected $guarded = ['id'];
    public $timestamps = true;
    public string $sourceField = 'id';

    /**
     * get receipt lines
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function receipt_layout()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\ReceiptLayouts', 'receipt_type_id', 'id');
    }
}
