<?php

namespace NewbridgeWeb\Repositories;

use Auth;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use NewbridgeWeb\Http\Controllers\Company\TerminalController;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use Spatie\Activitylog\Traits\CausesActivity;

class Pos extends NewbridgeRepository
{
    use SoftDeletes;
    use CausesActivity;
    use HasFactory;

    protected $table = 'sys_pos';
    protected $guarded = ['id'];
    public $timestamps = true;
    protected $dates = ['deleted_at'];
    public string $sourceField = 'id';

    public function site()
    {
        return $this->belongsTo(Sites::class, 'site_num', 'site_num');
    }

    public function company()
    {
        return $this->belongsTo(Company::class,'company_id', 'id');
    }

    public function settings()
    {
        return $this->hasMany(PosSettings::class, 'terminal_num', 'terminal_num');
    }

    public function getLastPingDatetimeAttribute()
    {
        $id = $this->id;

        $exists = Redis::connection('cache')->exists('terminal:' . $id);
        if ($exists) {
            $value = json_decode(Redis::connection('cache')->get('terminal:' . $id));
            $date = Carbon::parse($value->datetime, 'UTC')->setTimezone(TimezoneHelper::getTimezone());

            return $date->format('d/m/Y H:i');
        }

        return 'Too long';
    }

    /**
     * Register device first connection to the service, protected by an API secret key for the
     * company/site
     */
    public static function registerFirstConnection($data)
    {
        $data = [
            "guid" => $data['guid'],
            "site_id" => $data['site_id'],
            "site_num" => $data['site_num'],
            "terminal_id" => $data['terminal_num'],
            "company_id" => $data['company_id'],
            "last_ping" => Carbon::now(),
            "ip_address" => $data['ip_address']
        ];

        $exists = self::where('guid', $data['guid'])->count();

        if ($exists < 1) {
            self::create($data);

            return true;
        }

        return false;
    }

    public function getTerminalVersionAttribute()
    {
        $current_version = cache()->remember(
            'pos-version-' . $this->company_id . '-' . $this->site_num . '-' . $this->terminal_num,
            Carbon::now()->addMinutes(5),
            function () {
                return PosSettings::where('company_id', $this->company_id)
                    ->where('site_num', $this->site_num)
                    ->where('terminal_num', $this->terminal_num)
                    ->where('setting_id', 'ApplicationVersion')
                    ->first();
            }
        );
        if ($current_version) {
            return $current_version['value1'];
        }

        return 'Unknown';
    }

    /**
     * create a unique access key to be returned
     */
    public static function uniqueAccessKey()
    {
        $exists = true;
        $rand = null;

        while ($exists === true) {
            $rand = Str::random(12);
            $exists = Pos::checkExists($rand);
        }

        return $rand;
    }

    /**
     * Check that a company exists with the terminal access key used
     *
     * @param $rand
     * @return bool
     */
    public static function checkExists($rand)
    {
        $exists = Company::where('terminal_access_key', $rand)->count();

        if ($exists > 0) {
            return true;
        }

        return false;
    }

    public static function checkAuthorised($auth, $ip_address)
    {
        // fast authorised check
        $flatAuth = 'ApiAuth::' . base64_encode(implode(':', $auth));

        if (Redis::connection('cache')->exists($flatAuth)) {
            $result = json_decode(Redis::connection('cache')->get($flatAuth));

            self::updateTerminalRedis($result->terminal_id, $ip_address);

            Redis::connection('cache')->expire($flatAuth, 3600);

            return ['status' => 'ok'];
        }

        /**
         * Long auth check if not in redis
         */
        $authorised = cache()->remember($auth[0] . ':' . $auth[2], now()->addMinutes(60), function () use ($auth) {
            return Company::with([
                'sites' => function ($q) use ($auth) {
                    $q->where('site_num', $auth[2]);
                }
            ])->where('terminal_access_key', $auth[0])->first();
        });

        if ($authorised == null) {
            return [
                'status' => 'error',
                'message' => 'Invalid API Key - Please check the entry and try again',
                'reason_code' => 0
            ];
        }

        if (count($authorised->sites) == 0) {
            return [
                'status' => 'error',
                'message' => 'Invalid Site Number - Please make sure this site has been created in back office',
                'reason_code' => 1
            ];
        }

        if ($authorised && count($authorised->sites) > 0) {
            // lets find out if the terminal has already been registered
            $terminal = cache()->remember(
                'pos-' . $authorised->id . '-' . $auth[2] . '-' . $auth[1],
                now()->addMinutes(60),
                function () use ($auth, $authorised) {
                    return Pos::where('company_id', $authorised->id)->where('site_num', $auth[2])->where(
                        'terminal_num',
                        $auth[1]
                    )->limit(1)->first();
                }
            );

            // register if not registered already
            if ($terminal === null) {
                return [
                    'status' => 'error',
                    'message' => 'Terminal Unlicensed - Please Contact Newbridge Software',
                    'reason_code' => 2
                ];
            } else {
                self::updateTerminalRedis($terminal->id, $ip_address);
            }

            Redis::connection('cache')->set(
                $flatAuth,
                json_encode(['company_id' => $authorised->id, 'terminal_id' => $terminal->id])
            );
            Redis::connection('cache')->expire($flatAuth, 3600);

            return ['status' => 'ok', 'reason_code' => -1];
        } else {
            return [
                'status' => 'error',
                'message' => 'Site not found - please check the back office and try again.',
                'reason_code' => 1
            ];
        }
    }

    private static function updateTerminalRedis($terminal_id, $ip_address)
    {
        $now = Carbon::now()->toDateTimeString();
        Redis::connection('cache')->set(
            'terminal:' . $terminal_id,
            json_encode(['datetime' => $now, 'ip' => $ip_address])
        );
    }
}
