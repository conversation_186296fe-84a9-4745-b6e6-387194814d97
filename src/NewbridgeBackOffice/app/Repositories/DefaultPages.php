<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;

class DefaultPages extends NewbridgeRepository
{
    use SoftDeletes;
    protected $table = 'default_pages';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];

    /**
     * Get the site records associated with the product.
     */
    public function buttons()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\DefaultPosButtons', 'plu_product_page_guid', 'CommandUID');
    }
}
