<?php

namespace NewbridgeWeb\Repositories\Integrations;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\NewbridgeRepository;

class Integrations extends NewbridgeRepository
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sys_integrations';
    protected $guarded = ['id'];
    public $timestamps = true;
    public string $sourceField = 'id';

    /**
     * Get the product records associated with the product.
     */
    public function settings()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\Integrations\IntegrationSettings', 'integration_id', 'id');
    }

    /**
     * Get the product records associated with the product.
     */
    public function default_settings()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\Integrations\IntegrationDefaultSettings', 'integration_id', 'id');
    }
}
