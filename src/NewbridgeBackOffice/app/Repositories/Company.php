<?php

namespace NewbridgeWeb\Repositories;

use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\OAuth\CompanyOAuth;
use NewbridgeWeb\User;
use Spatie\Activitylog\Traits\LogsActivity;
use NewbridgeWeb\Http\Traits\SendsToServiceBus;

class Company extends NewbridgeRepository
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    use LogsActivity;
    use HasFactory;
    use SendsToServiceBus;

    protected $table = 'sys_company';
    protected $guarded = ['id'];
    protected array $cascadeDeletes = ['sites', 'users', 'pos', 'departments', 'subdepartments', 'suppliers', 'skus'];
    protected array $dates = ['deleted_at'];
    public $timestamps = true;
    public string $sourceField = 'id';

    public function sites(): HasMany
    {
        return $this->hasMany(Sites::class, 'company_id', 'id');
    }

    public function token(): HasOne
    {
        return $this->hasOne(CompanyJWT::class, 'company_id', 'id');
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'company_id', 'id');
    }

    public function departments(): HasMany
    {
        return $this->hasMany(Departments::class, 'company_id', 'id');
    }

    public function reseller(): BelongsTo
    {
        return $this->belongsTo(self::class, 'reseller_id', 'id');
    }

    public function subdepartments(): HasMany
    {
        return $this->hasMany(SubDepartment::class, 'company_id', 'id');
    }

    public function suppliers(): HasMany
    {
        return $this->hasMany(Suppliers::class, 'company_id', 'id');
    }

    public function skus(): HasMany
    {
        return $this->hasMany(SKU::class, 'company_id', 'id');
    }

    public function pos(): HasMany
    {
        return $this->hasMany(Pos::class, 'company_id', 'id');
    }

    public static function create($attributes = [])
    {
        $data = [
            "name" => '',
            "address_line1" => '',
            "address_line2" => '',
            "address_line3" => '',
            "county" => '',
            "city" => '',
            "postcode" => '',
            "access_key" => Pos::uniqueAccessKey()
        ];
    }

    public function getAuthToken($provider, $site_num = 1)
    {
        $token = CompanyOAuth::where('company_id', $this->id)->where('site_num', $site_num)->where('provider', $provider)->first();

        if ($token == null) {
            return false;
        }

        return $token;
    }

    public function getSitesTextAttribute()
    {
        $sites = Sites::where('company_id', $this->id)->get();

        $sitesText = '';

        foreach ($sites as $site) {
            $sitesText .= $site->site_num . ': '.$site->site_name.' | ';
        }

        return $sitesText;
    }
}
