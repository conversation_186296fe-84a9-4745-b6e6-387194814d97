<?php

namespace NewbridgeWeb\Repositories\Clerks;

use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\NewbridgeModel;
use NewbridgeWeb\Repositories\NewbridgeRepository;

class ShiftTypes extends NewbridgeModel
{
    use SoftDeletes;
    
    protected $table = 'sys_shift_types';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    public string $sourceField = 'id';
}
