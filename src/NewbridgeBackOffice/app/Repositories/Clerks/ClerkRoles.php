<?php

namespace NewbridgeWeb\Repositories\Clerks;

use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\NewbridgeRepository;
use Spatie\Activitylog\Traits\LogsActivity;

class ClerkRoles extends NewbridgeRepository
{
    use SoftDeletes;
    use LogsActivity;
    
    protected $table = 'sys_clerk_job_roles';
    protected $dates = ['deleted_at'];
    public $guarded = ['id'];
    public string $sourceField = 'id';
}
