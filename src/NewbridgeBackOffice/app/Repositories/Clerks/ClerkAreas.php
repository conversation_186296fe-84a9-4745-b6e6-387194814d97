<?php

namespace NewbridgeWeb\Repositories\Clerks;

use Auth;
use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\NewbridgeRepository;
use Spatie\Activitylog\Traits\LogsActivity;

class ClerkAreas extends NewbridgeRepository
{
    use SoftDeletes;
    use LogsActivity;
    
    protected $table = 'sys_clerk_areas';
    protected $dates = ['deleted_at'];
    public $guarded = ['id'];
    public string $sourceField = 'guid';

    /**
     * @return string
     */
    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $results = self::select('id', 'area_name')->where('company_id', $company_id)->get();

        $result = [];
        $result['Please select one...'] = '';

        foreach ($results as $k => $v) {
            $result[$v['area_name']] = $v['guid'];
        }

        return json_encode($result);
    }

    /**
     * Get all Departments for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompany()
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::where('company_id', $user->company_id)->get();

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get all departments for the current company.
     *
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompanyDataTables()
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::where('company_id', $user->company_id);

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get all departments for the current company, by ID
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse|null
     */
    public static function forCurrentCompanyById($id)
    {
        $user = session('user');

        if ($user->company_id) {
            $data = self::exists()->where('company_id', $user->company_id)->where('id', $id);

            return $data;
        } else {
            return null;
        }
    }

    /**
     * Get the clerk records associated with the product.
     */
    public function clerks()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\Clerks\Clerks', 'area_guid', 'guid');
    }
}
