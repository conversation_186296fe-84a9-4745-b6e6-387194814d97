<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;

class SiteOpeningTimes extends NewbridgeRepository
{
    use SoftDeletes;
    
    protected $table = 'sys_sites_opening_times';
    protected $dates = ['deleted_at'];
    public $guarded = ['id'];
    public string $sourceField = 'id';

    /**
     * Get the clerk records associated with the product.
     */
    public function opening()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\SiteOpening', 'opening_id', 'id');
    }
}
