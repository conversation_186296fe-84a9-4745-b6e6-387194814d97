<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyAccountsPaymentLinks extends NewbridgeRepository
{
    use SoftDeletes;
    
    /**
     * The table associated with the model.
     *
     * @var string
     */
    public string $sourceField = 'id';
    protected $table = 'sys_company_accounts_payment_links';
    protected $guarded = ['id'];
    public $timestamps = true;
}
