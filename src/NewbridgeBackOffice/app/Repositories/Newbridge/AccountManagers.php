<?php

namespace NewbridgeWeb\Repositories\Newbridge;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use NewbridgeWeb\Repositories\NewbridgeRepository;

class AccountManagers extends NewbridgeRepository
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sys_acc_managers';
    protected $guarded = ['id'];
    public $timestamps = true;
}
