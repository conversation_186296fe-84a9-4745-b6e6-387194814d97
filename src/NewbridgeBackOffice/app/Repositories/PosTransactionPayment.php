<?php

namespace NewbridgeWeb\Repositories;

use Illuminate\Database\Eloquent\SoftDeletes;
class PosTransactionPayment extends NewbridgeRepository
{
    use SoftDeletes;
    protected $table = 'pos_transaction_payment_details';
    protected $guarded = ['id'];
    public $timestamps = true;
    public string $sourceField = 'id';
    protected $casts = [
        'amount' => 'float',
        'id' => 'int',
        'trans_id' => 'int'
    ];

    public function transaction()
    {
        return $this->belongsTo('NewbridgeWeb\Repositories\PosTransaction', 'trans_id', 'id');
    }

    public function method()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\Payments', 'CommandUID', 'method_guid');
    }

    public function type()
    {
        return $this->hasOne('NewbridgeWeb\Repositories\PaymentTypes', 'id', 'method_type');
    }

    public function getMethodTypeTextAttribute()
    {
        switch ($this->method_type) {
            case 0:
                return "Card";
            case 1:
                return "Cash";
            case 2:
                return "Room Charge";
            case 3:
                return "Other";
            case 4:
                return "No Sale";
            case 5:
                return "Gift Card";
            case 6:
                return "Loyalty";
            case 7:
                return "Expenses";
            default:
                return "Undefined Payment Type";
        }
    }
}
