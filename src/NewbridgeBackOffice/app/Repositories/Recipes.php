<?php

namespace NewbridgeWeb\Repositories;

use Dyrynda\Database\Support\CascadeSoftDeletes;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Traits\LogsActivity;

class Recipes extends NewbridgeRepository
{
    use SoftDeletes;
    use CascadeSoftDeletes;
    use LogsActivity;
    
    protected $table = 'plu_recipes';
    protected $dates = ['deleted_at'];
    protected $guarded = ['id'];
    protected $cascadeDeletes = ['ingredients'];
    public string $sourceField = 'id';

    /**
     * @return string
     */
    public static function getAllCompanyJson()
    {
        $company_id = Auth::user()->company_id;
        $departments = self::select('guid', 'name')->where('company_id', $company_id)->get();

        $result = [];
        $result['Please select one...'] = '';
        foreach ($departments as $k => $v) {
            $result[$v['name']] = $v['guid'];
        }

        return json_encode($result);
    }

    public function ingredients()
    {
        return $this->hasMany('NewbridgeWeb\Repositories\RecipeLinks', 'recipe_guid', 'guid');
    }
}
