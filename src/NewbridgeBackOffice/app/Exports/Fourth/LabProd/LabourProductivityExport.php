<?php

namespace NewbridgeWeb\Exports\Fourth\LabProd;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Clerks\Clocking;
use NewbridgeWeb\Repositories\Sites;

class LabourProductivityExport
{
    //    private Sites $site;
    private int $company_id;
    private int $site_num;
    private string $groupGUID;
    private string $location;
    private Carbon $start;
    private Carbon $end;
    private Carbon $now;
    private \XMLWriter $xmlWriter;
    private array $clerks;
    private Collection $clocking;

    public function __construct(array $export_data)
    {
        $this->company_id = $export_data['company_id'];
        $this->site_num = $export_data['site_num'];
        $this->groupGUID = $export_data['group_guid'];
        $this->location = $export_data['location'];
        $this->start = $export_data['start'];
        $this->end = $export_data['end'];
        $this->now = $export_data['now'];
    }

    private function open()
    {
        $this->xmlWriter = new \XMLWriter();
        $this->xmlWriter->openMemory();
        $this->xmlWriter->setIndent(2);
        $this->xmlWriter->startDocument("1.0");
        $this->xmlWriter->startElement("Root");
        $this->xmlWriter->startAttribute('GroupGUID');
        $this->xmlWriter->text($this->groupGUID);
        $this->xmlWriter->endAttribute();
        $this->xmlWriter->startAttribute('DateTime');
        $this->xmlWriter->text($this->now->toDateTimeLocalString());
        $this->xmlWriter->endAttribute();
    }

    private function close()
    {
        $this->xmlWriter->endElement();
        $this->xmlWriter->endDocument();
    }

    private function appendRecord(Clocking $clock, int $clockStatus)
    {
        $this->xmlWriter->startElement('Record');
        $this->xmlWriter->startElement('EmpNo');
        $this->xmlWriter->text($this->clerks[$clock->employee_guid]['employee_no']);
        $this->xmlWriter->endElement();

        $this->xmlWriter->startElement('Location');
        $this->xmlWriter->text($this->location);
        $this->xmlWriter->endElement();

        $this->xmlWriter->startElement('ClockStatus');
        //        Clock Status 1 - Clock in (Check IN)
        //        Clock Status 0 - Clock Out (Check OUT)
        //        Clock Status 2 - Break Start (Check OUT)
        //        Clock Status 3 - Break End (Check IN)
        $this->xmlWriter->text($clockStatus);
        $this->xmlWriter->endElement();

        $this->xmlWriter->startElement('CheckIn');
        if (1 == $clockStatus) {
            $this->xmlWriter->text(Carbon::parse($clock->in)->toDateTimeLocalString());
        }
        $this->xmlWriter->endElement();

        $this->xmlWriter->startElement('CheckOut');
        if (0 == $clockStatus) {
            $this->xmlWriter->text(Carbon::parse($clock->out)->toDateTimeLocalString());
        }
        $this->xmlWriter->endElement();

        $this->xmlWriter->endElement();
    }

    private function load()
    {
        $this->clerks = Clerks::query()
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num, 0])
            ->get()
            ->keyBy('guid')
            ->toArray();

        $this->clocking = Clocking::query()
            ->whereBetween('in', [$this->start, $this->end])
            ->whereHas('Clerk', function (Builder $query) {
                $query->where('company_id', $this->company_id)
                    ->whereIn('site_num', [$this->site_num, 0]);
            })->get();
    }

    public function run()
    {
        $this->load();
        $this->open();

        foreach ($this->clocking as $clock) {
            if ($clock->in !== null) {
                $this->appendRecord($clock, 1);
            }
            if ($clock->out !== null) {
                $this->appendRecord($clock, 0);
            }
        }

        $this->close();

        return $this->xmlWriter->outputMemory();
    }
}
