<?php

namespace NewbridgeWeb\Exports\ProcureWizard;

use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use NewbridgeWeb\Http\Helpers\IntegrationsLoggingHelper;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\SubDepartment;
use NewbridgeWeb\Repositories\VoidReasons;

class AdvancedStockCostSalesExport implements FromArray, WithHeadings
{
    private int $company_id;
    private int $site_num;
    private int $site_id;
    private Carbon $start;
    private Carbon $end;
    private array $departments;
    private array $subDepartments;
    private array $productInfo;

    private IntegrationsLoggingHelper $logger;

    public function __construct($export_data, IntegrationsLoggingHelper $logger = null, int $site_id)
    {
        $this->company_id = $export_data['company_id'];
        $this->site_num = $export_data['site_num'];
        $this->start = $export_data['start'];
        $this->end = $export_data['end'];
        $this->site_id = $site_id;

        if (!empty($logger)) {
            $this->logger = $logger;
        }
    }

    public function array(): array
    {
        $data = [];

        $transactions = PosTransaction::where('company_id', $this->company_id)
            ->where('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with(['details'])->get();

        if ($transactions->count() == 0) {
            if (!empty($this->logger)) {
                $this->logger->setDescription('AdvancedStockCostSalesExport no transactions found');
                $this->logger->write();
            }

            return $data;
        }

        $this->departments = Departments::where('company_id', $this->company_id)->withTrashed()->get()->keyBy('guid')->toArray();
        $this->subDepartments = SubDepartment::where('company_id', $this->company_id)->withTrashed()->get()->keyBy('guid')->toArray();

        $this->productInfo = Products::whereCompanyId($this->company_id)->whereIn('site_num', [0, $this->site_num])
            ->select('guid', 'id', 'third_party_id')->withTrashed()->get()->keyBy('guid')->toArray();

        foreach ($transactions as $transaction) {
            /**
             * Commands
             * 3 = Sales Item
             * 44 = Service Charge
             */
            /**
             * Command Types
             * 6 = Refund
             * 5 = Void
             * 0/1 = Sales Item so not one of the above
             */
            foreach ($transaction->details as $detail) {
                if ($detail->command == 3 && in_array($detail->command_type, [0, 1])) {
                    $data[] = $this->addTransactionItem($detail);
                }
                if ($detail->command == 3 && $detail->command_type == 5) {
                    try {
                        $reason = $detail->reason()->first();
                        if ($reason->stock) {
                            $data[] = $this->addVoidItem($detail, $reason);
                        }
                    } catch (\Exception $e) {
                        if (!empty($this->logger)) {
                            $this->logger->setException($e);
                            $this->logger->setDescription(sprintf('AdvancedStockCostSalesExport missing void reason %s for transaction %d', $detail->void_reason_guid, $detail->trans_id));
                            $this->logger->write();
                        }
                    }
                }
            }
        }

        return $data;
    }

    private function addTransactionItem(PosTransactionDetail $detail)
    {
        $array = $this->addBlankRow();

        $array['store_id'] = $this->site_id;
        $array['revenue_category'] = $this->findDepartment($detail->department_guid)['acc_code'];
        $array['till_number'] = $detail->terminal_num;
        $array['transaction_id'] = $detail->trans_id;
        $array['epos_plu_number'] = $this->getProductPLU($detail->product_guid);
        $array['quantity'] = $detail->qty;
        $array['net_value'] = $detail->net_value - $detail->tax_value;
        $array['vat_value'] = $detail->tax_value;
        $array['gross_value'] = $detail->net_value;
        $array['transaction_date'] = $detail->finalised_date;
        $array['description'] = $detail->displayname;
        $array['type_id'] = 1;
        $array['reason_code'] = '';

        return $array;
    }

    private function addVoidItem(PosTransactionDetail $detail, VoidReasons $reason)
    {
        $array = $this->addBlankRow();

        $array['store_id'] = $this->site_id;
        $array['revenue_category'] = $this->findDepartment($detail->department_guid)['acc_code'];
        $array['till_number'] = $detail->terminal_num;
        $array['transaction_id'] = $detail->trans_id;
        $array['epos_plu_number'] = $this->getProductPLU($detail->product_guid);
        $array['quantity'] = $detail->qty;
        $array['net_value'] = $detail->net_value;
        $array['vat_value'] = $detail->tax_value;
        $array['gross_value'] = $detail->gross_value;
        $array['transaction_date'] = $detail->finalised_date;
        $array['description'] = $detail->displayname;
        $array['type_id'] = 2;
        $array['reason_code'] = $reason->reason;

        return $array;
    }

    private function addBlankRow()
    {
        return [
            'store_id' => '',
            'revenue_category' => '',
            'till_number' => '',
            'transaction_id' => '',
            'epos_plu_number' => '',
            'quantity' => '',
            'net_value' => '',
            'vat_value' => '',
            'gross_value' => '',
            'transaction_date' => '',
            'description' => '',
            'type_id' => '',
            'reason_code' => ''
        ];
    }

    public function headings(): array
    {
        return [
            'Location ID/Store ID',
            'Revenue Category',
            'Till Number',
            'Transaction ID',
            'Epos/PLU Number',
            'Quantity',
            'Net value',
            'Tax Value',
            'Gross Value',
            'Transaction Date',
            'Description',
            'Type ID',
            'Reason Code'
        ];
    }

    private function findDepartment(string $key): array
    {
        if (array_key_exists($key, $this->departments)) {
            return $this->departments[$key];
        }

        // do some kind of warning logging
        return ['acc_code' => ''];
    }

    private function getProductPLU(string $guid): string
    {
        if (isset($this->productInfo[$guid])) {
            if (!empty($this->productInfo[$guid]['third_party_id'])) {
                return $this->productInfo[$guid]['third_party_id'];
            }

            return $this->productInfo[$guid]['id'];
        }

        return $guid;
    }
}
