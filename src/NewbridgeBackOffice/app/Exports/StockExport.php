<?php

namespace NewbridgeWeb\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class StockExport implements FromView, ShouldAutoSize
{
    public function __construct(
        public $meta,
        public $summary,
        public $results,
        public $subDepartments,
        public $module,
        public $currency
    ) {
    }

    public function view(): View
    {
        return view('modules.stock.view-export-csv', [
            'meta' => $this->meta,
            'summary' => $this->summary,
            'results' => $this->results,
            'subdepartments' => $this->subDepartments,
            'module' => $this->module,
            'currency' => $this->currency
        ]);
    }
}
