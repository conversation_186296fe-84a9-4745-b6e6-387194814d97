<?php

namespace NewbridgeWeb\Exports\Debug;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use NewbridgeWeb\Exports\Debug\Sheets\DebugCompany;
use NewbridgeWeb\Exports\Debug\Sheets\DebugCustomerGroups;
use NewbridgeWeb\Exports\Debug\Sheets\DebugCustomers;
use NewbridgeWeb\Exports\Debug\Sheets\DebugCustomerTransactions;
use NewbridgeWeb\Exports\Debug\Sheets\DebugDepartments;
use NewbridgeWeb\Exports\Debug\Sheets\DebugPermissions;
use NewbridgeWeb\Exports\Debug\Sheets\DebugProducts;
use NewbridgeWeb\Exports\Debug\Sheets\DebugRecipeLinks;
use NewbridgeWeb\Exports\Debug\Sheets\DebugRecipes;
use NewbridgeWeb\Exports\Debug\Sheets\DebugRolePermissions;
use NewbridgeWeb\Exports\Debug\Sheets\DebugRoles;
use NewbridgeWeb\Exports\Debug\Sheets\DebugSites;
use NewbridgeWeb\Exports\Debug\Sheets\DebugSkus;
use NewbridgeWeb\Exports\Debug\Sheets\DebugStockSummary;
use NewbridgeWeb\Exports\Debug\Sheets\DebugStockTransactions;
use NewbridgeWeb\Exports\Debug\Sheets\DebugSubDepartments;
use NewbridgeWeb\Exports\Debug\Sheets\DebugTaxRates;
use NewbridgeWeb\Exports\Debug\Sheets\DebugTerminals;
use NewbridgeWeb\Exports\Debug\Sheets\DebugTerminalSettings;
use NewbridgeWeb\Exports\Debug\Sheets\DebugTransactionDetails;
use NewbridgeWeb\Exports\Debug\Sheets\DebugTransactionPaymentDetails;
use NewbridgeWeb\Exports\Debug\Sheets\DebugTransactionSummary;
use NewbridgeWeb\Exports\Debug\Sheets\DebugUserRoles;
use NewbridgeWeb\Exports\Debug\Sheets\DebugUsers;

class DebugExport implements WithMultipleSheets
{
    use Exportable;
    public string $uuid;
    public int $company_id;
    public int $site_num;

    public function __construct(string $uuid, int $company_id, int $site_num)
    {
        $this->uuid = $uuid;
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    public function sheets(): array
    {
        $sheets = [];
        $sheets['company'] = new DebugCompany($this->company_id, $this->site_num);
        $sheets['sites'] = new DebugSites($this->company_id, $this->site_num);
        $sheets['terminals'] = new DebugTerminals($this->company_id, $this->site_num);
        $sheets['pos_settings'] = new DebugTerminalSettings($this->company_id, $this->site_num);
        $sheets['products'] = new DebugProducts($this->company_id, $this->site_num);
        $sheets['departments'] = new DebugDepartments($this->company_id, $this->site_num);
        $sheets['sub_departments'] = new DebugSubDepartments($this->company_id, $this->site_num);
        $sheets['tax_rates'] = new DebugTaxRates($this->company_id, $this->site_num);
        $sheets['skus'] = new DebugSkus($this->company_id, $this->site_num);
        $sheets['recipes'] = new DebugRecipes($this->company_id, $this->site_num);
        $sheets['recipe_links'] = new DebugRecipeLinks($this->company_id, $this->site_num);
        $sheets['summaries'] = new DebugStockSummary($this->company_id, $this->site_num);
        $sheets['transactions'] = new DebugStockTransactions($this->company_id, $this->site_num);
        $sheets['users'] = new DebugUsers($this->company_id, $this->site_num);
        $sheets['customers'] = new DebugCustomers($this->company_id, $this->site_num);
        $sheets['customer_transactions'] = new DebugCustomerTransactions($this->company_id, $this->site_num);
        $sheets['customer_groups'] = new DebugCustomerGroups($this->company_id, $this->site_num);
        $sheets['user_roles'] = new DebugUserRoles($this->company_id, $this->site_num);
        $sheets['roles'] = new DebugRoles($this->company_id, $this->site_num);
        $sheets['permissions'] = new DebugPermissions($this->company_id, $this->site_num);
        $sheets['role_permissions'] = new DebugRolePermissions($this->company_id, $this->site_num);
        $sheets['transaction_summary'] = new DebugTransactionSummary($this->company_id, $this->site_num);
        $sheets['transaction_details'] = new DebugTransactionDetails($this->company_id, $this->site_num);
        $sheets['transaction_payments'] = new DebugTransactionPaymentDetails($this->company_id, $this->site_num);

        foreach($sheets as $k => $sheet) {
            if(empty($sheet->query())) {
                unset($sheets[$k]);
            }
        }

        return $sheets;
    }
}
