<?php

namespace NewbridgeWeb\Exports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\Company;

class DebugCompany implements FromCollection, WithTitle, WithHeadings
{
    public int $company_id;
    public int $site_num;

    public function __construct(int $company_id, int $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection(): \Illuminate\Support\Collection
    {
        $companies = Company::withoutAppends()->where('id', $this->company_id)->get();
        foreach ($companies as $company) {
            $company->contact_name = $company->company_name;
            $company->address_line1 = 'XXXXX';
            $company->address_line2 = 'XXXXX';
            $company->county = 'XXXXX';
            $company->city = 'XXXXX';
            $company->postcode = 'XXXXX';
            $company->telephone = '0000000';
            $company->vat_number = '0000000';
        }

        return $companies;
    }

    public function title(): string
    {
        return 'Company';
    }

    public function headings(): array
    {
        if (!empty($this->query())) {
            return array_keys($this->query()->toArray());
        } else {
            return [];
        }
    }

    public function query()
    {
        return Company::withoutAppends()->where('id', $this->company_id)->first();
    }
}
