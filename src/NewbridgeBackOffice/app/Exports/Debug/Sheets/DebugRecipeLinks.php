<?php

namespace NewbridgeWeb\Exports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\RecipeLinks;
use NewbridgeWeb\Repositories\Recipes;

class DebugRecipeLinks implements FromCollection, WithTitle, WithHeadings
{
    public int $company_id;
    public int $site_num;

    public function __construct(int $company_id, int $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection(): \Illuminate\Support\Collection
    {
        $recipeLinks = new RecipeLinks();
        $recipes = Recipes::where('company_id', $this->company_id)->whereIn('site_num', [0, $this->site_num])->pluck('guid');
        $recipeLinks = $recipeLinks->withoutAppends()->whereIn('recipe_guid', $recipes)->get();

        return $recipeLinks;
    }

    public function title(): string
    {
        return 'Recipe Links';
    }

    public function headings(): array
    {
        if(!empty($this->query())) {
            return array_keys($this->query()->toArray());
        } else {
            return [];
        }
    }

    public function query()
    {
        $recipes = Recipes::where('company_id', $this->company_id)->whereIn('site_num', [0, $this->site_num])->pluck('guid');

        return RecipeLinks::withoutAppends()->whereIn('recipe_guid', $recipes)->first();
    }
}
