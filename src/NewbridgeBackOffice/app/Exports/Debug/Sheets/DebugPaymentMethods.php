<?php

namespace NewbridgeWeb\Exports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\Products;

class DebugPaymentMethods implements FromCollection, WithTitle, WithHeadings
{
    public int $company_id;
    public int $site_num;

    public function __construct(int $company_id, int $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection(): \Illuminate\Support\Collection
    {
        $paymentMethods = new Payments();
        $paymentMethods = $paymentMethods->withoutAppends()->where('company_id', $this->company_id)->whereIn('site_num', [0, $this->site_num])->get();

        return $paymentMethods;
    }

    public function title(): string
    {
        return 'Payment Methods';
    }

    public function headings(): array
    {
        if(!empty($this->query())) {
            return array_keys($this->query()->toArray());
        } else {
            return [];
        }
    }

    public function query()
    {
        return Products::withoutAppends()->where('company_id', $this->company_id)->whereIn('site_num', [0, $this->site_num])->first();
    }
}
