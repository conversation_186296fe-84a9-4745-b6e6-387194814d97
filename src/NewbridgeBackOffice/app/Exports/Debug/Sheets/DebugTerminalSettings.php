<?php

namespace NewbridgeWeb\Exports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\PosSettings;

class DebugTerminalSettings implements FromCollection, WithTitle, WithHeadings
{
    public int $company_id;
    public int $site_num;

    public function __construct(int $company_id, int $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection(): \Illuminate\Support\Collection
    {
        $terminalSettings = PosSettings::withoutAppends()->where('company_id', $this->company_id)->get();

        $badWords = [
            'clientkey', 'accesskey', 'securitykey', 'secretkey', 'auth', 'password', 'secret'
        ];

        foreach($terminalSettings as $key => $terminalSetting) {
            foreach ($badWords as $badWord) {
                if (stristr($terminalSetting->setting_id, $badWord)) {
                    $terminalSettings[$key]->value1 = 'XXXXX' . substr($terminalSettings[$key]->value1, 5);
                }
            }

            if($terminalSetting->setting_id == 'GuestlinePMS') {
                $hotels = json_decode($terminalSetting->value7, true);
                if(!empty($hotels)) {
                    foreach ($hotels as $k => $hotel) {
                        $hotels[$k]['connection']['password'] = 'removed_for_security';
                    }
                }
            }
        }

        return $terminalSettings;
    }

    public function title(): string
    {
        return 'Terminal Settings';
    }

    public function headings(): array
    {
        if (!empty($this->query())) {
            return array_keys($this->query()->toArray());
        } else {
            return [];
        }
    }

    public function query()
    {
        return PosSettings::withoutAppends()->where('company_id', $this->company_id)->first();
    }
}
