<?php

namespace NewbridgeWeb\Exports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\Pos;

class DebugTerminals implements FromCollection, WithTitle, WithHeadings
{
    public int $company_id;
    public int $site_num;

    public function __construct(int $company_id, int $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection(): \Illuminate\Support\Collection
    {
        $terminals = Pos::withoutAppends()->where('company_id', $this->company_id)->get();

        return $terminals;
    }

    public function title(): string
    {
        return 'Terminals';
    }

    public function headings(): array
    {
        if (!empty($this->query())) {
            return array_keys($this->query()->toArray());
        } else {
            return [];
        }
    }

    public function query()
    {
        return Pos::withoutAppends()->where('company_id', $this->company_id)->first();
    }
}
