<?php

namespace NewbridgeWeb\Exports\Debug\Sheets;

use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\PosTransactionDetail;

class DebugTransactionDetails implements FromCollection, WithTitle, WithHeadings
{
    public int $company_id;
    public int $site_num;

    public function __construct(int $company_id, int $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection(): \Illuminate\Support\Collection
    {
        $transactionDetails = new PosTransactionDetail();
        $transactionDetails = $transactionDetails->withoutAppends()->where('company_id', $this->company_id)
            ->whereIn('site_num', [0, Session::get('current_site')])
            ->where('finalised_date', '>=', Carbon::now()->subDays(62))
            ->get();

        return $transactionDetails;


    }

    public function title(): string
    {
        return 'Transaction Details';
    }

    public function headings(): array
    {
        if(!empty($this->query())) {
            return array_keys($this->query()->toArray());
        } else {
            return [];
        }
    }

    public function query()
    {
        return PosTransactionDetail::withoutAppends()->where('company_id', $this->company_id)
            ->whereIn('site_num', [0, Session::get('current_site')])->first();
    }
}
