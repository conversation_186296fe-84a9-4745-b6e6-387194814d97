<?php

namespace NewbridgeWeb\Exports\Debug\Sheets;

use Faker\Factory;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\Customer;

class DebugCustomers implements FromCollection, WithTitle, WithHeadings
{
    public int $company_id;
    public int $site_num;

    public function __construct(int $company_id, int $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection(): \Illuminate\Support\Collection
    {
        $customers = new Customer();
        $faker = Factory::create();
        $customers = $customers->withoutAppends()->where('company_id', $this->company_id)->get();

        foreach($customers as $key => $customer) {
            $customers[$key]->first_name =  'customer'.$customer->id;
            $customers[$key]->last_name =  'lastname';
            $customers[$key]->full_name =  'customer'.$customer->id.' lastname';
            $customers[$key]->telephone = '00000000000';
            $customers[$key]->notes = null;
            $customers[$key]->email = 'customer'.$customer->id.'@newbridgecloud.co.uk';
        }

        return $customers;
    }

    public function title(): string
    {
        return 'Customers';
    }

    public function headings(): array
    {
        if(!empty($this->query())) {
            return array_keys($this->query()->toArray());
        } else {
            return [];
        }
    }

    public function query()
    {
        return Customer::withoutAppends()->where('company_id', $this->company_id)->first();
    }
}
