<?php

namespace NewbridgeWeb\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StartProductsStockCalculationRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'product_id' => 'nullable|exists:plu_products,id',
            'site_num' => 'nullable|int',
            'company_id' => 'nullable|exists:sys_company,id',
        ];
    }
}
