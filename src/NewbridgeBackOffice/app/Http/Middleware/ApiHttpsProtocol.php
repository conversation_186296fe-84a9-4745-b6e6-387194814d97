<?php

namespace NewbridgeWeb\Http\Middleware;

use Closure;
use Log;
use NewbridgeWeb\Repositories\Company;

class ApiHttpsProtocol
{
    public function handle($request, Closure $next)
    {
        $auth = $request->header('X-Newbridge-Auth');

        if ($auth) {
            $auth = explode(':', base64_decode($auth));
            $company = cache()->rememberForever($auth[0], function () use ($auth) {
    return Company::where('terminal_access_key', $auth[0])->first();
});

            if ($company != null) {
                if (in_array($company['id'], explode(',', config('newbridge.log_company_connections'))) && $request->path() === '/api/pos/transactions') {
                    Log::info('Logged Activity In API HTTPS Protocol', ['company' => $company['company_name'], 'terminal' => $auth[1]]);
                }
            } else {
                exit(401);
            }
        }

        return $next($request);
    }
}
