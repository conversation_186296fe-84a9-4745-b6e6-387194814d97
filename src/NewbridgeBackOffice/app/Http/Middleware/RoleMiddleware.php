<?php

namespace NewbridgeWeb\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $permission)
    {
        if (!Auth::user()->hasRole($permission)) {
            if ($request->ajax()) {
                return response()->json(['status' => 'error', 'message' => 'Sorry you have insufficient permissions to perform this action'], 403);
            } else {
                return abort(403);
            }
        }

        return $next($request);
    }
}
