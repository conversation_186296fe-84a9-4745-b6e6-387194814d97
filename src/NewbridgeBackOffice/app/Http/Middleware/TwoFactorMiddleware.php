<?php

namespace NewbridgeWeb\Http\Middleware;

use Auth;
use Closure;
use PragmaRX\Google2FALaravel\Support\Authenticator;

class TwoFactorMiddleware
{
    public function handle($request, Closure $next)
    {
        if (Auth::user()->google2fa_secret != null && Auth::user()->google2fa_secret != '') {
            $authenticator = new Authenticator($request);
            $authenticator->boot($request);

            if ($authenticator->isAuthenticated()) {
                return $next($request);
            }

            return $authenticator->makeRequestOneTimePasswordResponse();
        } else {
            return $next($request);
        }
    }
}
