<?php

namespace NewbridgeWeb\Http\Middleware;

use Auth;
use Closure;

class Maintenance
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $url = $request->getUri();
        $maintenance = config('newbridge.maintenance');
        if($maintenance) {
            if (auth()->check() && auth()->user()->hasRole('newbridge')) {
                session()->put(
                    'maintenance',
                    'You are accessing the system in maintenance mode, please be aware that things may not work as expected.'
                );

                return $next($request);
            };

            if (stristr($url, 'logout') ||
                stristr($url, 'login') ||
                stristr((string)$request->getUri(), 'worker') ||
                stristr((string)$request->getUri(), 'queue')) {
                return $next($request);
            }

            if ($maintenance === true &&
                !stristr((string)$request->getUri(), 'api') &&
                !stristr((string)$request->getUri(), 'redis-contents')) {
                abort(503);
            }

            abort(503);
        }
        session()->forget('maintenance');
        return $next($request);
    }
}
