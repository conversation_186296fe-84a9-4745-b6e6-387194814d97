<?php

namespace NewbridgeWeb\Http\Middleware;

use Auth;
use Closure;
use Illuminate\Support\Collection;
use NewbridgeWeb\Repositories\Newbridge\Modules;
use NewbridgeWeb\Repositories\Sites;

class ModuleControlMiddleware
{
    public function handle($request, Closure $next)
    {
        if (Auth::check()) {
            $allModules = $this->getModules();
            $site = $this->getSite();

            foreach ($allModules as $module) {
                if (in_array($request->segment($module->segment), $module->routes)) {
                    if ($site->allowed_modules & $module->bit_value) {
                        return $next($request);
                    } else {
                        return redirect('/no-module-access');
                    }
                }
            }
        }

        return $next($request);
    }

    private function getModules(): Collection
    {
        return cache()->rememberForever('allModules', function () {
            $allModules = Modules::all();

            foreach ($allModules as $k => $module) {
                $allModules[$k]->routes = json_decode($module->module_routes, true);
            }

            return $allModules;
        });
    }

    private function getSite(): Sites
    {
        return cache()->remember('site-'.session('user.company_id').'-'.session('current_site'), 60, function () {
            return Sites::where('company_id', session('user.company_id'))->where('site_num', session('current_site'))->first();
        });
    }
}
