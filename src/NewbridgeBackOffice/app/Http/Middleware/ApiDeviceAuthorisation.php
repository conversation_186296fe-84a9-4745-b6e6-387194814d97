<?php

namespace NewbridgeWeb\Http\Middleware;

use Carbon\Carbon;
use Closure;
use Log;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\CompanyJWT;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationCompanies;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationSites;
use NewbridgeWeb\Repositories\Pos;
use Ramsey\Uuid\Uuid;

class ApiDeviceAuthorisation
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param string|null $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if ($request->segment(2) == 'internal') {
            $auth = $request->input('access_key');

            if ($auth == config('newbridge.internal_api_key', '050bd205-bd7b-4603-a09e-fab9f0fcfeb9')) {
                return $next($request);
            }

            return response()->json(['message' => 'invalid access key'], 401);
        }

        if ($request->segment(2) == 'pos') {
            /**
             * Get the authentication headers for the terminal to register or
             * poll with the system
             */
            $auth = $request->header('X-Newbridge-Auth');

            if ($auth) {
                $auth = explode(':', base64_decode($auth));

                $company = cache()->rememberForever($auth[0], function () use ($auth) {
                    return Company::select(['terminal_access_key', 'id', 'company_name'])->where('terminal_access_key', $auth[0])->first();
                });

                if ($company == null) {
                    return response()->json(['error' => 'Invalid Access Key'], 401);
                }

                /**
                 * Check device has correct access key & is registered
                 * if the device is not registered, register it.
                 */
                $check = Pos::checkAuthorised($auth, $request->ip());

                if ($check['status'] == 'ok') {
                    return $next($request);
                } elseif ($check['status'] == 'error') {
                    return response()->json($check, 401);
                }
            } else {
                return response()->json(['POS REQUEST'], 401);
            }
        }

        if ($request->segment(2) == 'app') {
            // check token or auth
            if ($request->segment(3) != 'auth') {
                if ($request->header('token')) {
                    $required = ['token'];

                    foreach ($required as $require) {
                        if (!$request->header($require) || $request->header($require) === '' || $request->header(
                                $require
                            ) === null) {
                            return response()->json(['error' => '(1) Please provide valid API Credentials'], 401);
                        }
                    }

                    // get a company based on key
                    $tokenCompany = CompanyJWT::where('token', $request->header('token'))->first();

                    if ($tokenCompany == null) {
                        return response()->json(['error' => '(6) Token invalid or expired'], 401);
                    }

                    // validate the key
                    $company = Company::select(['terminal_access_key', 'id', 'company_name'])->with([
                        'token' => function ($q) use ($request) {
                            $q->where('token', $request->header('token'));
                        }
                    ])->where('id', $tokenCompany->company_id)->first();

                    if ($company !== null && $company->token !== null) {
                        if ($request->header('token') === $company->token->token & Carbon::now('Europe/London')->lt(
                                $company->token->expires
                            )) {
                            $request->merge(['company' => $company->toArray()]);

                            return $next($request);
                        } else {
                            return response()->json(['error' => '(6) Token invalid or expired'], 401);
                        }
                    } else {
                        return response()->json(['error' => '(2) Please provide a valid Token'], 401);
                    }
                } else {
                    return response()->json(['error' => '(5) Please provide a valid Token'], 401);
                }
            }

            if (!$request->header('token') && $request->segment(3) == 'auth') {
                $data = $request->input();

                $company = Company::with('token')->where('terminal_access_key', $data['key'])->where(
                    'secret_key',
                    $data['secret']
                )->first();

                if ($company !== null) {
                    $company->toArray();

                    // create a token and return with expiry
                    $guid = Uuid::uuid4();
                    $token = str_replace('-', '', $guid);
                    $expires = Carbon::now('UTC')->setTimezone('Europe/London')->addMinutes(30);

                    $authToken = new CompanyJWT();

                    $authToken->token = $token;
                    $authToken->company_id = $company['id'];
                    $authToken->expires = $expires;
                    $authToken->save();

                    return response()->json(
                        [
                            'token' => $token,
                            'expires' => $expires->toDateTimeString(),
                            'expires_timestamp' => (int)$expires->timestamp
                        ],
                        200
                    );
                } else {
                    return response()->json(['error' => '(3) Not authorised, please check your Key and Secret!'], 401);
                }
            }

            return $next($request);
        }

        if ($request->segment(2) == 'v2') {
            if ($request->header('x-newbridge-key') && $request->header('x-newbridge-secret')) {
                $integration = SiteIntegrationCompanies::where('app_key', $request->header('x-newbridge-key'))->first();
                if (empty($integration)) {
                    $integration = SiteIntegrationSites::where('app_key', $request->header('x-newbridge-key'))->first();
                }

                if (empty($integration)) {
                    return response()->json(['error' => 'Invalid Credentials'], 401);
                }

                if (!empty($integration->configuration) && $integration->app_key != null && $integration->app_secret != null) {
                    $expected = hash_hmac('sha256', $integration->app_key, $integration->app_secret, true);

                    if (!hash_equals($expected, base64_decode($request->header('x-newbridge-secret')))) {
                        return response()->json(['error' => 'Invalid Credentials'], 401);
                    } else {
                        $request->merge(['integration' => $integration->toArray()]);

                        return $next($request);
                    }
                } else {
                    abort(404);
                }
            } else {
                return response()->json(['error' => 'Please provide a both an App_Key and App_Secret'], 400);
            }
        }

        return $next($request);
    }
}
