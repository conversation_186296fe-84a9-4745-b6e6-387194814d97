<?php

namespace NewbridgeWeb\Http\Middleware;

use Auth;
use Closure;
use Illuminate\Support\Facades\Session;
use NewbridgeWeb\Http\Helpers\SiteHelper;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\User;
use Sentry\State\Scope;

class SessionDataUpdate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if (\Session::has('user')) {
            $user = \Session::get('user');

            $site_exists = Sites::where('company_id', $user['company_id'])->where('site_num', \Session::get('current_site'))->first();
        } else {
            if (Auth::check()) {
                $user = User::with('company')->find(Auth::user()->id);

                \Session::put('user', $user);

                $site_exists = null;
            } else {
                return $next($request);
            }
        }
        if (!\Session::has('current_site')
            || \Session::get('current_site') == ''
            || \Session::get('current_site') == null
            || \Session::get('current_site') == 0
            || $site_exists == null
        ) {
            \Session::put('current_site', 1);
            SiteHelper::userCurrentSite();

            return $next($request);
        }

        \Sentry\Laravel\Integration::configureScope(static function (Scope $scope) use ($user): void {
            $scope->setUser([
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'company' => $user->company_id,
                'real_company' => $user->real_company_id,
            ]);
        });

        return $next($request);
    }
}
