<?php

namespace NewbridgeWeb\Http\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\Timezone;

class TimezoneHelper
{
    /**
     * Get the timezone for a site
     *
     * @param int|null $siteNum
     * @param int|null $companyId
     * @return string The timezone string (e.g. 'Europe/London')
     */
    public static function getTimezone(int $siteNum = null, int $companyId = null): string
    {
        $siteNum = $siteNum ?? Session::get('current_site');
        $companyId = $companyId ?? Session::get('user.company_id');
        $cacheKey = 'site_timezone_' . $siteNum . '-' . $companyId;

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $site = Sites::where('site_num', $siteNum)
            ->where('company_id', $companyId)
            ->first();

        if (!$site || $site->timezone_id === null) {
            return 'Europe/London';
        }

        $timezone = Timezone::find($site->timezone_id);
        if ($timezone) {
            $timezoneString = $timezone->timezone;
            Cache::put($cacheKey, $timezoneString, 60 * 24);

            return $timezoneString;
        }

        return 'Europe/London';
    }

    public static function clearCache(int $siteNum, int $companyId): void
    {
        Cache::forget('site_timezone_' . $siteNum . '-' . $companyId);
    }
}
