<?php

namespace NewbridgeWeb\Http\Helpers;

use Illuminate\Support\Facades\Cache;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\CurrencySymbols;

class CurrencyHelper
{
    public static function symbol($company_id)
    {
        if (Cache::has('company_currency_symbol_' . $company_id)) {
            return Cache::get('company_currency_symbol_' . $company_id);
        }

        $currency = Company::find($company_id)->currency;
        $symbol = CurrencySymbols::find($currency);

        if ($symbol != null) {
            $char = $symbol->value != null ? $symbol->value : $symbol->code;
        } else {
            $char = '£';
        }

        Cache::put('company_currency_symbol_' . $company_id, $char, 60 * 24);

        return $char;
    }
}
