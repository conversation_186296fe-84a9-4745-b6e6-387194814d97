<?php

namespace NewbridgeWeb\Http\Helpers;

class NewbridgeLoyaltyHelper
{
    public static function sendPostRequest(string $endpoint, array $body): array
    {
        $client = new \GuzzleHttp\Client(['verify' => false]);

        try {
            $response = $client->post(config('nbloyalty.scheme') . config('nbloyalty.url') . $endpoint, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'x-newbridge-key' => config('nbloyalty.key'),
                    'x-newbridge-secret' => base64_encode(hash_hmac('sha256', config('nbloyalty.key'), config('nbloyalty.secret'), true))
                ],
                'body' => json_encode($body)
            ]);

            return [
                'body' => json_decode($response->getBody()->getContents(), true),
                'status' => $response->getStatusCode()
            ];
        } catch(\Exception $e) {
            report($e);

            return [
                'body' => null,
                'status' => $e->getCode()
            ];
        }
    }
}