<?php

namespace NewbridgeWeb\Http\Helpers;

use Auth;
use NewbridgeWeb\Repositories\AlarmResults;

class Alarms
{
    public static function display()
    {
        // get unread alarms
        $alarms = AlarmResults::where('company_id', Auth::user()->company_id)
            ->where('status', 0)
            ->get();

        return $alarms;
    }

    public static function count()
    {
        $alarms = AlarmResults::where('company_id', Auth::user()->company_id)
            ->where('status', 0)
            ->count();

        return (int) $alarms;
    }
}
