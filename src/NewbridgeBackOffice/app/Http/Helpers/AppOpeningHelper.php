<?php

namespace NewbridgeWeb\Http\Helpers;

use Carbon\Carbon;

class AppOpeningHelper
{
    public static function AreWeOpen($times, $timezone = 'Europe/London')
    {
        if (!empty($times)) {
            $day = self::todayBitValue();
            $now = Carbon::now('utc')->setTimezone($timezone);

            foreach ($times as $k => $time) {
                if ($time['days'] & $day) {
                    foreach ($time['times'] as $hours) {
                        if ($hours['day'] == $day) {
                            $hours['open'] = explode(':', $hours['open']);
                            $hours['close'] = explode(':', $hours['close']);
                            $open = Carbon::now('UTC')->setTimezone($timezone)->setTime($hours['open'][0], $hours['open'][1]);
                            $close = Carbon::now('UTC')->setTimezone($timezone)->setTime($hours['close'][0], $hours['close'][1]);

                            if ($now->between($open, $close)) {
                                return true;
                            }
                        }
                    }
                }
            }

            return false;
        } else {
            return true;
        }
    }

    public static function todayBitValue()
    {
        $day = Carbon::now()->format('l');

        switch($day) {
            case 'Monday':
                return 1;

            case 'Tuesday':
                return 2;

            case 'Wednesday':
                return 4;

            case 'Thursday':
                return 8;

            case 'Friday':
                return 16;

            case 'Saturday':
                return 32;

            case 'Sunday':
                return 64;

        }
    }
}
