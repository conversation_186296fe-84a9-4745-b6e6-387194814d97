<?php

namespace NewbridgeWeb\Http\Helpers;

use NewbridgeWeb\Repositories\Integrations\IntegrationLinks;
use NewbridgeWeb\Repositories\Integrations\Integrations;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrations;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationSites;

class IntegrationsHelper
{
    public static function get($id, $company_id)
    {
        $integration = Integrations::with(['settings' => function ($q) use ($company_id) {
            $q->where('company_id', $company_id);
        }])->find($id);

        $result = [
            "name" => $integration->name,
            "settings" => []
        ];

        foreach ($integration->settings as $k => $setting) {
            $result['settings'][$setting->setting_id] = $setting->value;
        }

        return $result;
    }

    public static function checkActiveForSite(int $bit, int $company_id, int $site_num): bool
    {
        $integration = SiteIntegrations::where('bit', $bit)->first();
        $settings = SiteIntegrationSites::where('integration_id', $integration->id)
            ->where('company_id', $company_id)
            ->where('site_num', $site_num)
            ->first();

        if($settings) {
            return true;
        }

        return false;
    }

    public static function mappings($integration_id, $company_id, $type, $site_num = 1, $subject_ids = [], $return = 'array', $return_value = 'third_party_reference')
    {
        $mappings = IntegrationLinks::where('integration_id', $integration_id)
            ->where('company_id', $company_id)
            ->where('subject_type', $type)
            ->where('site_num', $site_num);

        if (!empty($subject_ids)) {
            $mappings = $mappings->whereIn('subject_id', $subject_ids);
        }

        $mappings = $mappings->get();


        if (!$mappings->isEmpty()) {
            if ($return == 'array') {
                return $mappings->keyBy($return_value)->toArray();
            }
            if ($return == 'idlist') {
                return $mappings->pluck($return_value)->toArray();
            }

            return $mappings;
        }

        return [];
    }

    public static function findMapping($integration_id, $company_id, $type, $subject_id, $site_num = 1)
    {
        $mapping = IntegrationLinks::where('integration_id', $integration_id)
            ->where('company_id', $company_id)
            ->where('subject_type', $type)
            ->where('site_num', $site_num)
            ->where('subject_id', $subject_id)
            ->first();

        return $mapping;
    }
}
