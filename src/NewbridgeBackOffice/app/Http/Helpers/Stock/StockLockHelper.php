<?php

namespace NewbridgeWeb\Http\Helpers\Stock;

use Cache;

class StockLockHelper
{
    public static function checkForLocks(string $type, int $id, int $siteNum): bool
    {
        return Cache::has('stock-calculation-' . $type . "-" . $id . "-" . $siteNum);
    }

    public static function setLocks(string $type, int $id, int $siteNum): void
    {
        Cache::put('stock-calculation-' . $type . "-" . $id . "-" . $siteNum, true, 180);
    }

    public static function releaseLocks(string $type, int $id, int $siteNum): void
    {
        Cache::forget('stock-calculation-' . $type . "-" . $id . "-" . $siteNum);
    }
}