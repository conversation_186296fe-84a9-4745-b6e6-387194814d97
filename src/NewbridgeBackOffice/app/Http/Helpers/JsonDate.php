<?php

namespace NewbridgeWeb\Http\Helpers;

use Carbon\Carbon;
use Log;

class JsonDate
{
    public static function parseJsonDate($date, $diff, $type = 'date', $companyData = null, $timezone = null)
    {
        try {
            if (!stristr('-', $diff)) {
                $diff = '+' . $diff;
            }

            $date = str_replace(')/', $diff . ')/', $date);

            preg_match('/\/Date\((\d+)([+-]\d{4})\)/', $date, $matches); // Match the time stamp (microtime) and the timezone offset (may be + or -)

            $date = date('Y-m-d H:i:s', $matches[1] / 1000); // convert to seconds from microseconds

            return match ($type) {
                'date' => $date,
                'array' => explode('-', $date),
                'string' => $matches[1] . $matches[2],
                default => null,
            };

        } catch(\Exception $e) {
            // do nothing, this is a version change in the POS for JSONDate
        }

        try {
            $date = Carbon::parse($date, $timezone ?? 'Europe/London');

            return $date->setTimezone('UTC')->toDateTimeString();
        } catch (\Exception $e) {
            throw new \Exception($e);
        }
    }
}
