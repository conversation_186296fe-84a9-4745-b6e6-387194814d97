<?php

namespace NewbridgeWeb\Http\Helpers;

use Auth;
use Illuminate\Support\Facades\Session;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\UserSites;
use NewbridgeWeb\User;

class SiteHelper
{
    public static function allSites($company_id)
    {
        $array = Sites::select('site_num')
            ->where('company_id', $company_id)
            ->get()
            ->pluck('site_num');

        return $array;
    }

    /**
     * All sites for the current company
     * except the one currently selected via 2nd parameter
     *
     * @param $company_id
     * @param $site_num
     * @return mixed
     */
    public static function otherSites($company_id, $site_num)
    {
        $array = Sites::select('site_num')->where('company_id', $company_id)
            ->whereNotIn('site_num', [$site_num])
            ->get()
            ->pluck('site_num');

        return $array;
    }

    /**
     * All sites available to the logged in user
     * returns an array of sites available to the user
     *
     * @param bool $allSites
     * @param bool $view
     * @return mixed
     */
    public static function mySites(bool $allSites = false, bool $view = false, null|User $user = null)
    {
        if($user == null){
            $user = Auth::user();
        }
        $companyId = $user->company_id;

        $userSites = UserSites::where('company_id', $companyId)->where('user_id', $user->id)->get();

        if ($userSites == null) {
            $userSites = Sites::where('company_id', $companyId)->get();
        }

        $currentSite = session('current_site');

        if (!$userSites->isEmpty()) {
            $sites = $userSites->keyBy('site_num')->toArray();
        } else {
            $sites = Sites::where('company_id', $companyId)->get()->toArray();
        }

        /**
         * Comparison between number of allowed sites
         * and sites that exist for the users company
         */
        $companySites = Sites::where('company_id', $companyId)->count();
        $userAllowedSites = UserSites::where('user_id', $user->id)
            ->where('company_id', $companyId)->count();

        if ($userAllowedSites > 0) {
            if ($allSites) {
                $all = !($companySites !== $userAllowedSites);
            } else {
                $all = false;
            }
        } else {
            $all = true;
        }

        if (!$view) {
            return ['sites' => $sites, 'current_site' => $currentSite, 'all' => $all];
        }
    }

    public static function showSiteName($id)
    {
        $site = Sites::select(['site_name'])->where('company_id', Auth::user()->company_id)
            ->where('site_num', $id)->first();

        return $site?->site_name ?? '';
    }

    public static function userCurrentSite()
    {
        if (Auth::check()) {
            $user = Auth::user();

            if (Auth::user()->company() != null) {
                $user_default_site = UserSites::where('company_id', $user->company_id)->where('user_id', $user->id)->where('isdefault', 1)->first();

                if ($user_default_site == null) {
                    $user_default_site = UserSites::where('company_id', $user->company_id)->where('user_id', $user->id)->first();
                }

                if ($user_default_site == null) {
                    $user_default_site = Sites::where('company_id', $user->company_id)->first();
                }

                if ($user_default_site != null) {
                    Session::put('current_site', $user_default_site->site_num);
                } else {
                    Session::put('current_site', 1);
                }
            }
        }
    }
}
