<?php

namespace NewbridgeWeb\Http\Helpers;

use Guz<PERSON>Http\Client;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Ramsey\Uuid\Uuid;

class ServiceBusHelper
{
    private static function shouldCreateBlob($content): bool
    {
        $sizeInKB = strlen($content) / 1024;

        if ($sizeInKB >= config('newbridge.max_sb_message_size')) {
            return true;
        }

        return false;
    }

    private static function createBlob($content): string
    {
        $uuid = Uuid::uuid4()->toString();

        try {
            Storage::disk('azure_events')->put($uuid . '.json', $content);
            return $uuid . '.json';
        } catch (\Exception $e) {
            report($e);
        }

        return false;
    }

    /**
     * @throws Exception
     */
    public static function sendEventToTopic(string $topic, string $subject, string $type, array|string $content,
                                            bool $alwaysBlob = false, string $token = 'newbridge.service_bus_sas_token',
                                            string $url = 'newbridge.service_bus_url', string $key = 'backoffice-emit-events-policy'): void
    {
        Log::info('ServiceBusHelper sendEventToTopic', [
            'topic' => $topic,
            'subject' => $subject,
            'type' => $type,
            'content' => $content,
            'alwaysBlob' => $alwaysBlob,
            'token' => $token,
            'url' => $url,
            'key' => $key
        ]);

        if(is_array($content)){
            $content = json_encode($content);
        }

        $serviceBusSasKey = config($token);
        $serviceBusUrl = config($url) . '/' . $topic . '/messages';
        $serviceBusKeyName = $key;

        $expiry = time() + 60; // Generate SAS Token 1 min from now
        $sasToken = self::generateSasToken($serviceBusUrl, $serviceBusKeyName, $serviceBusSasKey, $expiry);

        if ($serviceBusUrl != null && $sasToken != null) {
            $client = new Client();
            try {

                $body = json_encode([
                    'topic' => $topic,
                    'subject' => $subject,
                    'type' => $type,
                    'data' => json_decode($content, true)
                ]);

                if ($alwaysBlob || self::shouldCreateBlob($content)) {

                    $blobFileName = self::createBlob($body);
                    if(!$blobFileName) {
                        return;
                    }

                    $content = json_encode([
                        'filename' => $blobFileName,
                        'url' => config('newbridge.blob_url')
                    ]);
                }

                $body = json_encode([
                    'topic' => $topic,
                    'subject' => $subject,
                    'type' => $type,
                    'data' => json_decode($content, true)
                ]);

                $response = $client->post($serviceBusUrl, [
                    'headers' => [
                        'Authorization' => $sasToken,
                        'Content-Type' => 'application/json;type=entry;charset=utf-8'
                    ],
                    'body' => $body
                ]);

                if ($response->getStatusCode() === 201) {
                    Log::info('ServiceBusHelper success', [
                        'topic' => $topic,
                        'subject' => $subject,
                        'type' => $type,
                        'content' => $content,
                        'serviceBusUrl' => $serviceBusUrl,
                    ]);
                    return;
                }

                Log::error('ServiceBusHelper error', [
                    'topic' => $topic,
                    'subject' => $subject,
                    'type' => $type,
                    'content' => $content,
                    'serviceBusUrl' => $serviceBusUrl,
                ]);

                throw new Exception('Failed to send event to service bus');

            } catch (GuzzleException $e) {
                report($e);
            }
        }
    }

    private static function generateSasToken(string $resourceUri, string $keyName, string $key, int $expiry): string
    {
        $encodedResourceUri = urlencode($resourceUri);
        $stringToSign = $encodedResourceUri . "\n" . $expiry;

        // Create HMAC-SHA256 signature
        $signature = base64_encode(hash_hmac('sha256', $stringToSign, $key, true));

        // Generate the SAS token
        return "SharedAccessSignature sr={$encodedResourceUri}&sig=" . urlencode($signature) . "&se={$expiry}&skn={$keyName}";
    }
}