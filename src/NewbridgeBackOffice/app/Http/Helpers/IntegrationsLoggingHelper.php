<?php

namespace NewbridgeWeb\Http\Helpers;

use Illuminate\Http\Client\Response;
use NewbridgeWeb\Repositories\Integrations\IntegrationLogs;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationSites;
use Psr\Http\Message\RequestInterface;
use Ramsey\Uuid\Uuid;

class IntegrationsLoggingHelper
{
    private string $event_uuid;
    private int $company_id;
    private int $site_num;
    private int $integration_id;
    private string $resource;
    private string $request;
    private string $description;
    private string $response;
    private string $exception;

    public function __construct()
    {
        $this->event_uuid = Uuid::uuid4();
    }

    public function setSiteIntegrations(SiteIntegrationSites $siteIntegrations): void
    {
        $this->company_id = $siteIntegrations->company_id;
        $this->site_num = $siteIntegrations->site_num;
        $this->integration_id = $siteIntegrations->integration_id;
    }

    public function setEmpty(): void
    {
        $this->company_id = 0;
        $this->site_num = 0;
        $this->integration_id = 0;
    }

    public function setIntegrationId(int $integration_id): void
    {
        $this->integration_id = $integration_id;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function setException(\Exception $e): void
    {
        $this->exception = $e->getMessage();
    }

    public function setRequest(RequestInterface $request): void
    {
        $this->request = $request->getMethod() . ' ';
        $uri = $request->getUri();
        $this->request .= $uri->getScheme() . '://' . $uri->getHost() . $uri->getPath();
        $headers = $request->getHeaders();
        $this->request .= ' headers:[ ';
        foreach ($headers as $key => $value) {
            $this->request .= $key . ': ' . join(',', $value) . '; ';
        }
        $this->request .= ']';
        $this->request .= ' body: { ' . $request->getBody() . ' } ';
    }

    public function setResponse(Response $response): void
    {
        $this->response = $response->body();
    }

    public function setResource(string $uri): void
    {
        $this->resource = $uri;
    }

    public function write(): void
    {
        IntegrationLogs::store(
            $this->event_uuid,
            !empty($this->company_id) ? $this->company_id : 0,
            !empty($this->site_num) ? $this->site_num : 0,
            !empty($this->integration_id) ? $this->integration_id : 0,
            $this->description,
            $this->resource ?? null,
            $this->request ?? null,
            $this->response ?? null,
            $this->exception ?? null
        );

        // clear
        $this->description = '';
        $this->resource = '';
        $this->request = '';
        $this->response = '';
        $this->exception = '';
    }
}
