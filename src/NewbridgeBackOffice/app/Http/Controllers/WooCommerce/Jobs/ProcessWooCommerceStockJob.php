<?php

namespace NewbridgeWeb\Http\Controllers\WooCommerce\Jobs;

use Automattic\WooCommerce\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;
use NewbridgeWeb\Repositories\Products;

class ProcessWooCommerceStockJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $company;
    public $integrationSettings;
    public $canRead;
    public $transaction;

    public function __construct($transaction, $company)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);

        $this->company = $company;
        $this->transaction = $transaction;

        $this->integrationSettings = IntegrationsHelper::get(4, $this->company->id);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        /**
         * do send something to woo-commerce, update the
         */
        $this->processStock();
    }

    private function processStock()
    {
        $product = Products::where('guid', $this->transaction->plu_product_guid)->first();

        try {
            $client = new Client($this->integrationSettings['settings']['store_url'], $this->integrationSettings['settings']['consumer_key'], $this->integrationSettings['settings']['consumer_secret']);
            $result = $client->get('products/'. $product->third_party_id);

            $data = [
                'product' => [
                    'stock_quantity' => ((int) $result['stock_quantity'] - 1)
                ]
            ];

            $result = $client->put('products/'. $product->third_party_id, $data);
        } catch(\Exception $e) {
            throw new \Exception($e);
        }

        return 'ok';
    }
}
