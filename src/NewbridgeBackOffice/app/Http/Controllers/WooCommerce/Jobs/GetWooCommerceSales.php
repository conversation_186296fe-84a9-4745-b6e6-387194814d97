<?php

namespace NewbridgeWeb\Http\Controllers\WooCommerce\Jobs;

use Automattic\WooCommerce\Client;

use Carbon\Carbon;
use DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;
use NewbridgeWeb\Repositories\Integrations\IntegrationSettings;
use NewbridgeWeb\Repositories\Products;


use NewbridgeWeb\Repositories\StockTransactions;

class GetWooCommerceSales implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $company;
    public $integrationSettings;
    public $canRead;
    public $transaction;

    public function __construct($company_id)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);

        $this->company = $company_id;

        $this->integrationSettings = IntegrationsHelper::get(4, $this->company);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        /**
         * do send something to woo-commerce, update the
         */
        $this->processStock();
    }

    private function processStock()
    {
        $data = [
            'status' => 'completed',
            'after' => Carbon::parse($this->integrationSettings['settings']['last_run_date'])->toDateTimeString()
        ];

        DB::beginTransaction();

        try {
            $client = new Client($this->integrationSettings['settings']['store_url'], $this->integrationSettings['settings']['consumer_key'], $this->integrationSettings['settings']['consumer_secret']);
            $result = $client->get('orders', $data);

            foreach ($result['orders'] as $order) {
            }

            $last_run = IntegrationSettings::where('setting_id', 'last_run_date')
                ->where('company_id', $this->company)
                ->where('integration_id', 4)
                ->first();
            $last_run->value = Carbon::now();
            $last_run->save();

            DB::commit();

            return 'ok';
        } catch (\Exception $e) {
            DB::rollback();

            throw new \Exception($e);

            abort(404);
        }
    }

    /**
     * @return bool
     * @throws \Exception
     */
    private function processProduct($soldProduct, $item, $order)
    {
        if ($soldProduct !== null) {
            if ($soldProduct->parent == null) {
                $data = [
                    'company_id' => $soldProduct->company_id,
                    'site_num' => $soldProduct->site_num,
                    'product_id' => $soldProduct->id,
                    'quantity' => $item['quantity'] * -1,//$refund === true ? abs((float)$this->detail['qty']) : (abs((float)$this->detail['qty'])*-1),
                    'summary_type' => 1,
                    'transaction_detail_id' => null,
                    'status' => 1,
                    'sku_guid' => $soldProduct->sku_guid,
                    'isTransaction' => 1,
                    'created_at' => Carbon::parse($order['completed_at'])
                ];

                $transaction = new StockTransactions();
                $transaction->fill($data);
                $transaction->save();
            } else {
                /**
                 * THIS IS CODE FOR A CHILD OF A CHILD?
                 */
                if ($soldProduct->parent->parent != null && $soldProduct->parent->parent->sku_guid != null) {
                    $data = [
                        'company_id' => $soldProduct->company_id,
                        'site_num' => $soldProduct->site_num,
                        'product_id' => $soldProduct->parent->parent->id,
                        // TODO: IN FUTURE THIS WILL BE Quantity * SKU_Quantity
                        'quantity' => ($soldProduct->sku_quantity * $item['quantity']) * -1,
                        'summary_type' => 1,
                        'transaction_detail_id' => null,
                        'status' => 1,
                        'sku_guid' => $soldProduct->parent->parent->sku_guid,
                        'isTransaction' => 1,
                        'created_at' => Carbon::parse($order['completed_at'])
                    ];

                    $transaction = new StockTransactions();
                    $transaction->fill($data);
                    $transaction->save();
                } else {
                    /**
                     * CHILD PRODUCT
                     */
                    if ($soldProduct->parent->sku_guid != null) {
                        if ($soldProduct['sku_quantity'] == null) {
                            return false;
                        }

                        $data = [
                            'company_id' => $soldProduct->company_id,
                            'site_num' => $soldProduct->site_num,
                            'product_id' => $soldProduct->parent->id,
                            // TODO: IN FUTURE THIS WILL BE Quantity * SKU_Quantity
                            'quantity' => ($soldProduct->sku_quantity * $item['quantity']) * -1,
                            'summary_type' => 1,
                            'transaction_detail_id' => null,
                            'status' => 1,
                            'sku_guid' => $soldProduct->parent->sku_guid,
                            'isTransaction' => 1,
                            'created_at' => Carbon::parse($order['completed_at'])
                        ];

                        $transaction = new StockTransactions();
                        $transaction->fill($data);
                        $transaction->save();
                    }
                }
            }
        }

        return true;
    }

    private function processRecipe($product, $item, $order)
    {
        $refund = $this->detail['command_type'] == 6 ? true : false;

        // get the product with the recipe
        $products = new Products();

        $soldProduct = $products->query()->where('guid', $product->guid)->with(['recipe' => function ($q) {
            $q->with(['ingredients' => function ($r) {
                $r->withTrashed();
            }]);
        }])->withTrashed()->first();

        for ($x = 0; $x < $soldProduct->recipe_qty; $x++) {
            foreach ($soldProduct->recipe->ingredients as $ingredient) {
                $ingredientProduct = Products::where('guid', $ingredient->product_guid)->first();

                if ($ingredientProduct) {
                    $data = [
                        'company_id' => $soldProduct->company_id,
                        'site_num' => $soldProduct->site_num,
                        'product_id' => $ingredientProduct['id'],
                        'quantity' => ($ingredient->quantity * $item['quantity']) * -1,
                        'summary_type' => 1,
                        'transaction_detail_id' => null,
                        'status' => 1,
                        'sku_guid' => $ingredientProduct['sku_guid'],
                        'isTransaction' => 1,
                        'created_at' => Carbon::parse($order['completed_at'])
                    ];

                    $transaction = new StockTransactions();
                    $transaction->fill($data);
                    $transaction->save();
                }
            }
        }
    }
}
