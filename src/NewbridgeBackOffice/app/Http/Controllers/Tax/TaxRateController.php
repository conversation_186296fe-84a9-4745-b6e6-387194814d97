<?php

namespace NewbridgeWeb\Http\Controllers\Tax;

use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use NewbridgeWeb\Repositories\Tax\TaxRuleLinks;
use NewbridgeWeb\Repositories\Tax\TaxRules;
use Ramsey\Uuid\Uuid;
use Yajra\Datatables\Datatables;

class TaxRateController extends Controller
{
    /**
     * @return View
     */
    public function index(): View
    {
        $taxrates = TaxRates::where('company_id', Auth::user()->company_id)->get();

        return view('modules.tax.rates.table', compact('taxrates'));
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables): JsonResponse
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);
            Taxrates::where('id', $k)->get()->each(function($model) use ($updates){
                $model->fill($updates);
                $model->save();
                \Event::dispatch(new UpdaterUpdateCrudEvent($model));
            });
        }

        return $this->getTaxrateData($dataTables);
    }

    /**
     * @param Request $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function editById(Request $request, Datatables $dataTables)
    {
        $input = $request->except('id');
        $id = $request->input('id');

        $taxrate = TaxRates::find($id);
        $taxrate->fill($input);
        $taxrate->received_from = 0;
        $taxrate->save();

        \Event::dispatch(new UpdaterUpdateCrudEvent($taxrate));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(Request $request)
    {
        $input = $request->input();

        $taxrate = new TaxRates();
        $taxrate->fill($input);
        $taxrate->company_id = Auth::user()->company_id;
        $taxrate->guid = Uuid::uuid4();
        $taxrate->received_at = Carbon::parse('1979-01-01 00:00:00');
        $taxrate->received_from = 0;
        $taxrate->save();

        \Event::dispatch(new UpdaterInsertCrudEvent($taxrate));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, Datatables $dataTables, $id)
    {
        $taxRate = TaxRates::find($id);

        $products = Products::where('tax_guid', $taxRate['guid'])->get();

        if (!$products->isEmpty()) {
            return response()->json(['status' => 'error', 'message' => 'Sorry! You cannot delete tax rates that are attached to products.'], 400);
        } else {

            $taxRate->delete();
            \Event::dispatch(new UpdaterUpdateCrudEvent($taxRate));

            $updatedTaxRules = TaxRuleLinks::whereRateGuid($taxRate->guid)->groupBy('rule_guid')->get();
            TaxRuleLinks::whereRateGuid($taxRate->guid)->delete();

            if(!empty($updatedTaxRules)) {
                foreach($updatedTaxRules as $updated) {
                    $taxRule = TaxRules::with('links')->where('guid', $updated->rule_guid);
                    \Event::dispatch(new UpdaterUpdateCrudEvent($taxRule));
                }
            }

            return response()->json(['status' => 'ok'], 200);
        }
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTaxrateData(Datatables $dataTables)
    {
        $model = TaxRates::where('company_id', Auth::user()->company_id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param Datatables $dataTables
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTaxrateDataSingle(Datatables $dataTables, $id)
    {
        $model = TaxRates::where('id', $id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function editModal($id = null)
    {
        $taxrate = TaxRates::find($id);

        return view('modules.tax.rates.includes.edit-modal', compact(['taxrate']));
    }
}
