<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Helpers\ActivityHelper;
use NewbridgeWeb\Http\Requests\BugReportDataTableRequests;
use NewbridgeWeb\Http\Requests\ProductDatatablesCreateRequest;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\LinkModifierGroups;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PriceLevelNames;
use NewbridgeWeb\Repositories\PrinterNames;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Recipes;
use NewbridgeWeb\Repositories\SKU;
use NewbridgeWeb\Repositories\StockSummary;
use NewbridgeWeb\Repositories\SubDepartment;
use NewbridgeWeb\Repositories\Suppliers;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use PDF;
use Ramsey\Uuid\Uuid;
use Yajra\Datatables\Datatables;

class ProductStockController extends Controller
{
    public $product_links = ['supplier', 'department', 'sku', 'subdepartment'];

    public function __construct()
    {
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $departments = Departments::getAllCompanyJson();
        $subdepartments = SubDepartment::getAllCompanyJson();
        $suppliers = Suppliers::getAllCompanyJson();
        $recipes = Recipes::getAllCompanyJson();
        $skus = SKU::getAllCompanyJson();
        $parents = Products::assignableProductsCompany(Auth::user()->company_id);
        $taxrates = TaxRates::where('company_id', Auth::user()->company_id)->get();

        /**
         * site specific data
         */
        $company = Company::find(Auth::user()->company_id);

        $modGroups = Commands::where('company_id', Auth::user()->company_id)->where('Command', 6)->get();

        return response()->view(
            'modules.products.datatables.product-table',
            compact(
                'company',
                'departments',
                'suppliers',
                'recipes',
                'skus',
                'modGroups',
                'parents',
                'subdepartments',
                'taxrates'
            )
        );
    }

    public function create()
    {
        $departments = Departments::where('company_id', Auth::user()->company_id)->get();
        $subdepartments = SubDepartment::where('company_id', Auth::user()->company_id)->get();
        $suppliers = Suppliers::where('company_id', Auth::user()->company_id)->get();
        $recipes = Recipes::where('company_id', Auth::user()->company_id)->get();
        $skus = SKU::where('company_id', Auth::user()->company_id)->get();
        $taxrates = TaxRates::where('company_id', Auth::user()->company_id)->get();
        $parents = Products::assignableProductsCompany(Auth::user()->company_id);
        $printers = PrinterNames::where('company_id', Auth::user()->company_id)->limit(6)->get();
        $sellingPriceNames = PriceLevelNames::where('company_id', Auth::user()->company_id)->limit(5)->get();
        $kp_categories = Commands::where('company_id', Auth::user()->company_id)->where('command', 113)->get();
        $companyDetails = Company::where('id', Auth::user()->company_id)->first()->toArray();


        if ($companyDetails['site_specific_products'] === 1) {
            $modGroups = Commands::where('company_id', Auth::user()->company_id)->where(
                'site_num',
                \Session::get('current_site')
            )->where('Command', 6)->get();
        } else {
            $modGroups = Commands::where('company_id', Auth::user()->company_id)->where('Command', 6)->get();
        }

        return view(
            'modules.products.create',
            compact(
                'companyDetails',
                'departments',
                'suppliers',
                'recipes',
                'skus',
                'modGroups',
                'parents',
                'subdepartments',
                'taxrates',
                'printers',
                'sellingPriceNames',
                'kp_categories'
            )
        );
    }

    /**
     * @param BugReportDataTableRequests $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function editProducts(BugReportDataTableRequests $request, Datatables $dataTables)
    {
        $input = $request->input();
        $action = $request->input('action');

        foreach ($input['data'] as $k => $v) {
            $product = Products::find($k);
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);

            if (isset($v['displayname'])) {
                // update the product
                PosButtons::where('displayname', $product->displayname)->where('company_id', $product->company_id)
                    ->get()
                    ->each(function ($model) use ($v) {
                        $model->update(['displayname' => $v['displayname'], 'updated_at' => Carbon::now()]);
                    });
                $buttons = PosButtons::with(['pos_button_style', 'lnk_buttonlinks', 'plu_product_page'])->where(
                    'displayname',
                    $v['displayname']
                )->where('company_id', $product->company_id)->get();

                $updates = array_merge($updates, ['short_desc' => $v['displayname']]);

                $this->QueueButtons($buttons);
            }

            Products::where('id', $k)->get()->each(function($model) use ($updates){
                $model->update($updates);
            });

            $product = Products::with('groups')->find($k);
            \Event::dispatch(new UpdaterUpdateCrudEvent($product));

            $data = Products::where('id', $k)->first();
        }

        return response()->json(['status' => 'ok', 'data' => $data], 200);
    }

    /**
     * @param BugReportDataTableRequests $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function editProduct(Request $request, Datatables $dataTables)
    {
        DB::beginTransaction();

        try {
            $input = $request->except(
                [
                    'current_stock',
                    'id',
                    'add-group',
                    'groups',
                    'stock',
                    'kitchen_printers',
                    'weighed_product',
                    'sub_product_name',
                    'sub_product_short_name',
                    'sub_product_price',
                    'sub_product_stock_quantity',
                    'ismanualweight',
                    'sub_product_barcode'
                ]
            );
            $stock = $request->input('stock');
            $id = $request->input('id');
            $groups = $request->input('groups');
            $groups = json_decode($groups);
            $kitchen_printers = $request->input('kitchen_printers');
            $weighed_product = $request->input('weighed_product');
            $manualweight = $request->input('ismanualweight');

            $sub_product_names = $request->input('sub_product_name');
            $sub_product_prices = $request->input('sub_product_price');
            $sub_product_stock_quantities = $request->input('sub_product_stock_quantity');
            $sub_product_barcodes = $request->input('sub_product_barcode');
            $product = Products::find($id);

            $originalName = $product->displayname;

            $productInfo = '<!DOCTYPE html> <html> 
          <head> 
            <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
          </head>
          <body>';

            $productInfo .= $input['productinfo'];
            $productInfo .= '</body></html>';

            $input['productinfo'] = $productInfo;

            $product->fill($input);

            $product->received_from = 0;
            $product->soldbyweight = $weighed_product;
            $product->ismanualweight = $manualweight;
            $product->updated_at = Carbon::now();

            if (!empty($kitchen_printers)) {
                $product->KP_Target = 0;

                foreach ($kitchen_printers as $printer) {
                    $product->KP_Target += $printer;
                }
            }

            $product->follow_course_separator = isset($input['follow_course_separator']) ? 1 : 0;

            if ($originalName != $product->displayname) {
                $buttons = PosButtons::where('displayname', $originalName)->where(
                    'company_id',
                    $product->company_id
                )->get()->each(function($model) use ($product){
                    $model->update(['displayname' => $product->displayname, 'updated_at' => Carbon::now()]);
                });
                $buttons = PosButtons::with(['pos_button_style', 'lnk_buttonlinks', 'plu_product_page'])->where(
                    'displayname',
                    $product->displayname
                )->where('company_id', $product->company_id)->get();

                $this->QueueButtons($buttons);
            }

            if ($input['barcode'] == '') {
                $product->barcode = $product->id;
            }

            $product->save();


            if (!empty($groups)) {
                LinkModifierGroups::where('plu_guid', $product->guid)->delete();

                foreach ($groups as $g) {
                    $groupLink = new LinkModifierGroups();
                    $groupLink->plu_guid = $product->guid;
                    $groupLink->position = $g->position;
                    $groupLink->received_at = '1970-01-01 00:00:00';
                    $groupLink->CommandUID = $g->guid;
                    $groupLink->save();
                }
            } else {
                LinkModifierGroups::where('plu_guid', $product->guid)->delete();
            }

            if (!empty($sub_product_names)) {
                foreach ($sub_product_names as $k => $sub_product) {
                    if ($sub_product != null) {
                        $exists = Products::where('guid', $k)->where('company_id', Auth::user()->company_id)->first();

                        if ($exists) {
                            $exists->displayname = $sub_product != '' ? $sub_product : $exists->displayname;
                            $exists->selling_price_1 = $sub_product_prices[$k] != '' ? $sub_product_prices[$k] : $exists->selling_price_1;
                            $exists->sku_quantity = $sub_product_stock_quantities[$k] != '' ? $sub_product_stock_quantities[$k] : $exists->sku_quantity;
                            $exists->barcode = $sub_product_barcodes[$k] != '' ? $sub_product_barcodes[$k] : $exists->barcode;
                            $exists->save();
                        } else {
                            if ($sub_product_prices[$k] != null
                                && $sub_product_stock_quantities[$k] != null) {
                                $sub_product = [
                                    'displayname' => $sub_product,
                                    'short_desc' => $sub_product,
                                    'barcode' => $sub_product_barcodes[$k],
                                    'sku_quantity' => $sub_product_stock_quantities[$k],
                                    'selling_price_1' => $sub_product_prices[$k]
                                ];

                                $this->createSubProduct($product, $sub_product);
                            } else {
                                DB::rollBack();

                                return response()->json(
                                    [
                                        'status' => 'error',
                                        'Please make sure that all sub product information has been filled in. Any sub product with a name must have all other information also.'
                                    ],
                                    400
                                );
                            }
                        }
                    }
                }
            }

            DB::commit();

            $product = Products::with('groups')->find($id);
            \Event::dispatch(new UpdaterUpdateCrudEvent($product));

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['status' => 'error', $e->getMessage()], 400);
        }
    }

    /**
     * @param BugReportDataTableRequests $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function massEditProducts(Request $request, $ids)
    {
        $data = $request->except(['current_stock', 'ids', 'add-group', 'groups', 'kitchen_printers']);

        $kitchen_printers = $request->input('kitchen_printers');
        $groups = $request->input('groups');

        $groups = json_decode($groups);

        $ids = json_decode(base64_decode($ids));

        foreach ($ids as $id) {
            $product = Products::find($id);
            $product->received_from = 0;

            $product->fill($data);

            if (!empty($kitchen_printers)) {
                $product->KP_Target = 0;
                foreach ($kitchen_printers as $printer) {
                    $product->KP_Target += $printer;
                }
            }

            $product->save();

            if (!empty($groups) && $groups != null) {
                LinkModifierGroups::where('plu_guid', $product->guid)->delete();
                foreach ($groups as $g) {
                    $groupLink = new LinkModifierGroups();
                    $groupLink->plu_guid = $product->guid;
                    $groupLink->received_at = '1970-01-01 00:00:00';
                    $groupLink->CommandUID = $g->guid;
                    $groupLink->position = $g->position;
                    $groupLink->save();
                }
            }

            $product = Products::with('groups')->find($id);
            \Event::dispatch(new UpdaterUpdateCrudEvent($product));
        }

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param ProductDatatablesCreateRequest $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function addProducts(Request $request, Datatables $dataTables)
    {
        $company = Company::find(Auth::user()->company_id);

        DB::beginTransaction();

        try {
            $input = $request->except(
                [
                    'current_stock',
                    'add-group',
                    'groups',
                    'stock',
                    'kitchen_printers',
                    'create_half',
                    'create_double',
                    'weighed_product',
                    'sub_product_name',
                    'sub_product_short_name',
                    'sub_product_price',
                    'sub_product_stock_quantity',
                    'sub_product_barcode'
                ]
            );
            $groups = $request->input('groups');
            $stock = $request->input('stock');

            // new with ordering
            $groups = json_decode($groups);

            $kitchen_printers = $request->input('kitchen_printers');
            $weighed_product = $request->input('weighed_product');

            $sub_product_names = $request->input('sub_product_name');
            $sub_product_prices = $request->input('sub_product_price');
            $sub_product_barcode = $request->input('sub_product_barcode');
            $sub_product_stock_quantities = $request->input('sub_product_stock_quantity');

            $uuid = Uuid::uuid4();

            $product = new Products();
            $product->company_id = Auth::user()->company_id;
            $product->site_num = $company->site_specific_products === 1 ? \Session::get('current_site') : 0;
            $product->guid = $uuid;
            $product->CommandUID = $uuid;
            $product->Command = 3;
            $product->received_at = Carbon::parse('1979-01-01 00:00:00');
            $product->received_from = 0;
            $product->soldbyweight = $weighed_product ? 1 : 0;

            $product->follow_course_separator = $request->input('follow_course_separator') !== null ? 1 : 0;

            $productInfo = '<!DOCTYPE html> <html> 
          <head> 
            <meta http-equiv="X-UA-Compatible" content="IE=edge" /> 
          </head>
          <body>';

            $productInfo .= $input['productinfo'];
            $productInfo .= '</body></html>';

            $input['productinfo'] = $productInfo;

            $product->fill($input);

            if ($input['recipe_guid'] != '') {
                /**
                 * Calculate the cost price of the product based on the
                 * cost price of the ingredients.
                 */
                $recipe = Recipes::with([
                    'ingredients' => function ($q) {
                        $q->with('product');
                    }
                ])->where('guid', $input['recipe_guid'])->first();

                $cost = 0;

                foreach ($recipe->ingredients as $i) {
                    $cost += $i->product->costprice * $i->quantity;
                }

                $product->costprice = $cost;
            }

            if ($product->selling_price_2 == null) {
                $product->selling_price_2 = $product->selling_price_1;
            }
            if ($product->selling_price_3 == null) {
                $product->selling_price_3 = $product->selling_price_1;
            }
            if ($product->selling_price_4 == null) {
                $product->selling_price_4 = $product->selling_price_1;
            }
            if ($product->selling_price_5 == null) {
                $product->selling_price_5 = $product->selling_price_1;
            }

            if (!empty($kitchen_printers)) {
                $product->KP_Target = 0;
                foreach ($kitchen_printers as $printer) {
                    $product->KP_Target += $printer;
                }
            }

            if (!empty($groups)) {
                foreach ($groups as $g) {
                    $groupLink = new LinkModifierGroups();
                    $groupLink->plu_guid = $product->guid;
                    $groupLink->CommandUID = $g->guid;
                    $groupLink->position = $g->position;
                    $groupLink->received_at = Carbon::parse('1979-01-01 00:00:00');
                    $groupLink->save();
                }
            }

            $product->save();

            if ($input['barcode'] == '') {
                $product->barcode = $product->id;
            }

            $product->save();

            $product = Products::with('groups')->find($product->id);
            \Event::dispatch(new UpdaterInsertCrudEvent($product));

            if (!empty($sub_product_names)) {
                foreach ($sub_product_names as $k => $sub_product) {
                    if ($sub_product != null) {
                        if ($sub_product_prices[$k] != null
                            && $sub_product_stock_quantities[$k] != null) {
                            $sub_product = [
                                'displayname' => $sub_product,
                                'short_description' => $sub_product,
                                'barcode' => $sub_product_barcode[$k],
                                'sku_quantity' => $sub_product_stock_quantities[$k],
                                'selling_price_1' => $sub_product_prices[$k],
                                'selling_price_2' => $sub_product_prices[$k],
                                'selling_price_3' => $sub_product_prices[$k],
                                'selling_price_4' => $sub_product_prices[$k],
                                'selling_price_5' => $sub_product_prices[$k]
                            ];

                            $this->createSubProduct($product, $sub_product);
                        } else {
                            DB::rollBack();

                            return response()->json(
                                [
                                    'status' => 'error',
                                    'message' => 'Please make sure that all sub product information has been filled in. Any sub product with a name must have all other information also.'
                                ],
                                400
                            );
                        }
                    }
                }
            }

            DB::commit();

            return response()->json(['status' => 'success', 'message' => 'Product Created Successfully!']);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 400);
        }
    }

    private function createSubProduct($product, $sub_product)
    {
        $product = $product->toArray();
        $groups = isset($product['groups']) ? $product['groups'] : [];
        unset($product['gross_profit']);
        unset($product['is_wasteable']);
        unset($product['is_modifier_text']);
        unset($product['groups']);
        unset($product['id']);

        $newProduct = new Products();
        $newProduct->fill($product);
        $newProduct->short_desc = $sub_product['displayname'];
        $newProduct->displayname = $sub_product['displayname'];
        $newProduct->selling_price_1 = $sub_product['selling_price_1'];
        $newProduct->selling_price_2 = $sub_product['selling_price_1'];
        $newProduct->selling_price_3 = $sub_product['selling_price_1'];
        $newProduct->selling_price_4 = $sub_product['selling_price_1'];
        $newProduct->selling_price_5 = $sub_product['selling_price_1'];
        $newProduct->guid = Uuid::uuid4();
        $newProduct->CommandUID = $newProduct->guid;
        $newProduct->plu_parent_guid = $product['guid'];
        $newProduct->sku_guid = null;
        $newProduct->is_modifier = $product['is_modifier'] == true ? 1 : 0;
        $newProduct->sku_quantity = $sub_product['sku_quantity'];
        $newProduct->barcode = $sub_product['barcode'] != '' ? $sub_product['barcode'] : null;

        $newProduct->save();

        if ($newProduct->barcode == '') {
            $newProduct->barcode = $newProduct->id;
        }

        $newProduct->save();

        foreach ($groups as $group) {
            $groupLink = new LinkModifierGroups();
            $groupLink->plu_guid = $newProduct->guid;
            $groupLink->CommandUID = $group['CommandUID'];
            $groupLink->received_at = Carbon::parse('1970-01-01 00:00:00');
            $groupLink->save();
        }

        $newProduct = Products::with('groups')->find($newProduct->id);
        \Event::dispatch(new UpdaterInsertCrudEvent($newProduct));

        return $newProduct;
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteProduct(Request $request, Datatables $dataTables, $id)
    {
        $product = Products::find($id);

        $product->links()->delete();
        $product->recipeLinks()->delete();
        $product->groups()->delete();
        $product->children()->plu_parent_guid = null;
        $product->save();

        $product->delete();

        \Event::dispatch(new UpdaterUpdateCrudEvent($product));

        return response()->json(["status" => 'ok'], 200);
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProductData(Datatables $dataTables)
    {
        $model = Products::forCurrentCompanyDataTables();

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param Datatables $dataTables
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function getProductDataSingle(Datatables $dataTables, $id)
    {
        $model = Products::forCurrentCompanyById($id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function getProductsByDepartment($guid)
    {
        $model = Products::forCurrentCompanyDataTables()->where('department_guid', $guid)->get();

        return response()->json($model, 200);
    }

    public function getProductsBySubDepartment($guid)
    {
        $model = Products::forCurrentCompanyDataTables()->where('sub_department_guid', $guid)->get();

        return response()->json($model, 200);
    }

    public function editProductModal($id)
    {
        $departments = Departments::where('company_id', Auth::user()->company_id)->get();
        $subdepartments = SubDepartment::where('company_id', Auth::user()->company_id)->get();
        $suppliers = Suppliers::where('company_id', Auth::user()->company_id)->get();
        $recipes = Recipes::where('company_id', Auth::user()->company_id)->get();
        $skus = SKU::where('company_id', Auth::user()->company_id)->get();
        $taxrates = TaxRates::where('company_id', Auth::user()->company_id)->get();
        $parents = Products::assignableProductsCompany(Auth::user()->company_id);
        $product = Products::forCurrentCompanyById($id)->first();
        $printers = PrinterNames::where('company_id', Auth::user()->company_id)->limit(6)->get();

        $linkedGroups = LinkModifierGroups::where('plu_guid', $product->guid)->with('command')->get();
        $sellingPriceNames = PriceLevelNames::where('company_id', Auth::user()->company_id)->limit(5)->get();
        $kp_categories = Commands::where('company_id', Auth::user()->company_id)->where('command', 113)->get();

        $companyDetails = Company::where('id', Auth::user()->company_id)->first()->toArray();
        if ($companyDetails['site_specific_products'] === 1) {
            $modGroups = Commands::where('company_id', Auth::user()->company_id)->where(
                'site_num',
                \Session::get('current_site')
            )->where('Command', 6)->get();
        } else {
            $modGroups = Commands::where('company_id', Auth::user()->company_id)->where('Command', 6)->get();
        }

        return view(
            'modules.products.edit',
            compact(
                'companyDetails',
                'product',
                'departments',
                'subdepartments',
                'skus',
                'recipes',
                'suppliers',
                'modGroups',
                'linkedGroups',
                'parents',
                'subdepartments',
                'taxrates',
                'printers',
                'sellingPriceNames',
                'kp_categories'
            )
        );
    }

    public function massEdit(Request $request, $string)
    {
        $ids = json_decode(base64_decode($string));

        $departments = Departments::where('company_id', Auth::user()->company_id)->get();
        $subdepartments = SubDepartment::where('company_id', Auth::user()->company_id)->get();
        $suppliers = Suppliers::where('company_id', Auth::user()->company_id)->get();
        $recipes = Recipes::where('company_id', Auth::user()->company_id)->get();
        $skus = SKU::where('company_id', Auth::user()->company_id)->get();
        $taxrates = TaxRates::where('company_id', Auth::user()->company_id)->get();
        $parents = Products::assignableProductsCompany(Auth::user()->company_id);
        $printers = PrinterNames::where('company_id', Auth::user()->company_id)->limit(6)->get();
        $sellingPriceNames = PriceLevelNames::where('company_id', Auth::user()->company_id)->limit(5)->get();
        $kp_categories = Commands::where('company_id', Auth::user()->company_id)->where('command', 113)->get();

        $products = Products::whereIn('id', $ids)->get();

        $companyDetails = Company::where('id', Auth::user()->company_id)->first()->toArray();

        if ($companyDetails['site_specific_products'] === 1) {
            $modGroups = Commands::where('company_id', Auth::user()->company_id)->where(
                'site_num',
                \Session::get('current_site')
            )->where('Command', 6)->get();
        } else {
            $modGroups = Commands::where('company_id', Auth::user()->company_id)->where('Command', 6)->get();
        }

        return view(
            'modules.products.mass-edit',
            compact(
                'products',
                'departments',
                'subdepartments',
                'skus',
                'recipes',
                'suppliers',
                'modGroups',
                'parents',
                'ids',
                'taxrates',
                'printers',
                'sellingPriceNames',
                'kp_categories'
            )
        );
    }

    public function printAmountModal($ids)
    {
        $products = json_decode(base64_decode($ids));

        $productsModal = Products::whereIn('id', $products)->get();

        return view('modules.products.includes.print-amount', compact('productsModal'));
    }

    //    public function printBarcodes(Request $request, $ids = null, $redis = true)
    //    {
    //
    //        if($redis) {
    //            $input = $request->input();
    //            $products = json_decode(base64_decode($ids));
    //        }
    //
    //        $barcodes = Products::whereIn('id', $products)->get()->toArray();
    //
    //
    //        $file = PDF::loadView('modules.products.barcode-sheet', ['barcodes' => $barcodes])->setPaper('a4', 'portrait');
    //
    //        return $file->download();
    //    }

    public function printBarcodes()
    {
        $company = $company = Auth::user()->company_id;
        $data = Redis::get('barcodes' . $company);
        $data = json_decode($data, true);


        $barcodes = [];

        foreach ($data['labels'] as $label) {
            $product = Products::find($label['id']);
            $barcodes[] = [
                'id' => $label['id'],
                'barcode' => $product['barcode'],
                'quantity' => $label['quantity'],
                'displayname' => $product['displayname'],
                'price' => $product['selling_price_1']
            ];
        }


        return view('modules.products.barcode-sheets.' . $data['size'], compact('barcodes'));

        //        $file = PDF::loadView('modules.products.barcode-sheets.'.$data['size'], ['barcodes' => $barcodes])->setPaper('a4', 'portrait');

        return $file->download();
    }

    public function barcodesToRedis(Request $request)
    {
        $data = $request->input('data');

        $company = Auth::user()->company_id;
        Redis::set('barcodes' . $company, json_encode($data));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * Update Queue Buttons
     *
     * @param $buttons
     */
    private function QueueButtons($buttons)
    {
        if (!empty($buttons) && !$buttons->isEmpty()) {
            foreach ($buttons as $button) {
                \Event::dispatch(new UpdaterUpdateCrudEvent($button));
            }
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function queue(Request $request)
    {
        if (is_array($request->input('ids')) && count($request->input('ids')) > 0) {
            $products = Products::with('groups')->where('company_id', Auth::user()->company_id)->whereIn(
                'id',
                $request->input('ids')
            )->get();
        } else {
            $products = Products::with('groups')->where('company_id', Auth::user()->company_id)->get();
        }

        foreach ($products as $product) {
            $product->received_from = 0;
            $product->save();
            \Event::dispatch(new UpdaterUpdateCrudEvent($product));
        }

        return response()->json(['status' => 'success', 'message' => 'Products successfully sent to all tills!']);
    }

    public function getListModifiers($id)
    {
        $product = Products::find($id);

        $modifiers = LinkModifierGroups::where('plu_guid', $product->guid)->get()->pluck('CommandUID');

        $links = Commands::select('id', 'displayname')->whereIn('CommandUID', $modifiers)->get();

        return $links;
    }

    public function addChildRow()
    {
        $view = view('modules.products.includes.sub-product-row')->render();

        return response()->json(['html' => $view], 200);
    }

    public function activity($id)
    {
        $activity = ActivityHelper::getActivity($id, 'Products');

        return response()->json(
            ['html' => view('modules.products.includes.view-activity', compact('activity'))->render()],
            200
        );
    }
}
