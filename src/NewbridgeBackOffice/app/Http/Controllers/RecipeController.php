<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Helpers\RecipeCostHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\RecipeLinks;
use NewbridgeWeb\Repositories\Recipes;
use Ramsey\Uuid\Uuid;
use Yajra\Datatables\Datatables;

class RecipeController extends Controller
{
    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        $departments = Departments::with(['products' => function ($q) {
            $q->whereIn('site_num', [0, Session::get('current_site')]);
        }])->where('company_id', Auth::user()->company_id)->get();

        $company = Company::find(Auth::user()->company_id);

        return view('modules.recipes.datatables.table', compact('departments', 'company'));
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        $model = Recipes::where('company_id', Auth::user()->company_id)->whereIn('site_num', [0, Session::get('current_site')]);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getCreate()
    {
        $departments = Departments::with(['products' => function ($q) {
            $q->whereIn('site_num', [0, Session::get('current_site')])
                ->whereNotNull('sku_guid');
        }])->where('company_id', Auth::user()->company_id)->get();

        $recipes = Recipes::where('company_id', Auth::user()->company_id)->whereIn('site_num', [0, Session::get('current_site')])->get();

        return view('modules.recipes.create', compact('departments', 'recipes'));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $input = $request->input('name');

        $products = $request->input('product');
        $quantities = $request->input('quantity');

        $company = Company::find(Auth::user()->company_id);

        $recipe = new Recipes();
        $recipe->name = $input;
        $recipe->company_id = Auth::user()->company_id;
        $recipe->site_num = $company->site_specific_products == 1 ? Session::get('current_site') : 0;
        $recipe->guid = Uuid::uuid4();
        $recipe->received_from = 0;
        $recipe->received_at = Carbon::parse('1979-01-01 00:00:00');
        $recipe->save();

        if (!empty($products) && !empty($quantities)) {
            foreach ($products as $k => $v) {
                $link = new RecipeLinks();
                $link->product_guid = $products[$k];
                $link->quantity = $quantities[$k];
                $link->recipe_guid = $recipe->guid;
                $link->received_at = Carbon::parse('1979-01-01 00:00:00');
                $link->save();
            }
        }

        $cost = RecipeCostHelper::calculateCostPriceRecipe($recipe->id);

        $recipe = Recipes::find($recipe->id);
        $recipe->cost = $cost;
        $recipe->save();

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {

            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);

            Recipes::where('id', $k)->get()->each(function($model) use ($updates){
                $model->update($updates);
            });

            $recipe = Recipes::find($k);
            \Event::dispatch(new UpdaterUpdateCrudEvent($recipe));
        }

        return $this->data($dataTables);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function editSingle(Request $request)
    {
        $input = $request->except('id');
        $id = $request->input('id');

        $company = Company::find(Auth::user()->company_id);

        $products = $request->input('product');
        $quantities = $request->input('quantity');

        $recipe = Recipes::find($id);
        $recipe->name = $input['name'];
        $recipe->site_num = $company->site_specific_products == 1 ? Session::get('current_site') : 0;
        $recipe->received_from = 0;
        $recipe->save();

        $cost = 0;

        if (!empty($products) && !empty($quantities)) {
            $ingredients = RecipeLinks::where('recipe_guid', $recipe->guid)->forceDelete();

            foreach ($products as $k => $v) {
                $product = Products::where('guid', $products[$k])->first();
                if ($product != null) {
                    $cost += $product->costprice * $quantities[$k];

                    $link = new RecipeLinks();
                    $link->product_guid = $products[$k];
                    $link->quantity = $quantities[$k];
                    $link->recipe_guid = $recipe->guid;
                    $link->save();
                }

                // update parent recipe cost
                $recipeCheck = Recipes::where('guid', $products[$k])->with('ingredients')->first();

                if ($recipeCheck != null) {
                    $link = new RecipeLinks();
                    $link->product_guid = $products[$k];
                    $link->quantity = $quantities[$k];
                    $link->recipe_guid = $recipe->guid;
                    $link->save();

                    $recipeCheck->cost = RecipeCostHelper::calculateCostPriceRecipe($recipeCheck->id);
                }
            }
        }

        $recipeCost = RecipeCostHelper::calculateCostPriceRecipe($recipe->id);
        $recipe->cost = $recipeCost;
        $recipe->save();

        // find and update cost price of linked products
        $products = Products::where('recipe_guid', $recipe->guid)->get();

        foreach ($products as $product) {
            $product->costprice = $recipeCost * $product->recipe_qty;
            $product->save();
        }

        return response()->json(['status' => 'ok', 'data' => $recipe], 200);
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getEdit($id = null)
    {
        $recipe = Recipes::with(['ingredients' => function ($q) {
            $q->with(['product']);
        }])->find($id);

        $departments = Departments::with(['products' => function ($q) {
            $q->whereIn('site_num', [0, Session::get('current_site')])
                ->whereNotNull('sku_guid');
        }])->where('company_id', Auth::user()->company_id)->get();

        $recipes = Recipes::where('company_id', Auth::user()->company_id)->whereIn('site_num', [0, Session::get('current_site')])->get();

        return view('modules.recipes.edit', compact('recipe', 'departments', 'recipes'));
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function editModal($id = null)
    {
        $recipe = Recipes::with(['ingredients' => function ($q) {
            $q->with(['product']);
        }])->find($id);

        $departments = Departments::with(['products' => function ($q) {
            $q->whereIn('site_num', [0, Session::get('current_site')]);
        }])->where('company_id', Auth::user()->company_id)->get();

        return view('modules.recipes.includes.edit-modal', compact('recipe', 'departments'));
    }

    // delete one modifier group based on input('id')
    public function delete(Request $request, $id)
    {
        $subDepartment = Recipes::find($id);
        $subDepartment->delete();

        return response()->json(['status' => 'success', 'message' => 'Recipe deleted successfully!']);
    }
}
