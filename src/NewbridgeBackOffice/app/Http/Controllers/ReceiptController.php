<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\DefaultReceiptLayouts;
use NewbridgeWeb\Repositories\ReceiptLayouts;
use NewbridgeWeb\Repositories\ReceiptTypes;
use NewbridgeWeb\Repositories\Sites;
use Ramsey\Uuid\Uuid;
use Ya<PERSON>ra\Datatables\Datatables;

class ReceiptController extends Controller
{
    public function index()
    {
        $sites = Sites::where('company_id', Auth::user()->company_id)->get();

        return view('modules.receipts.site-select', compact('sites'));
    }

    public function table()
    {
        $site_num = \Session::get('current_site');

        return view('modules.receipts.datatables.table', compact('site_num'));
    }

    public function data(Datatables $dataTables)
    {
        $model = ReceiptTypes::whereNotNull('id');

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param null $id
     */
    public function design($id, $site_num)
    {
        $company_id = Auth::user()->company_id;
        $receipt = ReceiptTypes::with(['receipt_layout' => function ($q) use ($company_id, $site_num) {
            $q->where('company_id', $company_id)->where('site_num', $site_num)->where('printer_size', 0);
        }])->find($id);

        $receipt->isA4Available = ReceiptTypes::whereHas('receipt_layout', function ($q) use ($company_id, $site_num) {
            $q->where('company_id', $company_id)->where('site_num', $site_num)->where('printer_size', 2);
        })->where('id', $id)->count();


        foreach ($receipt->receipt_layout as $k => $rl) {
            if ($rl->item_type == 99 && $rl->is2column !== 'false') {
                unset($receipt->receipt_layout[$k]);
            }
        }

        return view('modules.receipts.design', compact('receipt'));
    }

    public function postDesign(Request $request, $id, $site_num)
    {
        $layout = $request->input();

        DB::beginTransaction();

        try {
            ReceiptLayouts::where('receipt_type_id', $id)->where('company_id', Auth::user()->company_id)->where('site_num', $site_num)->forceDelete();

            $lastPosition = null;

            foreach ($layout['data'] as $k => $line) {
                if ($line['css_class'] == '2-column' && $lastPosition != $line['print_order']) {
                    $line['alignment'] = 0;
                }
                if ($line['css_class'] == '2-column' && $lastPosition == $line['print_order']) {
                    $line['alignment'] = 2;
                }

                $this->createLine($line, $layout['data'], $k, $site_num, $id);

                $lastPosition = $line['print_order'];
            }

            if ((int) $layout['also_a4'] === 1) {
                foreach ($layout['data'] as $k => $line) {
                    $line['printer_size'] = 2;

                    $this->createLine($line, $layout['data'], $k, $site_num, $id);
                }
            }


            DB::commit();

            return response()->json(['success'], 200);
        } catch(\Exception $e) {
            DB::rollback();
            report($e);

            return response()->json(['error', $e], 500);
        }
    }

    public function post()
    {
        // create based on the JS provided by the design form
    }

    public function put()
    {
        // delete the rows and re-create as provided by the redesign form
    }

    private function createLine($line, $data, $k, $site_num, $id)
    {
        $newLine = new ReceiptLayouts();
        $newLine->company_id = Auth::user()->company_id;
        $newLine->receipt_type_id = $id;
        $newLine->guid = Uuid::uuid4();
        $newLine->fill($line);
        $newLine->site_num = $site_num;
        $newLine->font_name = str_replace('-', ' ', $line['font_name']);
        $newLine->font_size = str_replace('size', '', $line['font_size']);
        $newLine->font_style = $line['font_style'] == 'normal' ? 0 : 3;
        // TODO: Sort Alignment on 2 column row if not already done, needs testing.

        if ($line['is2column'] != 'false') {
            $newLine->alignment = 2;

            $this->createTitleLine($line, $site_num, $id);
        } elseif ($newLine['css_class'] != '2-column') {
            if ($line['css_class'] == '1-column-left') {
                $newLine->alignment = 0;
            } elseif ($line['css_class'] == '1-column-right') {
                $newLine->alignment = 2;
            } elseif ($line['css_class'] == '1-column-center') {
                $newLine->alignment = 1;
            }
        }

        if ($id == 1) {
            $newLine->customer_receipt_print = true;
        }
        if ($id == 2) {
            $newLine->kitchen_receipt_print = true;
        }
        if ($id == 3) {
            $newLine->x_report = true;
        }
        if ($id == 4) {
            $newLine->z_report = true;
        }
        if ($id == 5) {
            $newLine->customer_bill_print = true;
        }
        if ($id == 6) {
            $newLine->gift_card_print = true;
        }
        if ($id == 7) {
            $newLine->clock_inout_print = true;
        }

        if ($newLine->receipt_text == null || $newLine->receipt_text == '') {
            $newLine->receipt_text = '          ';
        }

        $newLine->save();

        \Event::dispatch(new UpdaterInsertCrudEvent($newLine));
    }

    private function createTitleLine($line, $site_num, $id)
    {
        $newLine = new ReceiptLayouts();
        $newLine->company_id = Auth::user()->company_id;
        $newLine->receipt_type_id = $id;
        $newLine->guid = Uuid::uuid4();
        $newLine->fill($line);
        $newLine->item_type = 99;
        $newLine->receipt_text = $line['is2column'];
        $newLine->function = !in_array($line['function'], [ '=SubTotal', '=Total', '=Change', '=Terminal', '=Date', '=Time', '=Employee', '=TotalTransactions', '=Functions', '=From', '=To', '=MembershipNo']) ? str_replace('=', '>', $line['function']) : null;
        $newLine->site_num = $site_num;
        $newLine->font_name = str_replace('-', ' ', $line['font_name']);
        $newLine->font_size = str_replace('size', '', $line['font_size']);
        $newLine->font_style = $line['font_style'] == 'normal' ? 0 : 3;

        $newLine->alignment = 0;

        if ($id == 1) {
            $newLine->customer_receipt_print = true;
        }
        if ($id == 2) {
            $newLine->kitchen_receipt_print = true;
        }
        if ($id == 3) {
            $newLine->x_report = true;
        }
        if ($id == 4) {
            $newLine->z_report = true;
        }
        if ($id == 5) {
            $newLine->customer_bill_print = true;
        }
        if ($id == 6) {
            $newLine->gift_card_print = true;
        }

        $newLine->save();

        \Event::dispatch(new UpdaterInsertCrudEvent($newLine));
    }

    public static function generateDefaultReceipts($company_id, $site_num)
    {
        $defaults = DefaultReceiptLayouts::all();
        $company = Company::find($company_id);

        foreach ($defaults as $d) {
            unset($d['id']);
            $receipt = new ReceiptLayouts();
            $receipt->fill($d->toArray());
            $receipt->company_id = $company_id;
            $receipt->site_num = $site_num;

            if ($receipt->receipt_text == '#CompanyName') {
                $receipt->receipt_text = ucfirst($company->company_name);
            }

            if ($receipt->receipt_text == '#Address') {
                $receipt->receipt_text = ucfirst($company->address_line1.', '.$company->address_line2);
            }

            if ($receipt->receipt_text == '#Postcode') {
                $receipt->receipt_text = strtoupper($company->postcode);
            }

            if ($receipt->receipt_text == '#VATNo') {
                $receipt->receipt_text = 'TAX No. '.strtoupper($company->vat_number);
            }

            $receipt->created_at = Carbon::now();
            $receipt->updated_at = Carbon::now();
            $receipt->guid = Uuid::uuid4();
            $receipt->save();

            \Event::dispatch(new UpdaterInsertCrudEvent($receipt));
        }
    }
}
