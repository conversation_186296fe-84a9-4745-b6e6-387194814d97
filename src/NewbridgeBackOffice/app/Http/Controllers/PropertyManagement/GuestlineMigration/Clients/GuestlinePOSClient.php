<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\Clients;

use GuzzleHttp\Client;
use NewbridgeWeb\Http\Controllers\Controller;

class GuestlinePOSClient extends Controller
{
    public string $apiKey;
    public string $siteID;
    public string $tenantID;
    public string $baseUrl;

    public function __construct(array $attributes = [])
    {
        $this->apiKey = $attributes['api_key'];
        $this->siteID = $attributes['site_id'];
        $this->tenantID = $attributes['tenant_id'];
        $this->baseUrl = config('guestline.pos_url').$this->tenantID.'/'.$this->siteID.'/';
    }

    public function getTaxRules()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'TaxRules', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getCategories()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Categories', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return ['status' => 'error', 'message' => $e->getMessage(), 'code' => $e->getCode()];
        }

        return json_decode($response->getBody());
    }

    public function getChargeCodes()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'ChargeCodes', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getPlus()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'pluItems', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getModifierMenuLists()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'ModifierMenuLists', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getModifierSubMenus()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'ModifierSubMenus', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getModifiers()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Modifiers', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getBarScreenTabNames()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'BarScreenTabNames', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getTableRoomNames()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'TableRoomNames', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @param false $tabmenus
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMatrices($tabmenus = false)
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Matrices', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);

            $matrices = json_decode($response->getBody(), true);

            $data = [];
            foreach ($matrices as $k => $matrix) {
                if (!$tabmenus) {
                    $matrices[$k]['xmlData'] = json_decode(json_encode(simplexml_load_string($matrix['xmlData'])), true);
                    $data = json_decode(json_encode($matrices), true);
                } else {
                    if (strtolower($matrix['name']) == 'tabmenus') {
                        $matrix['xmlData'] = json_decode(json_encode(simplexml_load_string($matrix['xmlData'])), true);
                        $data = json_decode(json_encode($matrix), true);
                    }
                }
            }

            return response()->json($data);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }
    }

    public function getButtonStyles()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'MatrixThemes', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);

            $themes = json_decode($response->getBody(), true);

            $data = [];
            foreach ($themes as $k => $matrix) {
                $xmlData = json_decode(json_encode(simplexml_load_string($matrix['xmlData'])), true);

                foreach ($xmlData['themeButtonStyles']['themeButtonStyle'] as $style) {
                    $data[$style['@attributes']['id']] = $style['@attributes'];
                }
            }

            return response()->json($data);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }
    }

    public function getCourses()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Courses', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getPayTypes()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'PayTypes', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getUsers()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Users', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getRevenueCentres()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'RevenueCentres', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getTerminals()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Terminals', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getServiceAreas()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'ServiceAreas', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getTerminalLinkedToServiceAreas()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'TerminalLinkedToServiceAreas', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    /**
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function getDayParts()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Shifts', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody());
    }

    public function getRezLynxMappings()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'integrations/rezlynx', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }

    public function getReasons()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Reasons', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }

    public function getTerminalSettings()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Reasons', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }

    public function getPickLists()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'PickLists', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }

    public function getPrintHosts()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'PrintHosts', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }

    public function getPrintServices()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'PrintServices', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }

    public function getPrinters()
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'Printers', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }

    public function getTerminalPrinterSettings($terminal)
    {
        try {
            $client = new Client();
            $response = $client->get($this->baseUrl . 'terminals/'.$terminal.'/printersettings', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'ApiKey' => $this->apiKey
                ]
            ]);
        } catch(\Exception $e) {
            report($e);

            return response()->json(['status' => 'error', 'message' => $e->getMessage()], $e->getCode());
        }

        return json_decode($response->getBody(), true);
    }
}
