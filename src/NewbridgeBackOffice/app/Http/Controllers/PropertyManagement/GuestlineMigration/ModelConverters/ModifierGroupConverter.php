<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Company;
use Ramsey\Uuid\Uuid;

class ModifierGroupConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results;
    public array $data;

    /**
     * DepartmentConverter constructor.
     *
     * Converts a GL ChargeCode into a Department for Newbridge to Ingest - we will also consume any GUID's into
     * our structure
     *
     * @param $data
     * @param $company_id
     * @param $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            report($e);

            return null;
        }
    }

    /**
     * @throws \Exception
     */
    private function process()
    {
        foreach ($this->data as $k => $data) {
            $this->convert($data);
        }
    }

    /**
     * @param array $code
     * @throws \Exception
     */
    private function convert(array $data)
    {
        $company = Company::find($this->company_id);

        $uuid = Uuid::uuid4();

        $group = [];
        $group['DisplayName'] = $data['modifierSubMenu'];
        $group['Min'] = $data['forcePick'] == 0 ? 0 : 1;
        $group['Max'] = $data['allowHold'] == 0 ? 1 : 100 ;
        $group['site_num'] = $company->site_specific_products === 1 ? $this->site_num : 0;
        $group['Command'] = 6;
        $group['company_id'] = $this->company_id;
        $group['CommandUid'] = $uuid->toString();
        $group['received_from'] = 0;
        $group['price_override'] = null;
        $group['received_at'] = Carbon::parse('1970-01-01 00:00:00')->toDateTimeString();

        $this->results[$data['modifierSubMenu']] = $group;
    }

    public function getResults()
    {
        return $this->results;
    }
}
