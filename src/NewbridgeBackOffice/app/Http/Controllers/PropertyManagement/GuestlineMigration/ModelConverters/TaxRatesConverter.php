<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use NewbridgeWeb\Http\Controllers\Controller;
use Ramsey\Uuid\Uuid;

class TaxRatesConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results;
    public array $data;

    /**
     * Converts tax groups into tax rates for Newbridge
     *
     * @param array data
     * @param int $company_id
     * @param int $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            throw new \Exception($e);
        }
    }

    private function process()
    {
        foreach ($this->data as $k => $data) {
            $this->convert($data);
        }
    }

    private function convert($data)
    {
        $uuid = Uuid::uuid4()->toString();

        $this->results[$data['id']] =
            [
                "guid" => $uuid,
                "company_id" => $this->company_id,
                "displayname" => $data['name'],
                "rate" => $data['taxes'][0]['taxRate']
            ];
    }

    public function getResults()
    {
        return $this->results;
    }
}
