<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use NewbridgeWeb\Http\Controllers\Controller;
use Ramsey\Uuid\Uuid;

class RevenueCentreConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results;
    public array $data;

    /**
     * Converts tax groups into tax rates for Newbridge
     *
     * @param array data
     * @param int $company_id
     * @param int $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            throw new \Exception($e);
        }
    }

    private function process()
    {
        foreach ($this->data['revenue_centres'] as $k => $data) {
            $this->convert($data);
        }
    }

    private function convert($data)
    {
        $uuid = Uuid::uuid4()->toString();

        $terminal_guids = [];

        foreach ($this->data['terminals'] as $terminal) {
            if ($terminal['revenueCentre'] == $data['id']) {
                $terminal_guids[] = $terminal['id'];
            }
        }

        $this->results[$data['id']] =
            [
                "guid" => $data['id'],
                "company_id" => $this->company_id,
                "site_num" => $this->site_num,
                "name" => $data['name'],
                "terminal_guids" => $terminal_guids
            ];
    }

    public function getResults()
    {
        return $this->results;
    }
}
