<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use NewbridgeWeb\Http\Controllers\Controller;

class RezlynxMappingConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results = [];
    public array $data;

    /**
     * DepartmentConverter constructor.
     *
     * Converts a GL ChargeCode into a Department for Newbridge to Ingest - we will also consume any GUID's into
     * our structure
     *
     * @param $data
     * @param $company_id
     * @param $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            report($e);

            return null;
        }
    }

    /**
     * @throws \Exception
     */
    private function process()
    {
        foreach ($this->data['configuration'] as $k => $d) {
            $this->convert($d);
        }
    }

    /**
     * @param array $code
     * @throws \Exception
     */
    private function convert(array $data)
    {
        $connections = [];
        $i = 1;
        foreach ($data as $connection) {
            $connectionData = [
                'company_id' => $this->company_id,
                'value1' => $connection['hotelName'],
                'value2' => $connection['rezlynxSiteId'],
                'value3' => 7,
                'value4' => $connection['username'],
                'value5' => $connection['password'],
                'value6' => $i == 1 ? 1 : null,
                'value7' => null,
                'mappings' => null
            ];

            $mappings = $this->convertMappings($connection);
            $mappings['connection'] = [
                'site_id' => $connectionData['value2'],
                'name' => $connectionData['value1'],
                'device_id' => $connectionData['value3'],
                'username' => $connectionData['value4'],
                'password' => $connectionData['value5']
            ];
            $connectionData['mappings'] = $mappings;
            $connections[] = $connectionData;

            $i++;
        }

        $this->results = $connections;
    }

    private function convertMappings($connection)
    {
        $defaults = [
            "mapping_code" => $connection['revenueCentreMapping']['defaultRezlynxAnalysisCode'],
            "payment_code" => $connection['revenueCentreMapping']['defaultRezlynxPayType'],
            "header_code" => "BAR" // TODO?
        ];

        $chargeCodeMappings = [];
        $chargeCodeMappings['defaults'] = $defaults;

        foreach ($connection['revenueCentreMapping']['revenueCentreChargeCodeMappings'] as $chargeCodeMapping) {
            $revId = $chargeCodeMapping['revenueCentreId'];
            $chargeCodeMappings['revenue'][$revId] = [];
            $chargeCodeMappings['revenue'][$revId]['departments'] = [];
            foreach ($chargeCodeMapping['chargeCodeMappings'] as $mapping) {
                $chargeCodeMappings['revenue'][$revId]['departments'][$mapping['chargeCodeId']] = [
                    0 =>  $mapping['rezlynxAnalysisCode']
                ];

                foreach ($mapping['shiftMappings'] as $shiftMap) {
                    $chargeCodeMappings['revenue'][$revId]['departments'][$mapping['chargeCodeId']]['dayparts'][$shiftMap['shiftId']]
                        = $shiftMap['rezlynxAnalysisCode'] == "" ? $mapping['rezlynxAnalysisCode'] : $shiftMap['rezlynxAnalysisCode'];
                }
            }
        }

        foreach ($connection['revenueCentreMapping']['revenueCentrePayTypeMappings'][0]['payTypeMappings'] as $payTypeMapping) {
            $chargeCodeMappings['payments'][$payTypeMapping['payTypeId']] = $payTypeMapping['rezlynxPayType'];
        }

        return $chargeCodeMappings;
    }

    public function getResults()
    {
        return $this->results;
    }
}
