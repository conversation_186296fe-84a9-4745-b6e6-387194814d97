<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use NewbridgeWeb\Http\Controllers\Controller;
use Ramsey\Uuid\Uuid;

class SubDepartmentConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $chargeCode;
    public string $type;
    public array $results;

    /**
     * SubDepartmentConverter constructor.
     *
     * Converts a GL ChargeCode into a Department for Newbridge to Ingest
     *
     * Type can be code || category
     *
     * @param array data
     * @param int $company_id
     * @param int $site_num
     * @param string $type
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num, string $type = 'chargeCode')
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;
        $this->type = $type;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            throw new \Exception($e);
        }
    }

    private function process()
    {
        foreach ($this->data as $k => $data) {
            $this->convert($data);
        }
    }

    private function convert($data)
    {
        $uuid = Uuid::uuid4()->toString();
        if ($data['rFlag'] == 0) {
            $this->results[$data[$this->type]] =
                [
                    "guid" => $uuid,
                    "company_id" => $this->company_id,
                    "displayname" => $data[$this->type]
                ];
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
