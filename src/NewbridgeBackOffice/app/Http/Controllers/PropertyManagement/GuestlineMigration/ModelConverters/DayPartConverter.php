<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;

class DayPartConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results;
    public array $data;
    public $shiftCodes;

    /**
     * Converts tax groups into tax rates for Newbridge
     *
     * @param array data
     * @param int $company_id
     * @param int $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num, array $shiftCodes)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;
        $this->shiftCodes = $shiftCodes;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            throw new \Exception($e);
        }
    }

    private function process()
    {
        foreach ($this->data as $k => $data) {
            $this->convert($data);
        }
    }

    private function convert($data)
    {
        $shortCode = null;
        foreach ($this->shiftCodes as $sc) {
            if ($sc['shiftId'] == $data['id']) {
                $shortCode = $sc['rezlynxShift'];
            }
        }

        $this->results[$data['id']] =
            [
                "guid" => $data['id'],
                "company_id" => $this->company_id,
                "site_num" => $this->site_num,
                "name" => $data['description'],
                "time" => Carbon::parse($data['startTime'])->toDateTimeString(),
                "short_code" => $shortCode
            ];
    }

    public function getResults()
    {
        return $this->results;
    }
}
