<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Company;
use Ramsey\Uuid\Uuid;

class ClerkConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results;
    public array $data;

    /**
     * Converts tax groups into tax rates for Newbridge
     *
     * @param array data
     * @param int $company_id
     * @param int $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            throw new \Exception($e);
        }
    }

    private function process()
    {
        foreach ($this->data as $k => $data) {
            $this->convert($data);
        }
    }

    private function convert($data)
    {
        $uuid = Uuid::uuid4()->toString();
        $company = Company::find($this->company_id);
        if ($data['rFlag'] == 0) {
            $this->results[$uuid] =
                [
                    "guid" => $uuid,
                    "company_id" => $this->company_id,
                    "site_num" => $company->site_specific_clerks == 1 ? $this->site_num : 0,
                    "full_name" => $data['userName'],
                    "short_name" => $data['userName'],
                    "pin" => $data['pwd'],
                    "accesslevel" => stristr($data['userGroup'], 'admin') ? 16383 : 3580
                ];
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
