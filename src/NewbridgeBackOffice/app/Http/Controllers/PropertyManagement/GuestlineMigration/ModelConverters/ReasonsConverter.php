<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use NewbridgeWeb\Http\Controllers\Controller;
use Ramsey\Uuid\Uuid;

class ReasonsConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results;
    public array $data;
    public const WASTAGE = 2;
    public const VOID = 3;

    /**
     * Converts tax groups into tax rates for Newbridge
     *
     * @param array data
     * @param int $company_id
     * @param int $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();

            return $this->results;
        } catch(\Exception $e) {
            throw new \Exception($e);
        }
    }

    private function process()
    {
        foreach ($this->data as $k => $data) {
            $this->convert($data);
        }
    }

    private function convert($data)
    {
        $uuid = Uuid::uuid4()->toString();

        if ($data['rFlag'] == 0 && in_array($data['systemArea'], [self::WASTAGE, self::VOID])) {
            $this->results[$data['id']] = [
                'company_id' => $this->company_id,
                'guid' => Uuid::uuid4(),
                'reason' => $data['reason'],
                'reason_type' => $this->getReasonType($data['systemArea'])
            ];
        }
    }

    private function getReasonType($systemArea)
    {
        switch($systemArea) {
            case 2:
                return 3;

                break;
            case 3:
                return 0;

                break;
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
