<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use Ramsey\Uuid\Uuid;

class ButtonStyleConverter extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $results = [];
    public array $data;

    /**
     * Converts tax groups into tax rates for Newbridge
     *
     * @param array data
     * @param int $company_id
     * @param int $site_num
     * @throws \Exception
     */
    public function __construct(array $data, int $company_id, int $site_num)
    {
        $this->data = $data;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();
        } catch (\Exception $e) {
            throw new \Exception($e);
        }
    }

    private function process()
    {
        foreach ($this->data['original'] as $k => $data) {
            $this->convert($data, $k);
        }
    }

    private function convert($data, $key)
    {
        $uuid = Uuid::uuid4()->toString();
        $this->results[$key] = [
            "guid" => $uuid,
            "resourcestyle" => "default",
            "color1" => strtolower($data['backColorHtml']),
            "color2" => strtolower($data['backColorHtml']),
            "color3" => strtolower($data['backColorHtml']),
            "color4" => strtolower($data['backColorHtml']),
            "cornerradius" => 0,
            "typeface" => "",
            "fontsize" => 12,
            "alignment" => 1,
            "foreground" => strtolower($data['foreColorHtml']),
            "gradient" => 0,
            "received_at" => Carbon::now()->toDateTimeString()
        ];
    }

    public function getResults()
    {
        return $this->results;
    }
}
