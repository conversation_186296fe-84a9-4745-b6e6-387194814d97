<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement\GuestlineMigration\ModelConverters;

use NewbridgeWeb\Http\Controllers\Controller;
use Ramsey\Uuid\Uuid;

class ProductConverter extends Controller
{
    public array $products;
    public int $company_id;
    public int $site_num;
    public array $results;

    public function __construct($products, $company_id, $site_num)
    {
        $this->products = $products;
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        try {
            $this->process();
        } catch(\Exception $e) {
            throw new \Exception($e);
        }
    }

    /**
     * @throws \Exception
     */
    private function process()
    {
        foreach ($this->products as $k => $product) {
            $this->convert($product);
        }
    }

    /**
     * Links
     *
     * Drink = 0 | ChargeCode = Name of Department - when a Food Item, also SubDepartment name is ChargeCode Name
     * Drink = -1 | Category = Subdepartment name
     * taxRule | Linked by name of rule
     *
     * @throws \Exception
     */
    private function convert($product)
    {
        $uuid = Uuid::uuid4();
        if ($product['rFlag'] == 0) {
            $this->results['p' . $product['pluId']] = [
                "company_id" => $this->company_id,
                "site_num" => $this->site_num,
                "guid" => $uuid,
                "CommandUID" => $uuid,
                "short_desc" => $product['barScreenDescription'],
                "displayname" => $product['itemDescription'],
                "department_guid" => $product['chargeCode'], // MigrationController static Method getDepartment - product Charge Code Ref
                "sub_department_guid" => $product['drink'] == 0 ? $product['chargeCode'] : $product['category'], // MigrationController static Method getSubDepartment - product Charge Code Ref - if not drink else use category code
                "tax_guid" => $product['taxRule'], //taxRule
                "costprice" => $product['cost'],
                "barcode" => $product['ean'],
                "min_stock" => "0.00",
                "max_stock" => "0.00",
                "sku_guid" => null,
                "sku_quantity" => null,
                "recipe_guid" => null,
                "recipe_qty" => null,
                "plu_parent_guid" => null,
                "supplier_guid" => null,
                "supplier_ref" => null,
                "rrp" => $product['price'],
                "current_stock_value" => null,
                "non_stock" => 0,
                "saleprice" => null,
                "selling_price_1" => $product['price'],
                "selling_price_2" => $product['price'],
                "selling_price_3" => $product['price'],
                "selling_price_4" => $product['price'],
                "selling_price_5" => $product['price'],
                "selling_price_6" => $product['price'],
                "selling_price_7" => $product['price'],
                "vat_rate" => $product['taxRule'], // taxRule
                "print_on_receipt" => 1,
                "Command" => 3,
                "KP_Target" => 1,
                "follow_course_separator" => false,
                "is_modifier" => false,
                "app_available" => 0,
                "soldbyweight" => false,
                "third_party_reference" => "0",
                "ml_amount" => 0,
                "ismanualweight" => false,
                "third_party_id" => null,
                "description" => null,
                "show_child_list" => false,
                "modifier_group" => $product['modifierMenu']
            ];
        }
    }

    public function getResults()
    {
        return $this->results;
    }
}
