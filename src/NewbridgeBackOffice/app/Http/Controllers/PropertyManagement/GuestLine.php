<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement;

use GuzzleHttp\Client;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PMSCredentials;

/**
 * use api classes that are required for the update api
 */
class GuestLine extends Controller
{
    /**
     * @var \SoapClient
     */
    public \SoapClient $client;

    /**
     * @var array
     */
    public array $baseRequest = [];

    /**
     * @var array
     */
    public array $data = [];

    /**
     * @var array
     */
    public array $errors = [];
    public int $site;
    public int $company_id;
    public int $site_num;
    public int $connection_id;
    public string $session_id;
    public string $errorMessage = '';

    /**
     * Mews constructor.
     */
    public function __construct($company_id, $connection_id)
    {
        $this->company_id = (int) $company_id;
        $this->connection_id = (int) $connection_id;

        $connection = PMSCredentials::find($connection_id);

        $uri = $connection->value8;
        if (empty($uri)) {
            $uri = config('guestline.connections.we');
            $this->updateConnection($connection, $uri);
        }

        // Create a new SOAP client
        $this->client = new \SoapClient($uri, [
            'trace' => true,
            'exceptions' => true,
            'soap_version' => SOAP_1_2,
            'cache_wsdl' => WSDL_CACHE_NONE,
        ]);

        $parameters = [
            'InterfaceID' => $connection->value3,
            'SiteID' => $connection->value2,
            'OperatorCode' => $connection->value4,
            'Password' => $connection->value5
        ];

        try {
            $response = $this->client->Login($parameters);
        } catch (\Exception $e) {
            report($e);
            $this->errorMessage = 'Invalid credentials, please check and try again.';

            return [];
        }



        if ($response->SessionID != '') {
            $this->session_id = $response->SessionID;
        } else {

            $this->errorMessage = 'Invalid credentials, please check and try again.';

            return [];
        }

        $this->data = [];

        $codes = $this->getAnalList();

        foreach ($codes['AnalysisCodeList']['AnalCodes']['cpmscfg_AnalList_AnalItem'] as $k => $anal) {
            if ($anal['Usage'] == 'acr_InternalPostingsOnly') {
                unset($codes['AnalysisCodeList']['AnalCodes']['cpmscfg_AnalList_AnalItem'][$k]);
            }
        }

        $payTypes = $this->getPayTypeList();

        $this->client->LogOut(['SessionID' => $this->session_id]);

        $this->data = array_merge($codes, $payTypes);

        return $this->data;
    }

    public function getAnalList()
    {
        try {
            $response = $this->client->pmscfg_AnalList(['SessionID' => $this->session_id]);

        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }

        return json_decode(json_encode($response), true);
    }

    private function getPayTypeList()
    {
        try {
            $response = $this->client->pmscfg_PayTypeList(['SessionID' => $this->session_id]);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }

        return json_decode(json_encode($response), true);
    }

    private function updateConnection($connection, $uri)
    {
        $connection->value8 = $uri;
        $connection->save();
        PMSCredentials::updateCredentialsJsonForCompany($this->company_id);
        sleep(1);

        $guestline = new GuestLineController();
        $guestline->createOrUpdateOutletServiceMapping($this->company_id);
    }
}
