<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PMSCredentials;
use NewbridgeWeb\Repositories\Sites;
use Yajra\DataTables\DataTables;

/**
 * use api classes that are required for the update api
 */
class GuestlineCredentials extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $errors = [];

    public function index()
    {
        return response()->view('modules.pms-integration.guestline.table');
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        $model = PMSCredentials::where('company_id', Auth::user()->company_id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function getCreate()
    {
        $usedSites = PMSCredentials::where('company_id', Auth::user()->company_id)->get();

        $usedSitesList = [];
        foreach ($usedSites as $site) {
            $usedSitesList[] = $site->value6;
        }

        $sites = Sites::where(function ($q) use ($usedSitesList) {
            $q->where('company_id', Auth::user()->company_id)->whereNotIn('site_num', $usedSitesList);
        })->get();

        return view('modules.pms-integration.guestline.create', compact('sites'));
    }

    public function postCreate(Request $request)
    {
        $rules = [
            'name' => 'required',
            'site_id' => 'required',
            'device_id' => 'required',
            'username' => 'required',
            'password' => 'required'
        ];

        $validated = $request->validate($rules);

        $connections = config('guestline.connections');
        $region = empty($request['region']) ? 'we' : $request['region'];
        $connection = $connections[$region];

        $credentials = PMSCredentials::create([
            'company_id' => Auth::user()->company_id,
            'site_num' => \Session::get('current_site'),
            'value1' => $request['name'],
            'value2' => $request['site_id'],
            'value3' => $request['device_id'],
            'value4' => $request['username'],
            'value5' => $request['password'],
            'value6' => isset($request['defaultsite']) ? $request['defaultsite'] : null,
            'value8' => $connection
        ]);

        $message = ['status' => 'success', 'message' => 'PMS Connection added successfully.'];
        \Session::flash('message', $message);

        PMSCredentials::updateCredentialsJsonForCompany((int) session('user.company_id'));

        sleep(1);

        $guestline = new GuestLineController();
        $guestline->createOrUpdateOutletServiceMapping((int) session('user.company_id'));

        return redirect('/company/pms-configuration/guestline/connections');
    }

    public function getEdit($id)
    {
        $credential = PMSCredentials::findOrFail($id);
        $usedSites = PMSCredentials::where('company_id', Auth::user()->company_id)->get();

        $usedSitesList = [];
        foreach ($usedSites as $site) {
            if ($site->value6 != null) {
                $usedSitesList[] = $site->value6;
            }
        }

        if (!empty($usedSitesList)) {
            $sites = Sites::where(function ($q) use ($usedSitesList) {
                $q->where('company_id', Auth::user()->company_id)->whereNotIn('site_num', $usedSitesList);
            })
                ->orWhere(function ($q) use ($credential) {
                    $q->where('company_id', Auth::user()->company_id)->where('site_num', $credential->value6);
                })
                ->get();
        } else {
            $sites = Sites::where('company_id', Auth::user()->company_id)->get();
        }

        $connections = config('guestline.connections');
        foreach ($connections as $key => $value) {
            if($credential->value8 == $value) {
                $credential->value8 = $key;

                break;
            }
        }

        return view('modules.pms-integration.guestline.edit', compact('credential', 'sites'));
    }

    public function postEdit(Request $request, $id)
    {
        $rules = [
            'name' => 'required',
            'site_id' => 'required',
            'device_id' => 'required',
            'username' => 'required',
            'password' => 'required'
        ];

        $validated = $request->validate($rules);

        $credentials = PMSCredentials::where('id', $id)->first();

        $connections = config('guestline.connections');
        $region = empty($request['region']) ? 'we' : $request['region'];
        $connection = $connections[$region];

        $credentials->fill([
            'company_id' => Auth::user()->company_id,
            'site_num' => \Session::get('current_site'),
            'value1' => $request['name'],
            'value2' => $request['site_id'],
            'value3' => $request['device_id'],
            'value4' => $request['username'],
            'value5' => $request['password'],
            'value6' => isset($request['defaultsite']) ? $request['defaultsite'] : null,
            'value8' => $connection
        ]);

        $credentials->save();

        $message = ['status' => 'success', 'message' => 'The PMS Connection has been updated successfully.'];
        \Session::flash('message', $message);

        PMSCredentials::updateCredentialsJsonForCompany(Auth::user()->company_id);

        sleep(1);

        $guestline = new GuestLineController();
        $guestline->createOrUpdateOutletServiceMapping((int) session('user.company_id'));

        return redirect('/company/pms-configuration/guestline/connections');
    }

    public function delete($id)
    {
        $credential = PMSCredentials::find($id);
        $credential->forceDelete();

        $message = ['status' => 'success', 'message' => 'PMS Connection deleted successfully.'];
        \Session::flash('message', $message);

        sleep(1);

        $guestline = new GuestLineController();
        $guestline->createOrUpdateOutletServiceMapping((int) session('user.company_id'));

        return response()->json('ok');
    }
}
