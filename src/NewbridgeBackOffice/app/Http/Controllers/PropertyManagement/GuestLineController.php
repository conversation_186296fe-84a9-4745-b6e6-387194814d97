<?php

namespace NewbridgeWeb\Http\Controllers\PropertyManagement;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Jobs\UpdateGuestlinePMSMappingJob;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\DayParts;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PMSCredentials;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\PosSettings;
use NewbridgeWeb\Repositories\RevenueCenters;
use NewbridgeWeb\Repositories\Sites;
use Ramsey\Uuid\Uuid;

/**
 * use api classes that are required for the update api
 */
class GuestLineController extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $errors = [];

    public function selectConnection()
    {
        $connections = PMSCredentials::where('company_id', Auth::user()->company_id)->get();

        if ($connections->count() == 0) {
            $message = ['status' => 'error', 'message' => 'Please create a connection before proceeding to mappings.'];
            \Session::flash('message', $message);

            return redirect('/company/pms-configuration/guestline/connections');
        }

        return view('modules.pms-integration.mappings.select-connection', compact('connections'));
    }

    public function mewsForm($connection_id)
    {
        $connection = PMSCredentials::find($connection_id);

        $company = Company::find(Auth::user()->company_id);

        $this->company_id = Auth::user()->company_id;

        $departments = Departments::where('company_id', \Illuminate\Support\Facades\Auth::user()->company_id)->get();
        $commands = Commands::where('company_id', 0)->whereIn('Command', [37, 44, 88, 61, 46, 149, 174])->get()->toArray();
        $discounts = Discounts::where('company_id', Auth::user()->company_id)->where('auto', 0)->get();
        $payments = Payments::where('company_id', Auth::user()->company_id)->whereNotIn('method_type', [2, 4])->get()->toArray();
        $dayParts = DayParts::where('company_id', Auth::user()->company_id)->get();
        $revenueCenters = RevenueCenters::where('company_id', Auth::user()->company_id)->get();

        $terminals = Pos::where('company_id', Auth::user()->company_id)->get();

        if ($revenueCenters->count() == 0) {
            $message = ['status' => 'error', 'message' => 'Please create at least one revenue center before proceeding to mapping.'];
            \Session::flash('message', $message);

            return redirect('/company/revenue-centers');
        }

        $mapped = [];

        foreach ($revenueCenters as $revenueCenter) {
            $mapped = array_merge($mapped, json_decode($revenueCenter->terminals, true));
        }

        if (count($mapped) < $terminals->count()) {
            $message = ['status' => 'error', 'message' => 'Not all terminals have been mapped to a revenue center, please assign all terminals before proceeding.'];
            \Session::flash('message', $message);

            return redirect('/company/revenue-centers');
        }

        $connections = PMSCredentials::where('company_id', Auth::user()->company_id)->get();

        if ($connections->count() == 0) {
            $message = ['status' => 'error', 'message' => 'Please create a connection before proceeding to mapping.'];
            \Session::flash('message', $message);

            return redirect('/company/pms-configuration/guestline/connections');
        }

        if ($company->active_integrations & 512) {
            $data = new GuestLine(Auth::user()->company_id, $connection->id);
            if ($data->errorMessage != '') {
                $message = ['status' => 'error', 'message' => $data->errorMessage];
                \Session::flash('message', $message);

                return redirect('/company/pms-configuration/guestline/connections');
            }
        } else {
            return view('modules.pms-integration.mappings.integration-inactive');
        }

        $data = $data->data;

        $savedData = json_decode($connection->value7, true);

        return view('modules.pms-integration.mappings.guestline', compact('revenueCenters', 'dayParts', 'data', 'departments', 'payments', 'commands', 'discounts', 'savedData', 'connection'));
    }

    public function saveKey(Request $request)
    {
        $rules = [
            'site_id' => 'required',
            'username' => 'required',
            'password' => 'required',
        ];

        $validated = $request->validate($rules);

        $this->company_id = Auth::user()->company_id;
        $this->site_num = \Session::get('current_site');

        $site = Sites::where('company_id', $this->company_id)
            ->where('site_num', $this->site_num)
            ->first();

        $access = [
            'name' => $site->site_name,
            'device_id' => $request->input('device_id'),
            'site_id' => $request->input('site_id'),
            'username' => $request->input('username'),
            'password' => $request->input('password')
        ];

        $credentials = PMSCredentials::create([
            'company_id' => Auth::user()->company_id,
            'site_num' => 0,
            'value1' => $access['name'],
            'value2' => $access['site_id'],
            'value3' => $access['device_id'],
            'value4' => $access['username'],
            'value5' => $access['password']
        ]);

        PMSCredentials::updateCredentialsSettingsAndSite();

        return redirect('/company/pms-configuration/guestline');
    }

    public function createOrUpdateSettingsAndDepartmentMappings(Request $request, $connection_id)
    {
        $rules = [
            'default' => 'required',
            'default_payment' => 'required',
            'header' => 'required',
            'commands.*' => 'required',
            'revenue.*' => 'required',
            'payments.*' => 'required',
            'discounts.*' => 'required'
        ];

        $validated = $request->validate($rules);

        $this->company_id = Auth::user()->company_id;
        $this->site_num = \Session::get('current_site');

        $connection = PMSCredentials::find($connection_id);

        $mappingData = [
            'connection' => [
                'name' => $connection->value1,
                'site_id' => $connection->value2,
                'device_id' => $connection->value3,
                'username' => $connection->value4,
                'password' => $connection->value5,
                'region' => $connection->value8
            ],
            'defaults' => [
                'mapping_code' => $request->input('default'),
                'payment_code' => $request->input('default_payment'),
                'header_code' => $request->input('header')
            ],
            'commands' => $request->input('commands'),
            'revenue' => $request->input('revenue'),
            'payments' => $request->input('payments'),
            'discounts' => $request->input('discounts'),
            'site_num' => $connection->value6
        ];

        $this->saveMappings($mappingData, $connection_id);

        dispatch(new UpdateGuestlinePMSMappingJob($this->company_id));

        if (!empty($this->errors)) {
            return $this->errors;
        }

        $message = ['status' => 'success', 'message' => 'PMS Mappings Updated'];
        \Session::flash('message', $message);

        return redirect()->back();
    }

    private function saveMappings($mapping, $connection_id)
    {
        $connection = PMSCredentials::find($connection_id);
        $connection->value7 = json_encode($mapping);
        $connection->save();

        return 'ok';
    }

    public function createOrUpdateOutletServiceMapping(int $company_id)
    {
        $connections = PMSCredentials::where('company_id', $company_id)->get();

        $data = [];
        $data['hotels'] = [];

        foreach ($connections as $connection) {
            $mappingData = json_decode($connection->value7, true);

            if ($mappingData != null) {

                $mappingData = $this->formatMappings($mappingData);
                $mappingData['site_num'] = $connection->value6;

                $data['hotels'][] = $mappingData;

                $revenueCentres = RevenueCenters::where('company_id', $company_id)->get();

                foreach ($revenueCentres as $revenueCentre) {
                    $terminal_ids = json_decode($revenueCentre->terminals);

                    foreach ($terminal_ids as $terminal_id) {
                        $terminal = Pos::find($terminal_id);
                        if (!empty($terminal)) {
                            $data['terminal_revenue'][] = ['terminal_num' => $terminal->terminal_num, 'site_num' => $terminal->site_num, 'revenue_center_id' => $revenueCentre->id];
                        }
                    }
                }
                if (!empty($data['terminal_revenue'])) {
                    $data['terminal_revenue'] = array_values($data['terminal_revenue']);
                } else {
                    $data['terminal_revenue'] = [];
                }
            }
        }

        $terminals = Pos::where('company_id', $company_id)->get();

        /**
         * Create a list of settings to update
         */
        $settings = [
            [
                'setting_id' => 'GuestlinePMS',
                'description' => 'All Configuration options for Guestline',
                'value' => '',
                'json' => json_encode($data)
            ]
        ];

        foreach ($terminals as $terminal) {
            /**
             * Create or Update PMS Default Settings
             */
            foreach ($settings as $k => $setting) {
                $this->createOrUpdatePMSPosSetting($setting, $terminal);
            }
        }
    }

    private function formatMappings($mappings)
    {
        if (isset($mappings['commands'])) {
            foreach ($mappings['commands'] as $k => $command) {
                $mappings['commands'][] = ['mapping_guid' => $k, 'mapping_code' => $command];
                unset($mappings['commands'][$k]);
            }

            foreach ($mappings['revenue'] as $k2 => $revenue) {
                $mappings['revenue'][] = ['revenue_center_id' => $k2, 'departments' => $revenue['departments']];
                unset($mappings['revenue'][$k2]);
            }

            $mappings['revenue'] = array_merge($mappings['revenue']);

            foreach ($mappings['revenue'] as $k3 => $revenue2) {
                foreach ($revenue2['departments'] as $k4 => $department) {
                    $newDepartment = [
                        'default_mapping' => [
                            'mapping_guid' => $k4,
                            'mapping_code' => $department[0]
                        ],
                        'dayparts' => []
                    ];
                    if (isset($department['dayparts'])) {
                        foreach ($department['dayparts'] as $k5 => $daypart) {
                            $newDepartment['dayparts'][] = [
                                'mapping_guid' => $k5,
                                'mapping_code' => $daypart
                            ];
                        }
                    }

                    $mappings['revenue'][$k3]['departments'][] = $newDepartment;
                    unset($mappings['revenue'][$k3]['departments'][$k4]);
                }
            }

            foreach ($mappings['payments'] as $k6 => $payment) {
                $mappings['payments'][] = [
                    'mapping_guid' => $k6,
                    'mapping_code' => $payment
                ];

                unset($mappings['payments'][$k6]);
            }

            if (!empty($mappings['discounts']) && is_array($mappings['discounts'])) {
                foreach ($mappings['discounts'] as $k7 => $discount) {

                    $mappings['discounts'][] = [
                        'mapping_guid' => $k7,
                        'mapping_code' => $discount
                    ];

                    unset($mappings['discounts'][$k7]);
                }
            } else {
                $mappings['discounts'] = [];
            }

        } else {
            $mappings['revenue'] = [];
            $mappings['discounts'] = [];
            $mappings['commands'] = [];
        }

        return $mappings;
    }

    public function createOrUpdatePMSPosSetting($setting, $terminal)
    {

        $hasDefaultSetting = PosSettings::where('company_id', $terminal->company_id)
            ->where('site_num', $terminal->site_num)
            ->where('terminal_num', $terminal->terminal_num)
            ->where('setting_id', $setting['setting_id'])
            ->first();

        if ($hasDefaultSetting != null) {

            $hasDefaultSetting->Value1 = $setting['value'];
            $hasDefaultSetting->Value7 = $setting['json'];
            $hasDefaultSetting->received_from = 0;
            $hasDefaultSetting->save();
            sleep(1);
            \Event::dispatch(new UpdaterUpdateCrudEvent($hasDefaultSetting));

        } else {

            $newSetting = new PosSettings();
            $newSetting->guid = Uuid::uuid4();

            $newSetting->isreadonly = 0;
            $newSetting->company_id = $terminal->company_id;
            $newSetting->site_num = $terminal->site_num;
            $newSetting->terminal_num = $terminal->terminal_num;

            $newSetting->setting_id = $setting['setting_id'];
            $newSetting->label = $setting['description'];
            $newSetting->description = $setting['description'];
            $newSetting->category = 16;

            $newSetting->value1 = $setting['value'];
            $newSetting->value7 = $setting['json'];
            $newSetting->location = 0;
            $newSetting->type = 2;
            $newSetting->value2 = 0;
            $newSetting->received_from = 0;
            $newSetting->received_at = Carbon::now();
            $newSetting->save();
            sleep(1);
            \Event::dispatch(new UpdaterUpdateCrudEvent($newSetting));

        }
    }

    function mappingsAnalysis()
    {
        $company_id = Auth::user()->company_id;

        $connection = PMSCredentials::where('company_id', $company_id)->where('value6', session('current_site'))->first();

        if($connection) {
            $departments = Departments::where('company_id', $company_id)->withTrashed()->get()->keyBy('guid')->toArray();
            $paymentMethods = Payments::where('company_id', $company_id)->withTrashed()->whereNotIn('method_type', [2, 4])->get()->keyBy('CommandUID')->toArray();
            $commands = Commands::where('company_id', 0)->withTrashed()->whereIn('Command', [37, 44, 88, 61, 46, 149, 174])->get()->keyBy('CommandUID')->toArray();
            $revenueCentres = RevenueCenters::where('company_id', $company_id)->withTrashed()->get()->keyBy('id')->toArray();
            $dayParts = DayParts::where('company_id', $company_id)->withTrashed()->get()->keyBy('guid')->toArray();
            $discounts = Discounts::where('company_id', Auth::user()->company_id)->withTrashed()->where('auto', 0)->get()->keyBy('CommandUID')->toArray();

            $mappings = json_decode($connection->value7, true);

            return view('modules.pms-integration.mappings.analysis', compact('discounts', 'departments', 'paymentMethods', 'commands', 'revenueCentres', 'dayParts', 'mappings'));
        } else {

            session()->flash('message', ['status' => 'error', 'message' => 'No Guestline connection found.']);
            return redirect('/');
        }
    }
}
