<?php

namespace NewbridgeWeb\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\SubDepartment;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use PDF;

use Yajra\Datatables\Datatables;

class TransactionController extends Controller
{
    private Request $request;

    public function index(Request $request)
    {
        $this->request = $request;
        $ids = $this->request->input('transactions');

        $sites = Sites::where('company_id', Auth::user()->company_id)->get();
        $current_site = \Session::get('current_site');
        $subdepartments = SubDepartment::where('company_id', Auth::user()->company_id)->get();
        $departments = Departments::where('company_id', Auth::user()->company_id)->get();
        $products = Products::select('displayname', 'guid')->whereIn('site_num', [0, $current_site])->where('company_id', Auth::user()->company_id)->get();
        if ($this->request->input('start') && $this->request->input('end')) {
            $start = Carbon::parse($this->request->input('start'), TimezoneHelper::getTimezone())->setTimezone('UTC');
            $end = Carbon::parse($this->request->input('end'), TimezoneHelper::getTimezone())->setTimezone('UTC');
        } else {
            $start = Carbon::now()->subDays(7);
            $end = Carbon::now();
        }

        if ($this->request->has('payment_method')) {
            $payment_method = $this->request->input('payment_method');
        } else {
            $payment_method = 'all';
        }

        return view('modules.transactions.datatables.table', compact(['start', 'end', 'current_site', 'ids', 'sites', 'payment_method', 'subdepartments', 'departments', 'products']));
    }

    public function data(Datatables $dataTables, Request $request, $start = null, $end = null, $payment_method = 'all')
    {
        $this->request = $request;
        $site = (int) session('current_site');

        $start = Carbon::parse($start, TimezoneHelper::getTimezone())->setTimezone('UTC');
        $end = Carbon::parse($end, TimezoneHelper::getTimezone())->setTimezone('UTC');

        $model = PosTransaction::select(['id', 'site_num', 'finalised_date', 'order_number', 'table_location',
            'table_number', 'covers', 'room_number',
            'terminal_num', 'total_discount_value', 'membership_no', 'change', 'subtotal', 'total',
            'employee_guid', 'customer_guid', 'discount_method', 'discount_amount', 'location_name'])
            ->with(['payments:id,amount,trans_id,method_guid,terminal_num,membership_no',
                'payments.method:id,DisplayName,method_type,CommandUID'])
            ->with('clerk:id,guid,ishidden,full_name')->where('company_id', Auth::user()->company_id)
            ->whereBetween('finalised_date', [$start, $end])->whereIn('site_num', [0, $site]);


        if ($this->request->has('department') && !empty($this->request->get('department'))) {
            $model = $model->whereHas('details', function ($q) use ($request) {
                $q->whereIn('department_guid', explode(',', $this->request->get('department')));
            });
        }

        if ($this->request->has('subdepartment') && !empty($this->request->get('subdepartment'))) {
            $model = $model->whereHas('details', function ($q) {
                $q->whereIn('sub_department_guid', explode(',', $this->request->get('subdepartment')));
            });
        }

        if ($this->request->has('product') && !empty($this->request->get('product'))) {
            $model = $model->whereHas('details', function ($q) {
                $q->whereIn('product_guid', explode(',', $this->request->get('product')));
            });
        }

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function viewTransaction(int $id): \Illuminate\Contracts\View\Factory|\Illuminate\View\View | RedirectResponse
    {
        $transaction = PosTransaction::whereCompanyId(Auth::user()->company_id)->whereId($id)->first();
        if ($transaction != null) {

            $type = $transaction->member_type;

            $data = PosTransaction::where('id', $id)->with(['details' => function ($q) {
                $q->with(['product' => function ($q) {
                    $q->withTrashed();
                }], 'discount', 'promotion', 'taxes');
            }, 'clerk', 'payments' => function ($q) {
                $q->with('method', 'type');
            }, 'discount', 'check_discounts' => function ($q) {
                $q->with('discount');
            }, 'customer' => function ($q) use ($type) {
                $q->with('group')->where('company_id', Auth::user()->company_id)->where('customer_type', $type);
            }, 'nosales'])->first();

            $taxes = $this->getTaxData($data);

            return view('modules.transactions.includes.view', compact('data', 'taxes'));
        } else {
            $message = ['status' => 'error', 'message' => 'That transaction does not exist!'];
            \Session::flash('message', $message);

            return redirect('/transactions');
        }
    }

    private function getTaxData($transaction)
    {
        $data = [];
        $taxRates = TaxRates::whereCompanyId(Auth::user()->company_id)->withTrashed()->get();

        foreach ($taxRates as $rate) {
            $data[$rate->guid] = [
                'name' => $rate->displayname,
                'total' => 0
            ];
        }

        foreach ($transaction->details as $detail) {
            if($detail->command_type != 5) {
                if ($detail->taxes()->count() > 0) {
                    foreach ($detail->taxes as $tax) {
                        $data[$tax->rate_guid]['total'] += $tax->value;
                    }
                } else {
                    if ($detail->product_guid != null) {
                        $rate = TaxRates::whereCompanyId(Auth::user()->company_id)->where('rate', $detail->tax_rate)->withTrashed()->first();
                        $data[$rate->guid]['total'] += $detail->tax_value;
                    }
                }
            }
        }

        foreach ($data as $k => $d) {
            if($d['total'] == 0) {
                unset($data[$k]);
            }
        }

        return $data;

    }

    public function print(int $id)
    {
        $data = PosTransaction::where('id', $id)->with(['details' => function ($q) {
            $q->with('product', 'discount', 'promotion');
        }, 'clerk', 'payments' => function ($q) {
            $q->with('method', 'type');
        }, 'discount', 'check_discounts' => function ($q) {
            $q->with('discount');
        }, 'nosales'])->first();

        $customer = null;
        if ($data->membership_no != null) {
            $customer = Customer::where('membership_no', $data->membership_no)->where('company_id', $data->company_id)->where('customer_type', 0)->first();
        }

        $company = Company::find($data->company_id);

        $site = Sites::where('company_id', $data->company_id)->where('site_num', $data->site_num)->first();

        $taxes = $this->getTaxData($data);

        // replace with PDF method
        $file = PDF::loadView('modules.transactions.print', ['data' => $data, 'taxes' => $taxes, 'company' => $company, 'site' => $site, 'customer' => $customer]);

        $filename = 'receipt-' . $id . '.pdf';

        return $file->download($filename);
    }
}
