<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Carbon\Carbon;
use Event;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Log;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Helpers\EventGridHelper;
use NewbridgeWeb\Http\Helpers\ServiceBusHelper;
use NewbridgeWeb\RealRepositories\MappingsEntitiesRepository;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\DayParts;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\Integrations\IntegrationLogs;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationCompanies;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrations;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationSites;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\PosSettings;
use NewbridgeWeb\Repositories\RevenueCenters;
use NewbridgeWeb\Repositories\Sites;
use Illuminate\Validation\Rules\File;
use Ramsey\Uuid\Uuid;
use Session;

class SiteIntegrationsController extends Controller
{
    private array $validated;
    private $override;
    private null|array $input;
    private Company $company;
    private null|array $mappings;

    public function __construct(
        private SiteIntegrations           $integration,
        private MappingsEntitiesRepository $mappingsEntitiesRepository
    )
    {
    }

    public function index()
    {
        $integrations = SiteIntegrations::all();
        $sites = Sites::where('company_id', Auth::user()->company_id)->get();
        $terminals = Pos::where('company_id', Auth::user()->company_id)
            ->where('site_num', session('current_site'))
            ->get();

        $site = Sites::where('company_id', Auth::user()->company_id)
            ->where('site_num', session('current_site'))
            ->first();

        return view('modules.site-integrations.select', compact('integrations', 'site', 'sites', 'terminals'));
    }

    public function run(int $integration_id)
    {
        $integration = SiteIntegrations::find($integration_id);

        if (!isset($integration->configuration['artisan_command'])) {
            return response()->json([
                'status' => 'success',
                'message' => 'The integration does not have an artisan command to run.'
            ]);
        }

        $exitCode = Artisan::call($integration->configuration['artisan_command'], [
            'company' => Auth::user()->company_id,
            'site' => session('current_site')
        ]);

        if ($exitCode == 1) {
            return response()->json([
                'status' => 'error',
                'message' => 'The command failed to run, please check the logs'
            ], 400);
        } else {
            return response()->json([
                'status' => 'success',
                'message' => 'The command has been sent, please check the logs for the result.'
            ]);
        }
    }

    public function logs(int $integration_id)
    {
        $logs = IntegrationLogs::whereSiteNum(session('current_site'))
            ->whereCompanyId(Auth::user()->company_id)
            ->whereIntegrationId($integration_id)
            ->limit(50)
            ->orderBy('created_at', 'DESC')
            ->get();

        return view(
            'modules.site-integrations.logs-table',
            compact(
                'logs'
            )
        )->render();
    }

    public function configure($integration_id): Factory|View|Application
    {
        $integration = SiteIntegrations::find($integration_id);
        $companyId = Auth::user()->company_id;

        $terminals = Pos::where('company_id', $companyId)
            ->where('site_num', session('current_site'))
            ->get();
        $site = Sites::where('company_id', $companyId)
            ->where('site_num', session('current_site'))
            ->first();

        $company = Company::find($companyId);

        $configuration = $integration->integrationConfigurationAttribute()?->configuration;
        $storedMappings = $integration->integrationConfigurationAttribute()?->mappings;

        $integration->configuration = is_array($integration->configuration)
            ? $integration->configuration
            : json_decode($integration->configuration, true);

        if (isset($integration->configuration['mappings']) && $configuration != null) {
            $objects = [];
            $mappings = [];
            $types = array_keys($integration->configuration['mappings']['types']);

            foreach ($types as $type) {
                $mappings[$type] = $this->mappingsEntitiesRepository->getEntities($integration, $configuration, $type);
            }

            $objects['departments'] = in_array('departments', $types) ? Departments::where(
                'company_id',
                Auth::user()->company_id
            )->get() : [];
            $objects['functions'] = in_array('functions', $types) ? Commands::where('company_id', 0)->whereIn(
                'Command',
                [37, 44, 88, 61, 46, 149, 174]
            )->get() : [];
            $objects['discounts'] = in_array('discounts', $types) ? Discounts::where(
                'company_id',
                Auth::user()->company_id
            )->where('auto', 0)->get() : [];
            $objects['payments'] = in_array('payments', $types) ? Payments::where(
                'company_id',
                Auth::user()->company_id
            )->whereNotIn('method_type', [2, 4])->get() : [];

            $dayParts = $integration->configuration['mappings']['dayparts'] ? DayParts::where(
                'company_id',
                $companyId
            )->get() : [];
            $revenueCenters = RevenueCenters::where('company_id', $companyId)->get();

            $terminals = Pos::where('company_id', $companyId)->get();

            if ($revenueCenters->count() === 0) {
                Session::flash(
                    'message',
                    [
                        'status' => 'error',
                        'message' => 'Please create at least one revenue center before proceeding with mapping.'
                    ]
                );

                return view(
                    'modules.site-integrations.configure',
                    compact('integration', 'terminals', 'configuration', 'site')
                );
            }

            $mapped = [];

            foreach ($revenueCenters as $revenueCenter) {
                $mapped = array_merge($mapped, json_decode($revenueCenter->terminals, true));
            }

            if (count($mapped) < $terminals->count()) {
                Session::flash(
                    'message',
                    [
                        'status' => 'error',
                        'message' => 'Not all terminals have been mapped to a revenue center, please assign all terminals before proceeding with mapping.'
                    ]
                );

                return view(
                    'modules.site-integrations.configure',
                    compact('integration', 'terminals', 'configuration', 'site', 'company')
                );
            }

            return view(
                'modules.site-integrations.configure',
                compact(
                    'integration',
                    'terminals',
                    'configuration',
                    'site',
                    'mappings',
                    'storedMappings',
                    'objects',
                    'dayParts',
                    'revenueCenters',
                    'company'
                )
            );
        }

        return view(
            'modules.site-integrations.configure',
            compact('integration', 'terminals', 'configuration', 'site', 'company')
        );
    }

    public function configurePost(Request $request, $integration_id)
    {
        $integration = SiteIntegrations::find($integration_id);

        if (!is_array($integration->configuration)) {
            $integration->configuration = json_decode($integration->configuration, true);
        }

        if ($integration->configuration['company_integration']) {
            $class = new SiteIntegrationCompanies();
        } else {
            $class = new SiteIntegrationSites();
        }

        $previousConfiguration = $class->where('company_id', Auth::user()->company_id)->where(
            'integration_id',
            $integration_id
        )->first();
        $rules = [];

        foreach ($integration->configuration['settings'] as $setting) {
            $rule = ['required'];
            if ($setting['type'] == 'url') {
                $rule = array_merge($rule, ['url']);
            }

            if ($setting['type'] != 'upload') {
                $rules = array_merge($rules, [$setting['setting_id'] => $rule]);
            }

            if (!isset($setting['required']) || $setting['required']) {
                if ($setting['type'] == 'url') {
                    $rule = array_merge($rule, ['url']);
                }
                if ($setting['type'] != 'upload') {
                    $rules = array_merge($rules, [$setting['setting_id'] => $rule]);
                }
            }

            if ($setting['setting_id'] === 'logo_image') {
                $rules[$setting['setting_id']] = [
                    File::types(['image/*']),
                    File::image()
                        ->max('1mb'),
                    'dimensions:max_width=800,max_height=400'
                ];
            }
        }

        if ($integration->configuration['select_master']) {
            $rules = array_merge($rules, ['master_terminal' => 'required']);
        }

        $this->validated = Validator::validate($request->all(), $rules);

        if ($integration->api_test_url) {
            $integrationTester = resolve('NewbridgeWeb\Http\Controllers\Integrations\Tests\\' . $integration->name);
            $check = $integrationTester->testIntegration($integration, $request->input());

            if ($check != null) {
                return $check;
            }
        }

        $this->override = $request->has('OverridePath') ? $request->input('OverridePath') : null;
        $this->input = $request->except(['revenue', '_token']);
        $this->integration = $integration;
        $this->company = Company::find(Auth::user()->company_id);
        $this->mappings = $request->input('revenue');
        $this->input['name'] = $this->company['company_name'];

        foreach ($integration->configuration['settings'] as $setting) {
            if ($setting['type'] == 'upload') {

                if (!empty($request->allFiles())) {
                    $path = Storage::disk('azurepublic')->put(
                        'images/originals',
                        $request->file($setting['setting_id']),
                        'public'
                    );
                    $this->input[$setting['setting_id']] = Storage::disk('azurepublic')->publicUrl($path);
                } elseif (isset($previousConfiguration->configuration[$setting['setting_id']])) {
                    $this->input[$setting['setting_id']] = $previousConfiguration->configuration[$setting['setting_id']];
                }
            }
        }

        // save pos settings or not
        if (!($this->integration->configuration['type'] == 'internal_settings')) {
            $this->updatePos();
        }

        if ($this->integration->configuration['company_integration']) {
            $this->updateCompanyConfig();
        } else {
            $this->updateSiteConfiguration();
        }

        if ($this->integration->configuration['process_api_events']) {
            $this->processApiEvents();
        }

        if (isset($this->integration->configuration['mappings']) && isset($this->integration->configuration['mappings']['company_mapping'])) {
            $this->companyIntegrationMapping();
        }

        if (isset($this->integration->configuration['mappings']) && isset($this->integration->configuration['mappings']['company_mapping']['pos_setting_id']) && $this->integration->configuration['mappings']['company_mapping']['pos_setting_id'] != null) {
            $this->companyIntegrationPosSetting();
        }

        Session::flash(
            'message',
            [
                'status' => 'success',
                'message' => ucfirst(
                        $this->integration->name
                    ) . ' successfully tested and updated, please wait 5 minutes then restart all terminals.'
            ]
        );

        return redirect('/integrations/' . $this->integration->id . '/' . $this->integration->name);
    }

    private function companyIntegrationMapping()
    {
        $connections = SiteIntegrationSites::where('integration_id', $this->integration->id)
            ->where('company_id', Auth::user()->company_id)
            ->get();

        $data = [];
        $data[$this->integration->configuration['mappings']['company_mapping']['group_as']] = [];

        foreach ($connections as $connection) {
            $mappingData = $connection->mappings;
            $connectionData = $connection->configuration;

            if ($mappingData != null) {
                $data['company_id'] = $connection->company_id;
                $data[$this->integration->configuration['mappings']['company_mapping']['group_as']][] = $this->formatMappings(
                    $mappingData,
                    $connectionData,
                    $connection->site_num
                );

                $revenueCentres = RevenueCenters::where('company_id', Auth::user()->company_id)->get();

                foreach ($revenueCentres as $revenueCentre) {
                    $terminal_ids = json_decode($revenueCentre->terminals);

                    foreach ($terminal_ids as $terminal_id) {
                        $terminal = Pos::find($terminal_id);
                        if (!empty($terminal)) {
                            $data['terminal_revenue'][] = [
                                'terminal_num' => $terminal->terminal_num,
                                'site_num' => $terminal->site_num,
                                'revenue_center_id' => $revenueCentre->id
                            ];
                        }
                    }
                }
                if (!empty($data['terminal_revenue'])) {
                    $data['terminal_revenue'] = array_values($data['terminal_revenue']);
                } else {
                    $data['terminal_revenue'] = [];
                }
            }
        }

        $companyIntegration = SiteIntegrationCompanies::where('integration_id', $this->integration->id)->firstOrNew();
        $companyIntegration->fill([
            'company_id' => session('user.company_id'),
            'mappings' => $data,
            'integration_id' => $this->integration->id
        ]);
        $companyIntegration->save();

        EventGridHelper::send('company_integration', $companyIntegration->toArray());
        EventGridHelper::send('company_integration_mappings_' . strtolower(Str::slug($this->integration->name, '_')), $companyIntegration->mappings);
    }

    private function companyIntegrationPosSetting()
    {
        $mappings = SiteIntegrationCompanies::where('integration_id', $this->integration->id)
            ->where('company_id', session('user.company_id'))
            ->first()->mappings;

        $setting = [
            'setting_id' => $this->integration->configuration['mappings']['company_mapping']['pos_setting_id'],
            'description' => $this->integration->configuration['mappings']['company_mapping']['pos_setting_name'],
            'json' => json_encode($mappings),
            'value' => ''
        ];

        $terminals = Pos::where('company_id', $this->company->id)->get();

        foreach ($terminals as $terminal) {
            $this->createOrUpdatePosSetting($setting, $terminal);
        }
    }

    public function formatMappings($mappings, $connection, $site_num)
    {
        $data = $mappings;
        $mappings = [];
        $mappings['site_num'] = $site_num;
        $mappings['connection'] = $connection;
        $revenueCentres = [];
        if (is_array($data)) {
            foreach ($data as $key => $revenue) {
                $revenueCenter = [];
                $revenueCenter['revenue_center_id'] = $key;

                foreach ($revenue as $revenue_type_key => $revenue_type) {
                    foreach ($revenue_type as $key2 => $item) {
                        $newRevenueType = [
                            'default_mapping' => [
                                'mapping_guid' => $key2,
                                'mapping_code' => $item['id']
                            ],
                            'dayparts' => []
                        ];

                        if (isset($item['dayparts'])) {
                            foreach ($item['dayparts'] as $depday => $daypart) {
                                $newRevenueType['dayparts'][] = [
                                    'mapping_guid' => $depday,
                                    'mapping_code' => $daypart
                                ];
                            }
                        }
                        $revenueCenter[$revenue_type_key][] = $newRevenueType;
                    }
                }
                $revenueCentres[] = $revenueCenter;
            }
        }
        $mappings['revenue'] = $revenueCentres;

        return $mappings;
    }

    private function processApiEvents(): void
    {
        if (!empty($this->integration->configuration['api_events'])) {
            foreach ($this->integration->configuration['api_events'] as $event) {

                $headers = [
                    'Content-Type' => 'application/json',
                    'Accepts' => 'application/json'
                ];

                foreach ($event['headers'] as $header) {
                    if ($header['encoding']) {
                        if ($header['encoding'] == 'base64_hmac256') {
                            $headers[$header['header']] = base64_encode(
                                hash_hmac('sha256', $header['value'], $header['salt'], true)
                            );
                        }
                    } else {
                        if(stristr($header['value'], 'config::')){
                            $header['value'] = explode('::', $header['value'])[1];
                            $header['value'] = config($header['value']);
                        }
                        $headers[$header['header']] = $header['value'];
                    }
                }

                foreach($this->input as $k => $input)
                {
                    if($input == "true" || $input == "false")
                    {
                        $this->input[$k] = $input === "true";
                    }
                }

                try {
                    $client = new Client(['headers' => $headers, 'verify' => false]);
                    $result = $client->post($event['endpoints'][config('app.env')], ['body' => json_encode($this->input)]);

                    Log::info('API Event Sent', ['event' => $event, 'result' => $result->getBody()->getContents()]);
                } catch (Exception $e) {

                    report($e);
                }
            }
        }
    }

    private function updateCompanyConfig()
    {
        $siteIntegration = SiteIntegrationCompanies::firstOrNew([
            'company_id' => Auth::user()->company_id,
            'integration_id' => $this->integration->id
        ]);

        if ($this->integration->configuration['generate_api_tokens']) {
            if ($siteIntegration->app_key == null && $siteIntegration->app_secret == null) {
                $siteIntegration->app_key = Uuid::uuid4();
                $siteIntegration->app_secret = Uuid::uuid4();
            }
        }

        $siteIntegration->configuration = $this->input;
        $siteIntegration->save();

        $this->input['app_key'] = $siteIntegration->app_key;
        $this->input['app_secret'] = $siteIntegration->app_secret;

        EventGridHelper::send('company_integration_settings', $siteIntegration->toArray());

        return $siteIntegration;
    }

    /**
     * @throws Exception
     */
    public function updateSiteConfiguration(bool $serviceBus = null, string $topic = null): SiteIntegrationSites
    {
        $siteIntegration = SiteIntegrationSites::firstOrNew([
            'company_id' => Auth::user()->company_id,
            'site_num' => session('current_site'),
            'integration_id' => $this->integration->id
        ]);

        if ($this->integration->configuration['generate_api_tokens']) {
            if ($siteIntegration->app_key == null && $siteIntegration->app_secret == null) {
                $siteIntegration->app_key = Uuid::uuid4();
                $siteIntegration->app_secret = Uuid::uuid4();
            }
        }

        $siteIntegration->configuration = $this->input;
        $siteIntegration->mappings = $this->mappings;
        $siteIntegration->save();

        $this->input['app_key'] = $siteIntegration->app_key;
        $this->input['app_secret'] = $siteIntegration->app_secret;

        EventGridHelper::send('site_integration_settings', $siteIntegration->toArray());

        $this->sendServiceBusUpdate($siteIntegration->toArray());

        return $siteIntegration;
    }

    /**
     * @throws Exception
     */
    public function sendServiceBusUpdate(array $integration): void
    {
        $serviceBus = $this->integration->configuration['service_bus'] ?? null;
        Log::info('Service Bus WooCommerce Before Send', [
            'service_bus' => $serviceBus,
            'integration' => $integration
        ]);
        if ($serviceBus !== null) {
            if($serviceBus == 'woocommerce') {
                ServiceBusHelper::sendEventToTopic('woo-config',
                    'config/update/'.$integration['company_id'].'/'.$integration['site_num'],
                    'update',
                    array_merge([
                        "group_id" => $integration['company_id'],
                        "site_id" => $integration['site_num'],

                    ], $integration['configuration']),
                    false,
                    'woocommerce.service_bus_sas_token',
                    'woocommerce.service_bus_url',
                    'NewbridgePolicy');

                Log::info('Service Bus WooCommerce After Send', [
                    'service_bus' => $serviceBus,
                    'integration' => $integration,
                    'woocommerce.service_bus_url' => config('woocommerce.service_bus_url'),
                    'url' => config('woocommerce.service_bus_url') . '/woo-config/messages'
                ]);
            } else {

                ServiceBusHelper::sendEventToTopic('integration-config',
                    'config/update/'.$integration['company_id'].'/'.$integration['site_num'],
                    'update',
                    $integration);
            }
        }
    }

    private function updatePOS()
    {
        $master = isset($this->input['master_terminal']) ?? null;

        /**
         * Update all POS Settings
         */
        foreach ($this->integration->configuration['settings'] as $setting) {
            $this->updatePosSetting($setting, $this->input[$setting['setting_id']], $master);
        }

        /**
         * Set Master Terminal Settings and reset other terminals to default
         */
        if ($this->integration->configuration['select_master']) {
            foreach ($this->integration->configuration['master_settings'] as $setting) {
                $this->updatePosMasterSetting($setting, $this->input['master_terminal'], $this->override);
            }
        }

        /**
         * Run through all terminal settings
         */
        if (!empty($this->integration->configuration['all_terminal_settings'])) {
            foreach ($this->integration->configuration['all_terminal_settings'] as $setting) {
                $this->updateAllOtherTerminals($setting, $master, $this->override);
            }
        }
    }

    private function updatePosSetting($setting, $value = null, $master = null)
    {
        $valueString = 'value' . $setting['pos_setting_value_key'];

        if (!$setting['master_only']) {
            $posSettings = PosSettings::where('company_id', Auth::user()->company_id)
                ->where('site_num', Session::get('current_site'))
                ->where('setting_id', $setting['setting_id'])
                ->where('setting_id', $setting['setting_id'])->get();
        } else {
            $posSettings = PosSettings::where('company_id', Auth::user()->company_id)
                ->where('site_num', Session::get('current_site'))
                ->where('setting_id', $setting['setting_id'])
                ->where('setting_id', $setting['setting_id'])
                ->where('terminal_num', $master)->get();
        }

        if (!empty($posSettings)) {
            foreach ($posSettings as $posSetting) {
                $posSetting->$valueString = $value;
                $posSetting->received_from = 0;
                $posSetting->save();

                Event::dispatch(new UpdaterUpdateCrudEvent($posSetting));
            }
        }

        if ($setting['master_only']) {
            $posSettings = PosSettings::where('company_id', Auth::user()->company_id)
                ->where('site_num', Session::get('current_site'))
                ->where('setting_id', $setting['setting_id'])
                ->where('setting_id', $setting['setting_id'])
                ->where('terminal_num', '!=', $master)->get();

            if (!empty($posSettings)) {
                foreach ($posSettings as $posSetting) {
                    $posSetting->$valueString = null;
                    $posSetting->received_from = 0;
                    $posSetting->save();

                    Event::dispatch(new UpdaterUpdateCrudEvent($posSetting));
                }
            }
        }
    }

    private function updatePosMasterSetting($setting, $master_num, $path = null)
    {
        if (stristr($setting['pos_setting_default_value'], '@select_master@')) {
            if ($path == null) {
                $setting['pos_setting_default_value'] = str_replace(
                    '@select_master@',
                    $master_num,
                    $setting['pos_setting_default_value']
                );
            } else {
                $setting['pos_setting_default_value'] = $path;
            }
        }

        $valueString = 'value' . $setting['pos_setting_value_key'];

        $posSetting = PosSettings::where('company_id', Auth::user()->company_id)
            ->where('site_num', session('current_site'))
            ->where('setting_id', $setting['setting_id'])
            ->where('terminal_num', $master_num)
            ->first();

        if ($posSetting != null) {
            $posSetting->$valueString = $setting['pos_setting_default_value'];
            $posSetting->received_from = 0;
            $posSetting->save();

            Event::dispatch(new UpdaterUpdateCrudEvent($posSetting));
        }

        $otherPosSettings = PosSettings::where('company_id', Auth::user()->company_id)
            ->where('site_num', session('current_site'))
            ->where('setting_id', $setting['setting_id'])
            ->where('terminal_num', '!=', $master_num)
            ->get();

        if (!empty($otherPosSettings)) {
            foreach ($otherPosSettings as $otherPosSetting) {
                $otherPosSetting->$valueString = $setting['pos_setting_clear_value'];
                $otherPosSetting->received_from = 0;
                $otherPosSetting->save();

                Event::dispatch(new UpdaterUpdateCrudEvent($otherPosSetting));
            }
        }
    }

    private function updateAllOtherTerminals($setting, $master, $path = null)
    {
        if (stristr($setting['pos_setting_default_value'], '@select_master@')) {
            if ($path == null) {
                $setting['pos_setting_default_value'] = str_replace(
                    '@select_master@',
                    $master,
                    $setting['pos_setting_default_value']
                );
            } else {
                $setting['pos_setting_default_value'] = $path;
            }
        }

        $valueString = 'value' . $setting['pos_setting_value_key'];

        $otherPosSettings = PosSettings::where('company_id', Auth::user()->company_id)
            ->where('site_num', Session::get('current_site'))
            ->where('setting_id', $setting['setting_id'])
            ->get();

        if (!empty($otherPosSettings)) {
            foreach ($otherPosSettings as $otherPosSetting) {
                $otherPosSetting->$valueString = $setting['pos_setting_default_value'];
                $otherPosSetting->received_from = 0;
                $otherPosSetting->save();
                Event::dispatch(new UpdaterUpdateCrudEvent($otherPosSetting));
            }
        }
    }

    public function createOrUpdatePosSetting($setting, $terminal)
    {
        $hasDefaultSetting = PosSettings::where('company_id', $terminal->company_id)
            ->where('site_num', $terminal->site_num)
            ->where('terminal_num', $terminal->terminal_num)
            ->where('setting_id', $setting['setting_id'])
            ->first();

        if ($hasDefaultSetting != null) {
            $hasDefaultSetting->Value1 = $setting['value'];
            $hasDefaultSetting->Value7 = $setting['json'];
            $hasDefaultSetting->received_from = 0;
            $hasDefaultSetting->save();
            sleep(1);
            Event::dispatch(new UpdaterUpdateCrudEvent($hasDefaultSetting));
        } else {
            $newSetting = new PosSettings();
            $newSetting->guid = Uuid::uuid4();

            $newSetting->isreadonly = 0;
            $newSetting->company_id = $terminal->company_id;
            $newSetting->site_num = $terminal->site_num;
            $newSetting->terminal_num = $terminal->terminal_num;

            $newSetting->setting_id = $setting['setting_id'];
            $newSetting->label = $setting['description'];
            $newSetting->description = $setting['description'];
            $newSetting->category = 16;

            $newSetting->value1 = $setting['value'];
            $newSetting->value7 = $setting['json'];
            $newSetting->location = 0;
            $newSetting->type = 2;
            $newSetting->value2 = 0;
            $newSetting->received_from = 0;
            $newSetting->received_at = Carbon::now();
            $newSetting->save();
            sleep(1);
            Event::dispatch(new UpdaterUpdateCrudEvent($newSetting));
        }
    }
}
