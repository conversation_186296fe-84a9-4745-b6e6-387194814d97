<?php

namespace NewbridgeWeb\Http\Controllers\Purge;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosSettings;
use NewbridgeWeb\Repositories\Purge;
use NewbridgeWeb\Repositories\PurgeSettings;
use NewbridgeWeb\Repositories\Sites;

class PurgeController extends Controller
{
    public function index()
    {
        $sites = Sites::where('company_id', Auth::user()->company_id)->get();

        return view('modules.purge-files.index', compact('sites'));
    }

    public function loadForm($site_num)
    {
        $purgeSettings = Purge::with(['purgeSetting' => function ($q) use ($site_num) {
            $q->where('company_id', Auth::user()->company_id)
                ->where('site_num', $site_num);
        }])->select('id', 'setting_id', 'displayname')->get()->toArray();

        return response()->json(['html' => view('modules.purge-files.form', compact('purgeSettings'))->render()], 200);
    }

    public function updateTerminalSettings(Request $request)
    {
        $input  = $request->input();
        $site = $request->input('site');

        $validation = ['StackTracePath_duration', 'ApplicationLogPath_duration', 'Dump_duration'];

        foreach ($validation as $valid) {
            if ($input[$valid] == 0) {
                $input[$valid] = null;
            }
        }

        $purgeSettings = Purge::all();

        foreach ($purgeSettings as $purge) {
            $posSettings = PosSettings::where('company_id', Auth::user()->company_id)
                ->where('site_num', $site)
                ->where('setting_id', $purge->setting_id)
                ->get();

            foreach ($posSettings as $posSetting) {
                $posSetting->value2 = $input[$purge->setting_id.'_duration'];
                $posSetting->value3 = 2;
                $posSetting->value4 = $purge->path;
                $posSetting->save();

                $posPurgeSetting = PurgeSettings::firstOrNew(['company_id' => Auth::user()->company_id, 'site_num' => $site, 'purge_id' => $purge->id]);
                $posPurgeSetting->purge_id = $purge->id;
                $posPurgeSetting->company_id = $posSetting->company_id;
                $posPurgeSetting->site_num = $input['site'];
                $posPurgeSetting->value2 = $input[$purge->setting_id.'_duration'];
                $posPurgeSetting->value3 = 2;
                $posPurgeSetting->value4 = $purge->path;
                $posPurgeSetting->save();

                \Event::dispatch(new UpdaterUpdateCrudEvent($posSetting));
            }
        }

        return response()->json(['status' => 'success'], 200);
    }
}
