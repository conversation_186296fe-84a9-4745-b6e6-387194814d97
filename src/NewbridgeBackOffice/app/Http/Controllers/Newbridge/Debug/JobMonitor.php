<?php

namespace NewbridgeWeb\Http\Controllers\Newbridge\Debug;

use Illuminate\Support\Facades\Log;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\JobStatus\JobStatus;
use NewbridgeWeb\Repositories\RedisRepository;
use NewbridgeWeb\Repositories\RedisScan;

class JobMonitor extends Controller
{
    public function view()
    {

        $scan = new RedisScan();
        Log::info('scanAll(jobmonitor) start');

        $imports = $scan->scanAllForMatch('monitor*');
        $activeImports = [];

        foreach($imports as $import) {
            $job = json_decode(RedisRepository::getByKey($import), true);
            $jobStatus = new JobStatus($job);
            $activeImports[] = $jobStatus;
        }

        $activeImports = collect($activeImports);

        $activeImports = $activeImports->sortByDesc(fn ($a, $b) => $a->start);


        return view('modules.newbridge.debug.import-monitor', compact('activeImports'));
    }
}
