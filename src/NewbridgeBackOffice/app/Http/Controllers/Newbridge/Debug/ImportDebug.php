<?php

namespace NewbridgeWeb\Http\Controllers\Newbridge\Debug;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Jobs\ProcessDebugDataImportJob;
use NewbridgeWeb\Repositories\JobStatus\JobStatus;
use NewbridgeWeb\Repositories\RedisRepository;
use Ramsey\Uuid\Uuid;

class ImportDebug extends Controller
{
    public function uploadDebugImportForm(Request $request): \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\Contracts\Foundation\Application
    {
        if(!app()->environment('production')) {
            return view('modules.newbridge.debug.debug-file-upload');
        } else {
            abort(401);
        }
    }

    public function doUploadAndImport(Request $request): \Illuminate\Routing\Redirector|\Illuminate\Http\RedirectResponse|\Illuminate\Contracts\Foundation\Application
    {
        if(!app()->environment('production')) {
            $errors = [];

            if ($request->hasFile('debug-import')) {
                $file = $request->file('debug-import');
                $uuid = Uuid::uuid4();
                $dateTime = Carbon::now()->toDateTimeString();
                $filename = $dateTime.'-'.$file->getClientOriginalName();
                $store = Storage::putFile('/debug/import-files', $file);
                dispatch((new ProcessDebugDataImportJob($uuid, $store)));

                $this->updatejobStatus(['uuid' => $uuid, 'start' => Carbon::now(), 'type' => 'Debug Import']);

                $message = ['status' => 'success', 'message' => 'File uploaded successfully and added to the queue for processing, if in local remember to run the background worker with a long timeout..'];
                \Session::flash('message', $message);

                return redirect("/newbridge/debug-data/monitor")->with("errors", $errors);

            } else {
                $message = ['status' => 'error', 'message' => 'Please select a file to upload!'];
                \Session::flash('message', $message);

                return redirect("/newbridge/debug-data/upload")->with("errors", $errors);
            }
        } else {
            abort(401);
        }
    }

    public static function updatejobStatus(array $data = []): void
    {
        $jobStatus = JobStatus::updateOrCreate($data);
        RedisRepository::store('monitor-'.$data['uuid'], $jobStatus->toJson());
    }
}
