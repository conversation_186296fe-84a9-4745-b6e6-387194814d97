<?php

namespace NewbridgeWeb\Http\Controllers\Newbridge;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Events\ReSyncEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Modules;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\RedisRepository;
use Ramsey\Uuid\Uuid;

class ModulesController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return mixed
     */
    public function view($site_num)
    {
        $statuses = [];
        $statuses['results'] = [];

        $modules = Modules::All();

        foreach ($modules as $module) {
            $key = 'resync:' . Auth::user()->company_id . ':' . $module['update_id'];
            if (Redis::exists($key)) {
                $stat = json_decode(Redis::get($key), true);
                $stat['key'] = $key;
                $statuses['results'][] = $stat;
            }
        }

        $terminals = Pos::where('company_id', Auth::user()->company_id)
            ->where('site_num', $site_num)
            ->orderBy('terminal_num', 'desc')
            ->get();

        $statuses = json_encode($statuses);

        return view('modules.newbridge.push-modules.view', compact('modules', 'statuses', 'terminals', 'site_num'));
    }

    /**
     * Remove From Queue
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeFromQueue(Request $request)
    {
        Redis::del($request->input('key'));

        return response()->json(['status' => 'success']);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function sync(Request $request)
    {
        $input = $request->input();
        $data = ['update_id' => $input['update_id'], 'company_id' => $input['company_id'], 'site_num' => [$input['site_num'], 0]];

        $key = 'resync:' . $input['company_id'] . ':' . $input['update_id'];
        $status = [
            'datetime' => Carbon::now(),
            'status' => 'started',
            'guid' => Uuid::uuid4(),
            'records' => 0,
            'collectedby' => [],
            'update_id' => $input['update_id']
        ];

        Redis::set($key, json_encode($status));
        Redis::expire($key, (config('newbridge.report_expire_time') * 60));

        \Event(new ReSyncEvent($data));


        return response()->json(['status' => 'ok', 'key' => $key], 200);
    }

    /**
     * Create a new function to check the
     * status of the job.
     *
     * Return the status object from redis into the browser
     */
    public function getStatus(Request $request)
    {
        $keys = $request->input('key');

        // keys is a list of keys, loop through them below and add the results to an
        // array with the update_id as they key
        $results = [];

        if (!empty($keys)) {
            foreach ($keys as $k => $key) {
                $status = json_decode(Redis::get($key), true);
                $status['terminals'] = [];

                if ($status['status'] == 'awaiting-collection' || $status['status'] == 'collecting') {
                    $terminals = Pos::where('company_id', Auth::user()->company_id)
                        ->where('site_num', 1)->get();

                    foreach ($terminals as $terminal) {
                        $redisKey = 'c-' . Auth::user()->company_id . ':t-' . $terminal->id . ':updates:' . $status['update_id'] . ':*';

                        $status['terminals'][$terminal->id] = [
                            'terminal_num' => $terminal->terminal_num,
                            'name' => $terminal->name,
                            'remaining' => RedisRepository::count($redisKey)
                        ];

                        if ($status['terminals'][$terminal->id]['remaining'] < $status['queued']) {
                            $results['status'] = 'collecting';
                        }
                    }

                    $completed = 0;
                    foreach ($status['terminals'] as $term) {
                        if ($term['remaining'] == 0) {
                            $completed++;
                        }
                    }

                    if ($completed == count($status['terminals'])) {
                        $status['status'] = 'completed';
                    }
                }

                $results[$key] = $status;
                Redis::set($key, json_encode($results[$key]));
                Redis::expire($key, (config('newbridge.report_expire_time') * 60));
            }
        }

        return response()->json(['results' => $results], 200);
    }
}
