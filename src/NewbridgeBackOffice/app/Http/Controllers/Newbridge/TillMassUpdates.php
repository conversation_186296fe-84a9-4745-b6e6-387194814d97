<?php

namespace NewbridgeWeb\Http\Controllers\Newbridge;

use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosSettings;

class TillMassUpdates extends Controller
{
    public function setAllTillsPollingInterval(): string
    {
        $settings = PosSettings::where('setting_id', 'SendDataInterval')->get();

        foreach($settings as $setting) {
            $setting->value1 = 180000;
            $setting->save();

            \Event::dispatch(new UpdaterUpdateCrudEvent($setting));

        }

        return 'Done';
    }
}
