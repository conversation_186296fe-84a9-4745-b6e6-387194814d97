<?php

namespace NewbridgeWeb\Http\Controllers\Newbridge;

use Auth;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Sites;
use Request;

class PayWallController extends Controller
{
    public function noAccess(Request $request)
    {
        $module = '';
        $site = Sites::where('company_id', Auth::user()->company_id)->where('site_num', \Session::get('current_site'))->first();

        return view('modules.newbridge.paywall', compact('module', 'site'));
    }
}
