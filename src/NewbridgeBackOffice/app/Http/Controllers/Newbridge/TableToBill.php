<?php

namespace NewbridgeWeb\Http\Controllers\Newbridge;

use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;

class TableToBill extends Controller
{
    public function index()
    {
        return view('modules.newbridge.convert-xml.index');
    }

    public function convert(Request $request)
    {
        if ($request->hasFile('table')) {
            $file = $request->file('table');
            $contents = file_get_contents($file->getRealPath());
            $contents = preg_replace('/(<\?xml[^?]+?)utf-16/i', '$1utf-8', $contents);

            $xml = simplexml_load_string($contents);
            $json = json_encode($xml);
            $array = json_decode($json, true);


            $bill = $array; //$array['Items']['TRAN_Details'];

            return view('modules.newbridge.convert-xml.xml-convert', compact('bill'));
        }
    }
}
