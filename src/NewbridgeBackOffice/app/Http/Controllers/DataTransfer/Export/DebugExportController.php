<?php

namespace NewbridgeWeb\Http\Controllers\DataTransfer\Export;

use Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use NewbridgeWeb\Exports\Debug\DebugExport;
use NewbridgeWeb\Http\Controllers\Newbridge\Debug\ImportDebug;
use NewbridgeWeb\Jobs\DebugExportJob;
use Ramsey\Uuid\Uuid;

class DebugExportController
{
    public function startExport()
    {
        $uuid = Uuid::uuid4();
        $dateTimeString = Carbon::now()->toDateTimeString();
        $path = '/debug-export-files/'.$uuid.'.xlsx';

        $debug = new ImportDebug();
        $debug->updatejobStatus(['uuid' => $uuid, 'start' => Carbon::now(), 'download_path' => $path, 'type' => 'Debug Export']);

        $export = new DebugExport($uuid, Auth::user()->company_id, \Session::get('current_site'));

        dispatch((new DebugExportJob($uuid, Auth::user()->company_id, \Session::get('current_site'), $dateTimeString)));

        return response()->redirectTo('/newbridge/debug-data/monitor');
    }

    public function download($path)
    {
        return Storage::download('/debug-export-files/'.$path);
    }
}
