<?php

namespace NewbridgeWeb\Http\Controllers\DataTransfer\Export\NewbridgeExport\ExportSheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\Suppliers;

class SuppliersExport implements FromCollection, WithHeadings, WithTitle
{
    protected $company_id;

    public function __construct(int $company_id)
    {
        $this->company_id = $company_id;
    }

    public function collection()
    {
        return  Suppliers::select(
            'id',
            'name',
            'address1',
            'address2',
            'address3',
            'postcode',
            'tel',
            'fax',
            'email',
            'contact_name'
        )->where('company_id', $this->company_id)->get();
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Suppliers';
    }

    public function headings(): array
    {
        return [
            'id',
            'name',
            'address1',
            'address2',
            'address3',
            'postcode',
            'tel',
            'fax',
            'email',
            'contact_name'
        ];
    }
}
