<?php

namespace NewbridgeWeb\Http\Controllers\DataTransfer\Export\NewbridgeExport\ExportSheets;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use NewbridgeWeb\Repositories\Departments;

class DepartmentExport implements FromCollection, WithHeadings, WithTitle
{
    protected $company_id;

    public function __construct(int $company_id)
    {
        $this->company_id = $company_id;
    }

    public function collection()
    {
        return  Departments::selectRaw('id, displayname, acc_code')->where('company_id', $this->company_id)->get();
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Departments';
    }

    public function headings(): array
    {
        return [
            'id',
            'displayname',
            'acc_code'
        ];
    }
}
