<?php

namespace NewbridgeWeb\Http\Controllers\DataTransfer\Export;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class ReportsExport implements FromView
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function view(): View
    {
        return view($this->data['view'], $this->data['compact']);
    }
}
