<?php

namespace NewbridgeWeb\Http\Controllers\DataTransfer\Import;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Imports\RecipesImport;
use NewbridgeWeb\Jobs\BackgroundImportCustomersJob;
use NewbridgeWeb\Jobs\BackgroundImportJob;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\CustomerGroup;
use NewbridgeWeb\Repositories\CustomerTransactions;
use NewbridgeWeb\Repositories\CustomerTypes;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Import;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Recipes;
use NewbridgeWeb\Repositories\SKU;
use NewbridgeWeb\Repositories\SubDepartment;
use NewbridgeWeb\Repositories\Suppliers;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use NewbridgeWeb\User;
use Ramsey\Uuid\Uuid;

class ImportController extends Controller
{
    public $products;
    public $data;
    public $errors;

    public function __construct()
    {
        //        public $this->data->departments = [];
        //        public $this->data->subdepartments = [];
        //        public $this->data->suppliers = [];
        //        public $this->data->taxrates = [];
        //        public $this->data->skus = [];

        $this->data = new \StdClass();
    }

    public function index()
    {
        // TODO:: add import list history below the import box (datatables, paginate)

        return view('modules.import.index');
    }

    public function show($id)
    {
        $import = Import::findorFail($id);
        if ($import->user_id !== Auth::id()) {
            return redirect('/import');
        }

        return view('modules.import.show_progress', compact('import'));
    }

    public function checkStatus($guid)
    {
        $status = Redis::get('import-' . $guid . '-status');

        return $status;
    }

    public function recipesIndex()
    {
        return view('modules.import.recipes-index');
    }

    public function findAllByUser()
    {
        $imports = Import::where('user_id', Auth::id())->orderBy('updated_at', 'DESC')->get();

        return view('modules.import.imports_by_user', ['imports' => $imports]);
    }

    public function storeSheetsForFurtherProcessing(Request $request)
    {
        $guid = Uuid::uuid4();
        $ext = ".xlsx";
        $filename = (string)$guid . $ext;
        $file = $request->file('import');
        $location = Storage::putFileAs('/imports', $file, $filename);


        $import = new Import();
        $import->user_id = Auth::id();
        $import->file_location = "/" . $location;
        $import->upload_status = Import::IN_PROGRESS;
        $import->guid = $guid;
        $import->save();
        Redis::set(
            'import-' .
            $import->guid . '-status',
            json_encode(['status' => 'warning',
                'text' => 'Request received',
                'percent' => '50'])
        );
        dispatch(new BackgroundImportJob($import)); // run job

        return redirect('/import/' . $import->id . "/progress");
    }

    public function processImportedSheet(Import $import)
    {
        ini_set('max_execution_time', 3500);
        set_time_limit(3500);

        $user = User::find($import['user_id']);

        if (!Storage::exists($import->file_location)) {
            throw new \Exception("File not found");
        }

        $document = Storage::get($import->file_location);

        /**
         * Products
         */
        if ($document) {
            $data = Excel::toCollection(new \NewbridgeWeb\Imports\MainImport\ImportData(), $import->file_location);

            $company_id = $user->company_id;

            // 0 products
            // 1 Suppliers
            // 2 Departments
            // 3 SubDepartments
            // 4 Tax Rates
            // 5 SKU
            // 6 Customer Groups
            // 7 Customers

            DB::beginTransaction();

            $data = json_decode(json_encode($data));


            $percent = 35 / 12;
            $redis = json_decode(Redis::get('import-' . $import->guid . '-status'), true);

            $redisPercent = (float)$redis['percent'];

            try {
                $errors = [];

                if (!empty($data) && $data > 0) {
                    $redisPercent += $percent;
                    Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Importing Suppliers', 'percent' => $redisPercent]));

                    foreach ($data[1] as $k => $dep) {
                        if (!property_exists($dep, 'name') || $dep->name == '') {
                            $errors[] = ['type' => 'required', 'section' => 'Suppliers', 'row' => $k + 2, 'col' => 'Name', 'message' => 'Name is a required field and is missing in row ' . $k];
                        } else {
                            $supplierFill = array_filter((array)$dep, function ($value) {
                                return !is_null($value) && $value !== '';
                            });
                            unset($supplierFill['']);

                            $supplier = Suppliers::firstOrNew(['name' => $dep->name, 'company_id' => $company_id]);
                            $supplier->fill((array)$supplierFill); // Ex. Too few arguments to function
                            $supplier->guid = $supplier->guid != null && $supplier->guid != '' ? $supplier->guid : Uuid::uuid4();
                            $supplier->company_id = $company_id;
                            $supplier->received_at = '1970-01-01 00:00:00';
                            $supplier->received_from = 0;
                            $supplier->save();

                            $this->data->suppliers[trim(strtolower($supplier->name))] = $supplier;
                        }
                    }

                    $redisPercent += $percent;
                    Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Importing Departments', 'percent' => $redisPercent]));

                    foreach ($data[2] as $k => $dep) {
                        if (!property_exists($dep, 'displayname') || $dep->displayname == '') {
                            $errors[] = ['type' => 'required', 'section' => 'Departments', 'row' => $k + 2, 'col' => 'DisplayName'];
                        } else {
                            $departmentFill = array_filter((array)$dep, function ($value) {
                                return !is_null($value) && $value !== '';
                            });
                            unset($departmentFill['']);

                            $department = Departments::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                            $department->fill((array)$departmentFill);
                            $department->guid = $department->guid != null && $department->guid != '' ? $department->guid : Uuid::uuid4();
                            $department->company_id = $company_id;
                            $department->received_at = '1970-01-01 00:00:00';
                            $department->received_from = 0;
                            $department->save();

                            $this->data->departments[trim(strtolower($department->displayname))] = $department;
                        }
                    }

                    $redisPercent += $percent;
                    Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Importing Sub Departments', 'percent' => $redisPercent]));


                    foreach ($data[3] as $k => $dep) {
                        if (!property_exists($dep, 'displayname') || $dep->displayname == '' || $dep->displayname == null) {
                            $errors[] = ['type' => 'required', 'section' => 'SubDepartments', 'row' => $k + 2, 'col' => 'DisplayName'];
                        } else {
                            $departmentFill = array_filter((array)$dep, function ($value) {
                                return !is_null($value) && $value !== '';
                            });
                            unset($departmentFill['']);

                            $subdepartment = SubDepartment::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                            $subdepartment->fill((array)$departmentFill);
                            $subdepartment->guid = $subdepartment->guid != null && $subdepartment->guid != '' ? $subdepartment->guid : Uuid::uuid4();
                            $subdepartment->print_priority = $subdepartment->print_priority != null ? $subdepartment->print_priority : 0;
                            $subdepartment->company_id = $company_id;
                            $subdepartment->acc_code = $dep->acc_code != null ? $dep->acc_code : 0;
                            $subdepartment->received_at = '1970-01-01 00:00:00';
                            $subdepartment->received_from = 0;
                            $subdepartment->save();

                            $this->data->subdepartments[trim(strtolower($subdepartment->displayname))] = $subdepartment;
                        }
                    }

                    if (count($data[4]) > 0) {
                        $redisPercent += $percent;
                        Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Importing Tax Rates', 'percent' => $redisPercent]));

                        foreach ($data[4] as $k => $dep) {
                            if (!property_exists($dep, 'displayname') || $dep->displayname == '' || $dep->displayname == null) {
                                $errors[] = ['type' => 'required', 'section' => 'Tax Rates', 'row' => $k + 2, 'col' => 'DisplayName'];
                            } else {
                                $departmentFill = array_filter((array)$dep, function ($value) {
                                    return !is_null($value) && $value !== '';
                                });
                                unset($departmentFill['']);

                                $taxrate = TaxRates::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                                $taxrate->fill((array)$departmentFill);
                                $taxrate->company_id = $company_id;
                                $taxrate->guid = $taxrate->guid != null && $taxrate->guid != '' ? $taxrate->guid : Uuid::uuid4();
                                $taxrate->received_at = '1970-01-01 00:00:00';
                                $taxrate->received_from = 0;
                                $taxrate->save();

                                $this->data->taxrates[trim(strtolower($taxrate->displayname))] = $taxrate;
                            }
                        }
                    } else {
                        $errors[] = ['type' => 'sectionblank', 'section' => 'Tax Rates'];
                    }

                    $redisPercent += $percent;
                    Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Importing SKU\'s', 'percent' => $redisPercent]));

                    foreach ($data[5] as $k => $dep) {
                        if (!property_exists($dep, 'displayname') || $dep->displayname == '') {
                            $errors[] = ['type' => 'required', 'section' => 'SKU\'s', 'row' => $k + 2, 'col' => 'DisplayName'];
                        } else {
                            $departmentFill = array_filter((array)$dep, function ($value) {
                                return !is_null($value) && $value !== '';
                            });
                            unset($departmentFill['']);

                            $sku = SKU::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                            $sku->fill((array)$departmentFill);
                            $sku->company_id = $company_id;
                            $sku->guid = $sku->guid != null && $sku->guid != '' ? $sku->guid : Uuid::uuid4();
                            $sku->received_at = '1970-01-01 00:00:00';
                            $sku->received_from = 0;
                            $sku->save();

                            $this->data->skus[trim(strtolower($sku->displayname))] = $sku;
                        }
                    }

                    /**
                     * Report errors before processing products
                     */
                    if (count($errors) > 0) {
                        DB::rollback();

                        Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'error', 'text' => 'Import Failed with Errors', 'percent' => '100', 'errors' => $errors]));
                        $import->upload_status = Import::FAILED;
                        $import->errors = $this->errorArrayToString($errors);
                        $import->save();

                        return;
                    }


                    $count = count($data[0]);
                    if ($count > 0) {
                        $i = 0;
                        $percent = 10 / $count;

                        $redis = json_decode(Redis::get('import-' . $import->guid . '-status'), true);
                        $newPercent = $redisPercent;

                        foreach ($data[0] as $k => $prod) {
                            if ($prod->displayname != '' && $prod->displayname != null) {
                                if ($i == 10) {
                                    Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Importing Product ' . $k . ' of ' . $count, 'percent' => $newPercent]));
                                    $i = 0;
                                }

                                $i++;

                                $site_num = 0;
                                // departments
                                if (!isset($prod->site_num)) {
                                    $errors['site_num'] = ['type' => 'sectionblank', 'section' => 'Site Num (site_num)'];
                                } else {
                                    $site_num = $prod->site_num;
                                }

                                if ($prod->selling_price_1 == null) {
                                    $prod->selling_price_1 = 0;
                                }

                                $prodFill = array_filter((array)$prod, function ($value) {
                                    return !is_null($value) && $value !== '';
                                });
                                unset($prodFill['']);

                                $product = Products::firstOrNew(['displayname' => $prod->displayname, 'company_id' => $company_id, 'site_num' => $site_num]);
                                $product->fill($prodFill);
                                $product->rrp = $product->selling_price_1;
                                $product->guid = $product->guid == null && $product->guid == '' ? Uuid::uuid4() : $product->guid;
                                $product->CommandUID = $product->guid;
                                $product->company_id = $company_id;

                                $product->site_num = $site_num;

                                // departments
                                if ($prod->department_id != '') {
                                    if (isset($this->data->departments[trim(strtolower($prod->department_id))])) {
                                        $product->department_guid = $this->data->departments[trim(strtolower($prod->department_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Department', 'expected' => $prod->department_id];
                                    }
                                }

                                // sub departments
                                if ($prod->sub_department_id != '') {
                                    if (isset($this->data->subdepartments[trim(strtolower($prod->sub_department_id))])) {
                                        $product->sub_department_guid = $this->data->subdepartments[trim(strtolower($prod->sub_department_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Subdepartment', 'expected' => $prod->sub_department_id];
                                    }
                                }

                                // suppliers
                                if ($prod->supplier_id != '') {
                                    if (isset($this->data->suppliers[trim(strtolower($prod->supplier_id))])) {
                                        $product->supplier_guid = $this->data->suppliers[trim(strtolower($prod->supplier_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Supplier', 'expected' => $prod->supplier_id];
                                    }
                                }

                                // skus
                                if ($prod->sku_id != '' && $prod->plu_parent_id == '') {
                                    if (isset($this->data->skus[trim(strtolower($prod->sku_id))])) {
                                        $product->sku_guid = $this->data->skus[trim(strtolower($prod->sku_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Sku', 'expected' => $prod->sku_id];
                                    }
                                }

                                if (isset($prod->recipe_id)) {
                                    //recipe id
                                    if ($prod->recipe_id != '' && $prod->plu_parent_id == '' && $prod->sku_id == '') {
                                        $recipe = Recipes::where('name', $prod->recipe_id)->first();
                                        if ($recipe !== null) {
                                            $product->recipe_guid = $recipe['guid'];
                                            $product->recipe_qty = $prod->recipe_qty;
                                        } else {
                                            $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Recipe', 'expected' => $prod->recipe_id];
                                        }
                                    }
                                }

                                // parent products
                                if ($prod->plu_parent_id != '') {
                                    if (isset($this->data->products[trim(strtolower($prod->plu_parent_id)) . $prod->site_num])) {
                                        $product->sku_guid = null;
                                        $product->plu_parent_guid = $this->data->products[trim(strtolower($prod->plu_parent_id)) . $prod->site_num]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Parent Product', 'expected' => $prod->plu_parent_id];
                                    }
                                }

                                // tax ids
                                if ($prod->tax_id != '') {
                                    if (isset($this->data->taxrates[trim(strtolower($prod->tax_id))])) {
                                        $product->tax_guid = $this->data->taxrates[trim(strtolower($prod->tax_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Tax Rate', 'expected' => $prod->tax_id];
                                    }
                                } else {
                                    $errors[] = ['type' => 'required', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Tax Rate', 'message' => 'A Tax Rate  is a required field and is missing in row ' . $k + 2];
                                }

                                $product->KP_Target = $prod->kp_target != null ? $prod->kp_target : 0;
                                $product->received_at = '1970-01-01 00:00:00';
                                $product->received_from = 0;
                                $product->displayname = trim(ucfirst($prod->displayname));

                                $product->selling_price_2 = $prod->selling_price_2 != null ? $prod->selling_price_2 : $prod->selling_price_1;
                                $product->selling_price_3 = $prod->selling_price_3 != null ? $prod->selling_price_3 : $prod->selling_price_1;
                                $product->selling_price_4 = $prod->selling_price_4 != null ? $prod->selling_price_4 : $prod->selling_price_1;
                                $product->selling_price_5 = $prod->selling_price_5 != null ? $prod->selling_price_5 : $prod->selling_price_1;

                                unset($product->department_id);
                                unset($product->sub_department_id);
                                unset($product->supplier_id);
                                unset($product->sku_id);
                                unset($product->plu_parent_id);
                                unset($product->tax_id);
                                unset($product->kp_target);
                                unset($product->recipe_id);

                                $product->save();

                                $this->data->products[trim(strtolower($product->displayname)) . $product->site_num] = $product;
                            }
                        }
                    } else {
                        $errors[] = ['type' => 'missing', 'section' => 'Products'];
                    }
                }

                if (count($errors) > 0) {
                    DB::rollback();
                    Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'error', 'text' => 'We encountered errors parsing your file', 'percent' => '0', 'errors' => $errors]));


                    $import->upload_status = Import::FAILED;
                    $import->errors = $this->errorArrayToString($errors);
                    $import->save();

                    return;
                }

                DB::commit();

                Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'success', 'text' => 'Import complete!', 'percent' => '100']));
                $import->upload_status = Import::COMPLETE;
                $import->save();

                return;
            } catch (\Exception $e) {
                DB::rollback();
                Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'error', 'text' => 'There was an error in your import!', 'percent' => '100']));

                throw new \Exception($e);
            }
        }

        Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Document is corrupted!', 'percent' => 0]));
    }

    public function errorArrayToString($errors)
    {
        $result = "";
        foreach ($errors as $error) {
            foreach ($error as $k => $v) {
                $result .= $k . ": " . $v . ", ";
            }
        }

        return $result;
    }

    public function errorArrayToStringCustomer($errors)
    {
        $result = "";
        foreach ($errors as $error) {
            $result .= $error['message'] . ", ";

        }

        return $result;
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function upload(Request $request)
    {
        ini_set('max_execution_time', 3500);
        set_time_limit(3500);

        /**
         * TODO::
         *
         * Replace Request with get File from the storage class
         *
         * Load the file into
         *
         * TODO LAST::
         * Validate file based on columns, only return columns with headers (not yet)
         */

        /**
         * Departments
         */
        if ($request->input('company_name')) {
            if ($request->input('company_name') !== Auth::user()->company->company_name) {
                return redirect('/import')->with('error', 'Company incorrect');
            }
        }

        /**
         * Products
         */
        if ($request->hasFile('import')) {
            // TODO:: replace line above with file check in storage not request

            $data = Excel::toCollection(new ImportData(), $request->file()); //TODO - file);


            $company_id = Auth::user()->company_id;// TODO:: no access here

            // 0 products
            // 1 Suppliers
            // 2 Departments
            // 3 SubDepartments
            // 4 Tax Rates
            // 5 SKU
            // 6 Customer Groups
            // 7 Customers

            DB::beginTransaction();

            $data = json_decode(json_encode($data));


            try {
                $errors = [];

                if (!empty($data) && $data > 0) {
                    foreach ($data[1] as $k => $dep) {
                        if ($dep->name == '') {
                            $errors[] = ['type' => 'required', 'section' => 'Suppliers', 'row' => $k + 2, 'col' => 'Name', 'message' => 'Name is a required field and is missing in row ' . $k];
                        } else {
                            $supplier = Suppliers::firstOrNew(['name' => $dep->name, 'company_id' => $company_id]);
                            $supplier->fill((array)$dep);
                            $supplier->guid = $supplier->guid != null && $supplier->guid != '' ? $supplier->guid : Uuid::uuid4();
                            $supplier->company_id = $company_id;
                            $supplier->received_at = '1970-01-01 00:00:00';
                            $supplier->received_from = 0;
                            $supplier->save();

                            $this->data->suppliers[trim(strtolower($supplier->name))] = $supplier;
                        }
                    }

                    foreach ($data[2] as $k => $dep) {
                        if ($dep->displayname == '') {
                            $errors[] = ['type' => 'required', 'section' => 'Departments', 'row' => $k + 2, 'col' => 'DisplayName'];
                        } else {
                            $department = Departments::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                            $department->fill((array)$dep);
                            $department->guid = $department->guid != null && $department->guid != '' ? $department->guid : Uuid::uuid4();
                            $department->company_id = $company_id;
                            $department->received_at = '1970-01-01 00:00:00';
                            $department->received_from = 0;
                            $department->save();

                            $this->data->departments[trim(strtolower($department->displayname))] = $department;
                        }
                    }

                    foreach ($data[3] as $k => $dep) {
                        if ($dep->displayname == '' || $dep->displayname == null) {
                            $errors[] = ['type' => 'required', 'section' => 'SubDepartments', 'row' => $k + 2, 'col' => 'DisplayName'];
                        } else {
                            $subdepartment = SubDepartment::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                            $subdepartment->fill((array)$dep);
                            $subdepartment->guid = $subdepartment->guid != null && $subdepartment->guid != '' ? $subdepartment->guid : Uuid::uuid4();
                            $subdepartment->print_priority = $subdepartment->print_priority != null ? $subdepartment->print_priority : 0;
                            $subdepartment->company_id = $company_id;
                            $subdepartment->acc_code = $dep->acc_code != null ? $dep->acc_code : 0;
                            $subdepartment->received_at = '1970-01-01 00:00:00';
                            $subdepartment->received_from = 0;
                            $subdepartment->save();

                            $this->data->subdepartments[trim(strtolower($subdepartment->displayname))] = $subdepartment;
                        }
                    }

                    if (count($data[4]) > 0) {
                        foreach ($data[4] as $k => $dep) {
                            if ($dep->displayname == '' || $dep->displayname == null) {
                                $errors[] = ['type' => 'required', 'section' => 'Tax Rates', 'row' => $k + 2, 'col' => 'DisplayName'];
                            } else {
                                $taxrate = TaxRates::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                                $taxrate->fill((array)$dep);
                                $taxrate->company_id = $company_id;
                                $taxrate->guid = $taxrate->guid != null && $taxrate->guid != '' ? $taxrate->guid : Uuid::uuid4();
                                $taxrate->received_at = '1970-01-01 00:00:00';
                                $taxrate->received_from = 0;
                                $taxrate->save();

                                $this->data->taxrates[trim(strtolower($taxrate->displayname))] = $taxrate;
                            }
                        }
                    } else {
                        $errors[] = ['type' => 'sectionblank', 'section' => 'Tax Rates'];
                    }

                    foreach ($data[5] as $k => $dep) {
                        if ($dep->displayname == '') {
                            $errors[] = ['type' => 'required', 'section' => 'SKU\'s', 'row' => $k + 2, 'col' => 'DisplayName'];
                        } else {
                            $sku = SKU::firstOrNew(['displayname' => $dep->displayname, 'company_id' => $company_id]);
                            $sku->fill((array)$dep);
                            $sku->company_id = $company_id;
                            $sku->guid = $sku->guid != null && $sku->guid != '' ? $sku->guid : Uuid::uuid4();
                            $sku->received_at = '1970-01-01 00:00:00';
                            $sku->received_from = 0;
                            $sku->save();

                            $this->data->skus[trim(strtolower($sku->displayname))] = $sku;
                        }
                    }

                    /**
                     * Report errors before processing products
                     */
                    if (count($errors) > 0) {
                        DB::rollback();

                        return Redirect::to('/import')->with('errors', $errors);
                    }

                    if (count($data[0]) > 0) {
                        foreach ($data[0] as $k => $prod) {
                            if ($prod->displayname != '' && $prod->displayname != null) {
                                $site_num = 0;
                                // departments
                                if (!isset($prod->site_num)) {
                                    $errors['site_num'] = ['type' => 'sectionblank', 'section' => 'Site Num (site_num)'];
                                } else {
                                    $site_num = $prod->site_num;
                                }

                                if ($prod->selling_price_1 == null) {
                                    $prod->selling_price_1 = 0;
                                }

                                $prodFill = array_filter((array)$prod, function ($value) {
                                    return !is_null($value) && $value !== '';
                                });
                                unset($prodFill['']);

                                $product = Products::firstOrNew(['displayname' => $prod->displayname, 'company_id' => $company_id, 'site_num' => $site_num]);
                                $product->fill($prodFill);
                                $product->guid = $product->guid == null && $product->guid == '' ? Uuid::uuid4() : $product->guid;
                                $product->CommandUID = $product->guid;
                                $product->company_id = $company_id;

                                $product->site_num = $site_num;

                                // departments
                                if ($prod->department_id != '') {
                                    if (isset($this->data->departments[trim(strtolower($prod->department_id))])) {
                                        $product->department_guid = $this->data->departments[trim(strtolower($prod->department_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Department', 'expected' => $prod->department_id];
                                    }
                                }

                                // sub departments
                                if ($prod->sub_department_id != '') {
                                    if (isset($this->data->subdepartments[trim(strtolower($prod->sub_department_id))])) {
                                        $product->sub_department_guid = $this->data->subdepartments[trim(strtolower($prod->sub_department_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Subdepartment', 'expected' => $prod->sub_department_id];
                                    }
                                }

                                // suppliers
                                if ($prod->supplier_id != '') {
                                    if (isset($this->data->suppliers[trim(strtolower($prod->supplier_id))])) {
                                        $product->supplier_guid = $this->data->suppliers[trim(strtolower($prod->supplier_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Supplier', 'expected' => $prod->supplier_id];
                                    }
                                }

                                // skus
                                if ($prod->sku_id != '' && $prod->plu_parent_id == '') {
                                    if (isset($this->data->skus[trim(strtolower($prod->sku_id))])) {
                                        $product->sku_guid = $this->data->skus[trim(strtolower($prod->sku_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Sku', 'expected' => $prod->sku_id];
                                    }
                                }

                                if (isset($prod->recipe_id)) {
                                    //recipe id
                                    if ($prod->recipe_id != '' && $prod->plu_parent_id == '' && $prod->sku_id == '') {
                                        $recipe = Recipes::where('name', $prod->recipe_id)->first();
                                        if ($recipe !== null) {
                                            $product->recipe_guid = $recipe['guid'];
                                            $product->recipe_qty = $prod->recipe_qty;
                                        } else {
                                            $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Recipe', 'expected' => $prod->recipe_id];
                                        }
                                    }
                                }

                                // parent products
                                if ($prod->plu_parent_id != '') {
                                    if (isset($this->data->products[trim(strtolower($prod->plu_parent_id)) . $prod->site_num])) {
                                        $product->sku_guid = null;
                                        $product->plu_parent_guid = $this->data->products[trim(strtolower($prod->plu_parent_id)) . $prod->site_num]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Parent Product', 'expected' => $prod->plu_parent_id];
                                    }
                                }

                                // tax ids
                                if ($prod->tax_id != '') {
                                    if (isset($this->data->taxrates[trim(strtolower($prod->tax_id))])) {
                                        $product->tax_guid = $this->data->taxrates[trim(strtolower($prod->tax_id))]->guid;
                                    } else {
                                        $errors[] = ['type' => 'mismatch', 'section' => 'Products', 'row' => $k + 2, 'col' => 'Tax Rate', 'expected' => $prod->tax_id];
                                    }
                                }

                                //                        $product->department_guid = $prod->department_id != '' ? $this->data->departments[strtolower($prod->department_id)]->guid : null;

                                //                        $product->sub_department_guid = $prod->sub_department_id != '' ? $this->data->subdepartments[strtolower($prod->sub_department_id)]->guid : null;

                                //                        $product->supplier_guid = $prod->supplier_id != '' ? $this->data->suppliers[strtolower($prod->supplier_id)]->guid : null;

                                //                        $product->sku_guid = $prod->sku_id != '' && $prod->plu_parent_id == '' ? $this->data->skus[strtolower($prod->sku_id)]->guid : null;

                                //                        $product->plu_parent_guid = $prod->plu_parent_id != '' ? $this->data->products[strtolower($prod->plu_parent_id)]->guid : null;

                                //                        $product->tax_guid = $prod->tax_id != '' ? $this->data->taxrates[strtolower($prod->tax_id)]->guid : null;

                                $product->KP_Target = $prod->kp_target != null ? $prod->kp_target : 0;
                                $product->received_at = '1970-01-01 00:00:00';
                                $product->received_from = 0;
                                $product->displayname = trim(ucfirst($prod->displayname));

                                $product->selling_price_2 = $prod->selling_price_2 != null ? $prod->selling_price_2 : $prod->selling_price_1;
                                $product->selling_price_3 = $prod->selling_price_3 != null ? $prod->selling_price_3 : $prod->selling_price_1;
                                $product->selling_price_4 = $prod->selling_price_4 != null ? $prod->selling_price_4 : $prod->selling_price_1;
                                $product->selling_price_5 = $prod->selling_price_5 != null ? $prod->selling_price_5 : $prod->selling_price_1;

                                unset($product->department_id);
                                unset($product->sub_department_id);
                                unset($product->supplier_id);
                                unset($product->sku_id);
                                unset($product->plu_parent_id);
                                unset($product->tax_id);
                                unset($product->kp_target);
                                unset($product->recipe_id);

                                $product->save();

                                // insert a stock record of type adjustment (summary type 4)
                                // status 2 or 4 look at completed records
                                // -> metat date in stock controller

                                $this->data->products[trim(strtolower($product->displayname)) . $product->site_num] = $product;
                            }
                        }
                    } else {
                        $errors[] = ['type' => 'missing', 'section' => 'Products'];
                    }
                }

                if (count($errors) > 0) {
                    DB::rollback();

                    return Redirect::to('/import')->with('errors', $errors);
                }


                DB::commit();

                $this->queueAll($this->data);

                return redirect('/import/assign-screens');
            } catch (\Exception $e) {
                DB::rollback();

                throw new \Exception($e);
            }
        }
    }

    private function queueAll($data)
    {
        //Dept
        if (isset($data->departments)) {
            foreach ($data->departments as $dept) {
                $department = Departments::find($dept['id']);
                \Event::dispatch(new UpdaterInsertCrudEvent($department));
            }
        }

        //SubDept
        if (isset($data->subdepartments)) {
            foreach ($data->subdepartments as $subDept) {
                $subDepartment = SubDepartment::find($subDept['id']);
                \Event::dispatch(new UpdaterInsertCrudEvent($subDepartment));
            }
        }

        //TaxRates
        if (isset($data->taxrates)) {
            foreach ($data->taxrates as $rate) {
                $taxRate = TaxRates::find($rate['id']);
                \Event::dispatch(new UpdaterInsertCrudEvent($taxRate));
            }
        }

        //SKU
        if (isset($data->skus)) {
            foreach ($data->skus as $s) {
                $sku = SKU::find($s['id']);
                \Event::dispatch(new UpdaterInsertCrudEvent($sku));
            }
        }

        //Products
        if (isset($data->products)) {
            foreach ($data->products as $prod) {
                $product = Products::with('groups')->find($prod['id']);
                \Event::dispatch(new UpdaterInsertCrudEvent($product));
            }
        }
    }

    private function getDepartmentGuid($displayname)
    {
        foreach ($this->data->departments as $dep) {
            if ($dep->displayname == $displayname) {
                return $dep->guid;
            }
        }
    }

    private function getSubdepartmentGuid($displayname)
    {
        foreach ($this->data->subdepartments as $dep) {
            if ($dep->displayname == $displayname) {
                return $dep->guid;
            }
        }
    }

    private function getSupplierGuid($displayname)
    {
        foreach ($this->data->suppliers as $dep) {
            if ($dep->name == $displayname) {
                return $dep->guid;
            }
        }
    }

    private function getSkuGuid($displayname)
    {
        foreach ($this->data->skus as $dep) {
            if ($dep->displayname == $displayname) {
                return $dep->guid;
            }
        }
    }

    private function getParentProductGuid($displayname)
    {
        foreach ($this->data->products as $dep) {
            if ($dep->displayname == $displayname) {
                return $dep->guid;
            }
        }
    }

    public function uploadRecipes(Request $request)
    {
        $errors = [];

        if ($request->hasFile('import')) {
            DB::beginTransaction();

            try {
                $import = new RecipesImport();
                Excel::import($import, $request->file('import'));
                $errors = $import->getErrors();
            } catch (\Exception $e) {
                DB::rollback();

                return back()->with("errors", $errors);
            }

            if (count($errors) > 0) {
                DB::rollback();
            } else {
                DB::commit();
            }
        }

        return redirect("/recipes/import")->with("errors", $errors);
    }

    public function indexCustomers()
    {
        // TODO:: add import list history below the import box (datatables, paginate)

        return view('modules.import.customers_index');
    }

    public function uploadCustomerImportFile(Request $request)
    {
        $guid = Uuid::uuid4();
        $ext = ".csv";
        $filename = (string)$guid . $ext;
        $file = $request->file('import');

        if ($file->extension() == 'csv') {
            $location = Storage::putFileAs('/imports/customers', $file, $filename);

            $import = new Import();
            $import->user_id = Auth::user()->id;
            $import->file_location = "/" . $location;
            $import->upload_status = Import::IN_PROGRESS;
            $import->guid = $guid;
            $import->import_type = Import::CUSTOMER_IMPORT;
            $import->save();
            Redis::set(
                'import-' .
                $import->guid . '-status',
                json_encode([
                    'status' => 'warning',
                    'text' => 'Request received',
                    'percent' => '50'
                ])
            );

            $import = Import::find($import->id);

            dispatch(new BackgroundImportCustomersJob($import)); // run job

            return redirect('/import/' . $import->id . "/progress");
        } else {
            $message = ['status' => 'error', 'message' => 'Invalid file type, please upload a CSV file'];
            session()->flash('message', $message);
            return redirect('/import-customers');
        }
    }

    public function processCustomerUpload(Import $import)
    {
        ini_set('max_execution_time', 3500);
        ini_set('max_input_time', 5000);

        $user = User::find($import->user_id);

        $errors = [];

        if (Storage::exists($import->file_location)) {
            $document = Storage::get($import->file_location);

            $groups = CustomerGroup::where('company_id', $user->company_id)
                ->get()
                ->keyBy('displayname')
                ->toArray();

            $types = CustomerTypes::where('company_id', $user->company_id)
                ->get()
                ->keyBy('displayname')
                ->toArray();

            if ($document) {
                $data = Excel::toArray(new ImportCustomerData(), $import->file_location);

                Redis::set(
                    'import-' .
                    $import->guid . '-status',
                    json_encode(['status' => 'warning',
                        'text' => 'Loaded Data',
                        'percent' => '70'])
                );

                DB::beginTransaction();

                try {

                    $customersToUpsert = [];
                    $customerTransactionsToInsert = [];

                    if (!empty($data[0])) {

                        $loyaltyCustomers = Customer::select(['guid', 'customer_type', 'membership_no', 'company_id'])
                            ->where('company_id', $user->company_id)
                            ->where('customer_type', 0)
                            ->get()->keyBy('membership_no')->toArray();

                        $giftcardCustomers = Customer::select(['guid', 'customer_type', 'membership_no', 'company_id'])
                            ->where('company_id', $user->company_id)
                            ->where('customer_type', 1)
                            ->get()->keyBy('membership_no')->toArray();

                        foreach ($data[0] as $k => $customer) {
                            Redis::set(
                                'import-' .
                                $import->guid . '-status',
                                json_encode(['status' => 'warning',
                                    'text' => 'Customer - ' . $k,
                                    'percent' => '85'])
                            );

                            if (!isset($groups[$customer['group']]) && $customer['customer_type'] != 'giftcard') {
                                $errors[] = ['type' => 'required', 'section' => 'Groups', 'row' => $k + 2, 'col' => 'Group', 'message' => 'The group ' . $customer['group'] . ' not found specified in row ' . $k + 2, 'expected' => $customer['group']];
                            }

                            if (!isset($customer['membership_no'])) {
                                $errors[] = ['type' => 'required', 'section' => 'Membership Number', 'message' => 'Membership number is required, not found in row ' . $k + 2, 'expected' => 'membership_no', 'row' => $k + 2, 'col' => 'Membership No',];
                            }

                            if (count($errors) > 0) {
                                DB::rollback();

                                Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'error', 'text' => 'Import Failed with Errors', 'percent' => '100', 'errors' => $errors]));
                                $import->upload_status = Import::FAILED;
                                $import->errors = $this->errorArrayToStringCustomer($errors);
                                $import->save();

                                return;
                            }

                            $type = $customer['customer_type'] === 'giftcard' ? 1 : 0;
                            if($type === 1){
                                $guid = $giftcardCustomers[$customer['membership_no']]['guid'] ?? Uuid::uuid4();
                            } elseif($type === 0){
                                $guid = $loyaltyCustomers[$customer['membership_no']]['guid'] ?? Uuid::uuid4();
                            }

                            $customersToUpsert[] = [
                                'company_id' => $user->company_id,
                                'prefix' => $customer['prefix'],
                                'first_name' => empty($customer['first_name']) ? 'Loyalty' : $customer['first_name'],
                                'last_name' => empty($customer['last_name']) ? 'Customer' : $customer['last_name'],
                                'group_guid' => $customer['customer_type'] != 'giftcard' ? $groups[$customer['group']]['guid'] : null,
                                'type_guid' => isset($types[trim($customer['type'])]) ? $types[trim($customer['type'])]['guid'] : null,
                                'membership_no' => (string)$customer['membership_no'],
                                'customer_type' => $customer['customer_type'] === 'giftcard' ? 1 : 0,
                                'expires' => isset($customer['expiry_date']) ? Carbon::createFromFormat('d/m/Y', $customer['expiry_date'])->endOfDay() : null,
                                'email' => $customer['email'],
                                'guid' => $guid,
                                'telephone' => $customer['telephone']
                            ];

                            $type = $customer['customer_type'] === 'giftcard' ? 1 : 0;

                            if ((int)$type === 1 && !empty($customer['balance']) && (float)$customer['balance'] > 0) {
                                $customerTransactionsToInsert[] = [
                                    'customer_guid' => (string)$guid,
                                    'value' => (float)$customer['balance']
                                ];
                            }

                            if ((int)$type === 0 && !empty($customer['balance']) && (float)$customer['balance'] > 0) {
                                $customerTransactionsToInsert[] = [
                                    'customer_guid' => (string)$guid,
                                    'points' => (float)$customer['balance']
                                ];
                            }
                        }
                    }

                    if (!empty($customersToUpsert)) {

                        $chunks = array_chunk($customersToUpsert, 1000);

                        foreach ($chunks as $chunk) {
                            Customer::upsert($chunk, ['guid'], [
                                'company_id', 'prefix', 'first_name', 'last_name', 'group_guid', 'type_guid',
                                'membership_no', 'customer_type', 'expires', 'email', 'telephone', 'guid'
                            ]);
                        }
                    }

                    if (!empty($customerTransactionsToInsert)) {
                        CustomerTransactions::insert($customerTransactionsToInsert);
                    }

                    if (count($errors) > 0) {
                        DB::rollback();

                        return Redirect::to('/import-customers')->with('errors', $errors);
                    }

                    Redis::set('import-' . $import->guid . '-status', json_encode(['status' => 'success', 'text' => 'Import complete!', 'percent' => '100']));
                    $import->upload_status = Import::COMPLETE;
                    $import->save();

                    DB::commit();
                } catch (\Exception $e) {

                    DB::rollback();

                    throw new \Exception($e);
                }
            }
        }
    }
}
