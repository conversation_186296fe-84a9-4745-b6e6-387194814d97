<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use NewbridgeWeb\Events\ButtonQueueEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\Sites;
use Ramsey\Uuid\Uuid;

class PageController extends Controller
{
    private $pageType = 'standard';

    // create a new page of buttons
    public function create(Request $request)
    {
        $input = $request->input();

        $command = $input['type'] === 'extended' ? 117 : 1;

        if ($request->input('pageId') == null) {
            $data = [
                'company_id' => Auth::user()->company_id,
                'site_num' => $request['sitenum'],
                'Descriptor',
                'DisplayName' => $request['name'],
                'Link_1' => null,
                'Link_2' => null,
                'Value' => null,
                'Min' => 0,
                'Max' => 0,
                'CommandUID' => Uuid::uuid4(),
                'Command' => $command,
                'OperationMode' => 0,
                'CommandType' => 0,
                'Abbreviation' => null,
                'AccessLevel' => 0
            ];

            $command = new Commands();
            $command->fill($data);
            $command->received_at = '1970-01-01 00:00:00';
            $command->received_from = 0;
            $command->save();

            $buttons = new ButtonController();
            if ($input['type'] === 'extended') {
                $buttons->createSide(
                    [
                        'plu_product_page_guid' => $command->CommandUID,
                        'company_id' => $command->company_id,
                        'site_num' => $command->site_num
                    ]
                );
            } else {
                $buttons->create(
                    [
                        'plu_product_page_guid' => $command->CommandUID,
                        'company_id' => $command->company_id,
                        'site_num' => $command->site_num
                    ]
                );
            }
        } else {
            $page = Commands::where('id', $request->input('pageId'))->with([
                'buttons' => function ($q) {
                    $q->with(['pos_button_style', 'lnk_buttonlinks', 'plu_product_page'])->orderBy('id', 'ASC');
                }
            ])->first();

            $data = [
                'company_id' => Auth::user()->company_id,
                'site_num' => $request['sitenum'],
                'Descriptor',
                'DisplayName' => $request['name'],
                'Link_1' => null,
                'Link_2' => null,
                'Value' => null,
                'Min' => 0,
                'Max' => 0,
                'CommandUID' => Uuid::uuid4(),
                'Command' => $command,
                'OperationMode' => 0,
                'CommandType' => 0,
                'Abbreviation' => null,
                'AccessLevel' => 0
            ];

            $command = new Commands();
            $command->fill($data);
            $command->received_from = 0;
            $command->received_at = '1970-01-01 00:00:00';
            $command->save();

            foreach ($page->buttons->toArray() as $b) {
                $style = $b['pos_button_style'];

                unset($style['id']);
                unset($style['created_at']);
                unset($style['updated_at']);

                unset($b['created_at']);
                unset($b['updated_at']);

                $links = $b['lnk_buttonlinks'];

                unset($b['pos_button_style']);
                unset($b['lnk_buttonlinks']);
                unset($b['plu_product_page']);
                $style['guid'] = Uuid::uuid4();

                $b['guid'] = Uuid::uuid4();
                $b['pos_button_style_guid'] = $style['guid'];
                $b['site_num'] = $request['sitenum'];
                $b['plu_product_page_guid'] = $command->CommandUID;

                $button = new PosButtons();
                $button->fill($b);
                if ($request->input('copyFunctions') === true && $request->input('copyEverything') === false) {
                    $button->displayname = null;
                }
                $button->received_at = '1979-01-01 00:00:00';
                $button->received_from = 0;

                $style['guid'] = Uuid::uuid4();
                $style['received_at'] = '1979-01-01 00:00:00';

                $button->pos_button_style()->create($style);


                // is the button linked to a command or product?
                if ($request->input('copyFunctions') == 'true' && $request->input('copyEverything') == 'false') {
                    $position = 0;

                    foreach ($links as $l) {
                        if ($l['command_type'] != 3) {
                            unset($l['created_at']);
                            unset($l['updated_at']);
                            unset($l['deleted_at']);
                            unset($l['type_string']);
                            unset($l['type_color']);
                            unset($l['display_name']);
                            unset($l['intcommand']);
                            unset($l['product']);
                            unset($l['intcommand']);
                            unset($l['payment']);
                            unset($l['discount']);
                            $l['position'] = $l['indexer'];
                            unset($l['indexer']);

                            if (Payments::where('CommandUID', $l['commandUID'])->count() > 0) {
                                $link = $l;
                                $link['received_at'] = Carbon::parse('1979-01-01 00:00:00');
                                $link['received_from'] = 0;

                                $button->lnk_buttonlinks()->create($l);
                                $position++;
                            } elseif (in_array($l['command_type'], [1])) {
                                $link = $l;
                                $link['received_at'] = Carbon::parse('1979-01-01 00:00:00');
                                $link['received_from'] = 0;

                                $button->lnk_buttonlinks()->create($l);
                                $position++;
                            } else {
                                $button->displayname = '';
                            }
                        } else {
                            $button->displayname = '';
                        }
                    }
                }

                if ($request->input('copyFunctions') == 'false' && $request->input('copyEverything') == 'true') {
                    foreach ($links as $l) {
                        unset($l['created_at']);
                        unset($l['updated_at']);
                        unset($l['deleted_at']);
                        unset($l['type_string']);
                        unset($l['type_color']);
                        unset($l['display_name']);
                        unset($l['product']);
                        unset($l['intcommand']);
                        unset($l['payment']);
                        unset($l['discount']);
                        $l['position'] = $l['indexer'];
                        unset($l['indexer']);

                        $link = $l;
                        $link['received_at'] = Carbon::parse('1979-01-01 00:00:00');
                        $link['received_from'] = 0;
                        $button->lnk_buttonlinks()->create($l);
                    }
                }

                $button->save();
            }
        }

        /** fire event (button create) */
        \Event::dispatch(new ButtonQueueEvent($command));

        return response()->json(Commands::where('company_id', Auth::user()->company_id)->whereIn('Command', [1]), 200);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function get(Request $request)
    {
        $pages = Commands::where('site_num', $request->input('sitenum'))->where(
            'company_id',
            Auth::user()->company_id
        )->whereIn('Command', [1, 117])->get();

        if ($pages->isNotEmpty()) {
            return response()->json($pages, 200);
        }

        return response()->json(['status' => 'error', 'message' => 'No pages available for site'], 400);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRestricted(Request $request)
    {
        $pages = Commands::where('site_num', $request->input('sitenum'))
            ->where('company_id', Auth::user()->company_id)
            ->whereIn('Command', [1, 117])
            ->where('RestrictedCommand', '!=', 0)
            ->get();

        if ($pages->isNotEmpty()) {
            return response()->json($pages, 200);
        }

        return response()->json([], 200);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function restrictPage(Request $request)
    {
        $data = $request->input();
        $bit = 1;

        $command = Commands::find($data['page']);

        if ($command->RestrictedCommand !== 0) {
            return response()->json(['status' => 'success', 'message' => 'Page already restricted'], 200);
        }

        $lastValue = Commands::where('site_num', $request->input('sitenum'))
            ->where('company_id', Auth::user()->company_id)
            ->whereIn('Command', [1, 117])
            ->where('RestrictedCommand', '>', 0)
            ->orderby('RestrictedCommand', 'DESC')
            ->first();

        if ($lastValue !== null) {
            $bit = $lastValue->RestrictedCommand * 2;
        }

        $command->RestrictedCommand = $bit;
        $command->received_from = 0;
        $command->save();

        \Event::dispatch(new UpdaterUpdateCrudEvent($command));

        return response()->json(['status' => 'success', 'message' => 'Page successfully restricted'], 200);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function unrestrictPage(Request $request)
    {
        $data = $request->input();

        $command = Commands::find($data['page']);

        // update clerks and remove permission
        $clerks = Clerks::where('company_id', Auth::user()->copmpany_id)
            ->where('ishidden', 0)
            ->get();

        if (!$clerks->isEmpty()) {
            foreach ($clerks as $clerk) {
                if ($clerks->NavigationLevel & $command->RestrictedCommand) {
                    $clerk->NavigationLevel = ($clerk->NavigationLevel - $command->RestrictedCommand);
                    $clerk->save();
                }
            }
        }

        $command->RestrictedCommand = 0;
        $command->received_from = 0;
        $command->save();

        \Event::dispatch(new UpdaterUpdateCrudEvent($command));

        return response()->json(['status' => 'success', 'message' => 'Page successfully un-restricted'], 200);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGrouped(Request $request)
    {
        $groupedPages = Sites::with([
            'pages' => function ($q) {
                $q->where('company_id', Auth::user()->company_id);
            }
        ])->where('company_id', Auth::user()->company_id)->get();

        if ($groupedPages->isNotEmpty()) {
            return response()->json($groupedPages, 200);
        }

        return response()->json(['status' => 'error', 'message' => 'No pages available for site'], 400);
    }

    public function deletePage($id)
    {
        $page = Commands::find($id);

        // first available page
        $newPage = Commands::where('id', '!=', $id)->first();

        if ($newPage) {
            Clerks::where('default_screen_guid', $page->CommandUID)->where('company_id', Auth::user()->company_id)->get(
            )->each(function ($model) use ($newPage) {
                $model->update(['default_screen_guid' => $newPage->CommandUID]);
            });
        }

        $page->delete();

        \Event::dispatch(new UpdaterUpdateCrudEvent($page));

        return response()->json(['status' => 'ok'], 200);
    }

    public function sendPageToTills($id)
    {
        // get the page and all buttons
        $page = Commands::find($id);
        \Event::dispatch(new UpdaterUpdateCrudEvent($page));

        PosButtons::where('plu_product_page_guid', $page->CommandUID)->get()->each(function ($model) {
            $model->update(['received_from' => 0]);
        });
        $buttons = PosButtons::where('plu_product_page_guid', $page->CommandUID)->get();

        foreach ($buttons as $b) {
            $button = PosButtons::with(['pos_button_style', 'lnk_buttonlinks', 'plu_product_page'])->where(
                'id',
                $b->id
            )->first();
            \Event::dispatch(new UpdaterUpdateCrudEvent($button));
        }

        return response()->json(
            ['status' => 'success', 'message' => 'The selected page has been sent to all available terminals'],
            200
        );
    }

    //    public function copyPageToCompany(Request $request)
    //    {
    //        // this function must not be run before an initial
    //
    //        $page = Commands::with(['buttons' => function($q){
    //            $q->with('links', 'style');
    //        }])->where('id', $request->input('id'))->first();
    //
    //        $newPage = new Commands();
    //        $newPage->fill($page);
    //        $newPage->CommandUID = Uuid::uuid4();
    //        $newPage->created_at = Carbon::now();
    //        $newPage->company_id = $request->input('company_id');
    //        $newPage->save();
    //
    //        foreach($page->buttons as $button)
    //        {
    //            $newButton = new PosButtons();
    //            $newButton->fill($button);
    //            $newButton->guid = Uuid::uuid4();
    //            $newButton->company_id = $newPage->company_id;
    //            $newButton->plu_product_page_guid = $newPage->CommandUID;
    //
    //
    //            $newStyle = new PosButtonStyle();
    //            $newStyle->fill($button->style);
    //            $newStyle->guid = Uuid::uuid4();
    //            $newStyle->company_id = $newPage->company_id;
    //            $newStyle->save();
    //
    //            $newButton->pos_button_style_guid = $newStyle->guid;
    //            $newButton->save();
    //
    //            foreach($newButton->links as $link)
    //            {
    //                $newLink = new PosButtonLink();
    //                $newLink->fill($link);
    //
    //                if($link->command_type === 0){
    //                    // product - copy the product to the new company and change the GUID and insert here
    //                    // when copying the product we will need to loose the modifiers
    //                    $product = Products::where('guid', $link->CommandUID)->first();
    //
    //                    $newLink->CommandUID = '';
    //                }
    //                if($link->command_type === 1){
    //                    // command - copy the command to the new company if not a page and the page name does not already exist
    //                    // else use the page with the existing name
    //                    $newLink->CommandUID = '';
    //                }
    //                if($link->command_type === 2){
    //                    // payment - copy the payment method if it does not exist change the GUID and add here
    //                    $newLink->CommandUID = '';
    //                }
    //
    //
    //                $newLink->button_guid = $newButton->guid;
    //                $newLink->company_id = $newPage->company_id;
    //
    //            }
    //        }
    //
    //
    //    }
}
