<?php

namespace NewbridgeWeb\Http\Controllers\Api\Stock;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Jobs\StockManagement\StockManagerStatusUpdateJob;
use NewbridgeWeb\Jobs\StockManagement\StockManagerUpdateJob;
use NewbridgeWeb\Repositories\RedisRepository;
use Ramsey\Uuid\Uuid;

class UpdateStock extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    public function updateStockDetails(Request $request): JsonResponse
    {
        $user = auth('api')->user();

        if ($user === null) {
            return response()->json(['message' => 'Please check your token and try again'], 401);
        }

        $request->validate([
            'id' => 'required|integer',
            'details.*.id' => 'required',
            'details.*.product_id' => 'required',
            'details.*.qty_entered' => 'required',
        ], [
            'id.required' => 'Stock summary ID is required',
            'details.*.id.required' => 'Stock detail ID is required',
            'details.*.product_id.required' => 'Product ID is required',
            'details.*.qty_entered.required' => 'Quantity entered is required',
        ]);

        $uuid = Uuid::uuid4();

        RedisRepository::store($uuid, json_encode($request->all()));

        dispatch(new StockManagerUpdateJob($uuid, $user));

        return response()->json(['message' => 'Stock details submitted successfully.']);

    }

    public function updateStatus(Request $request): JsonResponse
    {
        $user = auth('api')->user();
        if($user === null) {
            return response()->json(['message' => 'Please check your token and try again'], 401);
        }

        $request->validate([
            'id' => 'required|integer',
            'status' => 'required|integer',
        ]);

        dispatch(new StockManagerStatusUpdateJob($request['id'], $request['status'], $user));

        return response()->json(['message' => 'Stock record updated.']);
    }
}