<?php

namespace NewbridgeWeb\Http\Controllers\Api\Stock;

use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Models\Api\Stock\StockSummary as StockSummaryModel;

class StockSummary extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }
    public function getAllStockSummariesForSite(int $site_num, ?int $type = null): JsonResponse
    {
        $user = auth('api')->user();
        if($user === null) {
            return response()->json(['message' => 'Please check your token and try again'], 401);
        }

        $stockSummary = StockSummaryModel::select(['id', 'status', 'summary_type', 'created_at', 'updated_at', 'site_num', 'company_id', 'stocktake_date', 'delivery_date', 'user_id'])
            ->where('company_id', $user->company_id)
            ->where('site_num', $site_num);
        if ($type) {
            $stockSummary = $stockSummary->where('summary_type', $type);
        }
        $stockSummary = $stockSummary->get();

        if ($stockSummary->isEmpty()) {
            return response()->json(['message' => 'No stock summaries found for this site and/or type'], 404);
        }

        return response()->json($stockSummary);
    }
}