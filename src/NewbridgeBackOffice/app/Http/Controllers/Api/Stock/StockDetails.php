<?php

namespace NewbridgeWeb\Http\Controllers\Api\Stock;

use Illuminate\Http\JsonResponse;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Models\Api\Stock\StockDetails as StockDetailsModel;
use \NewbridgeWeb\Models\Api\Stock\StockSummary as StockSummaryModel;
use DB;

class StockDetails extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    public function getStockDetailsBySummaryId(int $summary_id): JsonResponse
    {
        $user = auth('api')->user();
        if ($user === null) {
            return response()->json(['message' => 'Please check your token and try again'], 401);
        }

        $stockRecords = StockSummaryModel::select(['id', 'status', 'summary_type', 'created_at', 'updated_at', 'site_num', 'company_id', 'stocktake_date', 'delivery_date', 'user_id'])
            ->where('company_id', $user->company_id)
            ->where('id', $summary_id)
            ->with(['details' => function ($query) {
                $query->select(['id', 'product_id', 'qty_entered', 'summary_id']);
            }])
            ->first();

        if (empty($stockRecords)) {
            return response()->json(['message' => 'No stock details found for this summary id'], 404);
        }

        $products = $stockRecords->details->pluck('product_id');
        $productList = $products->implode(',');

        $date = $stockRecords->created_at;
        if ($stockRecords->delivery_date != null) {
            $date = $stockRecords->delivery_date;
        } elseif ($stockRecords->stocktake_date != null) {
            $date = $stockRecords->stocktake_date;
        }

        $productCurrentStock = DB::select("SELECT product_quantity, product_id
                FROM (
                    SELECT st.product_quantity, st.product_id, 
                           ROW_NUMBER() OVER (PARTITION BY st.product_id ORDER BY st.id DESC) AS rn
                    FROM plu_stock_transactions st
                    INNER JOIN (
                        SELECT product_id, MAX(created_at) as max_created_at
                        FROM plu_stock_transactions
                        WHERE status = 1
                            AND created_at <= '$date'
                            AND product_id IN ($productList)
                        GROUP BY product_id
                    ) as latest ON st.product_id = latest.product_id 
                                 AND st.created_at = latest.max_created_at
                ) AS ranked
                WHERE rn = 1");

        $productCurrentStock = collect($productCurrentStock);
        $productCurrentStock = $productCurrentStock->keyBy('product_id');

        if (!empty($stockRecords->details)) {
            foreach ($stockRecords->details as $k => $detail) {

                $stockRecords['details'][$k]->current_stock = $productCurrentStock[$detail->product_id]->product_quantity?? 0;
            }
        }


        return response()->json($stockRecords);
    }
}