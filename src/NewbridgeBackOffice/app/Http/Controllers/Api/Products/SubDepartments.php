<?php

namespace NewbridgeWeb\Http\Controllers\Api\Products;

use Auth;
use Illuminate\Http\JsonResponse;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Products as ProductModel;
use NewbridgeWeb\Repositories\SubDepartment;

class SubDepartments extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    public function getSubdepartments(): JsonResponse
    {
        $user = Auth::user();
        if($user === null) {
            return response()->json(['message' => 'Please check your token and try again'], 401);
        }

        $subdepartments = SubDepartment::select(['id', 'displayname', 'stock_order', 'guid', 'deleted_at'])
            ->where('company_id', $user->company_id)
            ->withTrashed()
            ->get();

        return response()->json([
            'status' => 'success',
            'subdepartments' => $subdepartments
        ]);
    }
}