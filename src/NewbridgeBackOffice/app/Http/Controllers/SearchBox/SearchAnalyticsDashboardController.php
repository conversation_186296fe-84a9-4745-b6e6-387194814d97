<?php

namespace NewbridgeWeb\Http\Controllers\SearchBox;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Models\Search\SearchAnalytics;

class SearchAnalyticsDashboardController extends Controller
{
    public function index(): View
    {
        $now = Carbon::now();

        $totalSearches = SearchAnalytics::count();
        $uniqueSearches = SearchAnalytics::distinct('query')->count('query');

        $searchesWithResults = SearchAnalytics::whereJsonLength('results', '>', 0)->count();
        $searchesWithoutResults = SearchAnalytics::whereJsonLength('results', 0)->count();

        $searchesWithResultsToday = SearchAnalytics::whereDate('created_at', $now)
            ->whereJsonLength('results', '>', 0)
            ->count();
        $searchesWithoutResultsToday = SearchAnalytics::whereDate('created_at', $now)
            ->whereJsonLength('results', 0)
            ->count();

        $searchesWithResultsThisWeek = SearchAnalytics::where('created_at', '<=', $now->copy())
            ->where('created_at', '>=', $now->copy()->subWeek())
            ->whereJsonLength('results', '>', 0)
            ->count();

        $searchesWithoutResultsThisWeek = SearchAnalytics::where('created_at', '<=', $now->copy())
            ->where('created_at', '>=', $now->copy()->subWeek())
            ->whereJsonLength('results', 0)->count();

        $currentMonthSearches = SearchAnalytics::where('created_at', '>=', $now->copy()->subMonth())
            ->count();

        $lastMonthSearches = SearchAnalytics::where('created_at', '<=', $now->copy()->subMonth())
            ->where('created_at', '>=', $now->copy()->subMonths(2))
            ->count();

        $currentWeekSearches = SearchAnalytics::where('created_at', '>=', $now->copy()->subWeek())
            ->count();

        $lastWeekSearches = SearchAnalytics::where('created_at', '<=', $now->copy()->subWeek())
            ->where('created_at', '>=', $now->copy()->subWeeks(2))
            ->count();

        $monthlyGrowth = $lastMonthSearches > 0
            ? (($currentMonthSearches - $lastMonthSearches) / $lastMonthSearches) * 100
            : 0;

        $weeklyGrowth = $lastWeekSearches > 0
            ? (($currentWeekSearches - $lastWeekSearches) / $lastWeekSearches) * 100
            : 0;

        $topQueries = SearchAnalytics::topQueries()->get();
        $noResultQueries = SearchAnalytics::noResultQueries()->get();
        $topSiteUrls = SearchAnalytics::topSiteUrls()->get();

        $topUsers = SearchAnalytics::query()
            ->join('users', 'search_analytics.user_id', '=', 'users.id')
            ->select(['users.name', DB::raw('COUNT(search_analytics.id) as total')])
            ->groupBy('users.name')
            ->orderByDesc('total')
            ->limit(10)
            ->get();

        $searchesLastWeek = SearchAnalytics::select([DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as total')])
            ->where('created_at', '>=', $now->copy()->subWeek())
            ->where('created_at', '<=', $now)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $chartLabels = $searchesLastWeek->pluck('date')->map(function ($date) {
            return Carbon::parse($date)->format('D, M d'); // np. "Mon, Nov 20"
        })->toArray();

        $chartData = $searchesLastWeek->pluck('total')->toArray();

        return view('modules.search.analytics-dashboard', [
            'topQueries' => $topQueries,
            'noResultQueries' => $noResultQueries,
            'topSiteUrls' => $topSiteUrls,
            'topUsers' => $topUsers,
            'totalSearches' => $totalSearches,
            'uniqueSearches' => $uniqueSearches,
            'currentMonthSearches' => $currentMonthSearches,
            'monthlyGrowth' => $monthlyGrowth,
            'currentWeekSearches' => $currentWeekSearches,
            'weeklyGrowth' => $weeklyGrowth,
            'searchesWithResults' => $searchesWithResults,
            'searchesWithoutResults' => $searchesWithoutResults,
            'searchesWithResultsToday' => $searchesWithResultsToday,
            'searchesWithoutResultsToday' => $searchesWithoutResultsToday,
            'searchesWithResultsThisWeek' => $searchesWithResultsThisWeek,
            'searchesWithoutResultsThisWeek' => $searchesWithoutResultsThisWeek,
            'chartLabels' => $chartLabels,
            'chartData' => $chartData,
        ]);
    }
}