<?php

namespace NewbridgeWeb\Http\Controllers\StatusAndHealth;

use DB;
use Laravel\Horizon\Http\Controllers\DashboardStatsController;
use Laravel\Horizon\Repositories\RedisJobRepository;
use Laravel\Horizon\Repositories\RedisWorkloadRepository;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\Controller;
use Queue;

class QueueHealth extends Controller
{

    public static function index()
    {
        $queues = [
            [
                'queue_name' => HorizonQueues::LOW_PRIORITY,
                'batch_names' => [],
            ],
            [
                'queue_name' => HorizonQueues::HIGH_PRIORITY,
                'batch_names' => [],
            ],
            [
                'queue_name' => HorizonQueues::HIGH_MEMORY,
                'batch_names' => [],
            ],
            [
                'queue_name' => HorizonQueues::STOCK,
                'batch_names' => [
                    'stock-transaction-processing*',
                    'calculate*',
                ]
            ]
        ];

        $queueHealth = [
            'queues' => [],
            'total_size' => 0
        ];

        foreach ($queues as $i => $queue) {
            $queueHealth['queues'][$i] = [
                'name' => $queue['queue_name']
            ];
            $queueHealth['queues'][$i]['jobs'] = Queue::size($queue['queue_name']);
            $queueHealth['queues'][$i]['total'] = $queueHealth['queues'][$i]['jobs'];
            if($queue['batch_names']) {
                $queueHealth['queues'][$i]['batches']  = DB::table('job_batches')
                    ->select('pending_jobs')
                    ->where('finished_at', null)
                    ->whereIn('name', $queue['batch_names'])
                    ->sum('pending_jobs');

                $queueHealth['queues'][$i]['total'] += $queueHealth['queues'][$i]['batches'];
            }
            $queueHealth['total_size'] += $queueHealth['queues'][$i]['total'];
        }

        $jobMonitor = new RedisJobRepository(app('redis'));
        $jobMonitor->completedJobExpires = 5;

        $queueHealth['completed_jobs'] = $jobMonitor->countCompleted();

        $queueMonitor = new DashboardStatsController();
        $queueHealth['stats'] = $queueMonitor->index();

        return response()->json($queueHealth, 200);
    }
}
