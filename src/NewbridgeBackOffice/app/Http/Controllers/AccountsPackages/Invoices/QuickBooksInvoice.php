<?php

namespace NewbridgeWeb\Http\Controllers\AccountsPackages\Invoices;

use Illuminate\Support\Facades\App;
use LifeOnScreen\LaravelQuickBooks\QuickBooksConnection;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\CompanyAccounts;
use QuickBooksOnline\API\Facades\Invoice;
use QuickBooksOnline\API\Facades\JournalEntry;
use QuickBooksOnline\API\Facades\Payment;

/**
 * Class QuickBooksInvoice
 * @package NewbridgeWeb\Http\Controllers\AccountsPackages\Invoices
 */
class QuickBooksInvoice extends Controller
{
    public $links;
    public $data;
    public $invoices = [];
    public $invoice = [
        'CustomerRef' => [
            'value' => null
        ],
        'Line' => [

        ]
    ];
    public $validationMessages = [];
    public $attempts = 0;
    public $errors = [];
    public $company_id;
    public $docRef;

    public function __construct($data, $company_id, $invoice = null)
    {
        $this->links = CompanyAccounts::where('company_id', $company_id)->first();
        $this->data = $data;
        $this->company_id = $company_id;

        if ($invoice == null) {
            // process all invoices together
            $this->processAll();
        } else {
            // process a single invoice
            $this->processSingle($invoice);
        }

        if (count($this->errors) > 0) {
            return response()->json(['status' => 'error', 'errors' => $this->errors], 400);
        }

        return response()->json(['status' => 'ok', 'message' => 'Invoices Posted Successfully'], 400);
    }

    private function processSingle($key)
    {
        $invoice = $this->data['invoices'][$key];

        $this->docRef = $this->links['contact_id'].str_replace('-', '', $invoice['date']);
        $quickbooks = App::make(QuickBooksConnection::class);
        $result = $quickbooks->getDataService()->Query("SELECT * FROM Invoice WHERE CustomerRef = '".$this->links['contact_id']."' and DocNumber = '".$this->docRef."'");

        $this->createInvoice($invoice);

        if (!empty($invoice['department_lines'])) {
            foreach ($invoice['department_lines'] as $line) {
                $this->addLine($line);
            }
        }
        if (!empty($invoice['addition_lines'])) {
            foreach ($invoice['addition_lines'] as $line) {
                $this->addLine($line);
            }
        }
        if (!empty($invoice['subtraction_lines'])) {
            foreach ($invoice['subtraction_lines'] as $line) {
                $this->addLine($line);
            }
        }
        if (!empty($invoice['expenses_lines'])) {
            foreach ($invoice['expenses_lines'] as $line) {
                $this->addLine($line);
            }
        }

        $this->invoices[] = $this->invoice;

        if ($result == null) {
            $submit = $this->submitInvoice($invoice['date']);

            if ($submit == false) {
                if (!empty($invoice['payment_lines'])) {
                    $this->payInvoice($invoice, $invoice['payment_lines']);

                }
            }
        } else {
            $submit = $this->submitInvoiceUpdate($result[0]);

            if ($submit == false) {
                if (!empty($invoice['payment_lines'])) {
                    $this->payInvoice($invoice, $invoice['payment_lines']);
                }
            }
        }
    }

    private function createInvoice($invoice)
    {
        $this->invoice = [
            'DocNumber' => $this->docRef,
            'TxnDate' => $invoice['date'],
            'CustomerRef' => [
                'value' => $this->links['contact_id']
            ],
            'Line' => [

            ],
            "GlobalTaxCalculation" => 'TaxInclusive'
        ];
    }

    //    private function createJournalEntry($line)
    //    {
    //        $this->journal = [
    //            'Line' => [
    //                [
    //                    "JournalEntryLineDetail" => [
    //                        "PostingType" => "Debit",
    //                        "AcountRef" => [
    //                            "name" => 'Accommodation Sales',
    //                            "value" => 200
    //                        ]
    //                    ],
    //                    "DetailType" => "JournalEntryLineDetail",
    //                    "Amount" => 100.00,
    //                    "Description" => "Amount of accommodation revenue for DATE"
    //                ],
    //                [
    //                    "JournalEntryLineDetail" => [
    //                        "PostingType" => "Credit",
    //                        "AcountRef" => [
    //                            "name" => 'Accommodation Receivable',
    //                            "value" => 200
    //                        ]
    //                    ],
    //                    "DetailType" => "JournalEntryLineDetail",
    //                    "Amount" => 100.00,
    //                    "Description" => "Amount of accommodation revenue receivable for DATE"
    //                ]
    //
    //            ]
    //        ];
    //
    //        try {
    //
    //            $journalEntry = JournalEntry::create($this->journal);
    //
    //        } catch (\Exception $e) {
    //
    //            throw new \Exception($e);
    //
    //        }
    //    }

    private function addLine($line)
    {
        $rate = explode('%', $line['accounting_tax_code_name']);

        if (count($rate) > 1) {
            $rate = '1.'.$rate[0];
        } else {
            $rate = 0;
        }
        if ($line['value'] != 0) {
            $this->invoice['Line'][] = [
                'Description' => $line['name'],
                'Amount' => $line['value'],
                "DetailType" => "SalesItemLineDetail",
                "SalesItemLineDetail" => [
                    "TaxInclusiveAmt" => $line['value'],
                    "ItemRef" => [
                        "value" => $line['accounting_code'],
                        "name" => $line['accounting_code_name']
                    ],
                    "TaxCodeRef" => [
                        "value" => $line['accounting_tax_code']
                    ],
                    'Qty' => 1
                ]
            ];
        }
    }

    private function payInvoice($invoice, $lines)
    {
        $quickbooks = App::make(QuickBooksConnection::class);
        $payInvoice = $quickbooks->getDataService()
            ->Query("select * from Invoice where CustomerRef = '".$this->links['contact_id']."' and DocNumber = '".$this->docRef."'");

        if ($payInvoice != null) {
            // check if the invoice is paid
            $payInvoice = $payInvoice[0];

            // have we already paid the invoice?
            if ($payInvoice->Balance > 0) {
                foreach ($lines as $line) {
                    $payment = [
                        'TotalAmt' => $line['value'],
                        'TxnDate' => $payInvoice->TxnDate,
                        'CustomerRef' => [
                            'value' => $this->links['contact_id']
                        ],
                        'DepositToAccountRef' => [
                            'value' => $line['bank_account_id'],
                            'name' => $line['bank_account_name']
                        ],
                        'Line' => [
                            'Amount' => $line['value'],
                            'LinkedTxn' => [
                                'TxnId' => $payInvoice->Id,
                                'TxnType' => 'Invoice'
                            ]
                        ]
                    ];

                    $qbPayment = Payment::create($payment);

                    $result = $quickbooks->getDataService()->Add($qbPayment);

                    $error = $quickbooks->getDataService()->getLastError();

                    if ($error !== false) {
                        $this->errors[] = [
                            'type' => 'Invoice Payment Failed',
                            'date' => $invoice['date'],
                            'message' => $error->getResponseBody(),
                            'account' => $line['bank_account_name']
                        ];
                    }
                }
            }
        }
    }

    private function submitInvoice($date)
    {
        $invoice = $this->invoice;

        $invoice = Invoice::create($invoice);

        $quickbooks = App::make(QuickBooksConnection::class);
        $result = $quickbooks->getDataService()->Add($invoice);

        $error = $quickbooks->getDataService()->getLastError();

        if ($error !== false) {
            $this->errors[] = [
                'type' => 'Invoice Posting Failed',
                'date' => $date,
                'message' => $error->getResponseBody()
            ];
        }

        return $error;
    }

    private function submitInvoiceUpdate($result)
    {
        $invoice = $this->invoice;

        $invoice = Invoice::update($result, $invoice);

        //        $invoice->SyncToken = $result->SyncToken;
        //        $invoice->sparse = true;
        //        $invoice->DocNumber = $invoice->DocNumber.'-'.$invoice->SyncToken;
        //
        //        $this->docRef = $invoice->DocNumber;

        $quickbooks = App::make(QuickBooksConnection::class);
        $result = $quickbooks->getDataService()->Update($invoice);

        $error = $quickbooks->getDataService()->getLastError();

        if ($error !== false) {
            $this->errors[] = [
                'type' => 'Invoice Update Failed',
                'date' => '',
                'message' => $error->getResponseBody()
            ];
        }

        return $error;
    }
}
