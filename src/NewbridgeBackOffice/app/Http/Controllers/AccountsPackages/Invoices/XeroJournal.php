<?php

namespace NewbridgeWeb\Http\Controllers\AccountsPackages\Invoices;

use Auth;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\CompanyAccounts;
use NewbridgeWeb\Repositories\CompanyXeroAuth;
use XeroPHP\Application\PublicApplication;
use XeroPHP\Models\Accounting\ManualJournal;
use XeroPHP\Models\Accounting\TrackingCategory\TrackingOption;

/**
 * Class QuickBooksInvoice
 * @package NewbridgeWeb\Http\Controllers\AccountsPackages\Invoices
 */
class XeroJournal extends Controller
{
    public $config;
    public $xero;
    public $links;
    public $data;
    public $invoices = [];
    public $invoice;
    public $validationMessages = [];
    public $attempts = 0;
    public $errors = [];
    public $company_id;
    public $docRef;
    public $journal;

    public function __construct($data, $company_id, $invoice = null, $callbackUrl = '')
    {
        $this->config = [
            'oauth' => [
                'consumer_key' => config('xero.clientId'),
                'consumer_secret' => config('xero.clientSecret'),
                'callback' => (string) config('xero.redirectUri') . $callbackUrl
            ]
        ];

        $this->links = CompanyAccounts::where('company_id', $company_id)->first();
        $this->data = $data;
        $this->company_id = $company_id;

        $this->xero = new PublicApplication($this->config);
        $authToken = CompanyXeroAuth::where('company_id', Auth::user()->company_id)->first();
        $this->xero->getOAuthClient()
            ->setToken($authToken['token'])
            ->setTokenSecret($authToken['secret']);

        if ($invoice !== null) {
            $this->processSingle($invoice);
        }
        if ($invoice !== null) {
        }
    }

    private function processSingle($key)
    {
        $invoice = $this->data['invoices'][$key];

        $this->createJournal($invoice);


        foreach ($invoice['mod_department_lines'] as $line) {
            $this->addJournalLine($line, true);
        }

        foreach ($invoice['department_lines'] as $line) {
            $this->addJournalLine($line, true);
        }


        if (!empty($invoice['balance_lines'])) {
            foreach ($invoice['balance_lines'] as $line) {
                $this->addJournalLine($line, true);
            }
        }

        if (!empty($invoice['journal_payment_lines'])) {
            foreach ($invoice['journal_payment_lines'] as $line) {
                $this->addJournalLine($line, false);
            }
        }


        $this->journal->save();
    }

    private function createJournal($invoice)
    {
        $this->journal = new ManualJournal($this->xero);
        $this->journal->setDate(\DateTime::createFromFormat('Y-m-d', $invoice['date']));
        $this->journal->setLineAmountType('Inclusive');
        $this->journal->setNarration('Journal for Highlevel '.$invoice['date']);
    }

    private function addJournalLine($line, $reverse = false)
    {
        try {
            $journalLine = new ManualJournal\JournalLine();
            $journalLine->setDescription($line['name']);
            $journalLine->setLineAmount($reverse ? $line['value'] * -1 : $line['value']);
            $journalLine->setAccountCode($line['accounting_code']);
            $journalLine->setTaxType($line['accounting_tax_code'] != null ? $line['accounting_tax_code'] : 'NONE');

            $tracking_option = new TrackingOption();
            $tracking_option->setName($this->links['tracking_option_name']);

            $trackingcategory = new ExtendedTrackingCategory();
            $trackingcategory->setName($this->links['tracking_category_name'])
                ->addOption($tracking_option);

            $journalLine->addTracking($trackingcategory);


            $this->journal->addJournalLine($journalLine);
        } catch (\Exception $e) {
            throw new \Exception($e);
        }
    }
}
