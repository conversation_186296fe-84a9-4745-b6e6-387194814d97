<?php

namespace NewbridgeWeb\Http\Controllers\AccountsPackages\HighLevel;

use GuzzleHttp\Client;
use NewbridgeWeb\Http\Controllers\Controller;

class Integration extends Controller
{
    private $config;
    public $client;

    public function __construct($config)
    {
        $this->config = $config;

        $this->client = new Client([
            'headers' => [
                'Content-Type' => 'application/json',
                'username' => $this->config['accounts_integration_username'],
                'password' => $this->config['accounts_integration_password'],
            ]
        ]);
    }

    public function getData($date)
    {
        $res = null;

        try {
            $res = $this->client->post($this->config['accounts_integration_url'], ['body' => json_encode(
                [
                    'date' => $date
                ]
            )]);
        } catch(\Exception $e) {
            report($e);

            return response()->json([
                'exception' => $e, 'code' => $e->getCode(),
                'seconds' => $e->getResponse()->getHeader('Retry-After')

            ], $e->getCode());
        }

        return $res;
    }
}
