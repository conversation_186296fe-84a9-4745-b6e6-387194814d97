<?php

namespace NewbridgeWeb\Http\Controllers\Customers;

use Auth;
use Carbon\Carbon;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CompanyLinks;
use NewbridgeWeb\Http\Helpers\NewbridgeLoyaltyHelper;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\CustomerGroup;
use NewbridgeWeb\Repositories\CustomerLinks;

use NewbridgeWeb\Repositories\CustomerTypes;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationCompanies;
use NewbridgeWeb\Repositories\PosTransactionCustomer;
use Ramsey\Uuid\Uuid;
use Yajra\Datatables\Datatables;

class CrmController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function checkExists(string $membershipNumber, ?string $customerId = null): JsonResponse
    {
        $exists = Customer::where('membership_no', $membershipNumber)
            ->where('company_id', session('user.company_id'))
            ->where('customer_type', Customer::LOYALTY_TYPE);

        if ($customerId != null) {
            $exists->where('id', '!=', $customerId);
        }

        $exists = $exists->count();

        return response()->json(['taken' => $exists], 200);
    }

    public function index(): Response
    {
        $groups = CustomerGroup::where('company_id', Auth::user()->company_id)->get();
        $types = CustomerTypes::where('company_id', Auth::user()->company_id)->get();

        $groups_select = [];
        $groups_select['Show all'] = '';

        $types_select = [];
        $types_select['Show all'] = '';

        foreach ($groups as $group) {
            $groups_select[$group->displayname] = $group->guid;
        }

        foreach ($types as $type) {
            $types_select[$type->displayname] = $type->guid;
        }

        $groups_select = json_encode($groups_select);
        $types_select = json_encode($types_select);

        return response()->view('modules.customers.datatables.table', compact('groups_select', 'types_select'));
    }

    public function create(): Response
    {
        $companies = CompanyLinks::links(Auth::user()->company_id);
        $customers = Customer::with('group', 'address', 'marketing', 'type')->whereIn('company_id', $companies)->where(
            'customer_type',
            0
        )->get();

        $groups = CustomerGroup::whereIn('company_id', $companies)->get();
        $types = CustomerTypes::whereIn('company_id', $companies)->get();

        $groups_select = [];

        foreach ($groups as $group) {
            $groups_select[$group->displayname] = $group->guid;
        }

        $groups_select = json_encode($groups_select);

        $types_select = [];

        foreach ($types as $type) {
            $types_select[$type->displayname] = $type->guid;
        }

        $types_select = json_encode($types_select);


        return response()->view(
            'modules.customers.create',
            compact('customers', 'groups', 'types', 'groups_select', 'types_select')
        );
    }

    /**
     * @param Request $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);
            Customer::where('id', $k)->get()->each(function($model) use ($updates){
                $model->update($updates);
            });
        }

        return $this->getData($dataTables);
    }

    /**
     * @param Request $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function editById(Request $request, Datatables $dataTables)
    {
        $id = $request->input('id');

        $input = $request->except(
            'id',
            'points_balance',
            'address_line1',
            'address_line2',
            'city',
            'county',
            'postcode',
            'customer_link',
            'expires',
            'points-reason'
        );
        $address = $request->except(
            'id',
            'points_balance',
            'first_name',
            'last_name',
            'telephone',
            'prefix',
            'email',
            'group_guid',
            'membership_no',
            'expires',
            'notes',
            'customer_link',
            'credit_limit',
            'type_guid',
            'points-reason'
        );

        $points = $request->input('points_balance');

        $expires = $request->input('expires') != '' ? Carbon::createFromFormat('Y-m-d', $request->input('expires'))->toDateString() : null;

        $creditLimit = $request->input('credit_limit');

        $customer = Customer::find($id);
        $customer->fill($input);
        $customer->expires = $expires;
        $customer->received_from = 0;
        $customer->save();


        $customer->address()->delete();
        $customer->address()->create($address);

        $customer->links()->delete();

        if (!empty($request->input('customer_link'))) {
            $link = new CustomerLinks();
            $link->parent_id = $request->input('customer_link');
            $link->child_id = $customer->id;
            $link->save();
        }

        return response()->json(['status' => 'ok'], 200);
    }

    public function postCreate(Request $request): RedirectResponse
    {
        $input = $request->except(
            'points_balance',
            'address_line1',
            'address_line2',
            'city',
            'county',
            'postcode',
            'customer_link'
        );
        $address = $request->except(
            'points_balance',
            'first_name',
            'last_name',
            'telephone',
            'prefix',
            'email',
            'group_guid',
            'membership_no',
            'expires',
            'notes',
            'customer_link',
            'type_guid'
        );

        $rules = [
            'first_name' => 'required',
            'last_name' => 'required',
            'email' => 'required',
            'group_guid' => 'required',
            'membership_no' => 'required'
        ];

        $this->validate($request, $rules);

        $points = $request->input('points_balance');

        $customer = new Customer();
        $customer->fill($input);
        $customer->company_id = Auth::user()->company_id;
        $customer->guid = Uuid::uuid4();
        $customer->received_at = Carbon::parse('1979-01-01 00:00:00');
        $customer->received_from = 0;
        $customer->save();

        $customer->address()->create($address);

        if (!empty($request->input('customer_link'))) {
            $link = new CustomerLinks();
            $link->parent_id = $request->input('customer_link');
            $link->child_id = $customer->id;
            $link->save();
        }

        $group = CustomerGroup::where('guid', $customer->group_guid)->first();

        if ($group->gives_points == 1 && $group->default_points_value > 0) {
            $default = [
                'points' => $group->default_points_value,
                'reason_text' => 'Loyalty Group Default Value Applied'
            ];

            $customer->points()->create($default);
        }

        $transaction = [
            'points' => $points,
            'reason_text' => 'Manual Top-up on Create'
        ];

        $customer->points()->create($transaction);

        $message = ['status' => 'success', 'message' => 'Customer Created'];
        \Session::flash('message', $message);

        return redirect('/customers');
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, Datatables $dataTables, $id)
    {
        $customer = Customer::find($id);
        $customer->delete();

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function getData(Datatables $dataTables)
    {
        $companies = CompanyLinks::links(Auth::user()->company_id);
        $model = Customer::with([
            'group' => function ($q) {
                $q->select('id', 'displayname', 'guid');
            }
        ])
            ->with('type')
            ->whereIn('company_id', $companies)->where('customer_type', 0);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function getTransactionData(Request $request, Datatables $dataTables, $id)
    {
        $customer = Customer::find($id);

        $companies = CompanyLinks::links(Auth::user()->company_id);
        $model = PosTransactionCustomer::where('customer_guid', $customer['guid'])
            ->where('company_id', Auth::user()->company_id)
            ->select('id', 'finalised_date', 'membership_no', 'total', 'order_number');

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function editModal($id = null)
    {
        $companies = CompanyLinks::links(Auth::user()->company_id);
        $customer = Customer::find($id);
        $groups = CustomerGroup::whereIn('company_id', $companies)->get();

        return view('modules.customers.includes.edit-modal', compact(['customer', 'groups']));
    }

    public function getEdit($id)
    {
        $companies = CompanyLinks::links(Auth::user()->company_id);
        $groups = CustomerGroup::whereIn('company_id', $companies)->get();
        $customers = Customer::with('group', 'address', 'marketing')->where('id', '!=', $id)->whereIn(
            'company_id',
            $companies
        )->where('customer_type', 0)->get();
        $types = CustomerTypes::whereIn('company_id', $companies)->get();

        $customer = Customer::where('id', $id)
            ->with(['group', 'address', 'parentLink'])
            ->with([
                'points' => function ($q) {
                    $q->orderBy('created_at', 'DESC');
                }
            ])
            ->first();

        $nbLoyalty = null;
        $loyaltyIntegration = SiteIntegrationCompanies::where('integration_id', 2)
            ->where('company_id', Auth::user()->company_id)
            ->first();

        if($loyaltyIntegration)
        {
            $body = [
                'email' => $customer->email,
                'app_key' => $loyaltyIntegration->app_key
            ];

            $nbLoyalty = NewbridgeLoyaltyHelper::sendPostRequest('/api/check-status', $body);

        }

        return view('modules.customers.profile', compact('customer', 'groups', 'customers', 'types', 'nbLoyalty'));
    }

    public function verifyLoyaltyCustomer($id): RedirectResponse
    {
        $customer = Customer::find($id);
        $loyaltyIntegration = SiteIntegrationCompanies::where('integration_id', 2)
            ->where('company_id', Auth::user()->company_id)
            ->first();

        if($loyaltyIntegration)
        {
            $body = [
                'email' => $customer->email,
                'app_key' => $loyaltyIntegration->app_key
            ];

            $nbLoyalty = NewbridgeLoyaltyHelper::sendPostRequest('/api/manual-verification', $body);
        }

        $message = ['status' => 'success', 'message' => 'Customer Verified'];
        \Session::flash('message', $message);

        return redirect('/customers/edit/' . $id);
    }

    public function points(Request $request, $type)
    {
        $customer = Customer::find($request->input('id'));

        if ($type == 'add') {
            $points = abs($request->input('points'));
        }
        if ($type == 'remove') {
            $points = abs($request->input('points')) * -1;
        }

        $transaction = [
            'points' => $points,
            'user_id' => Auth::user()->id,
            'reason_text' => $request->input('reason')
        ];

        $customer->points()->create($transaction);

        return Customer::find($request->input('id'));
    }
}
