<?php

namespace NewbridgeWeb\Http\Controllers\Customers;

use Auth;
use Illuminate\View\View;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CompanyLinks;
use NewbridgeWeb\Repositories\CustomerGroup;

use NewbridgeWeb\Repositories\SubDepartment;

class CrmSubGroupController extends Controller
{
    /**
     * @return View
     */
    public function index()
    {
        $companies = CompanyLinks::links(Auth::user()->company_id);
        //        $groups = CustomerGroup::whereIn('company_id', $companies)->get();
        //
        //        $groups_select = [];
        //
        //        foreach($groups as $group){
        //            $groups_select[$group->name] = $group->guid;
        //        }
        //
        //        $groups_select = json_encode($groups_select);

        return view('modules.customer-subgroups.datatables.table', compact('groups'));
    }

    public function create()
    {
        $companies = [Auth::user()->company_id];

        $sub_departments = [];

        foreach ($companies as $company) {
            $sub_departments[$company] = SubDepartment::where('company_id', $company)->with('products')->get();
        }

        return response()->view('modules.customer-groups.create', compact('sub_departments', 'companies'));
    }
}
