<?php

namespace NewbridgeWeb\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Products;
use Ramsey\Uuid\Uuid;
use Ya<PERSON>ra\Datatables\Datatables;

class DepartmentController extends Controller
{
    public function index()
    {
        $departments = Departments::forCurrentCompany();

        return response()->view('modules.departments.datatables.department-table', compact('departments'));
    }

    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);

            Departments::where('id', $k)->get()->each(function ($model) use ($updates) {
                $model->update($updates);
            });

            $department = Departments::find($k);
            \Event::dispatch(new UpdaterUpdateCrudEvent($department));
        }

        return $this->getDepartmentData($dataTables);
    }

    public function editById(Request $request, Datatables $dataTables)
    {
        $input = $request->except('id');
        $id = $request->input('id');

        $department = Departments::find($id);
        $department->fill($input);
        $department->received_from = 0;
        $department->save();

        \Event::dispatch(new UpdaterUpdateCrudEvent($department));

        return response()->json(['status' => 'ok'], 200);
    }

    public function add(Request $request)
    {
        $input = $request->input();

        $department = new Departments();
        $department->fill($input);
        $department->company_id = Auth::user()->company_id;
        $department->guid = Uuid::uuid4();
        $department->received_at = Carbon::parse('1979-01-01 00:00:00');
        $department->received_from = 0;
        $department->save();

        \Event::dispatch(new UpdaterInsertCrudEvent($department));

        return response()->json(['status' => 'ok'], 200);
    }

    public function delete(Request $request, Datatables $dataTables)
    {
        $id = $request->input('id');


        $department = Departments::find($id);


        $products = Products::with('groups')->where('department_guid', $department->guid)->get();

        foreach ($products as $p) {
            $p->department_guid = null;
            $p->save();
        }

        $department->delete();

        \Event::dispatch(new UpdaterUpdateCrudEvent($department));

        return response()->json(['status' => 'ok'], 200);
    }

    public function getDepartmentData(Datatables $dataTables)
    {
        $model = Departments::forCurrentCompanyDataTables();

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function getDepartmentDataSingle(Datatables $dataTables, $id)
    {
        $model = Departments::forCurrentCompanyById($id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function editModal($id = null)
    {
        $department = Departments::find($id);

        return view('modules.departments.includes.edit-modal', compact(['department']));
    }
}
