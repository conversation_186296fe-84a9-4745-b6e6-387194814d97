<?php

namespace NewbridgeWeb\Http\Controllers\Xero;

use Auth;
use DB;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\AccountsPackages\XeroAuth2;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\CompanyAccounts;
use NewbridgeWeb\Repositories\CompanyAccountsLinks;
use NewbridgeWeb\Repositories\CompanyAccountsPaymentLinks;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\OAuth\OAuth2Connection;
use NewbridgeWeb\Repositories\PaymentTypes;
use NewbridgeWeb\Repositories\XeroTaxTypes;
use XeroPHP\Application;
use XeroPHP\Models\Accounting\Account;
use XeroPHP\Models\Accounting\Contact;
use XeroPHP\Models\Accounting\Item;

class XeroSetup extends Controller
{
    private $config;
    private $xero;

    private function processItems($items)
    {
        $results = [];

        foreach ($items as $item) {
            $itemKey = str_replace(' ', '', strtolower($item->getName()));
            $results[$itemKey] = [
                'item_name' => $item->getName(),
                'item_code' => $item->getCode(),
                'tax_type' => $item['SalesDetails']->getTaxType(),
                'acc_code' => $item['SalesDetails']->getAccountCode(),
                'purchase_tax_type' => $item['PurchaseDetails']->getTaxType(),
                'purchase_acc_code' => $item['PurchaseDetails']->getAccountCode(),
                'key' => $itemKey
            ];
        }

        return $results;
    }

    public function __construct($callbackUrl = '')
    {
        $this->config = [
            'oauth' => [
                'callback' => config('app.url') . $callbackUrl,
                'consumer_key' => config('xero.clientId'),
                'consumer_secret' => config('xero.clientSecret')
            ]
        ];
    }

    public function view()
    {
        $checkXeroAuth = new XeroAuth2('/company/xero/callback');
        $result = $checkXeroAuth->checkSession()->getData();
        $company_id = Auth::user()->company_id;

        if ($result->status === 'success') {
            $token = OAuth2Connection::where("user_id", Auth::user()->id)
                ->where('company_id', Auth::user()->company_id)
                ->first();

            $this->xero = new Application($token->access_token, $token->tenant_id);
        } else {
            $url = $checkXeroAuth->getUrlAndSetState();

            return view('modules.xero.auth', compact('url'));
        }

        $accounts = $this->xero->load(Account::class)->execute()->getArrayCopy();

        $bankAccounts = [];
        $revenueAccounts = [];

        foreach ($accounts as $account) {
            if ($account->getType() === 'BANK') {
                $bankAccounts[] = $account;
            } else {
                $revenueAccounts[] = $account;
            }
        }

        $items = $this->xero->load(Item::class)->execute()->getArrayCopy();

        // do not show in the item list if not attached to a sales account
        foreach ($items as $k => $item) {
            if ($item->getSalesDetails()->getAccountCode() === null) {
                unset($items[$k]);
            }
        }

        $contacts = $this->xero->load(Contact::class)->execute()->getArrayCopy();
        $taxes = XeroTaxTypes::all();

        $departments = Departments::forCurrentCompany();

        $items = $this->processItems($items);

        $types = [
            ['shortname' => 'expense', 'displayname' => 'Expenses'],
            ['shortname' => 'discount', 'displayname' => 'Discounts'],
            ['shortname' => 'gratuity', 'displayname' => 'Gratuities'],
            ['shortname' => 'service', 'displayname' => 'Service Charges'],
            ['shortname' => 'giftcards', 'displayname' => 'Gift Card TopUp']
        ];

        $paymentMethods = PaymentTypes::where('id', '!=', 4)->with(['xerolinks' => function ($q) use ($company_id) {
            $q->where('company_id', $company_id);
        }])->get()->toArray();

        $currentLinks = CompanyAccountsLinks::where('company_id', Auth::user()->company_id)->get()->toArray();

        if (count($currentLinks) === 0) {
            // match departments with items in XERO
            foreach ($departments as $department) {
                $departmentKey = strtolower(str_replace(' ', '', $department->displayname));

                if (isset($items[$departmentKey])) {
                    $department->acc_code = $items[$departmentKey]['item_code'];
                    $department->tax_code = $items[$departmentKey]['tax_type'];
                    $department->save();
                }
            }

            foreach ($types as $key => $type) {
                $typeName = strtolower(str_replace(' ', '', $type['displayname']));

                if (isset($items[$typeName])) {
                    $link = CompanyAccountsLinks::firstOrNew(['company_id' => Auth::user()->company_id, 'command' => $type['shortname'], 'displayname' => $type['displayname']]);
                    $link->acc_code = $items[$typeName]['item_code'];
                    $link->tax_code = $items[$typeName]['tax_type'];
                    $link->save();
                } else {
                    $link = CompanyAccountsLinks::firstOrNew(['company_id' => Auth::user()->company_id, 'command' => $type['shortname'], 'displayname' => $type['displayname']]);
                    $link->acc_code = null;
                    $link->tax_code = null;
                    $link->save();
                }
            }

            $currentLinks = CompanyAccountsLinks::where('company_id', Auth::user()->company_id)->get()->toArray();
            $departments = Departments::forCurrentCompany();
        }

        $companyXero = CompanyAccounts::where('company_id', Auth::user()->company_id)->first();

        return view('modules.xero.setup', compact('accounts', 'revenueAccounts', 'bankAccounts', 'items', 'types', 'currentLinks', 'contacts', 'departments', 'companyXero', 'taxes', 'paymentMethods'));
    }

    public function callback(Request $request)
    {
        $checkXeroAuth = new XeroAuth2();
        $url = $checkXeroAuth->processCallback($request);

        return redirect($url);
    }

    public function save(Request $request)
    {
        DB::beginTransaction();

        try {
            $input = $request->input();

            if ($input['invoice_contact'] == null || $input['sales_account'] == null || $input['purchase_account'] == null) {
                DB::rollback();

                return response()->json(['status' => 'error', 'message' => 'Please ensure the required fields have a value'], 400);
            }

            $companyXero = CompanyAccounts::firstOrNew(['company_id' => Auth::user()->company_id]);
            $companyXero->contact_id = $input['invoice_contact'];
            $companyXero->sales_account_id = $input['sales_account'];
            $companyXero->purchase_account_id = $input['purchase_account'];
            $companyXero->save();



            $errors = [];

            if (!empty($input['bank_account'])) {
                if (in_array(null, $input['bank_account'])) {
                    DB::rollback();

                    return response()->json(['status' => 'error', 'message' => 'Please make sure all bank accounts a have a value, or are marked as \'Do Not Post\''], 400);
                }

                CompanyAccountsPaymentLinks::where('company_id', Auth::user()->company_id)->delete();
                foreach ($input['bank_account'] as $ki => $ba) {
                    if ($ba !== null) {
                        $paymentLink = new CompanyAccountsPaymentLinks();
                        $paymentLink->company_id = Auth::user()->company_id;
                        $paymentLink->payment_method = $ki;
                        $paymentLink->bank_account_id = $ba;
                        $paymentLink->save();
                    }
                }
            }

            if (!empty($input['departments'])) {
                foreach ($input['departments'] as $k => $department) {
                    if ($input['departments_tax'][$k] != null) {
                        $departmentTax = explode('::', $input['departments_tax'][$k]);
                    } else {
                        $departmentTax = null;
                    }

                    if ($department !== null) {
                        $department = explode('::', $department);

                        $update = Departments::where('guid', $department[0])->first();
                        $update->acc_code = $department[1];
                        $update->tax_code = $departmentTax[1];
                        $update->save();
                    } else {
                        $errors[] = 'Please make sure all departments have been associated with an item from Xero.';
                    }
                }
            }

            if (!empty($input['types'])) {
                foreach ($input['types'] as $k => $type) {
                    if ($type !== null) {
                        $link = explode('::', $type);
                        $update = CompanyAccountsLinks::find($link[0]);
                        $update->acc_code = $link[1];
                        $update->tax_code = $input['types_tax'][$k];
                        $update->save();
                    } else {
                        $errors[] = 'Please make sure all financial types have been associated with an item from Xero.';
                    }
                }
            }


            DB::commit();

            return response()->json(['status' => 'success', 'message' => 'Created!']);
        } catch (\Exception $e) {
            DB::rollback();

            return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }
}
