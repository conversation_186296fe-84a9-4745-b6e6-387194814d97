<?php

namespace NewbridgeWeb\Http\Controllers\Xero;

use Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\CompanyXeroAuth;
use XeroPHP\Application\PublicApplication;
use XeroPHP\Remote\Request;
use XeroPHP\Remote\URL;

class XeroAuth extends Controller
{
    public $config;
    public $xero;

    public function __construct($callbackUrl = '/xero/callback')
    {
        $this->config = [
            'oauth' => [
                'callback' => config('app.url') . $callbackUrl,
                'consumer_key' => config('xero.clientId'),
                'consumer_secret' => config('xero.clientSecret')
            ]
        ];

        $this->xero = new PublicApplication($this->config);
    }

    public function checkSession()
    {
        // we are waiting for a non expired session to appear
        $authToken = CompanyXeroAuth::where('company_id', Auth::user()->company_id)->first();

        if ($authToken !== null && $authToken->expires !== null) {
            $expired = Carbon::parse($authToken->expires)->lessThan(Carbon::now());

            if (!$expired) {
                return response()->json(['status' => 'success']);
            }
        }

        return response()->json(['status' => 'fail']);
    }

    /**
     * Create a function that either returns a URL or a true value
     */
    public function checkCurrentTokenOrStartAuthProcess()
    {
        $authToken = CompanyXeroAuth::where('company_id', Auth::user()->company_id)->first();

        if ($authToken === null) {
            $url = $this->getAuthUrl();

            return ['status' => 'login', 'url' => $url];
        } else {
            if ($authToken->expires !== null) {
                $expired = Carbon::parse($authToken->expires)->lessThan(Carbon::now());

                if ($expired) {
                    $url = $this->getAuthUrl();

                    return ['status' => 'login', 'url' => $url];
                } else {
                    return ['status' => 'success'];
                }
            } else {
                $url = $this->getAuthUrl();

                return ['status' => 'login', 'url' => $url];
            }
        }
    }

    public function getAuthUrl()
    {
        /**
         * The token has expired, so lets get a new one!
         */
        $url = new URL($this->xero, URL::OAUTH_REQUEST_TOKEN);
        $request = new Request($this->xero, $url);

        //Here's where you'll see if your keys are valid.
        //You can catch a BadRequestException.
        try {
            $request->send();
        } catch (\Exception $e) {
            /** Errors */
            if ($request->getResponse()) {
                print_r($request->getResponse()->getOAuthResponse());

                return 'no-url';
            }
        }

        // token and secret response with callback confirmation
        $oauth_response = $request->getResponse()->getOAuthResponse();


        if (isset($oauth_response['oauth_token'])) {
            $url = $this->xero->getAuthorizeURL($oauth_response['oauth_token']);


            $authToken = CompanyXeroAuth::firstOrNew(['company_id' => Auth::user()->company_id]);
            $authToken->token = $oauth_response['oauth_token'];
            $authToken->secret = $oauth_response['oauth_token_secret'];
            $authToken->expires = null;
            $authToken->save();

            return $url;
        } else {
            return 'no-url';
        }
    }

    /**
     * Create a function that processes the callback and confirms to the user
     * that the authorisation was successful.
     */
    public function processCallback(\Illuminate\Http\Request $request)
    {
        $callback = $request->input();

        $authToken = CompanyXeroAuth::where('company_id', Auth::user()->company_id)->first();

        $this->xero->getOAuthClient()
            ->setToken($authToken['token'])
            ->setTokenSecret($authToken['secret']);

        $this->xero->getOAuthClient()->setVerifier($callback['oauth_verifier']);
        $url = new URL($this->xero, URL::OAUTH_ACCESS_TOKEN);
        $request = new Request($this->xero, $url);

        try {
            $request->send();
        } catch (\Exception $e) {
            /** Errors */
            if ($request->getResponse()) {
                print_r($request->getResponse()->getOAuthResponse());
            }

            return response()->json(['status' => 'error', 'error' => json_encode($request->getResponse()->getOAuthResponse())]);
        }

        $oauth_response = $request->getResponse()->getOAuthResponse();

        $authToken = CompanyXeroAuth::firstOrNew(['company_id' => Auth::user()->company_id]);
        $authToken->token = $oauth_response['oauth_token'];
        $authToken->secret = $oauth_response['oauth_token_secret'];
        $authToken->expires = Carbon::now()->addSeconds($oauth_response['oauth_expires_in']);
        $authToken->save();

        $dest = Redis::get(Auth::user()->id.'intended_account_url');

        return $dest;
    }

    public function xeroSelfClose()
    {
        return view('modules.xero.xero-self-close');
    }
}
