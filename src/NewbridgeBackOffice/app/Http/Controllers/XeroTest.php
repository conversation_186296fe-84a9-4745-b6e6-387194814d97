<?php

namespace NewbridgeWeb\Http\Controllers;

use Carbon\Carbon;
use XeroPHP\Application\PublicApplication;
use XeroPHP\Models\Accounting\Item;
use XeroPHP\Remote\Request;
use XeroPHP\Remote\URL;

class XeroTest extends Controller
{
    private $config;
    public $xero;

    public function __construct($callbackUrl = null)
    {
        $this->config = [
            'oauth' => [
                'callback'        => 'https://newbridge-web.new/'.$callbackUrl,
                'consumer_key'    => 'MWKVHM3AHLSZHCQBJVD1VBCDEULGIZ',
                'consumer_secret' => 'QJ6OA0GBYEV80S4YH6SZNGSZBY3MXT',
            ]
        ];
    }

    public function Auth()
    {
        $xero = new PublicApplication($this->config);

        $session = \Session::get('Xer0Auth');

        $expired = Carbon::parse($session['expires'])->lessThan(Carbon::now());

        if ($session !== null && $expired === false) {
            return \Session::get('Xer0Auth');
        } else {
            $url = new URL($xero, URL::OAUTH_REQUEST_TOKEN);
            $request = new Request($xero, $url);


            //Here's where you'll see if your keys are valid.
            //You can catch a BadRequestException.
            try {
                $request->send();
            } catch (\Exception $e) {
                print_r($e);
                if ($request->getResponse()) {
                    print_r($request->getResponse()->getOAuthResponse());
                }
            }

            // token and secret response with callback confirmation
            $oauth_response = $request->getResponse()->getOAuthResponse();


            $this->setOAuthSession($oauth_response['oauth_token'], $oauth_response['oauth_token_secret'], null);

            printf(
                '<a href="%s">Click here to Authorize</a>',
                $xero->getAuthorizeURL($oauth_response['oauth_token'])
            );
        }
    }

    public function callback(\Illuminate\Http\Request $request)
    {
        $vars = $request->input();

        $xero = new PublicApplication($this->config);

        $oauth_session = \Session::get('Xer0Auth');

        $xero->getOAuthClient()
            ->setToken($oauth_session['token'])
            ->setTokenSecret($oauth_session['token_secret']);

        if (isset($vars['oauth_verifier'])) {
            $xero->getOAuthClient()->setVerifier($vars['oauth_verifier']);
            $url = new URL($xero, URL::OAUTH_ACCESS_TOKEN);
            $request = new Request($xero, $url);

            $request->send();

            $oauth_response = $request->getResponse()->getOAuthResponse();

            $this->setOAuthSession($oauth_response['oauth_token'], $oauth_response['oauth_token_secret'], $oauth_response['oauth_expires_in']);

            $this->xero = $xero;

            return redirect('/xero');
        }

        return false;
    }

    public function createInvoice()
    {
        $xero = new PublicApplication($this->config);

        $auth = $this->Auth();

        if ($auth !== null) {
            $xero->getOAuthClient()
                ->setToken($auth['token'])
                ->setTokenSecret($auth['token_secret']);


            $contacts = $xero->load(Item::class)->execute();


            return $contacts;
        }
    }

    //The following two functions are just for a demo
    //you should use a more robust mechanism of storing tokens than this!
    public function setOAuthSession($token, $secret, $expires = null)
    {
        if ($expires !== null) {
            $expires = Carbon::now()->addMinutes(30);
        }
        \Session::put('Xer0Auth', [
            'token' => $token,
            'token_secret' => $secret,
            'expires' => $expires
        ]);
    }
}
