<?php

namespace NewbridgeWeb\Http\Controllers\Company;

use Auth;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\AlarmPeriod;
use NewbridgeWeb\Repositories\AlarmResults;

use NewbridgeWeb\Repositories\Alarms;
use NewbridgeWeb\Repositories\AlarmTypes;
use Yajra\Datatables\Datatables;

class AlarmController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return mixed
     */
    public function __construct()
    {
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        return view('modules.company-alarms.datatables.table');
    }

    public function getCreate()
    {
        $alarms = AlarmTypes::all();
        $alarm_periods = AlarmPeriod::all();

        return view('modules.company-alarms.create', compact('alarms', 'alarm_periods'));
    }

    public function getEdit($id)
    {
        $alarmTypes = AlarmTypes::all();
        $alarm_periods = AlarmPeriod::all();
        $alarm = Alarms::find($id);

        return view('modules.company-alarms.edit', compact('alarmTypes', 'alarm', 'alarm_periods'));
    }

    public function results($id)
    {
        $alarm = Alarms::where('id', $id)->with('results')->first();

        $alarmResults = new AlarmResults();
        $alarmResults->where('alarm_id', $id)->where('status', 0)->update(['status' => 1]);

        return view('modules.company-alarms.results', compact('alarm'));
    }

    public function create(Request $request)
    {
        $input = $request->input();

        $alarm = new Alarms();
        $alarm->fill($input);
        $alarm->company_id = Auth::user()->company_id;
        $alarm->save();

        return response()->json(['status' => 'success', 'message' => 'Alarm Created!'], 200);
    }

    public function edit(Request $request, $id)
    {
        $input = $request->input();

        $alarm = Alarms::find($id);
        $alarm->fill($input);
        $alarm->save();

        return response()->json(['status' => 'success', 'message' => 'Alarm Created!'], 200);
    }

    public function delete(Request $request, $id)
    {
        $alarm = Alarms::find($id);
        $alarm->delete();

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        $model = Alarms::where('company_id', Auth::user()->company_id)->with('type')->with('period');

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public static function getAlarms(Request $request = null)
    {
        $alarms = new AlarmResults();
        $alarms = $alarms->with(['alarm' => function ($q) {
            $q->with('type')->withTrashed();
        }])->where('company_id', Auth::user()->company_id)
            ->where('status', 0)
            ->orderBy('id', 'DESC')
            ->get();


        if ($request != null && $request->segment(2) == 'new') {
            return view('includes.alarms', compact('alarms'))->render();
        }


        return view('includes.alarms', compact('alarms'))->render();
    }
}
