<?php

namespace NewbridgeWeb\Http\Controllers\Company;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\Sites;
use Ya<PERSON>ra\Datatables\Datatables;

class TerminalSettings extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        return view('modules.company.terminals.datatables.table', compact(['terminals']));
    }

    /**
     * @param DepartmentDatatablesRequest $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            Sites::where('id', $k)->update($v);
            Sites::where('id', $k);
            $data = Sites::where('id', $k)->first();
        }

        return $this->data($dataTables);
    }

    /**
     * @param Request $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function editById(Request $request, Datatables $dataTables)
    {
        $input = $request->except('id');
        $id = $request->input('id');

        $pos = new Pos();
        $pos = $pos->find($id);
        $pos->fill($input);
        $pos->received_from = 0;
        $pos->save();

        return response()->json(['status' => 'ok'], 200);
    }

    //    /**
    //     * @param Request $request
    //     * @param Datatables $dataTables
    //     * @return \Illuminate\Http\JsonResponse
    //     */
    //    public function create(Request $request)
    //    {
    //        $input = $request->input();
    //
    //        $currentSiteNumber = Sites::where('company_id', Auth::user()->company_id)->orderBy('site_num', 'DESC')->first();
    //        $site_num = $currentSiteNumber != null ? $currentSiteNumber->site_num+1 : 1;
    //        $company_id = Auth::user()->company_id;
    //
    //        $pos = new Sites();
    //        $pos->fill($input);
    //        $pos->site_num = $site_num;
    //        $pos->company_id = $company_id;
    //        $pos->save();
    //
    //        $company = new CompanyController();
    //        $company->createInitialPage($company_id, $site_num);
    //
    //        return response()->json(['status' => 'ok'], 200);
    //    }

    //    /**
    //     * @param Request $request
    //     * @param Datatables $dataTables
    //     * @return \Illuminate\Http\JsonResponse
    //     */
    //    public function delete(Request $request, Datatables $dataTables)
    //    {
    //
    ////            $id = $request->input('id');
    ////
    ////            $site = Sites::find($id);
    ////            $site->delete();
    //
    //        return response()->json(['status' => 'ok'], 200);
    //    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        $model = Pos::where('company_id', Auth::user()->company_id)->where('site_num', $this->site_num);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param Datatables $dataTables
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDataSingle(Datatables $dataTables, $id)
    {
        $model = Sites::forCurrentCompanyById($id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function editModal($id = null)
    {
        $site = Sites::find($id);

        return view('modules.company.sites.includes.edit-modal', compact(['site']));
    }
}
