<?php

namespace NewbridgeWeb\Http\Controllers\Company;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\View;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\ResellerUserHelper;
use NewbridgeWeb\Jobs\ProductsStocks\Calculate\ProductsStockForCompanyJob;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\CurrencySymbols;
use NewbridgeWeb\Repositories\Integrations\Integrations;
use NewbridgeWeb\Repositories\Integrations\IntegrationSettings;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PosSettings;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\UpdateVersions;
use Yajra\Datatables\Datatables;

class CompanyCrudController extends Controller
{
    public function index(): View
    {
        $company = Company::where('company_id', Auth::user()->company_id);

        return view('modules.company.companies.table', compact('company'));
    }

    public function recalculateAll()
    {
        $companies = Company::all();

        foreach ($companies as $company) {
            $sites = Sites::where('company_id', $company->id)->get();

            foreach ($sites as $site) {
                if ($site->hasStockModuleEnabled()) {
                    dispatch(new ProductsStockForCompanyJob($company->id))->onQueue(HorizonQueues::STOCK);
                    break;
                }
            }
        }

        return redirect()->back();
    }

    public function editById(Request $request, Datatables $dataTables): JsonResponse
    {
        $input = $request->except('id', 'integrations');
        $id = $request->input('id');
        $company = Company::find($id);

        $company->fill($input);
        $company->received_from = 0;
        $company->save();

        Cache::forget('company_currency_symbol_' . $id);

        return response()->json(['status' => 'ok'], 200);
    }

    public function edit(Request $request, Datatables $dataTables): JsonResponse
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            Company::where('id', $k)->update($v);
        }

        return $this->getData($dataTables);
    }

    public function updateCompany(Request $request, $id): JsonResponse
    {
        $company = Company::find($id);
        $input = $request->except('integrations', 'integration_settings', 'integration_sites', 'reseller_default_modules');
        $integrations = $request->input('integrations');
        $settings = $request->input('integration_settings');

        $sites = $request->input('integration_sites');

        Cache::forget('company_currency_symbol_' . $id);
        // throw some validation on company

        $currency = $input['currency'];
        $currencySymbol = CurrencySymbols::find($currency);

        $reseller_default_modules = 0;

        if ($request->has('reseller_default_modules')) {
            foreach ($request->input('reseller_default_modules') as $point) {
                $reseller_default_modules = $reseller_default_modules + $point;
            }
        }

        $company->reseller_default_modules = $reseller_default_modules;
        $company->save();

        $sites = Sites::where('company_id', $id)->with(['terminals' => function ($q) use ($id) {
            $q->where('company_id', $id);
        }])->get();

        foreach ($sites as $site) {
            foreach ($site->terminals as $terminal) {
                $setting = PosSettings::where('setting_id', 'CurrencyPrefix')->where('terminal_num', $terminal->terminal_num)->where('site_num', $site->site_num)
                    ->where('company_id', $site->company_id)->first();

                if ($setting != null) {
                    $setting->value1 = $currencySymbol->value != null ? $currencySymbol->value : $currencySymbol->code;

                    $setting->save();
                    \Event(new UpdaterUpdateCrudEvent($setting));
                }
            }
        }

        $sum = 0;

        if (!empty($integrations)) {
            foreach ($integrations as $k => $integration) {
                $integration = Integrations::find($k);
                $sum += $integration->bit_value;
                if (isset($settings[$k])) {
                    foreach ($settings[$k] as $sk => $setting) {
                        $settingUpdate = IntegrationSettings::firstOrNew([
                            'company_id' => $company->id,
                            'integration_id' => $integration->id,
                            'setting_id' => $sk
                        ]);

                        $settingUpdate->value = $setting;
                        $settingUpdate->save();
                    }
                }
                if ($integration->bit_value & 4) {
                    $clerk = Clerks::where('guid', $company->id . '-ORDOO-CLERK')->first();

                    $data = [
                        'full_name' => 'Ordoo App',
                        'short_name' => 'Ordoo App',
                        'company_id' => $company->id,
                        'pin' => 'ORDOOAPP',
                        'default_screen_guid' => null,
                        'employee_no' => *********,
                        'guid' => $company->id . '-ORDOO-CLERK',
                        'ishidden' => 1,
                        'ismanager' => 1,
                        'can_config' => 1,
                        'can_no_sale' => 1,
                        'can_void' => 1,
                        'can_refund' => 1,
                        'can_discount' => 1,
                        'istrainee' => 0,
                        'accesslevel' => 0,
                        'site_num' => 0
                    ];

                    if ($clerk == null) {
                        $clerk = new Clerks($data);
                        $clerk->save();
                    } else {
                        $clerk->fill($data);
                        $clerk->save();
                    }


                    \Event(new UpdaterUpdateCrudEvent($clerk));

                    $payment_method = Payments::firstOrCreate([
                        'DisplayName' => 'Ordoo Online',
                        'company_id' => $company->id,
                        'CommandUID' => $company->id . '-ORDOO-APP-PAYMENT',
                        'prefix_amount' => 0.00,
                        'EFTConnection' => 0,
                        'Command' => 11,
                        'CommandType' => 0,
                        'AccessLevel' => 0,
                        'InDrawerValue' => 0,
                        'method_type' => 3 // other
                    ]);

                    \Event(new UpdaterUpdateCrudEvent($payment_method));
                }

                if ($integration->bit_value = 16) {
                    $clerk = Clerks::where('guid', $company->id . '-WEBAPP-CLERK')->first();

                    $data = [
                        'full_name' => 'Web App Orders',
                        'short_name' => 'Web App Orders',
                        'company_id' => $company->id,
                        'pin' => 'WEBAPP123',
                        'default_screen_guid' => null,
                        'employee_no' => *********333,
                        'guid' => $company->id . '-WEBAPP-CLERK',
                        'ishidden' => 1,
                        'ismanager' => 1,
                        'can_config' => 1,
                        'can_no_sale' => 1,
                        'can_void' => 1,
                        'can_refund' => 1,
                        'can_discount' => 1,
                        'istrainee' => 0,
                        'accesslevel' => 0,
                        'site_num' => 0
                    ];

                    if ($clerk == null) {
                        $clerk = new Clerks($data);
                        $clerk->save();
                    } else {
                        $clerk->fill($data);
                        $clerk->save();
                    }

                    \Event(new UpdaterUpdateCrudEvent($clerk));

                    $payment_method = Payments::firstOrCreate([
                        'DisplayName' => 'Paid Online',
                        'company_id' => $company->id,
                        'CommandUID' => $company->id . '-APP-PAYMENT',
                        'prefix_amount' => 0.00,
                        'EFTConnection' => 0,
                        'Command' => 11,
                        'CommandType' => 0,
                        'AccessLevel' => 0,
                        'InDrawerValue' => 0,
                        'method_type' => 3 // other
                    ]);

                    \Event(new UpdaterUpdateCrudEvent($payment_method));
                }

                if (is_array($sites)) {
                    if (isset($sites[$k])) {
                        foreach ($sites[$k] as $k2 => $site_setting) {
                            $settingUpdate = IntegrationSettings::firstOrNew([
                                'company_id' => $company->id,
                                'integration_id' => $integration->id,
                                'setting_id' => 'site_' . $k2
                            ]);

                            $settingUpdate->value = $site_setting;
                            $settingUpdate->save();
                        }
                    }
                }
            }
        }

        $oldProductValue = $company->site_specific_products;
        $oldSiteValue = $company->site_specific_clerks;

        $company->fill($input);
        $company->active_integrations = $sum;
        $company->site_specific_clerks = !isset($input['site_specific_clerks']) ? 0 : 1;
        $company->site_specific_products = !isset($input['site_specific_products']) ? 0 : 1;
        $company->spotchecks_affect_stock = isset($input['spotchecks_affect_stock']) ? 1 : 0;
        $company->save();

        if (isset($input['site_specific_products']) && $input['site_specific_products'] == 1 && $oldProductValue == 0) {
            Products::where('company_id', $company->id)->get()
                ->each(function ($model) {
                    $model->update(['site_num' => '1']);
                });
            $modifiers = Commands::where('Command', 6)->where('company_id', $company->id)->get();

            foreach ($modifiers as $modifier) {
                $modifier->site_num = 1;
                $modifier->save();

                //update buttons
                PosButtons::where('plu_product_page_guid', $modifier->CommandUID)->get()
                    ->each(function ($model) {
                        $model->update(['site_num' => '1']);
                    });
            }
        } elseif ($oldProductValue == 1 && isset($input['site_specific_products']) && $input['site_specific_products'] == 0) {
            Products::where('company_id', $company->id)->get()
                ->each(function ($model) {
                    $model->update(['site_num' => 0]);
                });
            $modifiers = Commands::where('Command', 6)->where('company_id', $company->id)->get();

            foreach ($modifiers as $modifier) {
                $modifier->site_num = 0;
                $modifier->save();

                //update buttons
                PosButtons::where('plu_product_page_guid', $modifier->CommandUID)->get()
                    ->each(function ($model) {
                        $model->update(['site_num' => 0]);
                    });
            }
        }

        $newValue = !isset($input['site_specific_clerks']) ? 0 : 1;
        $change = $oldSiteValue == $newValue ? false : true;

        if ($change) {
            Clerks::where('company_id', $company->id)
                ->where('full_name', 'not like', '%newbridge%')
                ->get()
                ->each(function ($model) use ($newValue) {
                    $model->update(['site_num' => $newValue]);
                });
        }

        return response()->json(['status' => 'success', 'message' => 'Company updated, ' . $company->company_name], 200);
    }

    public function getData(Datatables $dataTables)
    {
        $company = Company::where('id', '>', 0);

        if (Auth::user()->hasRole('reseller')) {
            $companies = ResellerUserHelper::resellerCompanies()['companies'];

            $company_ids = array_column($companies, 'id');

            $company->whereIn('id', $company_ids)->with('reseller');
        }

        return $dataTables->eloquent($company)->setRowId('id')->make(true);
    }

    public function getDataAsJson()
    {
        $company = Company::where('id', '>', 0);
        if (Auth::user()->hasRole('reseller')) {
            $companies = ResellerUserHelper::resellerCompanies()['companies'];

            $company_ids = array_column($companies, 'id');

            $company->whereIn('id', $company_ids)->with('reseller');
        }
        $data = $company->get();
        $currentTemplate = Company::find(Auth::user()->real_company_id);
        $currentTemplate = $currentTemplate->template_company_id;

        return response()->json(['data' => $data, 'current' => $currentTemplate]);
    }

    public function setTemplate(Request $request)
    {
        try {
            $reseller = Company::where('id', Auth::user()->real_company_id)->firstOrFail();
            $reseller->template_company_id = $request->json('value');
            $reseller->save();

            return response()->json('Your template has been updated');
        } catch (\Exception $e) {
            report($e);

            return response()->json("Your template could not be updated due to an error.", 500);
        }
    }

    public function getDataSingle(Datatables $dataTables, $id)
    {
        $company = new Company();

        return $dataTables->eloquent($company)->setRowId('id')->make(true);
    }

    public function createForm($id = null)
    {
        $company = Company::find($id);

        return view('modules.company.register', compact(['company']));
    }

    public function editForm($id = null)
    {
        $company = Company::find($id);

        $versions = UpdateVersions::all();

        $currencies = CurrencySymbols::all();

        $resellers = Company::where('is_reseller', 1)->get();

        if (Auth::user()->hasRole('reseller')) {
            $companies = ResellerUserHelper::resellerCompanies()['companies'];

            $company_ids = array_column($companies, 'id');

            if (!in_array($id, $company_ids)) {
                abort(404);
            }
        }

        $integrations = Integrations::with('default_settings')->get();
        $sites = Sites::where('company_id', $id)->withCount(['terminals' => function ($q) use ($id) {
            $q->where('company_id', $id);
        }])->get();

        if ($company->active_integrations == null) {
            $company->active_integrations = 0;
        }
        $companyIntegrations = Integrations::whereRaw('bit_value & ' . $company->active_integrations)->get();

        if (!$companyIntegrations->isEmpty()) {
            $companyIntegrations = $companyIntegrations->keyBy('id')->toArray();

            foreach ($companyIntegrations as $k => $int) {
                $settings = IntegrationSettings::where('company_id', $company->id)->where('integration_id', $k)->get();

                if (!$settings->isEmpty()) {
                    $companyIntegrations[$k]['settings'] = $settings->keyBy('setting_id')->toArray();
                }
            }
        }

        return view('modules.company.edit-company', compact('company', 'integrations', 'companyIntegrations', 'sites', 'resellers', 'versions', 'currencies'));
    }
}
