<?php

namespace NewbridgeWeb\Http\Controllers\Webhooks;

use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Log;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Jobs\ProductsStocks\ExternalOrderJob;
use NewbridgeWeb\Repositories\RedisRepository;
use NewbridgeWeb\Repositories\Sites;

class ExternalStockOrders extends Controller
{
    public function __construct()
    {
    }

    public function options(Request $request): JsonResponse
    {
        Log::info('[External Stock Webhook] Request Received', ['request' => json_encode($request->input()), 'headers' => json_encode($request->headers->all())]);

        $authKey = $request->get('auth_key');

        if ($authKey !== config('newbridge.external_stock_orders_key')) {
            Log::error('[External Stock Webhook] Unauthorized Request', ['request' => json_encode($request->input())]);
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return response()->json(['message' => 'ok'])
            ->withHeaders([
                'WebHook-Allowed-Origin' => '*'
            ]);
    }

    public function receive(Request $request): JsonResponse
    {
        Log::info('[External Stock Webhook] Request Received', ['request' => json_encode($request->input())]);

        $authKey = $request->get('auth_key');

        if ($authKey !== config('newbridge.external_stock_orders_key')) {
            Log::error('[External Stock Webhook] Unauthorized Request', ['request' => json_encode($request->input())]);
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        if($request->input('type') == 'NewStockOrder')
        {

            $rules = [
                'data.CompanyID' => 'required:exists:sys_company,id',
                'data.SiteNumber' => 'required',
                'data.DeliveryRef' => 'required|unique:plu_stock_summary,delivery_no',
                'data.InvoiceRef' => 'required|unique:plu_stock_summary,invoice_no',
                'data.DeliveryDate' => 'required|date',
                'data.Items' => 'required|array',
                'data.Items.*.SupplierCode' => 'required',
                'data.Items.*.Qty' => 'required|numeric',
                'data.Items.*.NetPrice' => 'required|numeric'
            ];

            $validated = $request->validate($rules);

            $validated = $validated['data'];

            Log::info('[External Stock Webhook] Webhook Received', ['event' => json_encode($validated)]);

            $site = Sites::where('company_id',  $validated['CompanyID'])->where('site_num', $validated['SiteNumber'])->first();

            if (!$site) {
                Log::error('[External Stock Webhook] Site not found');
                return response()->json(['error' => 'Site not found'], 404);
            }

            $key = 'external_order_'.$validated['DeliveryRef'];
            RedisRepository::store($key, $validated);

            Log::info('[External Stock Webhook] Order Received for Processing', ['data' => json_encode($validated)]);

            dispatch(new ExternalOrderJob($key));

            return response()->json(['message' => 'Order Received for Processing'], 202);

        }

        // Return a 400 response if the event is not a validation event
        return response()->json(['message' => 'Event not handled'], 400);
    }
}