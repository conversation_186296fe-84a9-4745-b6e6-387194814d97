<?php

namespace NewbridgeWeb\Http\Controllers\Tableplan;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use NewbridgeWeb\AppInsights\Client\Channel\Contracts\Session;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\SiteHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\TableLocations;
use NewbridgeWeb\Repositories\Tables;
use Ramsey\Uuid\Uuid;
use Yajra\DataTables\DataTables;

class TablePlanController extends Controller
{
    public function index()
    {
        $sites = SiteHelper::mySites(true, false);

        return view('modules.tableplan.locations.table', compact('sites'));
    }

    public function editTables($id)
    {
        $location = TableLocations::where('id', $id)
            ->with('tables')
            ->first();

        $tables = json_encode($location->toArray()['tables']);

        return view('modules.tableplan.plan.edit', compact('tables', 'location'));
    }

    public function deleteTables($id)
    {
        $plan = TableLocations::find($id);
        $plan->delete();

        $update = Tables::with(['tablelocation' => function ($q) {
            $q->withTrashed();
        }])->where('plan_guid', $plan->guid)->withTrashed()->first();


        \Event(new UpdaterUpdateCrudEvent($update));

        return response()->json(['status' => 'success', 'message' => 'Location & Tables deleted'], 200);
    }

    public function deleteTable($id)
    {
        $table = Tables::find($id);
        $table->delete();

        $table = Tables::with('tablelocation')->withTrashed()->find($table->id);

        \Event(new UpdaterUpdateCrudEvent($table));

        return response()->json(['status' => 'success', 'message' => 'Table deleted'], 200);
    }

    public function getCreate($site)
    {
        return view('modules.tableplan.locations.create', compact('site'));
    }

    public function saveTable(Request $request)
    {
        $id = $request->input('id');
        $tableData = $request->except('id', 'location');
        $location = TableLocations::find($request->input('location'));
        $location->received_from = 0;
        $location->save();

        $table = Tables::find($id);
        $table->fill($tableData);
        $table->received_from = 0;
        $table->save();

        $table = Tables::with('tablelocation')->find($table->id);

        \Event(new UpdaterUpdateCrudEvent($table));

        return response()->json(['status' => 'success',
            'message' => 'Tables updated!']);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);
            TableLocations::where('id', $k)->get()->each(function($model) use ($updates){
                $model->update($updates);
            });

            $location = TableLocations::find($k);

            $table = Tables::where('plan_guid', $location->guid)->with('tablelocation')->first();
            $table['received_from'] = 0;

            \Event::dispatch(new UpdaterUpdateCrudEvent($table));
        }

        return $this->data($dataTables);
    }

    public function getEditLocation($id)
    {
        $location = TableLocations::findOrFail($id);

        return view('modules.tableplan.locations.edit', compact('location'));
    }

    public function putEditLocation(Request $request, $id)
    {
        $location_data = $request->input();
        $location = TableLocations::find($id);
        $location->fill($location_data);
        $location->RDAreaID = $location->id;
        $location->received_from = 0;
        $location->save();

        return response()->json(['status' => 'success',
            'message' => 'Table plan location '.$location->name.' updated!'], 200);
    }

    public function uploadBackgroundImage(Request $request, $id)
    {
        $request->validate([
            'background_image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        $location = TableLocations::find($id);
        $location->background_image = base64_encode(file_get_contents($request->file('background_image')));
        $location->save();

        \Session::flash('success', 'Background image uploaded!');
        return redirect()->back();

    }

    public function postCreate(Request $request)
    {
        $location_data = $request->except('no_tables', 'prefix', 'tableStartNumber');
        $no_tables = $request->input('no_tables');
        $prefix = $request->input('prefix');
        $tableStartNumber = $request->input('tableStartNumber');
        $location = new TableLocations();
        $location->company_id = Auth::user()->company_id;
        $location->fill($location_data);
        $location->received_from = 0;
        $location->guid = Uuid::uuid4();
        $location->save();

        if($request->hasFile('background_image') and $request->file('background_image')->isValid()) {
            $location->background_image = base64_encode($request->file('background_image'));
            $location->save();
        }

        $location->table_location_id = $location->id;
        $location->RDAreaID = $location->id;
        $location->save();

        $this->createTables($location->guid, $no_tables, $prefix, $tableStartNumber, $location->site_num);
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(DataTables $dataTables)
    {
        $model = TableLocations::where('company_id', Auth::user()->company_id)
            ->where('site_num', \Session::get('current_site'))
            ->where('Segment_From_date', '<=', Carbon::now())
            ->where('Segment_To_date', '>=', Carbon::now());

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function addTable(Request $request)
    {
        $data = $request->except('location');
        $location = $request->input('location');

        $location = TableLocations::findOrFail($location);
        $location->received_from = 0;
        $location->save();

        $exists = Tables::where('plan_guid', $location->guid)
            ->where('tablenumber', $data['tablenumber'])
            ->count();

        if ($exists > 0) {
            return response()->json(
                [
                    'status' => 'error',
                    'message' => 'Table number in use, please check and try again.'],
                400
            );
        }

        $table = new Tables();
        $table->fill($data);
        $table->company_id = $location->company_id;
        $table->guid = Uuid::uuid4();
        $table->plan_guid = $location->guid;
        $table->received_from = 0;
        $table->save();

        $table->RDTableID = $table->id;
        $table->save();


        $table = Tables::with('tablelocation')->find($table->id);
        \Event(new UpdaterInsertCrudEvent($table));

        return response()->json(['status' => 'success', 'table' => $table], 200);
    }

    //TODO change site num to be dynamic
    public function createTables($location, $qty, $prefix, $tableStartNumber, $site_num = 1)
    {
        $row = 0;
        $column = 0;
        $cur = 0;
        $location = TableLocations::where('guid', $location)->first();

        if (isset($tableStartNumber)) {
            $number = $tableStartNumber;
        } else {
            $number = 1;
        }

        for ($i = 0; $i < $qty; $i++) {
            if ($cur == 10) {
                $cur = 0;
                $row++;
                $column = 0;
            }

            $position = $this->calculatePosition($column, $row, 80, 80, 10, 20);

            if ($location != null) {
                $table_data = [
                    'company_id' => $location->company_id,
                    'site_num' => $site_num,
                    'guid' => (string) Uuid::uuid4(),
                    'plan_guid' => $location->guid,
                    'displayname' => $prefix . ' ' . $number,
                    'tablenumber' => $number,
                    'cornerradius' => 0,
                    'angle' => 0,
                    'width' => '75',
                    'height' => '75',
                    'left' => $position['left'],
                    'top' => $position['top'],
                    'editable' => 0,
                    'received_from' => 0
                ];

                $table = new Tables();
                $table->fill($table_data);
                $table->received_from = 0;
                $table->save();

                $table->RDTableID = $table->id;
                $table->save();

                $column++;
                $cur++;
                $number++;

                \Event(new UpdaterInsertCrudEvent($table));
            }
        }
    }

    function calculatePosition($column, $row, $elementWidth = 80, $elementHeight = 80, $gridWidth = 10, $spacing = 20) {

        $left = $column * ($elementWidth + $spacing);
        $top = $row * ($elementHeight + $spacing);

        return ['left' => $left, 'top' => $top];
    }

}
