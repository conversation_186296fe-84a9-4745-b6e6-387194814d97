<?php

namespace NewbridgeWeb\Http\Controllers\Reservations\Rooms;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Reservations\Asset;
use NewbridgeWeb\Repositories\Reservations\AssetGroups;
use NewbridgeWeb\Repositories\Sites;
use Yajra\Datatables\Datatables;

class AssetGroupController extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function assetGroups()
    {
        return view('modules.reservations.asset-groups.datatables.table');
    }

    public function create()
    {
        $company = Auth::user()->company_id;
        $sites = Sites::where('company_id', $company)->get();

        return view('modules.reservations.asset-groups.create', compact('company', 'sites'));
    }

    public function addAssetGroup(Request $request)
    {
        $input = $request->input();

        $assetGroup = new AssetGroups();
        $assetGroup->fill($input);
        $assetGroup->company_id = Auth::user()->company_id;
        $assetGroup->save();

        return response()->json(['status' => 'ok', 'message' => 'Asset Group created'], 200);
    }

    public function edit($id)
    {
        $assetGroup = AssetGroups::find($id);

        return view('modules.reservations.asset-groups.edit', compact('assetGroup'));
    }

    public function inlineEdit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            AssetGroups::where('id', $k)->update($v);
        }

        return $this->getAssetGroups($dataTables);
    }

    public function updateAssetGroup(Request $request, $id)
    {
        $input = $request->input();

        $assetGroup = AssetGroups::find($id);
        $assetGroup->fill($input);
        $assetGroup->save();

        return response()->json(['status' => 'ok', 'message' => 'Asset Group updated'], 200);
    }

    public function delete($id)
    {
        $assetGroup = AssetGroups::find($id);

        $assets = Asset::where('asset_group', $id)->get();

        if (!$assets->isEmpty()) {
            return response()->json(['status' => 'error', 'message' => 'Sorry! You cannot delete Asset Groups that are attached to Assets.'], 400);
        } else {
            $assetGroup->delete();

            return response()->json(['status' => 'ok'], 200);
        }
    }

    public function getAssetGroups(Datatables $dataTables)
    {
        $model = AssetGroups::where('company_id', Auth::user()->company_id)
            ->with(['site' => function ($q) {
                $q->where('company_id', Auth::user()->company_id);
            }]);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }
}
