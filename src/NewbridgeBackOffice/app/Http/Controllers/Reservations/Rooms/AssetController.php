<?php

namespace NewbridgeWeb\Http\Controllers\Reservations\Rooms;

use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Reservations\Asset;
use NewbridgeWeb\Repositories\Reservations\AssetGroups;
use NewbridgeWeb\Repositories\Reservations\AssetImage;
use NewbridgeWeb\Repositories\Reservations\Booking;
use Yajra\Datatables\Datatables;

class AssetController extends Controller
{
    public function asset($id)
    {
        $assetGroup = AssetGroups::find($id);

        return view('modules.reservations.asset.datatables.table', compact('id', 'assetGroup'));
    }

    public function getAsset(Datatables $dataTables, $id)
    {
        $model = Asset::where('asset_group', $id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function create($id)
    {
        $assetGroup = AssetGroups::find($id);

        $data = [
            'asset_group' => $id,
            'company_id' => $assetGroup['company_id'],
            'site_num' => $assetGroup['site_num'],
        ];

        return view('modules.reservations.asset.create', compact('data'));
    }

    public function inlineEdit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            Asset::where('id', $k)->update($v);
        }

        return $this->getAsset($dataTables, $request->segment(2));
    }

    public function addAsset(Request $request)
    {
        $input = $request->except('image-base64');
        $image = $request->input('image-base64');


        $assetGroup = AssetGroups::find($request->segment(2));

        $asset = new Asset();
        $asset->fill($input);
        $asset->asset_group = $assetGroup['id'];
        $asset->company_id = $assetGroup['company_id'];
        $asset->site_num = $assetGroup['site_num'];
        $asset->save();

        $assetImage = new AssetImage();
        $assetImage->asset_id = $asset['id'];
        $assetImage->image = $image;

        $assetImage->save();

        return response()->json(['status' => 'ok', 'message' => 'Asset created'], 200);
    }

    public function delete($id, $asset_id)
    {
        $booking = Booking::where('asset_id', $asset_id)->get();

        if (!$booking->isEmpty()) {
            return response()->json(['status' => 'error', 'message' => 'Sorry! You cannot delete an Asset that has bookings on it!'], 400);
        } else {
            $asset = Asset::find($asset_id);
            $asset->delete();

            $assetImage = AssetImage::where('asset_id', $asset_id);
            $assetImage->delete();

            return response()->json(['status' => 'ok'], 200);
        }
    }

    public function edit($id, $asset_id)
    {
        $assetGroup = AssetGroups::find($id);
        $asset = Asset::find($asset_id);
        $assetImage = AssetImage::where('asset_id', $asset_id)->first();

        return view('modules.reservations.asset.edit', compact('assetGroup', 'asset', 'assetImage'));
    }

    public function updateAsset(Request $request, $id, $asset_id)
    {
        $input = $request->except('image-base64');
        $image = $request->input('image-base64');


        $assetGroup = AssetGroups::find($id);

        $asset = Asset::find($asset_id);
        $asset->fill($input);
        $asset->company_id = $assetGroup['company_id'];
        $asset->site_num = $assetGroup['site_num'];
        $asset->save();

        $assetImage = AssetImage::where('asset_id', $asset_id)->first();
        $assetImage->image = $image;
        $assetImage->save();

        return response()->json(['status' => 'ok', 'message' => 'Asset Group updated'], 200);
    }
}
