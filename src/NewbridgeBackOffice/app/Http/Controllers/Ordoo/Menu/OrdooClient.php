<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Menu;

use GuzzleHttp\Client;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Ordoo\Auth\OrdooAuth;
use NewbridgeWeb\Repositories\OAuth\CompanyOAuth;

/**
 * use api classes that are required for the update api
 */
class OrdooClient extends Controller
{
    public $client;
    public $headers;
    private $token;

    public function __construct($company_id, $site_num = 1)
    {
        $auth = new OrdooAuth($company_id, $site_num);

        if ($auth->isAuthenticated()) {
            $this->token = CompanyOAuth::where('company_id', $company_id)
                ->where('site_num', $site_num)
                ->where('provider', 'ordoo')->first();

            $this->headers = [
                'Authorization' => 'Bearer ' . $this->token['token'],
                'Host' => 'api.ordoo.app',
                'Accept' => 'application/vnd.ordoo.v2'
            ];

            $this->client = new Client(['base_uri' => 'https://api.ordoo.app/']);
        }
    }

    public function getRequest($path = '/me')
    {
        $response = $this->client->get($path, ['headers' => $this->headers]);

        return $response;
    }

    public function putRequest($path = '/', $body = '')
    {
        $response = $this->client->put($path, ['headers' => $this->headers, 'json' => $body]);

        return $response;
    }

    public function postRequest($path = '/', $body = '')
    {
        $response = $this->client->post($path, ['headers' => $this->headers, 'json' => $body]);

        return $response;
    }

    public function deleteRequest($path = '/')
    {
        $response = $this->client->delete($path, ['headers' => $this->headers]);

        return $response;
    }
}
