<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Menu;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;

/**
 * use api classes that are required for the update api
 */
class Traits extends Controller
{
    private $settings;
    private $company_id;
    private $store_id;
    private $site_num;

    public function __construct($company_id, $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
        $this->settings = IntegrationsHelper::get(3, $this->company_id)['settings'];

        if ($this->settings['multisite'] == 1) {
            $this->store_id = $this->settings['site_'.$site_num];
        } else {
            $this->store_id = $this->settings['store_id'];
        }
    }

    /**
     * Get List of Traits
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTraits($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->getRequest('products/'.$id.'/traits');

        if ($response->getStatusCode() < 202) {
            return response()->json(['status' => 'success', 'message' => 'Traits Received', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error getting traits list'], 400);
        }
    }

    /**
     * Get a single Trait
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTrait($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);

        try {
            $response = $client->getRequest('/traits/' . $id);

            return response()->json(['status' => 'success', 'message' => 'Trait Retrieved', 'data' => json_decode($response->getBody(), true)], 200);
        } catch(\Exception $e) {
            report($e);
            return response()->json(['status' => 'error', 'message' => 'Error getting trait data'], 400);
        }
    }

    /**
     * Delete a collection
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteTrait($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->deleteRequest('/traits/'.$id);

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Trait Updated'], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error deleting trait'], 400);
        }
    }

    /**
     * Update a single Trait
     *
     * @param $id
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateTrait($id, $data = [])
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->putRequest('/traits/'.$id, $data);

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Trait Updated', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error updating trait'], 400);
        }
    }

    /**
     * Create a single Trait
     *
     * @param $id
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function createTrait($id, $data = [])
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->postRequest('/products/'.$id.'/traits', $data);

        if ($response->getStatusCode() <= 202) {
            return response()->json(['status' => 'success', 'message' => 'Trait Updated', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error creating trait'], 400);
        }
    }
}
