<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Menu;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;

/**
 * use api classes that are required for the update api
 */
class Products extends Controller
{
    private $settings;
    private $company_id;
    private $store_id;
    private $site_num;

    public function __construct($company_id, $site_num = 1)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
        $this->settings = IntegrationsHelper::get(3, $this->company_id)['settings'];

        if ($this->settings['multisite'] == 1) {
            $this->store_id = $this->settings['site_'.$site_num];
        } else {
            $this->store_id = $this->settings['store_id'];
        }
    }

    /**
     * Get List of Products
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProducts($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->getRequest('/collections/'.$id.'/products');

        if ($response->getStatusCode() < 202) {
            return response()->json(['status' => 'success', 'message' => 'Products Retrieved', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error getting products list'], 400);
        }
    }

    /**
     * Get a single Product
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProduct($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->getRequest('/products/'.$id);

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Product Retrieved', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error getting product data'], 400);
        }
    }

    /**
     * Delete a collection
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteProduct($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->deleteRequest('/products/'.$id);

        if ($response->getStatusCode() < 202) {
            return response()->json(['status' => 'success', 'message' => 'Product Deleted'], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error deleting product'], 400);
        }
    }

    /**
     * Update a single Product
     *
     * @param $id
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProduct($id, $data = [])
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->putRequest('/products/'.$id, $data);

        if ($response->getStatusCode() < 202) {
            return response()->json(['status' => 'success', 'message' => 'Product Updated', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error updating product'], 400);
        }
    }

    /**
     * Create a single Product
     *
     * @param $id //collection id
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function createProduct($id, $data = [])
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->postRequest('/collections/'.$id.'/products', $data);

        if ($response->getStatusCode() < 202) {
            return response()->json(['status' => 'success', 'message' => 'Product Created', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error creating product'], 400);
        }
    }
}
