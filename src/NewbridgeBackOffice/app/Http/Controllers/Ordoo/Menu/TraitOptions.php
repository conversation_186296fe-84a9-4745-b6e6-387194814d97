<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Menu;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;

/**
 * use api classes that are required for the update api
 */
class TraitOptions extends Controller
{
    private $settings;
    private $company_id;
    private $site_num;
    private $store_id;

    public function __construct($company_id, $site_num)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;
        $this->settings = IntegrationsHelper::get(3, $this->company_id)['settings'];

        if ($this->settings['multisite'] == 1) {
            $this->store_id = $this->settings['site_'.$site_num];
        } else {
            $this->store_id = $this->settings['store_id'];
        }
    }

    /**
     * Get List of Trait Options
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTraitOptions($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->getRequest('traits/'.$id.'/trait_options');

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Trait Option Updated', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error getting trait_options list'], 400);
        }
    }

    /**
     * Get a single Trait Option
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTraitOption($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->getRequest('/trait_options/'.$id);

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Trait Option Updated', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error getting trait data'], 400);
        }
    }

    /**
     * Delete a collection
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteTraitOption($id)
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->deleteRequest('/trait_options/'.$id);

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Trait Option Updated'], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error deleting trait'], 400);
        }
    }

    /**
     * Update a single Trait Option
     *
     * @param $id
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateTraitOption($id, $data = [])
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->putRequest('/trait_options/'.$id, $data);

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Trait Option Updated'], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error updating trait'], 400);
        }
    }

    /**
     * Create a single Trait Option
     *
     * @param $id
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function createTraitOption($id, $data = [])
    {
        $client = new OrdooClient($this->company_id, $this->site_num);
        $response = $client->postRequest('/traits/'.$id.'/trait_options', $data);

        if ($response->getStatusCode() !== 200) {
            return response()->json(['status' => 'success', 'message' => 'Trait Option Updated', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error creating trait'], 400);
        }
    }
}
