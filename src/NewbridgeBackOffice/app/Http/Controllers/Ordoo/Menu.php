<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Ordoo\Menu\Collections;
use NewbridgeWeb\Http\Controllers\Ordoo\Menu\Products as OrdooProducts;
use NewbridgeWeb\Http\Controllers\Ordoo\Menu\TraitOptions;
use NewbridgeWeb\Http\Controllers\Ordoo\Menu\Traits;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;
use NewbridgeWeb\Repositories\Integrations\IntegrationLinks;
use NewbridgeWeb\Repositories\LinkModifierGroups;
use NewbridgeWeb\Repositories\PosButtonLink;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\SubDepartment;

/**
 * use api classes that are required for the update api
 */
class Menu extends Controller
{
    public $product = null;
    public $company = null;
    public $site = 1;

    public function __construct($data)
    {
        $this->company = $data['company'];
        $this->product = $data['product'];
        $this->site = $data['site'];


        if ((int) $this->company['active_integrations'] & 4) {
            $this->syncCategories();
            $this->syncProducts();
            $this->syncTraits();
            $this->syncTraitOptions();
        }
    }

    private function existingCategories()
    {
        $collections = new Collections($this->company['id'], $this->site);
        $result = $collections->getCollections();
        $result = $result->getData();

        $ids = [];

        foreach ($result->data->data as $collection) {
            $ids[] = $collection->id;
        }

        return $ids;
    }

    private function deleteCategoriesNotIncluded()
    {
        $collections = new Collections($this->company['id'], (int) $this->site);
        $mappings = IntegrationsHelper::mappings(4, $this->company['id'], 1, $this->site, [], 'array');

        foreach ($mappings as $mapping) {
            $subdepartment = $subdepartments = SubDepartment::whereHas('products', function ($q) {
                $q->where('app_available', 1)
                    ->whereIn('site_num', [$this->site, 0]);
            })
                ->withTrashed()
                ->find($mapping['subject_id']);

            if ($subdepartment == null) {
                $collections->deleteCollection($mapping['third_party_reference']);
                $this->deleteMappingSubject($mapping['subject_id'], 1);
            }
        }
    }

    public function syncCategories()
    {
        $this->deleteCategoriesNotIncluded();

        $existing_catergories_ordoo = $this->existingCategories();
        $mapped_categories_newbridge_subject = IntegrationsHelper::mappings(4, $this->company['id'], 1, $this->site, [], 'array', 'subject_id');

        $collections = new Collections($this->company['id'], (int) $this->site);

        $subDepartments = SubDepartment::where('company_id', $this->company['id'])
            ->whereHas('products', function ($q) {
                $q->where('app_available', 1)
                    ->whereIn('site_num', [$this->site, 0]);
            })
            ->withTrashed();

        if ($this->product != null) {
            $subDepartments = $subDepartments->where('guid', $this->product['sub_department_guid']);
        }

        $subDepartments = $subDepartments->get();

        foreach ($subDepartments as $subdepartment) {
            $collection = [
                'collection' => [
                    "title" => $subdepartment->displayname,
                    "description" => $subdepartment->description,
                    "available" => true,
                    "metadata" => [
                        "newbridge_id" => $subdepartment->guid
                    ]
                ]
            ];

            if (isset($mapped_categories_newbridge_subject[$subdepartment->id])) {
                // check if this now exists externally in ordoo
                if (in_array($mapped_categories_newbridge_subject[$subdepartment->id]['third_party_reference'], $existing_catergories_ordoo)) {
                    // update the collection just in case this is already in ordoo and is mapped in newbridge
                    $result = $collections->updateCollection($mapped_categories_newbridge_subject[$subdepartment->id]['third_party_reference'], $collection);
                    $result = $result->getData();

                    //                    $this->createOrUpdateMapping($subdepartment->id, 1, $result->data->data->id);
                } else {
                    // send the collection to Ordoo and create mapping record
                    $result = $collections->createCollection($collection);
                    $result = $result->getData();

                    $this->createOrUpdateMapping($subdepartment->id, 1, $result->data->data->id);
                }
            } else {
                // this subdepartment is not mapped in newbridge, ordoo unknown
                $result = $collections->createCollection($collection);
                $result = $result->getData();

                $this->createOrUpdateMapping($subdepartment->id, 1, $result->data->data->id);
            }
        }

        $existing_catergories_ordoo = $this->existingCategories();
        $mapped = IntegrationsHelper::mappings(4, $this->company['id'], 1, $this->site, [], 'idlist', 'third_party_reference');
    }

    public function syncProducts()
    {
        $ordooProducts = new OrdooProducts($this->company['id'], $this->site);

        $subdepartments = SubDepartment::with(['products' => function ($q) {
            if ($this->product != null) {
                $q->where('id', $this->product['id'])->withTrashed();
            } else {
                $q->withTrashed()->whereIn('site_num', [$this->site, 0]);
            }
        }, 'mapping' => function ($q) {
            $q->whereIn('site_num', [$this->site, 0])
                ->where('subject_type', 1);
        }])
            ->where('company_id', $this->company['id'])
            ->withTrashed()
            ->get();


        foreach ($subdepartments as $subdepartment) {
            foreach ($subdepartment->products as $prod) {
                $mapping = IntegrationsHelper::findMapping(4, $this->company['id'], 2, $prod->id, $this->site);

                $product = [
                    "product" => [
                        "title" => ucwords(strtolower($prod->displayname)),
                        "description" => $prod->description == null ? ucwords(strtolower($prod->displayname)) : $prod->description,
                        "available" => $prod->app_available == 1 ? true : false,
                        "base_cost" => [
                            "GBP" => [
                                "cents" => ((float) $prod->selling_price_1 * 100)
                            ]
                        ],
                        "metadata" => [
                            "newbridge_id" => $prod->guid
                        ]
                    ]
                ];

                if ($mapping != null) {
                    if ($prod->deleted_at != null || (int) $prod->app_available === 0) {
                        try {
                            $result = $ordooProducts->deleteProduct($mapping->third_party_reference);

                            $this->deleteMappingSubject($prod->id, 2);
                        } catch (\Exception $e) {
                            if ($e->getCode() == 404) {
                                $this->deleteMappingSubject($prod->id, 2);
                            }
                        }
                    } else {
                        $result = $ordooProducts->updateProduct($mapping->third_party_reference, $product);
                        $result = $result->getData();

                        if ($result->status == 'success') {
                            $this->createOrUpdateMapping($prod->id, 2, $result->data->data->id);
                        }
                    }
                } else {
                    if ($prod->deleted_at == null && (int) $prod->app_available === 1) {
                        $result = $ordooProducts->createProduct($subdepartment->mapping->third_party_reference, $product);
                        $result = $result->getData();

                        if ($result->status == 'success') {
                            $this->createOrUpdateMapping($prod->id, 2, $result->data->data->id);
                        }
                    }
                }
            }
        }
    }

    public function deleteAllTraits()
    {
        $ordooTraits = new Traits($this->company['id'], $this->site);

        $products = Products::where('company_id', $this->company['id'])
            ->whereHas('mapping', function ($q) {
                $q->where('subject_type', 2)->whereIn('site_num', [$this->site, 0]);
            })
            ->whereHas('groups', function ($q) {
                $q->withTrashed();
            });

        if ($this->product != null) {
            $products = $products->where('id', $this->product['id']);
        }

        $products = $products->get();

        foreach ($products as $product) {
            $traits = $ordooTraits->getTraits($product->mapping->third_party_reference);

            if (!empty($traits->getData()->data->data)) {
                $traits = $traits->getData()->data->data;

                foreach ($traits as $trait) {
                    $ordooTraits->deleteTrait($trait->id);

                    $this->deleteMappingSubject($trait->id, 3);
                }
            }
        }

        return 'All Traits Deleted';
    }

    public function syncTraits()
    {
        $this->deleteAllTraits();

        $ordooTraits = new Traits($this->company['id'], $this->site);

        $products = Products::with(['groups' => function ($q) {
            $q->with('command');
        }])
            ->where('app_available', 1)
            ->with(['mapping' => function ($q) {
                $q->whereIn('site_num', [$this->site, 0])
                    ->where('subject_type', 2);
            }])
            ->where('company_id', $this->company['id'])
            ->whereIn('site_num', [$this->site, 0]);


        if ($this->product != null) {
            $products = $products->where('id', $this->product['id']);
        }

        $products = $products->get();


        foreach ($products as $prod) {
            foreach ($prod->groups as $group) {
                $trait = [
                    "trait" => [
                        "title" => ucwords(strtolower($group->command->DisplayName)),
                        "description" => '',
                        "required" => $group->command->min > 0 ? true : false,
                        "allow_multiple" => $group->command->max > 1 ? true : false,
                        "maximum_trait_options" => $group->command->max
                    ]
                ];

                $mapping = IntegrationsHelper::findMapping(4, $this->company['id'], 3, $group->id, $this->site);

                if ($mapping != null) {
                    $exists = $ordooTraits->getTrait($mapping->third_party_reference);

                    if ($exists->getData()->status == 'success') {
                        if ($group->deleted_at != null) {
                            $result = $ordooTraits->deleteTrait($mapping->third_party_reference);

                            $this->deleteMappingSubject($group->link_id, 3);
                        } else {
                            $result = $ordooTraits->updateTrait($mapping->third_party_reference, $trait);
                            $result = $result->getData();

                            if ($result->status == 'success') {
                                $this->createOrUpdateMapping($group->link_id, 3, $result->data->data->id);
                            }
                        }
                    } else {
                        if ($prod->deleted_at == null) {
                            $result = $ordooTraits->createTrait($prod->mapping->third_party_reference, $trait);
                            $result = $result->getData();

                            if ($result->status == 'success') {
                                $this->createOrUpdateMapping($group->link_id, 3, $result->data->data->id);
                            }
                        } else {
                            $this->deleteMappingSubject($group->link_id, 3);
                        }
                    }
                } else {
                    if ($prod->deleted_at == null) {
                        $result = $ordooTraits->createTrait($prod->mapping->third_party_reference, $trait);
                        $result = $result->getData();

                        if ($result->status == 'success') {
                            $this->createOrUpdateMapping($group->link_id, 3, $result->data->data->id);
                        }
                    }
                }
            }
        }
    }

    public function syncTraitOptions()
    {
        $ordooTraitOptions = new TraitOptions($this->company['id'], $this->site);

        $products = Products::select('guid')->whereIn('site_num', [$this->site, 0])->where('company_id', $this->company['id']);

        if ($this->product != null) {
            $products = $products->where('id', $this->product['id']);
        }

        $products = $products->get()
            ->pluck('guid');

        $companyProducts = Products::select('guid')->where('company_id', $this->company['id'])
            ->whereIn('site_num', [$this->site, 0])
            ->get()
            ->pluck('guid');

        $groups = LinkModifierGroups::with(['mapping' => function ($q) {
            $q->whereIn('site_num', [$this->site, 0])
                ->where('subject_type', 3);
        }])
            ->whereHas('mapping', function ($q) {
                $q->whereIn('site_num', [$this->site, 0])
                    ->where('subject_type', 3);
            })
            ->whereIn('plu_guid', $products)
            ->get();

        foreach ($groups as $group) {
            $groupButtons = PosButtons::where('plu_product_page_guid', $group->CommandUID)->get()->pluck('guid');
            $groupButtonProducts = PosButtonLink::with(['product' => function ($q) {
                $q->select('id', 'guid', 'selling_price_1', 'displayname');
            }])
                ->with(['mapping' => function ($q) {
                    $q->whereIn('site_num', [$this->site, 0])
                        ->where('subject_type', 4);
                }])
                ->whereIn('button_guid', $groupButtons)
                ->whereIn('commandUID', $companyProducts)
                ->get();

            foreach ($groupButtonProducts as $prod) {
                $option = [
                    "trait_option" => [
                        "title" => ucwords(strtolower($prod->displayname)),
                        "description" => "",
                        "available" => true,
                        "default" => false,
                        "cost_adjustment" => [
                            "GBP" => [
                                "cents" => $group->price_override !== null ? ($group->price_override * 100) : ($prod->selling_price_1 * 100)
                            ]
                        ],
                        "metadata" => [
                            "newbridge_id" => $prod->commandUID
                        ]
                    ]
                ];

                $result = $ordooTraitOptions->createTraitOption($group->mapping->third_party_reference, $option);
                $result = $result->getData();

                if ($result->status == 'success') {
                    $this->createOrUpdateMapping($prod->id, 4, $result->data->data->id);
                }
            }
        }
    }

    private function createOrUpdateMapping($subject_id, $subject_type, $third_party_id)
    {
        $mapping = IntegrationLinks::firstOrNew([
            'integration_id' => 4,
            'subject_id' => $subject_id,
            'subject_type' => $subject_type,
            'company_id' => $this->company['id'],
            'site_num' => $this->site
        ]);

        $mapping->third_party_reference = $third_party_id;
        $mapping->save();
    }

    private function deleteMappingSubject($subject_id, $subject_type)
    {
        IntegrationLinks::where([
            'integration_id' => 4,
            'subject_type' => $subject_type,
            'subject_id' => $subject_id,
            'company_id' => $this->company['id'],
            'site_num' => $this->site
        ])->delete();
    }

    private function deleteMappingthirdParty($subject_id, $subject_type)
    {
        IntegrationLinks::where([
            'integration_id' => 4,
            'subject_type' => $subject_type,
            'third_party_reference' => $subject_id,
            'company_id' => $this->company['id'],
            'site_num' => $this->site
        ])->delete();
    }
}
