<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Orders;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Ordoo\Menu\OrdooClient;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;

/**
 * use api classes that are required for the update api
 */
class Orders extends Controller
{
    private $settings;
    private $company_id;

    public function __construct($company_id)
    {
        $this->company_id = $company_id;
        $this->settings = IntegrationsHelper::get(3, $this->company_id)['settings'];
        $this->client = $client = new OrdooClient($this->company_id);
    }

    /**
     * Get List of tables
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function status($id, $status)
    {
        try {
            switch ($status) {
                case 1:
                    $response = $this->client->putRequest('/orders/' . $id . '/accept');

                    break;
                case 2:
                    $response = $this->client->putRequest('/orders/' . $id . '/decline');

                    break;
                case 3:
                    $response = $this->client->putRequest('/orders/' . $id . '/ready');

                    break;
                case 4:
                    $response = $this->client->putRequest('/orders/' . $id . '/fulfill');

                    break;
            }

            if ($response->getStatusCode() <= 205) {
                return response()->json(['status' => 'success', 'message' => 'Order Updated', 'data' => json_decode($response->getBody(), true)], 200);
            } else {
                return response()->json(['status' => 'error', 'message' => 'Error Updating order'], 400);
            }
        } catch(\Exception $e) {
            report($e);
            return response()->json(['status' => 'error', 'message' => 'Error Updating order'], 400);
        }
    }
}
