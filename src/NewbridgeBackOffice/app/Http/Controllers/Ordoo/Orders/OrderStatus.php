<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Orders;

use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Orders\Orders;

/**
 * use api classes that are required for the update api
 */
class OrderStatus extends Controller
{
    public function updateStatus(Request $request)
    {
        $auth = $request->header('X-Newbridge-Auth');
        $auth = explode(':', base64_decode($auth));

        /**
         * Validate the order number provided and the source of the
         * request
         */
        $content = json_decode($request->getContent(), true);

        $company = cache()->rememberForever($auth[0], function () use ($auth) {
            return Company::where('terminal_access_key', $auth[0])->first();
        });

        $order = Orders::where('order_id', $content['OrderNumber'])->where('company_id', $company['id'])->first();

        if($content['OrderNumber'] == null || $content['OrderNumber'] == '') {
            return response()->json('invalidOrderNumber', 400);
        }

        if ($order !== null) {

            if ($order != null) {

                $order->pos_status = $order->pos_status + 1;
                $order->save();

            }

            return response()->json(['status' => 'success'], 200);

        } else {

            return response()->json(['status' => 'error', 'result' => '', 'message' => 'This order was not found.'], 400);

        }
    }
}
