<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Tables;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Ordoo\Menu\OrdooClient;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;

/**
 * use api classes that are required for the update api
 */
class Tables extends Controller
{
    private $settings;
    private $company_id;
    private $site_num;
    private $store_id;

    public function __construct($company_id, $site_num)
    {
        $this->company_id = $company_id;
        $this->settings = IntegrationsHelper::get(3, $this->company_id)['settings'];

        if ($this->settings['multisite'] == 1) {
            $this->store_id = $this->settings['site_'.$site_num];
        } else {
            $this->store_id = $this->settings['store_id'];
        }

        $this->client = $client = new OrdooClient($this->company_id, $site_num);
    }

    /**
     * Get List of tables
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTables()
    {
        $response = $this->client->getRequest('/stores/'.$this->store_id.'/tables');

        if ($response->getStatusCode() <= 205) {
            return response()->json(['status' => 'success', 'message' => 'Tables Received', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error getting tables'], 400);
        }
    }

    /**
     * Delete a table
     *
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteTable($id)
    {
        $response = $this->client->deleteRequest('/tables/'.$id);

        if ($response->getStatusCode() <= 205) {
            return response()->json(['status' => 'success', 'message' => 'Table Deleted'], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error deleting table'], 400);
        }
    }

    /**
     * Create a single Collection
     *
     * @param $data
     * @return \Illuminate\Http\JsonResponse
     */
    public function createTable($data = [])
    {
        $response = $this->client->postRequest('/stores/'.$this->store_id.'/tables', $data);

        if ($response->getStatusCode() <= 205) {
            return response()->json(['status' => 'success', 'message' => 'Table Created', 'data' => json_decode($response->getBody(), true)], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Error creating the table'], 400);
        }
    }
}
