<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Auth;

use Auth;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Sites;

/**
 * use api classes that are required for the update api
 */
class MultiSiteAuth extends Controller
{
    public function index()
    {
        $company = Company::with('sites')->find(Auth::user()->company_id);
        $result = [];

        $auth = new OrdooAuth($company->id, \Session::get('current_site'));
        $site = Sites::where('company_id', $company->id)->where('site_num', \Session::get('current_site'))->first();

        $result = [
            'name' => $site->site_name,
            'number' => $site->site_num,
            'auth' => $auth->isAuthenticated($site->site_num)->getStatusCode() == 302 ? false : true
        ];

        return view('modules.ordoo.multi-auth', compact('company', 'result'));
    }
}
