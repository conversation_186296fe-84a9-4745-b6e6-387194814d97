<?php

namespace NewbridgeWeb\Http\Controllers\Ordoo\Auth;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use League\OAuth2\Client\Provider\GenericProvider;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\OAuth\CompanyOAuth;

/**
 * use api classes that are required for the update api
 */
class OrdooAuth extends Controller
{
    public $provider = [];
    public $company_id;
    public $site_num;

    public function __construct($company_id = null, $site_num = 1)
    {
        $this->company_id = $company_id;
        $this->site_num = $site_num;

        $this->provider = new GenericProvider([
            'clientId'                => config('ordoo.oauth.client_id'),        // The client ID assigned to you by the provider
            'clientSecret'            => config('ordoo.oauth.client_secret'),    // The client password assigned to you by the provider
            'redirectUri'             => config('ordoo.oauth.callback_url'),
            'urlAuthorize'            => 'https://app.ordoo.co.uk/oauth/authorize',
            'urlAccessToken'          => 'https://app.ordoo.co.uk/oauth/token',
            'urlResourceOwnerDetails' => 'https://app.ordoo.co.uk/oauth/'
        ]);
    }

    public function isAuthenticated($site_num = 1, $redirect = false)
    {
        $this->site_num = $site_num;
        \Session::put('current_site', $site_num);

        if ($this->company_id == null) {
            $this->company_id = Auth::user()->company_id;
        }

        $company = Company::find($this->company_id);

        $integrationSettings = IntegrationsHelper::get(3, $this->company_id);

        $token = $company->getAuthToken('ordoo', $this->site_num);

        if ($token) {
            if ($token->hasExpired()) {
                $newAccessToken = $this->provider->getAccessToken('refresh_token', [
                    'refresh_token' => $token->secret
                ]);
                $token->token = $newAccessToken->getToken();
                $token->secret = $newAccessToken->getRefreshToken();
                $token->expires = Carbon::createFromTimestamp($newAccessToken->getExpires());
                $token->save();

                return response()->json('authorised', 200);
            } else {
                return response()->json('authorised', 200);
            }
        } else {
            // redirect to auth URL
            $authorizationUrl = $this->provider->getAuthorizationUrl().'&scope=read_orders+read_menu+write_menu+write_orders+read_stores+write_stores';
            Session::put('OrdooAuthState', $this->provider->getState());

            return redirect($authorizationUrl);
        }
    }

    public function callback(Request $request)
    {
        $state = Session::get('OrdooAuthState');
        $newState = $request->input('state');

        $site_num = \Session::get('current_site');

        if ($this->company_id == null) {
            $this->company_id = Auth::user()->company_id;
        }

        $company = Company::find($this->company_id);
        $integrationSettings = IntegrationsHelper::get(3, $this->company_id);

        $token = $company->getAuthToken('ordoo', $site_num);


        try {
            if (empty($state) || empty($newState) || $state != $newState) {
                return view('modules.ordoo.auth.error');
            } else {
                $accessToken = $this->provider->getAccessToken('authorization_code', [
                    'code' => $request->input('code')
                ]);

                if ($token) {
                    $token->company_id = $company->id;
                    $token->site_num = $site_num;
                    $token->provider = 'ordoo';
                    $token->token = $accessToken->getToken();
                    $token->secret = $accessToken->getRefreshToken();
                    $token->expires = Carbon::createFromTimestamp($accessToken->getExpires());
                    $token->save();
                } else {
                    $token = new CompanyOAuth();
                    $token->company_id = $company->id;
                    $token->site_num = $site_num;
                    $token->provider = 'ordoo';
                    $token->token = $accessToken->getToken();
                    $token->secret = $accessToken->getRefreshToken();
                    $token->expires = Carbon::createFromTimestamp($accessToken->getExpires());
                    $token->save();
                }

                return redirect('/');
            }
        } catch (\Exception $e) {
            if (config('app.debug')) {
                throw new \Exception($e);
            }

            return view('modules.ordoo.auth.error');
        }
    }
}
