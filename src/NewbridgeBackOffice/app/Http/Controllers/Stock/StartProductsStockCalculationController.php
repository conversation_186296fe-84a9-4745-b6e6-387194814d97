<?php

namespace NewbridgeWeb\Http\Controllers\Stock;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Requests\StartProductsStockCalculationRequest;
use NewbridgeWeb\Jobs\ProductsStocks\Calculate\ProductsStockForCompanyJob;
use NewbridgeWeb\Jobs\ProductsStocks\Calculate\ProductStockJob;

class StartProductsStockCalculationController extends Controller
{
    public function __invoke(StartProductsStockCalculationRequest $request): JsonResponse
    {
        if($request->validated('product_id')) {
            dispatch(new ProductStockJob(
                $request->input('product_id'),
                $request->input('site_num') ?? session('current_site')
            ))->onQueue(HorizonQueues::STOCK);

             Log::info('[STOCK] Recalculate triggered for product: ' . $request->input('product_id') .
                 ' site: ' . $request->input('site_num'));

            return response()->json(['message' => 'Stock calculation for product started!']);
        }

        $this->dispatch(new ProductsStockForCompanyJob($request->input('company_id')))->onQueue(HorizonQueues::STOCK);

         Log::info('[STOCK] Recalculate triggered for company: ' . $request->input('company_id'));

        return response()->json(['message' => 'Stock calculation for company started!']);
    }
}
