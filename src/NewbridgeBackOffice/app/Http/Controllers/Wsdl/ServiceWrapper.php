<?php

namespace NewbridgeWeb\Http\Controllers\Wsdl;

use NewbridgeWeb\Http\Controllers\Controller;
use SoapFault;

class ServiceWrapper extends Controller
{
    /**
     * @var SoapWrapper
     */
    private $client;

    /**
     * SoapController constructor.
     *
     * @param SoapWrapper $soapWrapper
     */
    public function __construct($url = 'http://ec2-3-10-230-142.eu-west-2.compute.amazonaws.com/Newbridge_PHP_Service/Service.svc?wsdl')
    {
        ini_set("soap.wsdl_cache_enabled", "0");

        $this->client = new \SoapClient(
            $url,
            array(
                'trace' => true,
                'connection_timeout' => 10,
                "exception" => 1,
                'soap_version' => SOAP_1_1
            )
        );
    }

    /**
     * Use the SoapWrapper
     *
     * @param null $function
     * @param null $paramName
     * @param null $object
     * @return mixed
     * @throws SoapFault
     */
    public function request($function = null, $paramName = null, $object = null)
    {
        ini_set("soap.wsdl_cache_enabled", "0");

        try {
            $response = $this->client->$function([$paramName => $object]);

            return $response;
        } catch(SoapFault $fault) {
            throw new \SoapFault($fault);
        }
    }
}
