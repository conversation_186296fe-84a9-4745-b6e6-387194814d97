<?php

namespace NewbridgeWeb\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use NewbridgeWeb\Http\Requests\BugReportDataTableRequests;
use NewbridgeWeb\Mail\BugReports\NewRequestNotification;
use NewbridgeWeb\Mail\BugReports\NewRequestReceivedResponse;
use NewbridgeWeb\Mail\BugReports\StatusUpdatedNotification;
use NewbridgeWeb\Mail\BugReports\StatusUpdatedResponse;
use NewbridgeWeb\Repositories\BugReport;
use NewbridgeWeb\User;
use Yajra\Datatables\Datatables;

class BugReportController extends Controller
{
    public function index()
    {
        return view('modules.bug-reports.index');
    }

    public function data(DataTables $datatables)
    {
        $model = BugReport::with('user');

        return $datatables->eloquent($model)->setRowId('id')->make(true);
    }

    /*
     * May not be used
     */
    public function create()
    {
        return view('modules.bug-reports.form');
    }

    public function show($id)
    {
        $issue = BugReport::where('id', $id)->with('user')->first();
        $details = json_decode($issue->details);

        return view('modules.bug-reports.show', ['issue' => $issue, 'details' => $details]);
    }

    public function delete($id)
    {
        $issue = BugReport::find($id);
        $issue->delete();

        return response()->json(['status' => 'ok', 'data' => "Issue deleted successfully"], 200);
    }

    /*
     * Saves request to database
     */
    public function store(Request $request)
    {
        $detailText = [
            "q1" => $request->input('q1'),
            "a1" => $request->input('a1'),
            "q2" => $request->input('q2'),
            "a2" => $request->input('a2'),
        ];


        $bugReport = new BugReport();
        $bugReport->type = (int) $request->input('bug_type');
        $bugReport->status = BugReport::STATUS_NEWLY_SUBMITTED;
        $bugReport->user_id = Auth::id();
        $bugReport->details = json_encode($detailText);
        $bugReport->current_url = url()->previous();
        $bugReport->session_data = json_encode(Session::all());
        $bugReport->save();
        $details = json_decode($bugReport['details'], true);
        $emailData = [
            'user' => User::find(Auth::id()),
            'issue' => [
                'url' => url()->previous(),
                'status' => $bugReport->getStatusTextAttribute(),
                'type' => $bugReport->getTypeTextAttribute(),
                "question1" => $details['q1'],
                "answer1" => $details['a1'],
                "question2" => $details['q2'],
                "answer2" => $details['a2']
            ]
        ];
        $admin = ['<EMAIL>'];
        Mail::to($admin)->send(new NewRequestNotification($emailData));
        Mail::to([$emailData['user']->email])->send(new NewRequestReceivedResponse($emailData));

        return response()->json('Submitted successfully', 200);
    }

    /*
     *  JS Table Controller function
     */

    public function editBugReport(BugReportDataTableRequests $request, Datatables $dataTables)
    {
        $input = $request->input();
        foreach ($input['data'] as $k => $v) {
            $bug = BugReport::find($k);
            $prevStatus = $bug->getStatusTextAttribute();
            $bug->update($v);
            $data = $bug;

            if ($data && isset($v['status'])) {
                $details = json_decode($data->details);
                $emailData = [
                    'user' => User::find($data->user_id),
                    'issue' => [
                        'progressed_by' => Auth::user()->username,
                        'date_reported' => $data->created_at,
                        'url' => $data->current_url,
                        'prev_status' => $prevStatus,
                        'status' => $data->getStatusTextAttribute(),
                        'type' => $data->getTypeTextAttribute(),
                        "question1" => $details->q1,
                        "answer1" => $details->a1,
                        "question2" => $details->q2,
                        "answer2" => $details->a2,
                    ]
                ];
                $admin = [config('newbridge.support_email'), config('newbridge.cameron_email')];
                Mail::to($admin)->send(new StatusUpdatedNotification($emailData));
                Mail::to($emailData['user']->email)->send(new StatusUpdatedResponse($emailData));
            }
        }

        return $this->data($dataTables);
    }
}
