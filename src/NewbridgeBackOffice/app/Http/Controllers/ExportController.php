<?php

namespace NewbridgeWeb\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Jobs\BackgroundExportJob;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Export;
use Ramsey\Uuid\Uuid;

class ExportController extends Controller
{
    public function create()
    {
        $company = Company::find(Auth::user()->company_id);
        $filters = [
            'site' => null,
            'terminal' => null,
            'start' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->startOfDay(),
            'end' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->endOfDay(),
            'start_compare' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->subYear()->startOfDay(),
            'end_compare' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->subYear()->endOfDay(),
            'offset' => $company->time_offset,
            'start_time' => Carbon::now('UTC')->startOfDay()->toTimeString(),
            'end_time' => Carbon::now('UTC')->endOfDay()->toTimeString(),
            'subdepartment' => null,
            'department' => null,
        ];

        return view("modules.export.create", ["filters" => $filters]);
    }

    public function showCompanyExports()
    {
        $exports = Export::where('company_id', Auth::user()->company_id)->orderBy("updated_at", "DESC")->get();

        return view('modules.export.index', ['exports' => $exports]);
    }

    public function createExport(Request $request)
    {
        $export_data = $request->input();

        $export_data['site'] = !isset($export_data['site']) || empty($export_data['site']) || $export_data['site'][0] == 0 ?
            null : $export_data['site'];

        foreach ($export_data['export_type'] as $type) {
            $export = new Export();
            $export->company_id = Auth::user()->company_id;
            $export->guid = Uuid::uuid4();
            $export->exportable_class_id = $type;
            $export->options = json_encode($export_data);
            $export->recipients = $export_data['recipients'];
            $export->status = Export::IN_PROGRESS;
            $export->save();
            $this->dispatch(new BackgroundExportJob($export, $export_data));
        }
    }

    public function download(Request $request)
    {
        $path = $request->input('path');

        if ($path == null) {
            abort(404);
        }

        return Storage::download($path);
    }

    public function delete(int $id)
    {
        Export::find($id)->delete();

        return "Export deleted successfully";
    }
}
