<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use NewbridgeWeb\Http\Requests\CreateUser;
use NewbridgeWeb\Mail\NewUserRegistration;
use NewbridgeWeb\Mail\UserPasswordChange;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\UserAreas;
use NewbridgeWeb\Repositories\UserRole;
use NewbridgeWeb\Repositories\UserSites;
use NewbridgeWeb\User;
use Spatie\Permission\Traits\RefreshesPermissionCache;
use Yajra\Datatables\Datatables;

class UserController extends Controller
{
    use RefreshesPermissionCache;

    public function index()
    {
        if (Auth::user()->hasRole('newbridge')) {
            $roles = UserRole::whereIn('company_id', [Auth::user()->company_id, 0])->get();
        } elseif (Auth::user()->hasRole('reseller')) {
            $roles = UserRole::whereNotIn('id', [1, 347])->whereIn('company_id', [Auth::user()->company_id, 0])->get();
        } else {
            $roles = UserRole::whereNotIn('id', [1, 347])->whereIn('company_id', [Auth::user()->company_id])->get();
        }

        $areas = ClerkAreas::where('company_id', Auth::user()->company_id)->get();

        $sites = Sites::where('company_id', Auth::user()->company_id)->get();

        $company = Company::find(Auth::user()->company_id);

        return view('modules.users.datatables.table', compact('roles', 'company', 'sites', 'areas'));
    }

    public function data(Datatables $dataTables)
    {
        $model = User::where('company_id', Auth::user()->company_id)->get();

        $data = [];
        $data['data'] = [];

        foreach ($model as $user) {
            $user->DT_RowId = $user->id;

            if ((Auth::user()->hasRole('newbridge') && $user->hasRole('newbridge')) || ((Auth::user()->hasRole(
                'newbridge'
            ) || Auth::user()->hasRole('reseller')) && $user->hasRole('reseller'))) {
                $data['data'][] = $user->toArray();
            } elseif (!$user->hasRole('newbridge') && !$user->hasRole('reseller')) {
                $data['data'][] = $user->toArray();
            }
        }

        return $data;
    }

    public function create(CreateUser $request)
    {
        $input = $request->except('roles', 'available_sites', 'areas');
        $roles = $request->input('roles');
        $sites = $request->input('available_sites');
        $areas = $request->input('areas');

        $company = Company::find(Auth::user()->company_id);

        $user = new User();
        $user->fill($input);
        $user->company_id = $company->id;
        $user->real_company_id = $company->id;
        $user->username = $input['username'];
        $user->password = bcrypt($input['password']);
        $user->save();

        if(!$user->hasRole('super-admin')) {
            $roles = array_filter($roles, function ($role) {
                return $role !== 'super-admin';
            });
        }

        $user->syncRoles($roles);

        if (!empty($areas)) {
            foreach ($areas as $area) {
                UserAreas::create([
                    'user_id' => $user->id,
                    'area_guid' => $area,
                ]);
            }
        }

        // add sites to user
        if (!empty($sites)) {
            foreach ($sites as $site) {
                $site = Sites::where('company_id', Auth::user()->company_id)->where('site_num', $site)->first();
                $userSite = new UserSites();
                $userSite->user_id = $user->id;
                $userSite->company_id = Auth::user()->company_id;
                $userSite->site_num = $site->site_num;
                $userSite->site_name = $site->site_name;
                $userSite->created_at = Carbon::now();
                $userSite->save();
            }
        }

        // pass some email data to the welcome mailer
        $data = [
            'to' => $user->email,
            'subject' => 'Your new Newbridge Software account ' . $user->name,
            'name' => $user->name,
            'username' => $user->username,
            'password' => $input['password']
        ];

        self::UserWelcomeEmail($data);

        return response()->json(['status' => 'ok'], 200);
    }

    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            User::where('id', $k)->update($v);
            $data = User::where('id', $k)->first();
        }

        return $this->data($dataTables);
    }

    public function editSingle(Request $request): RedirectResponse|JsonResponse
    {
        /**
         * get data from the request, this data wil be used
         * update the user object
         */
        $input = $request->except(
            ['id', 'roles', 'new_password', 'confirm_new_password', 'email_password', 'available_sites', 'areas']
        );

        $id = $request->input('id');

        $roles = $request->input('roles');

        $sites = $request->input('available_sites');

        $areas = $request->input('areas');

        if (!empty($areas)) {
            UserAreas::where('user_id', $id)->forceDelete();
            foreach ($areas as $area) {
                UserAreas::create([
                    'user_id' => $id,
                    'area_guid' => $area,
                ]);
            }
        }

        /**
         * We use the company name to
         * append the username
         */
        $redirectToLogin = false;
        /**
         * Generate the new password if
         * the user has entered a new value
         * and confirmed it.
         */
        if ($request->input('new_password') != ''
            && $request->input('new_password') == $request->input('confirm_new_password')) {
            $input['password'] = Hash::make($request->input('new_password'));
            if ($id == session('user.id')) {
                $redirectToLogin = true;
            }
        }

        /**
         * Find and update the user
         */
        $user = User::find($id);
        $user->fill($input);
        $user->username = $input['username'];
        $user->save();

        UserSites::where('company_id', $user->company_id)->where('user_id', $user->id)->forceDelete();

        // add sites to user
        if (!empty($sites)) {
            foreach ($sites as $site) {
                $site = Sites::where('company_id', Auth::user()->company_id)->where('site_num', $site)->first();
                $userSite = new UserSites();
                $userSite->user_id = $user->id;
                $userSite->company_id = Auth::user()->company_id;
                $userSite->site_num = $site->site_num;
                $userSite->site_name = $site->site_name;
                $userSite->created_at = Carbon::now();
                $userSite->save();
            }
        }

        if(!$user->hasRole('super-admin')) {
            $roles = array_filter($roles, function ($role) {
                return $role !== 'super-admin';
            });
        }

        if (Auth::user()->hasRole('newbridge')) {
            if ($user->hasRole('newbridge')) {
                $roles[] = 'newbridge';
            }
        } else {
            if ($user->hasRole('reseller')) {
                $roles[] = 'reseller';
            } elseif ($user->hasRole('owner')) {
                $roles[] = 'owner';
            }
        }

        $user->syncRoles($roles);

        /**
         * Send the password changed email,
         * if we have been asked to on the form.
         */
        if ($request->input('new_password') != '' && $request->input('email_password') == 1) {
            // pass some email data to the welcome mailer
            $email_data = [
                'to' => $user->email,
                'subject' => 'Newbridge Software - Your Password Has Changed',
                'name' => $user->name,
                'username' => $user->username,
                'password' => $request->input('new_password')
            ];
            self::UserPasswordChangedEmail($email_data);
        }

        // return OK status
        return response()->json(['status' => 'ok', 'data' => $user, 'redirect' => $redirectToLogin], 200);
    }

    public function editModal($id = null)
    {
        $currentRoles = [];
        $user = User::with('roles')->with([
            'areas' => function ($q) {
                $q->pluck('area_guid');
            }
        ])->find($id);
        $areas = ClerkAreas::where('company_id', $user->company_id)->get();
        $company = Company::find(Auth::user()->company_id);
        $sites = Sites::where('company_id', Auth::user()->company_id)->get();

        $userSites = UserSites::where('user_id', $id)->where('company_id', Auth::user()->company_id)->get();

        if (!$userSites->isEmpty()) {
            $userSites = $userSites->toArray();
            $userSites = array_column($userSites, 'site_num');
        } else {
            $userSites = [];
        }

        if (!empty($user->roles) && $user->roles != null) {
            $currentRoles = array_column($user->roles->toArray(), 'id');
        }

        if (Auth::user()->hasRole('newbridge')) {
            $roles = UserRole::whereIn('company_id', [Auth::user()->company_id, 0])->get();
        } elseif (Auth::user()->hasRole('reseller')) {
            $roles = UserRole::whereNotIn('id', [1, 347])->whereIn('company_id', [Auth::user()->company_id, 0])->get();
        } else {
            $roles = UserRole::whereNotIn('id', [1, 347])->whereIn('company_id', [Auth::user()->company_id])->get();
        }

        $user = User::find($id);

        return view(
            'modules.users.includes.edit-modal',
            compact('user', 'roles', 'currentRoles', 'company', 'userSites', 'sites', 'areas')
        );
    }

    // delete one modifier group based on input('id')
    public function delete(Request $request, $id)
    {
        $subDepartment = User::find($id);
        $subDepartment->delete();

        return response()->json(['status' => 'ok']);
    }

    /**
     * Send the new user registration email
     *
     * @param $data
     * @return string
     */
    public static function UserWelcomeEmail($data)
    {
        $data['preHeaderText'] = 'Welcome to Newbridge Software, your new account details are enclosed.';
        $data['mainTitle'] = 'Welcome!';
        $data['textBlock1'] = 'Welcome to Newbridge Software <b>' . $data['name'] . '</b>, we are happy to have you!';
        $data['buttonUrl'] = 'https://console.newbridgesoftware.co.uk';
        $data['buttonText'] = "Ready? Go!";
        $data['lowerBlocks'][] = [
            'background-color' => 'FFFFFF',
            'text-color' => '888888',
            'content' => '<p><b>Username:</b> ' . $data['username'] . ' <br /> <b>Password:</b> ' . $data['password'] . ' <br /></p>',
            'additional-css' => ''
        ];

        $data['lowerBlocks'][] = [
            'background-color' => 'FFFFFF',
            'text-color' => '666666',
            'content' => 'We are here to answer any questions you may have, contact the team on <strong>02920 003 273</strong> or <a href="mailto:<EMAIL>">email us</a> and we will get back to you as soon as we can.',
            'additional-css' => ''
        ];

        Mail::to($data['to'])->send(new NewUserRegistration($data));

        return 'ok';
    }

    /**
     * Send the new user registration email
     *
     * @param $data
     * @return string
     */
    public static function UserPasswordChangedEmail($data)
    {
        $data['preHeaderText'] = 'Newbridge Software - Password Changed';
        $data['mainTitle'] = 'Your password has been changed';
        $data['textBlock1'] = 'Hey <b>' . $data['name'] . '</b>! Your password has been changed, if you did not request this change please contact a Manager or the Support Desk.';
        $data['buttonUrl'] = 'https://console.newbridgesoftware.co.uk';
        $data['buttonText'] = "Log In";
        $data['lowerBlocks'][] = [
            'background-color' => 'FFFFFF',
            'text-color' => '888888',
            'content' => '<p><b>Username:</b> ' . $data['username'] . ' <br /> <b>Password:</b> ' . $data['password'] . ' <br /></p>',
            'additional-css' => ''
        ];
        $data['lowerBlocks'][] = [
            'background-color' => 'FFFFFF',
            'text-color' => '666666',
            'content' => 'We are here to answer any questions you may have, contact the team on <strong>02920 003 273</strong> or <a href="mailto:<EMAIL>">email us</a> and we will get back to you as soon as we can.',
            'additional-css' => ''
        ];

        Mail::to($data['to'])->send(new UserPasswordChange($data));

        return 'ok';
    }

    public function checkUsername($username)
    {
        $has = User::where('username', $username)->count();

        return response()->json(['taken' => $has], 200);
    }

    public function enable2fa(Request $request)
    {
        $user = Auth::user();

        // initialise the 2FA class
        $google2fa = app('pragmarx.google2fa');

        // generate a new secret key for the user
        $user->google2fa_secret = $google2fa->generateSecretKey();

        // save the user
        $user->save();

        // generate the QR image
        $QR_Image = $google2fa->getQRCodeInline(
            config('app.name'),
            $user->email,
            $user->google2fa_secret
        );

        // Pass the QR barcode image to our view.
        return view('auth.register-2fa', [
            'QR_Image' => $QR_Image,
            'secret' => $user->google2fa_secret,
            'reauthenticating' => true
        ]);
    }

    public function set2FAForm(Request $request)
    {
        $user = User::find(Auth::user()->id);
        $user->google2fa_secret = $request->input('google2fa_secret');
        $user->save();

        Auth::logout();

        return redirect('/login');
    }
}
