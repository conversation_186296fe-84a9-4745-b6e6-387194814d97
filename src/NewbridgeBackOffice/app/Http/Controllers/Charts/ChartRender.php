<?php

namespace NewbridgeWeb\Http\Controllers\Charts;

use Auth;
use Carbon\Carbon;
use Charts;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;

class ChartRender extends Controller
{
    public $start = null;
    public $end = null;
    public $chartType = 'bar';
    public $table = false;
    public $legend = false;

    public function __construct(Request $request)
    {
        if ($request->input('start') && $request->input['end']) {
            $this->start = Carbon::parse($request->input('start') . ' 00:00:00');
            $this->end = Carbon::parse($request->input('end') . ' 23:59:59');
        } else {
            $this->start = Carbon::now()->subDay();
            $this->end = Carbon::now();
        }

        $this->chartType = $request->input('type');
        $this->table = $request->input('table');
        $this->table = $request->input('legend');
    }

    public function productSales()
    {
        $productSales = Products::with(['transactions' => function ($q) {
            $q->whereBetween('datetime', [$this->start, $this->end]);
        }])->where('company_id', Auth::user()->company_id)
            ->withTrashed()
            ->get();

        $labels = [];
        $values = [];

        foreach ($productSales as $sale) {
            if ($sale->transactions->count() > 0) {
                $labels[] = $sale->displayname;
                $values[] = $sale->transactions->count();
            }
        }

        //       $chart = Charts::create('donut', 'morris')
        //            ->title('Product Sales')
        //            ->labels($labels)
        //            ->values($values)
        //            ->responsive(true);

        $chart = Charts::database(PosTransactionDetail::all(), 'bar', 'highcharts')->elementLabel("Transactions")->groupByHour();

        $data = Shopping::all();
        $chart = Charts::create('bar', 'highcharts')
            ->title('My nice chart')
            ->elementLabel('My nice label')
            ->labels($data->pluck('shoppingDate'))
            ->values($data->pluck('price'))
            ->responsive(true);

        return view('modules.test.chart', compact('chart', 'productSales'));
    }

    public function clerkSales()
    {
        $productSales = Products::with(['transactions' => function ($q) {
            $q->whereBetween('datetime', [$this->start, $this->end]);
        }])->where('company_id', Auth::user()->company_id)
            ->withTrashed()
            ->get();

        $labels = [];
        $values = [];

        foreach ($productSales as $sale) {
            if ($sale->transactions->count() > 0) {
                $labels[] = $sale->displayname;
                $values[] = $sale->transactions->count();
            }
        }

        //        $chart = Charts::create('donut', 'morris')
        //            ->title('Product Sales')
        //            ->labels($labels)
        //            ->values($values)
        //            ->responsive(true);

        $chart = Charts::database(PosTransactionDetail::all(), 'bar', 'highcharts')->groupByDay();

        return view('modules.test.chart', compact('chart', 'productSales'));
    }
}
