<?php

namespace NewbridgeWeb\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PaymentTypes;
use NewbridgeWeb\Repositories\PosButtons;
use Ramsey\Uuid\Uuid;
use Ya<PERSON>ra\Datatables\Datatables;

class PaymentController extends Controller
{
    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        $types = PaymentTypes::where('is_hidden', 0)->get();

        $methods = [];

        foreach ($types as $t) {
            $methods[$t->name] = $t->id;
        }

        return view('modules.payment-types.datatables.table', compact('methods', 'types'));
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        $model = Payments::whereNotIn('method_type', [2,4,5])->where('company_id', Auth::user()->company_id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $input = $request->input();

        $payment = new Payments();
        $payment->fill($input);
        $payment->company_id = Auth::user()->company_id;
        $payment->CommandUID = Uuid::uuid4()->__toString();
        $payment->Command = 11;
        $payment->received_from = 0;
        $payment->received_at = '1970-01-01 00:00:00';
        $payment->save();

        \Event::dispatch(new UpdaterInsertCrudEvent($payment));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);

            Payments::where('id', $k)->get()->each(function ($model) use ($updates) {
                $model->update($updates);
            });

            $payment = Payments::find($k);
            \Event::dispatch(new UpdaterUpdateCrudEvent($payment));
        }

        return $this->data($dataTables);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function editSingle(Request $request)
    {
        $input = $request->except('id');
        $id = $request->input('id');

        $payment = Payments::find($id);
        $payment->received_from = 0;
        $payment->fill($input);
        $payment->save();

        \Event::dispatch(new UpdaterUpdateCrudEvent($payment));

        return response()->json(['status' => 'ok', 'data' => $payment], 200);
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function editModal($id = null)
    {
        $types = PaymentTypes::where('is_hidden', 0)->get();
        $payment = Payments::find($id);

        return view('modules.payment-types.includes.edit-modal', compact(['payment', 'types']));
    }

    public function delete(Request $request, $id)
    {
        $payment = Payments::with('button_links')->find($id);

        foreach ($payment->button_links as $l) {
            PosButtons::where('guid', $l->button_guid)->get()->each(function($model){
                $model->update(['updated_at' => Carbon::now()]);
            });

            $button = new PosButtons();
            $button->find($l->id);

            \Event::dispatch(new UpdaterUpdateCrudEvent($button));
        }

        $payment->button_links()->delete();
        $payment->delete();

        \Event::dispatch(new UpdaterUpdateCrudEvent($payment));

        return response()->json(['status' => 'ok']);
    }
}
