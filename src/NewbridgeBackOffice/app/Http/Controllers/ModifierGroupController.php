<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Helpers\ModifierGroupPricingHelper;
use NewbridgeWeb\Repositories\ButtonDefaultStyles;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PosButtonStyle;
use NewbridgeWeb\Repositories\Products;
use Ramsey\Uuid\Uuid;
use Yajra\Datatables\Datatables;

class ModifierGroupController extends Controller
{
    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        $company = Company::find(Auth::user()->company_id);

        return view('modules.modifier-groups.datatables.modifier-group-table', compact('company'));
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        $company = Company::find(Auth::user()->company_id);

        if ($company['site_specific_products'] === 1) {
            $model = Commands::where('company_id', Auth::user()->company_id)->where(
                'site_num',
                \Session::get('current_site')
            )->where('Command', 6);
        } else {
            $model = Commands::where('company_id', Auth::user()->company_id)->where('Command', 6);
        }


        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $company = Company::find(Auth::user()->company_id);

        DB::beginTransaction();

        try {
            $input = $request->input();

            $group = new Commands();
            $group->fill($input);
            $group->site_num = $company->site_specific_products === 1 ? \Session::get('current_site') : 0;
            $group->Command = 6;
            $group->company_id = Auth::user()->company_id;
            $group->CommandUid = Uuid::uuid4();
            $group->received_from = 0;
            $group->price_override = null;
            $group->received_at = Carbon::parse('1970-01-01 00:00:00');
            $group->save();

            // get the default button style
            $defaultStyle = ButtonDefaultStyles::select(
                [
                    'resourcestyle',
                    'color1',
                    'color2',
                    'color3',
                    'color4',
                    'cornerradius',
                    'typeface',
                    'fontsize',
                    'alignment',
                    'foreground'
                ]
            )->first();

            for ($r = 0; $r < 8; $r++) {
                for ($c = 0; $c < 6; $c++) {
                    $button = new PosButtons();
                    $button->guid = Uuid::uuid4();
                    $button->company_id = Auth::user()->company_id;
                    $button->site_num = $company->site_specific_products === 1 ? \Session::get('current_site') : 0;
                    $button->column = $c;
                    $button->row = $r;
                    $button->viewmodel = 5;
                    $button->received_at = Carbon::parse('1970-01-01 00:00:00');
                    $button->plu_product_page_guid = $group['CommandUid'];

                    $style = new PosButtonStyle();
                    $style->fill($defaultStyle->toArray());
                    $style->guid = Uuid::uuid4();
                    $style->received_at = Carbon::parse('1970-01-01 00:00:00');
                    $style->save();

                    $button->pos_button_style_guid = $style->guid;
                    $button->save();

                    $button = PosButtons::with(['pos_button_style', 'lnk_buttonlinks', 'plu_product_page'])->find(
                        $button->id
                    );

                    if ($group->price_override >= 0 && $group->price_override != '') {
                        ModifierGroupPricingHelper::prefixCommands($group, $button);
                    }

                    \Event::dispatch(new UpdaterInsertCrudEvent($button));
                }
            }

            $group = Commands::find($group->id);
            \Event::dispatch(new UpdaterInsertCrudEvent($group));

            DB::commit();

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();

            throw new \Exception($e);
        }
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);

            Commands::where('id', $k)->get()->each(function ($model) use ($updates) {
                $model->update($updates);
                \Event::dispatch(new UpdaterUpdateCrudEvent($model));
            });
        }

        return $this->data($dataTables);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function editSingle(Request $request)
    {
        $input = $request->except('id');
        $id = $request->input('id');

        $updates = ['received_from' => 0];
        $updates = array_merge($updates, $input);

        $modifier = Commands::find($id);
        $modifier->fill($updates);
        $modifier->save();

        $screenButtons = PosButtons::where('plu_product_page_guid', $modifier->CommandUID)->get();

        foreach ($screenButtons as $button) {
            if ($modifier->price_override >= 0 && $modifier->price_override != '') {
                ModifierGroupPricingHelper::prefixCommands($modifier, $button);
            }
        }

        \Event::dispatch(new UpdaterUpdateCrudEvent($modifier));

        return response()->json(['status' => 'ok', 'data' => $modifier], 200);
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function editModal($id = null)
    {
        $group = Commands::find($id);

        return view('modules.modifier-groups.includes.edit-modal', compact(['group']));
    }

    // delete one modifier group based on input('id')
    public function delete(Request $request, $id)
    {
        $group = Commands::with('group_links')->find($id);

        foreach ($group->group_links as $link) {
            $product = Products::where('guid', $link->plu_guid)->first();
            $product->updated_at = Carbon::now();
            $product->save();

            \Event::dispatch(new UpdaterUpdateCrudEvent($product));
        }

        $group->links()->delete();
        $group->buttons()->delete();
        $group->delete();

        \Event::dispatch(new UpdaterUpdateCrudEvent($group));

        $groups = Commands::where('Command', 6)->get();

        return response()->json(['status' => 'ok', 'data' => $groups]);
    }
}
