<?php

namespace NewbridgeWeb\Http\Controllers\Clerks;

use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\AccessLevels;
use NewbridgeWeb\Repositories\Clerks\ClerkPermissionGroups;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\User;
use Ramsey\Uuid\Uuid;
use Ya<PERSON>ra\Datatables\Datatables;

class ClerkPermissionGroupsController extends Controller
{
    /**
     * @return View
     */
    public function index(): View
    {
        return view('modules.clerk-permission-groups.datatables.table');
    }

    /**
     * @return View
     */
    public function getCreate(): View
    {
        return view('modules.clerk-permission-groups.create', ['accessLevels' => AccessLevels::orderBy('order', 'ASC')->get()]);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return JsonResponse
     * @throws Exception
     */
    public function edit(Request $request, Datatables $dataTables): JsonResponse
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            ClerkPermissionGroups::where('id', $k)->update($v);
        }

        return $this->getData($dataTables);
    }

    /**
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function editById(Request $request, int $id): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|max:24',
            'accesslevel' => 'required|array'
        ]);

        $permissionGroup = ClerkPermissionGroups::find($id);

        if(!empty($permissionGroup)) {

            $permissionGroup->name = $validated['name'];
            $permissionGroup->company_id = Auth::user()->company_id;
            $permissionGroup->access_level = isset($validated['accesslevel']) ? array_sum(filter_var_array($validated['accesslevel'], FILTER_SANITIZE_NUMBER_INT)) : 0;
            $permissionGroup->save();

            $clerks = Clerks::wherePermissionGroupGuid($permissionGroup->guid)->get();

            if(!empty($clerks)) {
                foreach ($clerks as $c) {
                    $c->accesslevel = $permissionGroup->access_level;
                    $c->save();

                    $clerk = Clerks::with('lnk_buttonlinks', 'ClkClerkRoles')->where('guid', $c->guid)->first();
                    \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));
                }
            }

            return response()->json(['status' => 'ok'], 200);
        }

        abort(404);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function add(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|max:24',
            'accesslevel' => 'required|array'
        ]);

        $permissionGroup = new ClerkPermissionGroups();
        $permissionGroup->name = $validated['name'];
        $permissionGroup->company_id = Auth::user()->company_id;
        $permissionGroup->guid = Uuid::uuid4();
        $permissionGroup->access_level = isset($validated['accesslevel']) ? array_sum(filter_var_array($validated['accesslevel'], FILTER_SANITIZE_NUMBER_INT)) : 0;
        $permissionGroup->save();

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function delete(int $id): JsonResponse
    {
        $permissionGroup = ClerkPermissionGroups::whereCompanyId(Auth::user()->company_id)->whereId($id)->first();

        $clerks = Clerks::wherePermissionGroupGuid($permissionGroup->guid)->get();

        if (!empty($permissionGroup) && !empty($clerks)) {
            foreach ($clerks as $c) {
                $c->permission_group_guid = null;
                $c->save();

                $clerk = Clerks::with('lnk_buttonlinks', 'ClkClerkRoles')->where('guid', $c->guid)->first();
                \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));
            }
            $permissionGroup->forceDelete();
        }

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Datatables $dataTables
     * @return JsonResponse
     * @throws Exception
     */
    public function getData(Datatables $dataTables): JsonResponse
    {
        $model = ClerkPermissionGroups::whereCompanyId(Auth::user()->company_id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param int $id
     * @return \Illuminate\View\View|JsonResponse
     */
    public function getEdit(int $id): \Illuminate\View\View|JsonResponse
    {
        $permissionGroup = ClerkPermissionGroups::whereId($id)->whereCompanyId(Auth::user()->company_id)->first();

        if(!empty($permissionGroup)) {
            $accessLevels = AccessLevels::orderBy('order', 'ASC')->get();

            return view('modules.clerk-permission-groups.edit', compact('permissionGroup', 'accessLevels'));
        } else {
            abort(404);
        }
    }

    /**
     * @param int $company_id
     * @return bool
     */
    public static function copyDefaultClerkPermissionGroups(int $company_id, User $user): bool
    {
        $reseller = $user->hasRole('reseller') ? Company::find($user->real_company_id) : null;
        $default_company_id = $user->hasRole('reseller') && ($reseller->template_company_id != null && $reseller->template_company_id != 0) ? $reseller->template_company_id : config('newbridge.default_company_id');

        $defaultGroups = ClerkPermissionGroups::whereCompanyId($default_company_id)->get();
        foreach ($defaultGroups as $permissionGroup) {
            $newPermissionGroup = new ClerkPermissionGroups();
            $newPermissionGroup->fill($permissionGroup->toArray());
            $newPermissionGroup->company_id = $company_id;
            $newPermissionGroup->guid = Uuid::uuid4();
            $newPermissionGroup->save();
        }

        return true;
    }
}
