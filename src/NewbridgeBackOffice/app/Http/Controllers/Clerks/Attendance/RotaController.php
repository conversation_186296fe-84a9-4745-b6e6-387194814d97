<?php

namespace NewbridgeWeb\Http\Controllers\Clerks\Attendance;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\AreaHelper;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Http\Helpers\SiteHelper;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Http\Helpers\WagesHelper;
use NewbridgeWeb\Repositories\ActivityRepository;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Clerks\ClerkRoles;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Clerks\Clocking;
use NewbridgeWeb\Repositories\Clerks\ShiftTypes;
use NewbridgeWeb\Repositories\Sites;
use Ramsey\Uuid\Uuid;

class RotaController extends Controller
{
    public $data = [];
    public $currencySymbol = '£';

    public function update(Request $request)
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $data = $request->input();

        $clocking = Clocking::find($data['id']);

        if (isset($data['shiftType'])) {
            $shiftType = ShiftTypes::where('company_id', Auth::user()->company_id)
                ->where('id', $data['shiftType'])->first();
        } else {
            $shiftType = ShiftTypes::where('company_id', Auth::user()->company_id)
                ->where('id', $clocking['shift_type'])->first();
        }

        $clerk = Clerks::where('guid', $clocking['employee_guid'])->withTrashed()->first();

        if ($shiftType->timed == 1) {
            $clocking->in = Carbon::parse($data['in'], TimezoneHelper::getTimezone())->setTimezone('UTC');
            $clocking->out = Carbon::parse($data['out'], TimezoneHelper::getTimezone())->setTimezone('UTC');
        } else {
            $clocking->in = Carbon::parse($data['in'], TimezoneHelper::getTimezone())->setTime('12', '00')->setTimezone('UTC');
            $clocking->out = Carbon::parse($data['out'], TimezoneHelper::getTimezone())->setTime('13', '00')->setTimezone('UTC');
        }

        if (isset($data['jobRole'])) {
            $clocking->role_guid = $data['jobRole'] != 'none' ? $data['jobRole'] : null;
        }

        if (isset($data['shiftType'])) {
            $clocking->shift_type = $data['shiftType'] != 'none' ? $data['shiftType'] : null;
        }

        $hasClocking = Clocking::where('employee_guid', $clerk->guid)
            ->where('id', '!=', $clocking->id)
            ->where('is_rota', 1)
            ->whereBetween('in', [Carbon::parse($clocking->in)->subHour()->subMinutes(5), Carbon::parse($clocking->out)->subHour()->addMinutes(5)])
            ->count();

        if ($hasClocking > 0) {
            return response()->json(['status' => 'error', 'message' => 'A clocking record exists for this clerk at the specified time!'], 400);
        }

        $clocking = WagesHelper::setRate($clocking, $clerk);
        $clocking->save();

        $event = [
            'id' => $clocking->id,
            'resourceIds' => [$clerk->id],
            'title' => ucwords(strtolower((string) $clerk->full_name)) . ' (' . $clocking->hours_worked . ' Hours) ('.$this->currencySymbol.' ' . $clocking->total_pay .' )',
            'order' => 2,
            'description' => 'Hours Worked: ' . $clocking->hours_worked,
            'start' => Carbon::parse($clocking->in)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            'end' => Carbon::parse($clocking->out)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            "constraint" => [
                "resourceIds" => [ $clerk->id ] // constrain dragging to these
            ],
            'classNames' => 'shift',
            "backgroundColor" => "$shiftType->color"
        ];

        return response()->json(['status' => 'success', 'event' => $event], 200);
    }

    public function indexWeekly()
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $areas = AreaHelper::myAreas();

        $clerks = Clerks::where(function ($q) use ($areas) {
            $q->where('company_id', Auth::user()->company_id)->whereIn('area_guid', $areas)->where('ishidden', 0);
        })->orWhere(function ($q) {
            $q->where('company_id', Auth::user()->company_id)->whereNull('area_guid')->where('ishidden', 0);
        })->get();

        $sites = SiteHelper::MySites(Auth::user()->company_id);

        return view('modules.attendance.index-weekly-rota', ['clerks' => $clerks, 'sites' => $sites]);
    }

    public function loadForm($clerk)
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $clerk = Clerks::where('guid', $clerk)->first();
        $shiftTypes = ShiftTypes::where('company_id', Auth::user()->company_id)->get();
        $clerkRoles = ClerkRoles::where('employee_guid', $clerk->guid)->orderBy('isdefault', 'DESC')->get();

        $rolesCount = $clerkRoles->count();
        $typesCount = $shiftTypes->count();
        $days = [
            '0',
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
        ];

        return response()->json(['html' => view('modules.attendance.weekly-rota', ['clerk' => $clerk, 'days' => $days, 'shiftTypes' => $shiftTypes, 'clerkRoles' => $clerkRoles, 'typesCount' => $typesCount, 'rolesCount' => $rolesCount])->render()], 200);
    }

    public function weeklyCreate(Request $request)
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $clerk = $request->only(['clerk']);
        $site = $request->only(['site']);
        $days[0] = $request->only(['working_0','role_guid_0','shift_type_0','start_date_0','end_date_0']);
        $days[1] = $request->only(['working_1','role_guid_1','shift_type_1','start_date_1','end_date_1']);
        $days[2] = $request->only(['working_2','role_guid_2','shift_type_2','start_date_2','end_date_2']);
        $days[3] = $request->only(['working_3','role_guid_3','shift_type_3','start_date_3','end_date_3']);
        $days[4] = $request->only(['working_4','role_guid_4','shift_type_4','start_date_4','end_date_4']);
        $days[5] = $request->only(['working_5','role_guid_5','shift_type_5','start_date_5','end_date_5']);
        $days[6] = $request->only(['working_6','role_guid_6','shift_type_6','start_date_6','end_date_6']);

        $clerkDetails = Clerks::where('company_id', Auth::user()->company_id)
            ->where('guid', $clerk)->first();

        if ($clerk['clerk'] === null) {
            return response()->json(['status' => 'error', 'message' => 'Please select a clerk'], 400);
        } else {
            foreach ($days as $k => $day) {
                if ($day['shift_type_'.$k]) {
                    $shiftType = ShiftTypes::find($day['shift_type_'.$k]);

                    if ($shiftType->timed == 1) {
                        $in = Carbon::createFromFormat('d/m/Y H:i', $day['start_date_' . $k], TimezoneHelper::getTimezone())->setTimezone('UTC')->toDateTimeString();
                        $out = Carbon::createFromFormat('d/m/Y H:i', $day['end_date_' . $k], TimezoneHelper::getTimezone())->setTimezone('UTC')->toDateTimeString();
                    } else {
                        $in = Carbon::createFromFormat('d/m/Y H:i', $day['start_date_' . $k], TimezoneHelper::getTimezone())->setTimezone('UTC')->setTime('12', '00')->toDateTimeString();
                        $out = Carbon::createFromFormat('d/m/Y H:i', $day['end_date_' . $k], TimezoneHelper::getTimezone())->setTimezone('UTC')->setTime('13', '00')->toDateTimeString();
                    }
                }

                if (isset($day['working_' . $k])) {
                    $hasClocking = Clocking::where(function ($q) use ($clerkDetails, $in, $out) {
                        $q->where('employee_guid', $clerkDetails->guid)
                            ->whereNotNull('out')
                            ->where('is_rota', 1)
                            ->whereBetween('in', [Carbon::parse($in)->addSecond(), Carbon::parse($out)->subSecond()]);
                    })->orwhere(function ($q) use ($clerkDetails, $in, $out) {
                        $q->where('employee_guid', $clerkDetails->guid)
                            ->whereNotNull('out')
                            ->where('is_rota', 1)
                            ->whereBetween('out', [Carbon::parse($in)->addSecond(), Carbon::parse($out)->subSecond()]);
                    })
                        ->orwhere(function ($q) use ($clerkDetails, $in, $out) {
                            $q->where('employee_guid', $clerkDetails->guid)
                                ->whereNotNull('out')
                                ->where('is_rota', 1)
                                ->where('in', '<', Carbon::parse($in)->addSecond())
                                ->where('out', '>', Carbon::parse($out)->subSecond());
                        })
                        ->count();

                    if ($hasClocking > 0) {
                        return response()->json(['status' => 'error', 'message' => 'A rota shift already exists for '.$clerkDetails['full_name'].' on '.Carbon::now()->startOfWeek()->addDays($k)->format('l').' !'], 400);
                    } else {
                        $rotaShift = new Clocking();

                        $rotaShift->guid = Uuid::uuid4();

                        $rotaShift->employee_guid = $clerk['clerk'];
                        $rotaShift->site_num = $site['site'];
                        $rotaShift->role_guid = $day['role_guid_' . $k] ?? null;
                        $rotaShift->in = $in;
                        $rotaShift->out = $out;
                        $rotaShift->area_guid = $clerkDetails->area_guid;
                        $rotaShift->is_rota = 1;
                        $rotaShift->shift_type = $day['shift_type_' . $k] ?? null;

                        $rotaShift = WagesHelper::setRate($rotaShift, $clerkDetails);

                        $rotaShift->save();
                    }
                }
            }

            return response()->json(['status' => 'success', 'message' => 'Clerk weekly rota created', 'clerk' => $clerkDetails['full_name']], 200);
        }
    }

    public function add(Request $request)
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $defaultType = ShiftTypes::where('company_id', Auth::user()->company_id)
            ->where('isdefault', 1)->first();

        $employee = Clerks::where('id', (int) $request->input('id'))->withTrashed()->with('jobRoles')->first();

        $start = explode(':', (string) $employee->default_shift_start);
        $end = explode(':', (string) $employee->default_shift_end);

        if (is_array($request->input('date'))) {
            $clock_in = Carbon::parse($request->input('date')[0], TimezoneHelper::getTimezone())->setTimezone('UTC');
            $clock_out = Carbon::parse($request->input('date')[1], TimezoneHelper::getTimezone())->setTimezone('UTC');
        } else {
            $clock_in = Carbon::parse($request->input('date'), TimezoneHelper::getTimezone())->setTimezone('UTC');
            $clock_out = Carbon::parse($request->input('date'), TimezoneHelper::getTimezone())->setTimezone('UTC');
        }

        $dateTime1 = str_replace('Z', '', explode('T', (string) $request->input('date')[0])[1]);
        $dateTime2 = str_replace('Z', '', explode('T', (string) $request->input('date')[1])[1]);


        if (($employee->default_shift_start != null & $employee->default_shift_start != 0) !== 0) {
            if ($dateTime1 == '00:00:00' && $dateTime2 == '00:00:00') {
                $clock_in = Carbon::parse($request->input('date')[0], TimezoneHelper::getTimezone())->setTime($start[0], $start[1])->setTimezone('UTC');
                $clock_out = Carbon::parse($request->input('date')[1], TimezoneHelper::getTimezone())->setTime($end[0], $end[1])->subDay()->setTimezone('UTC');
            }

            if ($dateTime1 == '00:00:00' && $dateTime2 == '12:00:00') {
                $clock_in = Carbon::parse($request->input('date')[0], TimezoneHelper::getTimezone())->setTime($start[0], $start[1])->setTimezone('UTC');
                $clock_out = Carbon::parse($request->input('date')[1], TimezoneHelper::getTimezone())->setTime($end[0], $end[1])->subDay()->setTimezone('UTC');
            }

            if ($dateTime1 == '12:00:00' && $dateTime2 == '00:00:00') {
                $clock_in = Carbon::parse($request->input('date')[0], TimezoneHelper::getTimezone())->setTime($start[0], $start[1])->setTimezone('UTC');
                $clock_out = Carbon::parse($request->input('date')[1], TimezoneHelper::getTimezone())->setTime($end[0], $end[1])->subDay()->setTimezone('UTC');
            }
        }

        $clocking = new Clocking();
        $clocking->guid = Uuid::uuid4();

        $clocking->employee_guid = $employee->guid;
        $clocking->site_num = \Session::get('current_site');
        $clocking->in = $clock_in;
        $clocking->out = $clock_out;
        $clocking->area_guid = $employee->area_guid;
        $clocking->is_rota = 1;
        $clocking->shift_type = $defaultType->id;

        if (!empty($employee->jobRoles)) {
            $defaultRole = ClerkRoles::where('employee_guid', $employee->guid)
                ->where('isdefault', 1)->first();

            if ($defaultRole != null) {
                $clocking->role_guid = $defaultRole->guid;
            } elseif (!$employee->jobRoles->isEmpty()) {
                $clocking->role_guid = $employee->jobRoles[0]->guid;
            } else {
                $clocking->role_guid = null;
            }
        }

        $hasClocking = Clocking::where(function ($q) use ($employee, $clocking) {
            $q->where('employee_guid', $employee->guid)
                ->whereNotNull('out')
                ->where('is_rota', 1)
                ->whereBetween('in', [Carbon::parse($clocking->in)->addSecond(), Carbon::parse($clocking->out)->subSecond()]);
        })->orwhere(function ($q) use ($employee, $clocking) {
            $q->where('employee_guid', $employee->guid)
                ->whereNotNull('out')
                ->where('is_rota', 1)
                ->whereBetween('out', [Carbon::parse($clocking->in)->addSecond(), Carbon::parse($clocking->out)->subSecond()]);
        })
            ->orwhere(function ($q) use ($employee, $clocking) {
                $q->where('employee_guid', $employee->guid)
                    ->whereNotNull('out')
                    ->where('is_rota', 1)
                    ->where('in', '<', Carbon::parse($clocking->in)->addSecond())
                    ->where('out', '>', Carbon::parse($clocking->out)->subSecond());
            })
            ->count();

        if ($hasClocking > 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'A rota shift already exists for this clerk at the specified time. This could be at another site.'
            ], 400);
        }

        $clocking = WagesHelper::setRate($clocking, $employee);

        $clocking->save();

        $event = [
            'id' => $clocking->id,
            'resourceIds' => [$employee->id],
            'title' => $employee->full_name . ' (' . $clocking->hours_worked . ' Hours) ('.$this->currencySymbol.' ' . $clocking->total_pay .' )',
            'order' => 2,
            'description' => 'Hours Worked: ' . $clocking->hours_worked,
            'start' => Carbon::parse($clocking->in)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            'end' => Carbon::parse($clocking->out)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            "constraint" => [
                "resourceIds" => [ $employee->id ] // constrain dragging to these
            ]
        ];

        return  response()->json(['status' => 'ok', 'event' => $event], 200);
    }

    public function getActivity($id)
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $activity = ActivityRepository::whereIn('subject_type', ['NewbridgeWeb\Repositories\Clerks\ClerkClocking', 'NewbridgeWeb\Repositories\Clerks\Clocking'])
            ->where('subject_id', $id)
            ->orderBy('id', 'DESC')
            ->get()
            ->toArray();

        $timezone = TimezoneHelper::getTimezone();

        foreach ($activity as &$act) {
            foreach (['activity_data_old', 'activity_data_new'] as $field) {
                if (!empty($act[$field]) && (is_array($act[$field]) || is_object($act[$field]))) {
                    $data = (array) $act[$field];
                    foreach ($data as $k => $v) {
                        if (in_array($k, ['in', 'out', 'created_at', 'updated_at']) && !empty($v)) {
                            $data[$k] = Carbon::parse($v, 'UTC')
                                ->setTimezone($timezone)
                                ->format('Y-m-d H:i:s');
                        }
                    }
                    $act[$field] = $data;
                }
            }
        }

        return view('modules.attendance.activity_modal', ['activity' => $activity]);
    }

    public function newIndex(Request $request)
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $wages_show = Auth::user()->ability('newbridge,owner,reseller', 'clerk_wages');
        $date = $request->input('date');
        $company_id = Auth::user()->company_id;

        $bgColor = '';

        $myAreas = AreaHelper::myAreas();

        $sites = Sites::where('company_id', Auth::user()->company_id)->get();

        $areas = ClerkAreas::where('company_id', Auth::user()->company_id)->get();

        $clerks = Clerks::with(['clockings' => function ($q) use ($company_id) {
            $q->whereNotNull('out')
                ->where('site_num', \Session::get('current_site'))
                ->with('type');
            $q->where('is_rota', 1);
        }])
            ->whereIn('area_guid', $myAreas)
            ->where('ishidden', 0)
            ->where('company_id', $company_id)
            ->whereIn('site_num', [0,\Session::get('current_site')])
            ->with('area')
            ->get();

        $resources = [];
        $events = [];

        foreach ($clerks as $clerk) {
            $resources[] = [
                'id' => $clerk->id,
                'groupId' => $clerk->area_guid != null && $clerk->area->area_name != '' ? $clerk->area->area_name : 'All Areas',
                'title' => ucwords(strtolower((string) $clerk->full_name))
            ];

            foreach ($clerk->clockings as $clocking) {
                $bgColor = $clocking->type != null ? $clocking->type->color : '#A9A9A9';

                if ($wages_show) {
                    $title = ucwords(strtolower((string) $clerk->full_name)) . ' (' . $clocking->hours_worked . ' Hours) ('.$this->currencySymbol.' ' . $clocking->total_pay .' )';
                } else {
                    $title = ucwords(strtolower((string) $clerk->full_name)) . ' (' . $clocking->hours_worked . ' Hours)';
                }

                $events[] = [
                    'id' => $clocking->id,
                    'resourceId' => $clerk->id,
                    'title' => $title,
                    'order' => 2,
                    'description' => 'Hours Worked: ' . $clocking->hours_worked,
                    'start' => Carbon::parse($clocking->in)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
                    'end' => Carbon::parse($clocking->out)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
                    'backgroundColor' => "$bgColor",
                    "constraint" => [
                        "resourceIds" => [ $clerk->id ] // constrain dragging to these
                    ],

                ];
            }
        }

        $start = Carbon::now()->setTime(8, 0);
        $end = Carbon::now()->setTime(17, 0);



        return view('modules.attendance.rota-calendar', ['clerks' => $clerks, 'areas' => $areas, 'date' => $date, 'resources' => $resources, 'events' => $events, 'start' => $start, 'end' => $end, 'sites' => $sites, 'bgColor' => $bgColor]);
    }

    public function splitShift(Request $request)
    {
        $this->currencySymbol = CurrencyHelper::symbol(Auth::user()->company_id);

        $input = $request->input();

        $id = $input['id'];
        $splitTime = $input['split_time'];
        $splitLength = $input['split_length'];

        $clocking1 = Clocking::find($id);
        $clerk = Clerks::where('guid', $clocking1->employee_guid)->first();
        $originalOut = Carbon::parse($clocking1->out, TimezoneHelper::getTimezone())->setTimezone('UTC');

        $splitTime = Carbon::createFromFormat('d/m/Y H:i', $splitTime);

        $clocking1->out = Carbon::parse($splitTime, TimezoneHelper::getTimezone())->setTimezone('UTC');

        $clocking1 = WagesHelper::setRate($clocking1, $clerk);
        $clocking1->save();

        $newStartTime = Carbon::parse($clocking1->out, TimezoneHelper::getTimezone())->setTimezone('UTC')->addMinutes($splitLength);
        $clocking2 = $clocking1->replicate();
        $clocking2->in = $newStartTime;
        $clocking2->out = $originalOut;
        $clocking2 = WagesHelper::setRate($clocking2, $clerk);
        $clocking2->push();

        $events = [];

        if (Auth::user()->ability('newbridge,owner,reseller', 'clerk_wages')) {
            $title1 = ucwords(strtolower((string) $clerk->full_name)) . ' (' . $clocking1->hours_worked . ' Hours) ('.$this->currencySymbol.' ' . $clocking1->total_pay .' )';
            $title2 = ucwords(strtolower((string) $clerk->full_name)) . ' (' . $clocking2->hours_worked . ' Hours) ('.$this->currencySymbol.' ' . $clocking2->total_pay .' )';
        } else {
            $title1 = ucwords(strtolower((string) $clerk->full_name)) . ' (' . $clocking1->hours_worked . ' Hours)';
            $title2 = ucwords(strtolower((string) $clerk->full_name)) . ' (' . $clocking2->hours_worked . ' Hours)';
        }

        $events[] = [
            'id' => $clocking1->id,
            'resourceId' => $clerk->id,
            'title' => $title1,
            'description' => 'Hours: ' . $clocking1->hours_worked,
            'start' => Carbon::parse($clocking1->in)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            'end' => Carbon::parse($clocking1->out)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            "constraint" => [
                "resourceIds" => [ $clerk->id ] // constrain dragging to these
            ],
            'classNames' => 'rota'
        ];

        $events[] = [
            'id' => $clocking2->id,
            'resourceId' => $clerk->id,
            'title' => $title2,
            'description' => 'Hours: ' . $clocking2->hours_worked,
            'start' => Carbon::parse($clocking2->in)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            'end' => Carbon::parse($clocking2->out)->setTimezone(TimezoneHelper::getTimezone())->format('Y-m-d H:i:s'),
            "constraint" => [
                "resourceIds" => [ $clerk->id ] // constrain dragging to these
            ],
            'classNames' => 'rota'
        ];

        return response()->json(['events' => $events], 200);
    }
}
