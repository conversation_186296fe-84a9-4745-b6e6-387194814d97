<?php

namespace NewbridgeWeb\Http\Controllers\Clerks;

use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\AreaHelper;
use NewbridgeWeb\Repositories\AccessLevels;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Clerks\ClerkPermissionGroups;
use NewbridgeWeb\Repositories\Clerks\ClerkRoles;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\CompanyClerkRoles;
use NewbridgeWeb\Repositories\PosButtonLink;
use NewbridgeWeb\Repositories\Sites;
use Ramsey\Uuid\Uuid;
use Ya<PERSON>ra\Datatables\Datatables;

class ClerkController extends Controller
{
    /**
     * @return View
     */
    public function index(): View
    {
        $screens = Commands::getAllCompanyJsonGuid([1]);
        $areas = ClerkAreas::whereCompanyId(Auth::user()->company_id)->get();
        $accessLevels = AccessLevels::orderBy('order', 'ASC')->get();
        $permissionGroups = ClerkPermissionGroups::getAllGuidsForDatatableSelect();

        return view('modules.clerks.datatables.table', compact('screens', 'areas', 'accessLevels', 'permissionGroups'));
    }

    /**
     * @return View
     */
    public function getCreate(): View
    {
        $screens = Commands::getAllCompanyJsonGuid([1]);
        $areas = ClerkAreas::where('company_id', Auth::user()->company_id)->get();
        $roles = CompanyClerkRoles::where('company_id', Auth::user()->company_id)->whereNotIn('guid', ["00000000-0000-0000-0000-000000000000"])->get();

        $permissionGroups = ClerkPermissionGroups::where('company_id', Auth::user()->company_id)->get();
        $accessLevels = AccessLevels::orderBy('order', 'ASC')->get();
        $restrictedPages = Commands::where('company_id', Auth::user()->company_id)
            ->whereIn('site_num', [0, \Session::get('current_site')])
            ->whereIn('Command', [1, 117])
            ->where('RestrictedCommand', '!=', 0)
            ->get();

        return view('modules.clerks.create', compact('screens', 'areas', 'accessLevels', 'restrictedPages', 'roles', 'permissionGroups'));
    }

    /**
     * @param int $id
     * @return View
     */
    public function getEdit(int $id): View
    {
        $screens = Commands::whereCompanyId(Auth::user()->company_id)->where('RestrictedCommand', 0);
        if (Auth::user()->company->site_specific_clerks == 1) {
            $screens = $screens->where('site_num', \Session::get('current_site'));
        }
        $screens = $screens->get();

        $permissionGroups = ClerkPermissionGroups::where('company_id', Auth::user()->company_id)->get();
        $areas = ClerkAreas::where('company_id', Auth::user()->company_id)->get();
        $accessLevels = AccessLevels::orderBy('order', 'ASC')->get();
        $clerk = Clerks::with('actions')->find($id);

        if ($clerk == null) {
            abort(404);
        }

        $roles = CompanyClerkRoles::where('company_id', Auth::user()->company_id)->whereNotIn('guid', ["00000000-0000-0000-0000-000000000000"])->get();
        $attachedRoles = ClerkRoles::where('employee_guid', $clerk->guid)->get();

        if (!$attachedRoles->isEmpty()) {
            $attachedRoles = $attachedRoles->keyBy('role_id')->toArray();
        }

        $commands = Commands::select('id', 'displayname', 'CommandUID', 'Command')->whereIn('company_id', [0])->whereNotIn('Command', explode(',', (string) config('newbrdge.exclude_commands')))->get();

        $sites = Sites::where('company_id', Auth::user()->company_id)->get();
        $intCommand['pages'] = [];

        foreach ($sites as $site) {
            $pages = Commands::select('id', 'displayname', 'CommandUID')->where('company_id', Auth::user()->company_id)->where('site_num', $site->site_num)->whereIn('Command', [1])->get();
            if (!empty($pages)) {
                $pages = $pages->toArray();
            }
            $intCommand['pages'][$site->site_name] = $pages;
        }

        if (!empty($commands)) {
            $commands = $commands->toArray();
        }

        $intCommand['commands'] = $commands;

        $restrictedPages = Commands::where('company_id', Auth::user()->company_id)
            ->whereIn('site_num', [0, \Session::get('current_site')])
            ->whereIn('Command', [1, 117])
            ->where('RestrictedCommand', '!=', 0)
            ->get();

        return view('modules.clerks.edit', compact('clerk', 'screens', 'areas', 'accessLevels', 'restrictedPages', 'attachedRoles', 'roles', 'intCommand', 'permissionGroups'));
    }

    public function addLink(Request $request)
    {
        $input = $request->input();
        $clerk = $input['clerk'];


        $command = Commands::where('CommandUID', $input['command'])->first();
        $clerk = Clerks::find($clerk);

        if ($command !== null) {
            $link = new PosButtonLink();
            $link->company_id = Auth::user()->company_id;
            $link->button_guid = $clerk['guid'];
            $link->commandUID = $input['command'];
            $link->position = $input['position'];
            $link->command_type = 1;
            //            $link->display_name = $command['DisplayName'];
            $link->type = 1;
            $link->save();

            $clerk = Clerks::with('lnk_buttonlinks', 'ClkClerkRoles')->where('guid', $clerk['guid'])->first();
            \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));

            return response()->json(['status' => 'success', 'link' => $link], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Unable to proceess action'], 400);
        }
    }

    public function removeLink($id)
    {
        $link = PosButtonLink::find($id);
        $link->delete();

        return response()->json(['status' => 'success'], 200);
    }

    public function reOrderLinks(Request $request)
    {
        $data = $request->input('data');
        $clerkGuid = null;

        foreach ($data as $link) {
            $l = PosButtonLink::find($link['id']);
            $l->position = $link['position'];
            $l->save();
            $clerkGuid = $l->button_guid;
        }

        $clerk = Clerks::with(['lnk_buttonlinks'])->where('guid', $clerkGuid)->first();
        \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        if (Auth::user()->hasRole(['newbridge', 'reseller'])) {
            $model = Clerks::with(['screen', 'permission_group'])
                ->where('company_id', Auth::user()->company_id)
                ->whereIn('site_num', [0, \Session::get('current_site')])
                ->whereIn('ishidden', [1,0]);
        } else {
            $areas = AreaHelper::myAreas();

            $model = Clerks::with(['screen', 'permission_group'])->where(function ($q) use ($areas) {
                $q->where('company_id', Auth::user()->company_id)->whereIn('area_guid', $areas)->where('ishidden', 0)
                    ->whereIn('site_num', [0, \Session::get('current_site')]);
            })->orWhere(function ($q) {
                $q->where('company_id', Auth::user()->company_id)->whereNull('area_guid')->where('ishidden', 0)
                    ->whereIn('site_num', [0, \Session::get('current_site')]);
            });
        }

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $input = $request->except('accesslevel', 'pages', 'rolerate', 'hasrole', 'isdefault');
        $pageRestrictions = $request->input('pages');

        $roles = $request->input('hasrole');
        $rates = $request->input('rolerate');
        $default = $request->input('isdefault');

        $alreadyInUse = Clerks::where('company_id', Auth::user()->company_id)
            ->whereIn('site_num', [0, \Session::get('current_site')])
            ->where('employee_no', $request->input('employee_no'))
            ->first();

        if ($alreadyInUse != null) {
            return response()->json(['status' => 'error', 'message' => 'Sorry that employee number is already in use please choose another'], 400);
        }

        $alreadyInUse2 = Clerks::where('company_id', Auth::user()->company_id)
            ->whereIn('site_num', [0, \Session::get('current_site')])
            ->where('pin', $request->input('pin'))
            ->first();

        if ($alreadyInUse2 != null) {
            return response()->json(['status' => 'error', 'message' => 'Sorry that PIN number is already in use please choose another'], 400);
        }

        $pages = 0;
        if (!empty($pageRestrictions)) {
            foreach ($pageRestrictions as $page) {
                $pages += $page;
            }
        }

        foreach ($input as $k => $v) {
            $input[$k] = $v == 'on' ? 1 : $v;
        }

        $clerk = new Clerks();
        $clerk->navigationlevel = $pages;
        $clerk->company_id = Auth::user()->company_id;
        $clerk->site_num = Auth::user()->company->site_specific_clerks == 1 ? \Session::get('current_site') : 0;
        $clerk->fill($input);

        $accesslevel = 0;
        if(!empty($input['permission_group_guid'])) {
            $permissionGroup = ClerkPermissionGroups::where('guid', $input['permission_group_guid'])->first();
            $accesslevel = $permissionGroup->access_level;
        }
        $clerk->guid = Uuid::uuid4();
        $clerk->received_from = 0;
        $clerk->ishidden = 0;
        $clerk->received_at = Carbon::parse('1979-01-01 00:00:00');
        $clerk->accesslevel = $accesslevel;

        if ($clerk->salary == 1) {
            $clerk->rate = (float) ($clerk->rate / 365);
        }
        $clerk->save();


        // Role Error Checking

        if (!empty($roles)) {
            $error = $this->checkRoleErrors($roles, $rates, $default, $clerk->salary);
            if ($error != null) {
                return response()->json(['status' => 'error', 'message' => $error], 400);
            }

            foreach ($roles as $k => $role) {
                $role = CompanyClerkRoles::find($k);

                $newRole = new ClerkRoles();
                $newRole->company_id = $clerk->company_id;
                $newRole->displayname = $role->displayname;
                $newRole->employee_guid = $clerk->guid;
                $newRole->guid = $role->guid;
                $newRole->role_id = $role->id;
                $newRole->rate = $clerk->salary != 1 ? $rates[$k] : 0;
                $newRole->isdefault = $default == $role->id ? 1 : 0;
                $newRole->save();
            }
        }

        $clerk = Clerks::with('lnk_buttonlinks', 'ClkClerkRoles')->find($clerk->id);
        $clerk->displayname = $clerk->full_name;

        \Event::dispatch(new UpdaterInsertCrudEvent($clerk));

        return response()->json(['status' => 'ok'], 200);
    }

    public function copyClerkWithoutRolesOrWages($id)
    {
        $clerk = Clerks::find($id);

        $newData = [
            'full_name' => '',
            'short_name' => '',
            'pin' => '',
            'employee_no' => '',
            'biometricdata' => null,
            'hourly_rate' => 0,
            'salary' => 0,
            'rate' => 0,
            'guid' => Uuid::uuid4()
        ];

        $copy = $clerk->replicate();
        $copy->fill($newData);
        $copy->save();

        return redirect('/clerks/edit/'.$copy->id);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->except('int_commands');

        foreach ($input as $k => $v) {
            $input[$k] = $v == 'on' ? 1 : $v;
        }

        foreach ($input['data'] as $k => $v) {
            foreach ($v as $vk => $kv) {
                if ($vk == 'pin') {
                    $alreadyInUse = Clerks::where('company_id', Auth::user()->company_id)
                        ->whereIn('site_num', [0, \Session::get('current_site')])
                        ->where('pin', $kv)
                        ->where('id', '!=', $k)
                        ->first();

                    if ($alreadyInUse != null) {
                        return response()->json(['data' => '', 'error' => 'Please use a different pin number'], 400);
                    }
                }
                if ($vk == 'permission_group_guid' && $kv != '') {
                    $clerk = Clerks::find($k);
                    $permissionGroup = ClerkPermissionGroups::whereGuid($kv)->first();
                    $clerk->accesslevel = $permissionGroup->access_level;
                    $clerk->save();
                } elseif($vk == 'permission_group_guid' && $kv == '') {
                    $clerk = Clerks::find($k);
                    $clerk->accesslevel = 0;
                    $clerk->save();
                }
                if ($vk == 'employee_no') {
                    $alreadyInUse2 = Clerks::where('company_id', Auth::user()->company_id)
                        ->whereIn('site_num', [0, \Session::get('current_site')])
                        ->where('employee_no', $kv)
                        ->where('id', '!=', $k)
                        ->first();

                    if ($alreadyInUse2 != null) {
                        return response()->json(['data' => '', 'error' => 'Please use a different employee number'], 400);
                    }
                }
            }

            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);
            Clerks::with('lnk_buttonlinks')->with('ClkClerkRoles')->where('id', $k)->get()->each(function($model) use ($updates){
                $model->fill($updates);
                $model->save();
                $model->displayname = $model->full_name;
                \Event::dispatch(new UpdaterUpdateCrudEvent($model));
            });
        }

        return $this->data($dataTables);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function editSingle(Request $request)
    {
        $input = $request->except('id', 'accesslevel', 'pages', 'commands', 'rolerate', 'hasrole', 'isdefault', 'int_commands');
        $id = $request->input('id');
        $accesslevels = $request->input('accesslevel');
        $pageRestrictions = $request->input('pages');

        $roles = $request->input('hasrole');
        $rates = $request->input('rolerate');
        $default = $request->input('isdefault');

        $alreadyInUse = Clerks::where('company_id', Auth::user()->company_id)
            ->where('employee_no', $request->input('employee_no'))
            ->whereIn('site_num', [0, \Session::get('current_site')])
            ->where('id', '!=', $id)
            ->first();

        if ($alreadyInUse != null) {
            return response()->json(['status' => 'error', 'message' => 'Sorry that employee number is already in use please choose another'], 400);
        }

        $alreadyInUse2 = Clerks::where('company_id', Auth::user()->company_id)
            ->where('pin', $request->input('pin'))
            ->whereIn('site_num', [0, \Session::get('current_site')])
            ->where('id', '!=', $id)
            ->first();

        if ($alreadyInUse2 != null) {
            return response()->json(['status' => 'error', 'message' => 'Sorry that PIN number is already in use please choose another'], 400);
        }

        $pages = 0;

        if (!empty($pageRestrictions)) {
            foreach ($pageRestrictions as $page) {
                $pages += $page;
            }
        }

        foreach ($input as $k => $v) {
            $input[$k] = $v == 'on' ? 1 : $v;
        }

        $clerk = Clerks::find($id);
        $clerk->received_from = 0;
        $clerk->navigationlevel = $pages;

        if(!empty($input['permission_group_guid'])) {
            $permissionGroup = ClerkPermissionGroups::where('guid', $input['permission_group_guid'])->first();
            $accesslevel = $permissionGroup->access_level;
            $clerk->accesslevel = $accesslevel;
        }

        $clerk->fill($input);

        if ($clerk->salary == 1) {
            $clerk->rate /= 365;
        }

        ClerkRoles::where('employee_guid', $clerk->guid)->delete();

        if (!empty($roles)) {
            $error = $this->checkRoleErrors($roles, $rates, $default, $clerk->salary);
            if ($error != null) {
                return response()->json(['status' => 'error', 'message' => $error], 400);
            }

            foreach ($roles as $k => $role) {
                $role = CompanyClerkRoles::find($k);

                $newRole = new ClerkRoles();
                $newRole->company_id = $clerk->company_id;
                $newRole->displayname = $role->displayname;
                $newRole->employee_guid = $clerk->guid;
                $newRole->guid = $role->guid;
                $newRole->role_id = $role->id;
                $newRole->rate = $clerk->salary != 1 ? $rates[$k] : 0;
                $newRole->isdefault = $default == $role->id ? 1 : 0;
                $newRole->save();
            }
        }


        $clerk->save();

        $clerk = Clerks::with('lnk_buttonlinks', 'ClkClerkRoles')->find($clerk->id);

        $clerk->displayname = $clerk->full_name;

        \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));

        return response()->json(['status' => 'ok', 'data' => $clerk], 200);
    }

    // delete one modifier group based on input('id')
    public function delete(Request $request, $id)
    {
        $clerk = Clerks::with('lnk_buttonlinks', 'ClkClerkRoles')->find($id);
        $clerk->delete();

        \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));

        return response()->json(['status' => 'ok']);
    }

    public function reQueueAll($company_id): string
    {
        $clerks = Clerks::with('lnk_buttonlinks', 'ClkClerkRoles')->where('company_id', $company_id)->get();

        foreach ($clerks as $clerk) {
            $clerk->received_from = 0;
            $clerk->save();

            \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));
        }

        return 'done';
    }

    private function checkRoleErrors($roles, $rates, $default, $salary)
    {
        if ($default == '' && !empty($roles)) {
            return 'No default role selected, please choose a default.';
        }

        if ($salary != 1) {
            foreach ($roles as $k => $role) {
                if ($rates == null || $rates[$k] == '') {
                    return 'A role has been selected without a rate, please enter a rate of 0 or more.';
                }
            }
        }
    }
}
