<?php

namespace NewbridgeWeb\Http\Controllers\Clerks;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Requests\DepartmentDatatablesRequest;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Clerks\ClerkRoles;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\CompanyClerkRoles;
use <PERSON>\Uuid\Uuid;
use Yajra\Datatables\Datatables;

class ClerkRolesController extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        if (!Auth::user()->hasRole('newbridge')) {
            $roles = CompanyClerkRoles::where('company_id', Auth::user()->company_id)
                ->where('guid', '!=', '00000000-0000-0000-0000-000000000000')
                ->get();
        } else {
            $roles = CompanyClerkRoles::where('company_id', Auth::user()->company_id)
                ->get();
        }

        $areas = ClerkAreas::where('company_id', Auth::user()->company_id)->get();



        return response()->view('modules.clerk-roles.datatables.table', ['roles' => $roles, 'areas' => $areas]);
    }

    /**
     * @param DepartmentDatatablesRequest $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit(Request $request, Datatables $dataTables)
    {
        $input = $request->input();

        foreach ($input['data'] as $k => $v) {
            $updates = ['received_from' => 0];
            $updates = array_merge($updates, $v);
            CompanyClerkRoles::where('id', $k)->get()->each(function($model) use ($updates){
                $model->fill($updates);
                $model->save();
                \Event::dispatch(new UpdaterUpdateCrudEvent($model));

                $clerkRoles = ClerkRoles::where('guid', $model->guid)->get();

                foreach ($clerkRoles as $role) {
                    $role->displayname = $model->displayname;
                    $role->save();

                    $clerk = Clerks::where('guid', $role->employee_guid)->with('lnk_buttonlinks', 'ClkClerkRoles')->first();

                    \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));
                }
            });
        }

        return $this->getData($dataTables);
    }

    /**
     * @param Request $request
     * @param Datatables $datatables
     * @return \Illuminate\Http\JsonResponse
     */
    public function editById(Request $request, Datatables $dataTables)
    {
        $input = $request->except('id');
        $id = $request->input('id');

        $area = CompanyClerkRoles::find($id);
        $area->fill($input);
        $area->received_from = 0;
        $area->save();

        $clekroles = ClerkRoles::where('guid', $area->guid)->get();

        foreach ($clekroles as $role) {
            $role->displayname = $area->displayname;
            $role->save();

            $clerk = Clerks::where('guid', $role->employee_guid)->with('lnk_buttonlinks', 'ClkClerkRoles')->first();

            \Event::dispatch(new UpdaterUpdateCrudEvent($clerk));
        }


        $area = CompanyClerkRoles::find($area->id);

        \Event::dispatch(new UpdaterUpdateCrudEvent($area));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function add(Request $request)
    {
        $input = $request->input();

        $area = new CompanyClerkRoles();
        $area->fill($input);
        $area->company_id = Auth::user()->company_id;
        $area->guid = Uuid::uuid4();
        $area->received_at = Carbon::parse('1979-01-01 00:00:00');
        $area->received_from = 0;
        $area->save();

        $area = CompanyClerkRoles::find($area->id);

        \Event::dispatch(new UpdaterInsertCrudEvent($area));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Request $request
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, Datatables $dataTables, $id)
    {
        $area = CompanyClerkRoles::find($id);

        $clerks = Clerks::where('area_guid', $area->guid)->get();

        foreach ($clerks as $c) {
            $c->area_guid = null;
            $c->save();
        }

        $area->forceDelete();

        \Event::dispatch(new UpdaterUpdateCrudEvent($area));

        return response()->json(['status' => 'ok'], 200);
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function getData(Datatables $dataTables)
    {
        $model = CompanyClerkRoles::where('company_id', Auth::user()->company_id)->where('guid', '!=', '00000000-0000-0000-0000-000000000000')->with('area');

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param Datatables $dataTables
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSingle(Datatables $dataTables, $id)
    {
        $model = CompanyClerkRoles::forCurrentCompanyById($id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    /**
     * @param null $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function editModal($id = null)
    {
        $role = CompanyClerkRoles::find($id);

        $areas = ClerkAreas::where('company_id', Auth::user()->company_id)->get();

        return view('modules.clerk-roles.includes.edit-modal', ['role' => $role, 'areas' => $areas]);
    }
}
