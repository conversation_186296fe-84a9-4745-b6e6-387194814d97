<?php

namespace NewbridgeWeb\Http\Controllers\Reports\Customers;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Reports\ReportAbstractController;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\CustomerGroup;
use NewbridgeWeb\Repositories\CustomerTransactions;
use NewbridgeWeb\Repositories\PosTransaction;

class LoyaltyPointsStatements extends ReportAbstractController
{
    public $summary = [];
    public $customer;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Customer Points Statement" => [
                "columns" => [
                    "" => [null, ['first_name', 'last_name', 'membership_no'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Type" => [null, 'type', 'string'],
                        "Date" => [null, 'date', 'date'],
                        "Transaction ID" => [null, 'transaction', 'string'],
                        "Points Amount" => [null, 'points', 'string']
                    ],
                    "data" => [
                        'column_count' => 3,
                        'resultset' => 'points',
                        'summary_result' => null
                    ]
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'customers',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->customer = $report_data['customer'];

        return $this->customers();
    }

    /**
     * Cashback Report Report
     */
    private function customers()
    {
        $query = Customer::where('company_id', $this->company_id)
            ->where('customer_type', 0)
            ->with(['points' => function ($q) {
                $q->whereBetween('created_at', [$this->start, $this->end])
                    ->where('points', '!=', 0);
            }])
            ->whereHas('points', function ($q) {
                $q->whereBetween('created_at', [$this->start, $this->end])
                    ->where('points', '!=', 0);
            });
        if ($this->customer != null) {
            $query = $query->where('guid', $this->customer['guid']);
        }

        $query = $query->get();

        $results = [
            'customers' => []
        ];

        $currencySymbol = CurrencyHelper::symbol($this->company_id);

        foreach ($query as $q) {
            $scheme = CustomerGroup::where('guid', $q->group_guid)->withTrashed()->first();
            $opening = CustomerTransactions::where('customer_guid', $q->guid)
                ->where('created_at', '<=', $this->start)->sum('points');
            $closing = CustomerTransactions::where('customer_guid', $q->guid)
                ->where('created_at', '<=', $this->end)->sum('points');

            $results['customers'][$q->guid] =
                [
                    'id' => $q->id,
                    'first_name' => $q->first_name,
                    'last_name' => $q->last_name,
                    'membership_no' => $q->membership_no,
                    'points' => []
                ];

            $results['customers'][$q->guid]['points'][] = [
                'id' => $q->id,
                'type' => 'Opening Balance',
                'date' => $this->start,
                'transaction' => '',
                'points' => $currencySymbol.$opening * $scheme->points_spending_value
            ];

            foreach ($q->points as $point) {
                $tr = PosTransaction::where('reference', $point->transaction_reference)
                    ->orWhere('id', $point->transaction_id)->first();

                $results['customers'][$q->guid]['points'][] = [
                    'id' => $point->id,
                    'type' => $tr != null ? 'Transaction' : 'Manual Adjustment ('.$point->reason_text.')',
                    'date' => $point->created_at,
                    'transaction' => $tr != null ? $tr->id : '',
                    'points' => $currencySymbol. $point->points * $scheme->points_spending_value
                ];
            }

            $results['customers'][$q->guid]['points'][] = [
                'id' => $q->id,
                'type' => 'Closing Balance',
                'date' => $this->end,
                'transaction' => '',
                'points' => $currencySymbol.$closing * $scheme->points_spending_value
            ];
        }

        return $results;
    }
}
