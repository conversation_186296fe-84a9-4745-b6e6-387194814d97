<?php

namespace NewbridgeWeb\Http\Controllers\Reports\Customers;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Reports\ReportAbstractController;
use NewbridgeWeb\Repositories\Giftcards;

class GiftCardExpiryReport extends ReportAbstractController
{
    public $summary = [];

    public function setTables($var = null): array
    {
        $this->tables = [
            "Expired Giftcards" => [
                "columns" => [
                    "Gift Card Number" => [null , 'membership_no', 'string'],
                    "Remaining Balance" => [null, 'balance', 'decimal'],
                    "Expiry Date" => [null, 'expires', 'string']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'giftcards',
                    'summary_result' => null
                ]

            ],
            "Summary" => [
                "columns" => [
                    "Total Balance" => [null , 'total_balance', 'decimal'],
                    "Count" => [null, 'count', 'int']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'giftcard_summary',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results['giftcards'] = $this->giftcards();
        $results['giftcard_summary'] = $this->summary;

        return $results;
    }

    /**
     * Cashback Report Report
     */
    private function giftcards()
    {
        $query = Giftcards::where('company_id', $this->company_id)
            ->where('customer_type', 1)
            ->whereNotNull('expires')
            ->whereBetween('expires', [$this->start, $this->end])
            ->get();

        $this->summary[] = [
            'total_balance' => $query->sum('balance'),
            'count' => $query->count()
        ];

        return $query->toArray();
    }
}
