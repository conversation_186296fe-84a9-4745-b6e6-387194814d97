<?php

namespace NewbridgeWeb\Http\Controllers\Reports\Customers;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Reports\ReportAbstractController;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;

class CustomerTransactionStatement extends ReportAbstractController
{
    public $summary = [];
    public $customer;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Customer Transaction Statement" => [
                "columns" => [
                    "" => [null, ['first_name', 'last_name', 'membership_no'], 'title']
                ],
                "nested" => [
                    "columns" => [
                        "Product" => [null, 'displayname', 'string'],
                        "Quantity" => [null, 'quantity', 'int'],
                        "Total" => [null, 'sum_total', 'decimal']
                    ],
                    "data" => [
                        'column_count' => 3,
                        'resultset' => 'transactions',
                        'summary_result' => "summary"
                    ],
                    "summary" => [
                        "blank" => [null, 'blank', 'blank'],
                        "Total" => [null, 'total', 'decimal']
                    ]
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'customers',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->customer = $report_data['customer'];
        return $this->customers();
    }

    private function customers(): array
    {
        $query = Customer::where('company_id', $this->company_id)
            ->where('customer_type', 0);

        if ($this->customer != null) {
            $query = $query->where('guid', $this->customer['guid']);
        }

        $query = $query->get();

        $results = [
            'customers' => [],
        ];

        foreach ($query as $q) {

            $transactions = PosTransaction::where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->where('membership_no', $q->membership_no)
                ->get()
                ->pluck('id');

            $productSales = PosTransactionDetail::selectRaw('id, trans_id, product_guid, displayname, sum(net_value) as sum_total, finalised_date, company_id, site_num, command, command_type, count(*) as quantity')
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereIn('trans_id', $transactions)
                ->where('command', 3)
                ->where('command_type', '!=', 5)
                ->groupBy('product_guid')
                ->get();

            if(!$productSales->isEmpty()) {
                $productSales = $productSales->toArray();
                $results['customers'][$q->membership_no] = $q->toArray();
                $results['customers'][$q->membership_no]['summary'] = ['total' => 0];

                $results['customers'][$q->membership_no]['summary']['blank'] = '';

                foreach($productSales as $sale) {
                    $results['customers'][$q->membership_no]['transactions'][] = $sale;
                    $results['customers'][$q->membership_no]['summary']['total'] += $sale['sum_total'];
                }
            }

        }

        return $results;
    }
}
