<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CompanyLinks;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\Promotions;
use NewbridgeWeb\Repositories\Sites;
use PDF;

class DiscountReport extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->tables = [
            "Discount Report" => [
                "columns" => [
                    "Discount" => [null, 'name', 'string'],
                    "# of Products Discounted" => [null, 'count', 'int'],
                    "Sales Value" => [null, 'value', 'decimal']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'discounts',
                    'summary_result' => null
                ]
            ],
            "Promotion Report" => [
                "columns" => [
                    "Promotion" => [null, 'name', 'string'],
                    "# of Products Discounted" => [null, 'count', 'int'],
                    "Sales Value" => [null, 'value', 'decimal']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'promotions',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results['discounts'] = $this->discounts();
        $results['promotions'] = $this->promotions();

        return $results;
    }

    public function discounts()
    {
        $companies = CompanyLinks::links($this->company_id);

        $query = Discounts::with(['transactions' => function ($q) {
            $q->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereNotIn('command_type', [5,6])
                ->whereIn('site_num', $this->site_num)
                ->where('company_id', $this->company_id);
            if ($this->terminal != '') {
                $q->whereIn('terminal_num', $this->terminal);
            }
        }])
            ->whereIn('company_id', $companies)
            ->withTrashed()
            ->get();

        $results = [];

        foreach ($query as $q) {
            if ($q->transactions->count() > 0) {
                $results[] = [
                    'name' => $q->displayname,
                    'count' => $q->transactions->count(),
                    'value' => ($q->transactions->sum('discount_value') * -1)
                ];
            }
        }

        return $results;
    }

    public function promotions()
    {
        $companies = CompanyLinks::links($this->company_id);

        $query = Promotions::with(['transactions' => function ($q) {
            $q->whereBetween('finalised_date', [$this->start, $this->end])
                ->where('command_type', '!=', 5)
                ->where('command', '!=', 60)
                ->whereIn('site_num', $this->site_num)
                ->where('company_id', $this->company_id);
            if ($this->terminal != '') {
                $q->whereIn('terminal_num', $this->terminal);
            }
        }])
            ->whereIn('company_id', $companies)
            ->withTrashed()
            ->get();

        $results = [];

        foreach ($query as $q) {
            if ($q->transactions->count() > 0) {
                $results[] = [
                    'name' => $q->displayname,
                    'count' => $q->transactions->count(),
                    'value' => ($q->transactions->sum('discount_value') * -1)
                ];
            }
        }

        return $results;
    }
}
