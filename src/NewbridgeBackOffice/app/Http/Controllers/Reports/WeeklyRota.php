<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Clerks\Clocking;
use NewbridgeWeb\Repositories\Clerks\ShiftTypes;

class WeeklyRota extends ReportAbstractController
{
    public $days;
    private $currentStart;
    private $currentEnd;
    public $areas = [];

    public function setTables($days = [1,2,3,4,5,6,7], $var = null): array
    {
        $this->tables = [
            "Weekly Rota" => [
                "columns" => [
                    "clerk" => [null, 'full_name', 'string', 'width' => '250px'],
                    "day1" => [null, 1, 'html'],
                    "day2" => [null, 2, 'html'],
                    "day3" => [null, 3, 'html'],
                    "day4" => [null, 4, 'html'],
                    "day5" => [null, 5, 'html'],
                    "day6" => [null, 6, 'html'],
                    "day7" => [null, 7, 'html']
                ],
                "data" => [
                    'column_count' => 6,
                    'resultset' => 'rota_shifts',
                    'summary_result' => ''
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->days = $report_data['days'];
        $this->end = Carbon::parse($this->start)->addDays(6)->endOfDay();
        $this->areas = $report_data['areas'];

        $results = [];
        $results['rota_shifts'] = $this->getRotaShifts();

        return $results;
    }

    private function getRotaShifts()
    {
        $results = [];

        $clerks = Clerks::select(['id', 'full_name', 'guid'])
            ->where('company_id', $this->company_id)
            ->whereHas('clockings', function ($q) {
                $q->whereBetween('in', [$this->start, $this->end])
                    ->whereNotNull('out')
                    ->whereIn('site_num', $this->site_num)
                    ->where('is_rota', 1);
            })
            ->whereIn('area_guid', $this->areas)
            ->get();

        foreach ($clerks as $clerk) {
            $days = 7;
            $currentDay = 1;
            $this->currentStart = Carbon::parse($this->start)->startOfDay();
            $this->currentEnd = Carbon::parse($this->start)->endOfDay();

            $clerkResult = [
                'full_name' => $clerk->full_name,
                1 => '',
                2 => '',
                3 => '',
                4 => '',
                5 => '',
                6 => '',
                7 => ''
            ];

            while ($currentDay <= $days) {
                $dayNum = Carbon::parse($this->currentStart)->dayOfWeekIso;

                $clockings = Clocking::where('employee_guid', $clerk->guid)
                    ->whereBetween('in', [$this->currentStart, $this->currentEnd])
                    ->whereNotNull('out')
                    ->whereIn('site_num', $this->site_num)
                    ->where('is_rota', 1)
                    ->get();

                if (!$clockings->isEmpty()) {
                    $clockings = $clockings->toArray();
                    $count = count($clockings);

                    if (count($clockings) > 1) {
                        $i = 1;

                        foreach ($clockings as $clock) {
                            $shiftType = '';
                            $shiftColor = 'none';
                            $fontColor = '#000000;';
                            $isTimed = null;

                            if ($clock['shift_type'] != null && $clock['shift_type'] != 0) {
                                $shiftTypes = ShiftTypes::select('short_type', 'color', 'timed')
                                    ->where('company_id', $this->company_id)
                                    ->where('id', $clock['shift_type'])->first();

                                $isTimed = $shiftTypes->timed;
                                $shiftType = '('.$shiftTypes->short_type.')';
                                $shiftColor = $shiftTypes->color;
                                $fontColor = '#ffffff;';
                            }
                            if ($i < $count) {
                                if ($isTimed == 1) {
                                    $clerkResult[$dayNum] .= '<div data-daynum="'.$dayNum.'" style="text-align: center; color: '.$fontColor.'; background-color:'.$shiftColor.';">'.Carbon::parse($clock['in'])->format('H:i').' - '.Carbon::parse($clock['out'])->format('H:i').' '.$shiftType.'</div>';
                                } else {
                                    $clerkResult[$dayNum] .= '<div data-daynum="'.$dayNum.'" style="text-align: center; color: '.$fontColor.'; background-color:'.$shiftColor.';">'.$shiftType.'</div>';
                                }
                            } else {
                                if ($isTimed == 1) {
                                    $clerkResult[$dayNum] .= '<div data-daynum="'.$dayNum.'" style="text-align: center; color: '.$fontColor.'; background-color:'.$shiftColor.';">'.Carbon::parse($clock['in'])->format('H:i').' - '.Carbon::parse($clock['out'])->format('H:i').' '.$shiftType.'</div>';
                                } else {
                                    $clerkResult[$dayNum] .= '<div data-daynum="'.$dayNum.'" style="text-align: center; color: '.$fontColor.'; background-color:'.$shiftColor.';">'.$shiftType.'</div>';
                                }
                            }


                            $i++;
                        }
                    } else {
                        foreach ($clockings as $clock) {
                            $shiftType = '';
                            $shiftColor = 'none';
                            $fontColor = '#000000';
                            $isTimed = null;

                            if ($clock['shift_type'] != null && $clock['shift_type'] != 0) {
                                $shiftTypes = ShiftTypes::select('short_type', 'color', 'timed')
                                    ->where('company_id', $this->company_id)
                                    ->where('id', $clock['shift_type'])->first();

                                $isTimed = $shiftTypes->timed;
                                $shiftType = '('.$shiftTypes->short_type.')';
                                $shiftColor = $shiftTypes->color;
                                $fontColor = '#ffffff';
                            }


                            if ($isTimed == 1) {
                                $clerkResult[$dayNum] = '<div data-daynum="'.$dayNum.'" style="text-align: center; color: '.$fontColor.'; background-color:'.$shiftColor.';">'.Carbon::parse($clock['in'])->format('H:i').' - '.Carbon::parse($clock['out'])->format('H:i').' '.$shiftType.'</div>';
                            } else {
                                $clerkResult[$dayNum] = '<div data-daynum="'.$dayNum.'" style="text-align: center; color: '.$fontColor.'; background-color:'.$shiftColor.';">'.$shiftType.'</div>';
                            }
                        }
                    }
                }

                $this->currentStart = Carbon::parse($this->currentStart)->addDay();
                $this->currentEnd = Carbon::parse($this->currentEnd)->addDay();
                $currentDay++;
            }

            $results[] = $clerkResult;
        }

        return $results;
    }

    public function getDays($start_date)
    {
        $days = [];

        $no_of_days = 6;
        $current_day = 0;

        while ($current_day <= $no_of_days) {
            $day = Carbon::parse($start_date)->startOfWeek()->addDays($current_day)->format('D');

            $days[] = (string) $day;

            $current_day++;
        }

        return $days;
    }
}
