<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\GpHelper;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\StockTransactions;

class WeeklySalesAnalysisReport extends ReportAbstractController
{
    public function setTables($var = null): array
    {
        $this->tables = [
            "Sales Mix %" => [
                "columns" => [
                    "Department" => [null, 'displayname', 'string'],
                    "% of Sales" => [null, 'percentage', 'percent'],

                ],
                "data" => [
                    'column_count' => 2,
                    'resultset' => 'sales',
                    'summary_result' => null
                ],
            ],
            "Gross Profit %" => [
                "columns" => [
                    "Department" => [null, 'displayname', 'string'],
                    "% of Sales" => [null, 'gp_percent', 'string'],
                ],
                "data" => [
                    'column_count' => 2,
                    'resultset' => 'gross',
                    'summary_result' => null
                ],
            ],
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $data = $this->salesMix();
        $results['sales'] = $data['salesMix'];
        $results['gross'] = $data['grossProfit'];

        return $results;
    }

    public function salesMix()
    {
        $departments = Departments::where('company_id', $this->company_id)->withTrashed()->get();
        $results = [];
        $results['salesMix'] = [];
        $results['grossProfit'] = [];

        /**
         * Loop though departments and get
         * Net Sales
         * cost of sales (stock)
         * GP on above
         * Labor Cost
         * Total profit
         */
        foreach ($departments as $department) {
            $sales = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as transaction_amount'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0, 6])
                ->where('department_guid', $department->guid)
                ->first();

            $products = Products::where('department_guid', $department->guid)->pluck('id');

            $wastage = StockTransactions::whereIn('product_id', $products)
                ->whereBetween('created_at', [$this->start, $this->end])
                ->whereIn('summary_type', [5])
                ->sum('costprice_total');

            $voids = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [5])
                ->where('department_guid', $department->guid)
                ->sum('cost_price');


            $costOfSales = ($wastage + $voids + $sales->sum_cost_price);


            $total_trans = PosTransactionDetail::where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0, 6])
                ->count();

            if ($sales->transaction_amount > 0) {
                $results['salesMix'][$department->guid] = [];
                $results['salesMix'][$department->guid]['displayname'] = $department->displayname;
                $results['salesMix'][$department->guid]['trans_amount'] = $sales->transaction_amount;
                $results['salesMix'][$department->guid]['percentage'] = ($sales->transaction_amount / $total_trans) * 100;
                $results['grossProfit'][$department->guid]['displayname'] = $department->displayname;
                $results['grossProfit'][$department->guid]['gp_value'] = $sales->sum_net_value - $costOfSales;
                $results['grossProfit'][$department->guid]['gp_percent'] = GpHelper::calculateGPFromNet($sales->sum_net_value, $costOfSales);
            }
        }

        return $results;
    }
}
