<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\PosTransactionDetail;

class DailyDepartmentSalesReport extends ReportAbstractController
{
    public $department;

    public function setTables($days = [1,2,3,4,5,6,7], $var = null): array
    {
        $this->tables = [
            "NET Weekly Sales" => [
                "columns" => [
                    "Department" => [null, 'displayname', 'string', 'width' => '250px'],
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
                "data" => [
                    'column_count' => 7,
                    'resultset' => 'daily_departments',
                    'summary_result' => 'daily_departments_summary'
                ],
                "summary" => [
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results = [];
        $results['daily_departments'] = $this->dailyDepartmentSales();
        $results['daily_departments_summary'] = $this->dailyDepartmentSalesSummary();

        return $results;
    }

    public function getDays($start_date)
    {
        $days = [];

        $no_of_days = 6;
        $current_day = 0;

        while ($current_day <= $no_of_days) {
            $day = Carbon::parse($start_date)->addDays($current_day)->format('D');

            $days[] = (string) $day;

            $current_day++;
        }

        return $days;
    }

    private function getDepartments()
    {
        return Departments::where('company_id', $this->company_id)->get();
    }

    private function dailyDepartmentSales()
    {
        $departments = $this->getDepartments();

        $results = [];

        foreach ($departments as $k => $department) {
            // get department sales by day
            $query = PosTransactionDetail::select(DB::raw('datetime, department_guid, SUM(gross_value) as sum_gross_value, (SUM(net_value) - sum(tax_value)) as sum_net_value, COUNT(id) as count, DAYOFWEEK(datetime) as day'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->where('department_guid', $department->guid)
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0, 6])
                ->whereNotIn('command_type', [5])
                ->having('sum_gross_value', '>', 0)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->groupBy('day')
                ->orderBy('day')
                ->get();

            $results[]['displayname'] = $department->displayname;

            foreach ($query as $q) {
                if ($query != null) {
                    $results[$k][$q->day] = (float) $q->sum_net_value;
                } else {
                    $results[$k][$q->day] = 0.00;
                }
            }

            if (!isset($results[$k][1])) {
                $results[$k][1] = 0.00;
            }
            if (!isset($results[$k][2])) {
                $results[$k][2] = 0.00;
            }
            if (!isset($results[$k][3])) {
                $results[$k][3] = 0.00;
            }
            if (!isset($results[$k][4])) {
                $results[$k][4] = 0.00;
            }
            if (!isset($results[$k][5])) {
                $results[$k][5] = 0.00;
            }
            if (!isset($results[$k][6])) {
                $results[$k][6] = 0.00;
            }
            if (!isset($results[$k][7])) {
                $results[$k][7] = 0.00;
            }
        }


        return $results;
    }

    private function dailyDepartmentSalesSummary()
    {
        $results = [];

        $query = PosTransactionDetail::select(DB::raw('datetime, SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, SUM(gross_value) as sum_gross_value, (SUM(net_value) - sum(tax_value)) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as count, SUM(discount_value) as sum_discount_value, DAYOFWEEK(datetime) as day'))
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0,6])
            ->whereNotIn('command_type', [5])
            ->having('sum_gross_value', '>', 0)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->groupBy('day')
            ->get();


        foreach ($query as $q) {
            $results[$q->day] = (float) $q->sum_net_value;
        }

        if (!isset($results[1])) {
            $results[1] = 0.00;
        }
        if (!isset($results[2])) {
            $results[2] = 0.00;
        }
        if (!isset($results[3])) {
            $results[3] = 0.00;
        }
        if (!isset($results[4])) {
            $results[4] = 0.00;
        }
        if (!isset($results[5])) {
            $results[5] = 0.00;
        }
        if (!isset($results[6])) {
            $results[6] = 0.00;
        }
        if (!isset($results[7])) {
            $results[7] = 0.00;
        }

        return $results;
    }
}
