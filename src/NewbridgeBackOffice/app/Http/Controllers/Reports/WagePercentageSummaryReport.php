<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Clerks\Clocking;
use NewbridgeWeb\Repositories\PosTransactionDetail;

class WagePercentageSummaryReport extends ReportAbstractController
{
    public $department;

    public function setTables($days = [1,2,3,4,5,6,7], $var = null): array
    {
        $this->tables = [
            "NET Weekly Sales" => [
                "columns" => [
                    "Department" => ['department', 'displayname', 'string', 'width' => '250px'],
                    "Summary" => [null, 'sum_net_value', 'decimal']
                ],
                "data" => [
                    'column_count' => 0,
                    'resultset' => 'departments',
                    'summary_result' => 'departments_summary'
                ],
                "summary" => [
                    "Summary" => [null, 'sum_net_value', 'decimal'],

                ],
            ],
            "Wages" => [
                "columns" => [
                    "Area" => [null, 'displayname', 'string', 'width' => '250px'],
                    "Summary" => [null, 'sum_pay', 'decimal']
                ],
                "data" => [
                    'column_count' => 0,
                    'resultset' => 'wages_hours',
                    'summary_result' => 'wages_hours_summary'
                ],
                "summary" => [
                    "Summary" => [null, 'sum_pay', 'decimal'],

                ],
            ],
            "Hours" => [
                "columns" => [
                    "Area" => [null, 'displayname', 'string', 'width' => '250px'],
                    "Summary" => ['', 'sum_hours', 'decimal-only']
                ],
                "data" => [
                    'column_count' => 0,
                    'resultset' => 'wages_hours',
                    'summary_result' => 'wages_hours_summary'
                ],
                "summary" => [
                    "Summary" => [null, 'sum_hours', 'decimal-only'],

                ],
            ],
            "Wages vs Sales" => [
                "columns" => [
                    "Area" => [null, 'displayname', 'string', 'width' => '250px'],
                    "Summary" => [null, 'percentage', 'percent']
                ],
                "data" => [
                    'column_count' => 0,
                    'resultset' => 'wages_vs_sales',
                    'summary_result' => 'wages_vs_sales_summary'
                ],
                "summary" => [
                    "Summary" => [null, 'total', 'percent']
                ],
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results = [];
        $results['departments'] = $this->DepartmentSales();
        $results['departments_summary'] = $this->DepartmentSalesSummary();
        $results['wages_hours'] = $this->wagesByArea();
        $results['wages_hours_summary'] = $this->wagesByAreaSummary();
        $results['wages_vs_sales'] = $this->getDailySalesVsWages($results['departments_summary'], $results['wages_hours']);
        $results['wages_vs_sales_summary'] = $this->getWagesVsSalesSummary($results['departments_summary'], $results['wages_hours_summary']);

        return $results;
    }

    private function DepartmentSales()
    {
        //TODO Change with to a join only returning display name of dept

        $query = PosTransactionDetail::select(DB::raw('datetime, department_guid, SUM(gross_value) as sum_gross_value, (SUM(net_value) - sum(tax_value)) as sum_net_value, COUNT(id) as count, DAYOFWEEK(datetime) as day'))
            ->where('company_id', $this->company_id)
            ->with(['department' => function ($q) {
                $q->withTrashed();
            }])
            ->whereIn('site_num', $this->site_num)
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0, 6])
            ->whereNotIn('command_type', [5])
            ->having('sum_gross_value', '>', 0)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->groupBy('department_guid');

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->get();
    }

    private function DepartmentSalesSummary()
    {
        $query = PosTransactionDetail::select(DB::raw('datetime, SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, SUM(gross_value) as sum_gross_value, (SUM(net_value) - sum(tax_value)) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as count, SUM(discount_value) as sum_discount_value, DAYOFWEEK(datetime) as day'))
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0,6])
            ->whereNotIn('command_type', [5])
            ->having('sum_gross_value', '>', 0)
            ->whereBetween('finalised_date', [$this->start, $this->end]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    private function wagesByArea()
    {
        $clerks = Clerks::select('guid', 'area_guid')->where('company_id', $this->company_id)->withTrashed()->get()->toArray();
        $clerkArray = array_column($clerks, 'guid');

        $query = "area_guid, `in`, `out`, DAYOFWEEK(`in`) as `day`, employee_guid, (TIMESTAMPDIFF(MINUTE,`in`,`out`) / 60) as hours, sys_clerk_areas.area_name as displayname, sum(hours_worked) as sum_hours, sum(total_pay) as sum_pay";
        $companyClerkHours = Clocking::select(DB::raw($query))->whereBetween('in', [$this->start, $this->end])
            ->whereNotNull('out')
            ->leftJoin('sys_clerk_areas', 'sys_clerk_clocking.area_guid', '=', 'sys_clerk_areas.guid')
            ->whereIn('employee_guid', $clerkArray)
            ->whereIn('sys_clerk_clocking.site_num', $this->site_num)
            ->groupBy('area_guid')
            ->get();

        foreach ($companyClerkHours as $k => $v) {
            if ($v->displayname == null) {
                $companyClerkHours[$k]->displayname = 'Unknown Area';
            }
        }

        return $companyClerkHours;
    }

    private function wagesByAreaSummary()
    {
        $clerks = Clerks::select('guid', 'area_guid')->where('company_id', $this->company_id)->withTrashed()->get()->toArray();
        $clerkArray = array_column($clerks, 'guid');

        $query = "`in`, `out`, DAYOFWEEK(`in`) as `day`, employee_guid, (TIMESTAMPDIFF(MINUTE,`in`,`out`) / 60) as hours, sys_clerk_areas.area_name as displayname, sum(hours_worked) as sum_hours, sum(total_pay) as sum_pay";
        $companyClerkHours = Clocking::select(DB::raw($query))->whereBetween('in', [$this->start, $this->end])
            ->whereNotNull('out')
            ->leftJoin('sys_clerk_areas', 'sys_clerk_clocking.area_guid', '=', 'sys_clerk_areas.guid')
            ->whereIn('employee_guid', $clerkArray)
            ->whereIn('sys_clerk_clocking.site_num', $this->site_num)
            ->first();

        return $companyClerkHours;
    }

    private function getDailySalesVsWages($DepartmentSummary, $wagesByArea)
    {
        $results = [];

        foreach ($wagesByArea as $k => $wba) {
            $wages = $wba['sum_pay'];
            $sales = $DepartmentSummary != null ? $DepartmentSummary->sum_net_value : 0;

            if ($wages > 0 && $sales > 0) {
                $percentage = abs(($wages / (float) $sales) * 100);
            } else {
                $percentage = 0.00;
            }

            $results[$wba->area_guid]['displayname'] = $wba->displayname != null ? $wba->displayname : 'Unknown Area';
            $results[$wba->area_guid]['percentage'] = $percentage;
        }

        return $results;
    }

    private function getWagesVsSalesSummary($DepartmentSummary, $wagesSummary)
    {
        if ($DepartmentSummary != null && $wagesSummary != null) {
            $sales = $DepartmentSummary->sum_net_value;
            $wages = $wagesSummary->sum_pay;
        } else {
            $sales = 0;
            $wages = 0;
        }

        if ($wages > 0 && $sales > 0) {
            $percentage = abs(($wages / (float) $sales) * 100);
        } else {
            $percentage = 0.00;
        }

        // TODO return something

        $results = [
            'total' => $percentage
        ];


        return $results;
    }
}
