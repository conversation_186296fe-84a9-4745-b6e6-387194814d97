<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosTransactionPayment;

class PaymentTransactions extends ReportAbstractController
{
    public $data;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Payment Type Sales" => [
                "columns" => [
                    "Payment Method" => [null, 'name', 'string'],
                    "Sales Value" => [null, 'sales_value', 'decimal'],
                    "# of Transactions" => [null, 'sales_count', 'int']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'payments',
                    'summary_result' => 'summary'
                ],
                "summary" => [
                    "Value" => [null, 'sales_value', 'decimal'],
                    "Count" => [null, 'sales_count', 'int'],
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->paymentTypeSales();
    }

    public function paymentTypeSales()
    {
        $paymentQuery = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value'))
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('method_type', '<>', 1)
            ->groupBy('method_guid')
            ->with('method');

        if ($this->terminal != '') {
            $paymentQuery = $paymentQuery->whereIn('terminal_num', $this->terminal);
        }

        $paymentQuery = $paymentQuery->get();

        $cashQuery = $this->cashSales();

        $results = [];

        if (!empty($paymentQuery)) {
            foreach ($paymentQuery as $p) {
                $results[] = [
                    'name' => $p->method->DisplayName,
                    'sales_value' => $p->sales_value,
                    'sales_count' => $p->sales_count,
                ];
            }
        }

        if (!empty($cashQuery)) {
            foreach ($cashQuery as $c) {
                $results[] = [
                    'name' => 'Cash',
                    'sales_value' => $c->sales_value,
                    'sales_count' => $c->sales_count,
                ];
            }
        }

        $result = [];

        $result['payments'] = $results;
        $result['summary'] = [
            'sales_count' => 0,
            'sales_value' => 0
        ];
        if (!empty($results)) {
            foreach ($results as $q) {
                $result['summary']['sales_count'] += $q['sales_count'];
                $result['summary']['sales_value'] += $q['sales_value'];
            }
        }


        return $result;
    }

    public function cashSales()
    {
        $query = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value'))
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('method_type', 1)
            ->groupBy('method_type');

        if ($this->terminal != '') {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->get();
    }
}
