<?php

namespace NewbridgeWeb\Http\Controllers\Reports\GrossProfit;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Reports\ReportAbstractController;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Http\Helpers\GpHelper;
use NewbridgeWeb\Repositories\CustomerGroup;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Promotions;
use NewbridgeWeb\Repositories\StockTransactions;
use NewbridgeWeb\Repositories\VoidReasons;

class EnhancedGrossProfit extends ReportAbstractController
{
    public $comparison_option;
    public $times;
    private $summary = [
        "total_net" => 0,
        "total_gross" => 0,
        "total_cost" => 0,
        "total_tax" => 0,
        "total_wastage" => 0
    ];

    public function setTables($strings = [], $var = false): array
    {
        $this->currencySymbol = CurrencyHelper::symbol($this->company_id);

        $this->tables = [
            "Enhanced Gross Profit" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Type" => [null, 'displayname', 'string'],
                        "GP (" . $this->currencySymbol . ")" => [null, 'total', 'decimal'],
                        "GP (%)" => [null, 'percent', 'string']
                    ],
                    "data" => [
                        'column_count' => 5,
                        'resultset' => 'lines',
                        'summary_result' => null, //'department_summary',
                        'showdates' => false
                    ]
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'departments',
                    'summary_result' => null,
                    'showdates' => false
                ]
            ]
        ];

        return $this->tables;

    }

    public function data($transactions, $report_data): array
    {
        parent::data($transactions, $report_data);

        $this->comparison_option = $report_data['comparison_option'];

        $salesData = $this->getDepartmentData();
        $results = [];
        $results['departments'] = $this->doDepartmentCalculations($salesData);


        $summary = [
            "id" => 999,
            "displayname" => "GP Summary",
            "lines" => [
                [
                    "displayname" => "Total Notional GP",
                    "total" => $this->summary['total_gross'] - $this->summary['total_cost'] - $this->summary['total_tax'],
                    "percent" => GpHelper::calculateGPFromNet($this->summary['total_gross'], ($this->summary['total_cost'] + $this->summary['total_tax']))
                ],
                [
                    "displayname" => "Total Actual GP",
                    "total" => $this->summary['total_net'] - $this->summary['total_cost'] - $this->summary['total_tax'] - abs($this->summary['total_wastage']),
                    "percent" => GpHelper::calculateGPFromNet($this->summary['total_net'], ($this->summary['total_cost'] + $this->summary['total_tax'] + $this->summary['total_wastage']))
                ]
            ]
        ];
        array_unshift($results['departments'], $summary);

        return $results;
    }

    public function doDepartmentCalculations(array $departments): array
    {
        $data = [];

        foreach($departments as $k => $department) {

            $this->summary['total_gross'] += $department['gross_sales'];
            $this->summary['total_net'] += $department['net_sales'];
            $this->summary['total_wastage'] += $department['wastage'];
            $this->summary['total_cost'] += $department['cost_of_sales'];
            $this->summary['total_tax'] += $department['tax'];

            $departmentData = [
                "id" => $k,
                "displayname" => $department['displayname'],
                "gross_sales" => $department['gross_sales'],
                "net_sales" => $department['net_sales'],
                "cost" => $department['cost_of_sales'],
                "wastage" => $department['wastage'],
                "lines" => [
                    [
                        "displayname" => "Department Notional GP",
                        "total" => $department['gross_sales'] - $department['tax'] - $department['cost_of_sales'],
                        "percent" => GpHelper::calculateGPFromNet($department['gross_sales'], ($department['tax'] + $department['cost_of_sales'])),
                    ],
                    [
                        "displayname" => "Department Actual GP",
                        "total" => $department['net_sales'] - $department['tax'] - $department['cost_of_sales'] - $department['wastage'],
                        "percent" => GpHelper::calculateGPFromNet($department['net_sales'], ($department['tax'] + $department['cost_of_sales'] + $department['wastage']))
                    ],
                    [
                        "displayname" => "Wastage",
                        "total" => $department['wastage'],
                        "percent" => GpHelper::calculatePercentageOf($department['wastage'], $department['net_sales']).'%'
                    ]
                ]
            ];

            if(!empty($department['discounts'])) {
                foreach($department['discounts'] as $discount) {
                    $discount['percent'] = GpHelper::calculatePercentageOf($discount['total'], $department['net_sales']).'%';
                    $departmentData['lines'][] = $discount;
                }
            }

            $data[] = $departmentData;
        }

        return $data;
    }

    public function getDepartmentData(): array
    {
        $departments = Departments::where('company_id', $this->company_id)->withTrashed()->get();
        $discounts = Discounts::where('company_id', $this->company_id)->where('auto', 0)
            ->where('isloyaltycard', 0)->withTrashed()->get();
        $promotions = Promotions::where('company_id', $this->company_id)->where('isloyalty', 0)->withTrashed()->get();
        $loyaltySchemes = CustomerGroup::whereCompanyId($this->company_id)->withTrashed()->get();
        $results = [];

        foreach($departments as $department) {
            $data = [
                "id" => 1,
                "displayname" => $department->displayname,
                "gross_sales" => 0,
                "net_sales" => 0,
                "cost_of_sales" => 0,
                "tax" => 0
            ];

            $sales = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, 
                 department_guid, sub_department_guid, SUM(net_value) as sum_net_value, SUM(gross_value) as sum_gross_value, SUM(tax_value) as sum_tax_value'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0, 6])
                ->where('department_guid', $department->guid)
                ->first();

            $data['gross_sales'] = (float) $sales['sum_gross_value'];
            $data['net_sales'] = (float) $sales['sum_net_value'];
            $data['cost_of_sales'] = (float) $sales['sum_cost_price'];
            $data['tax'] = (float) $sales['sum_tax_value'];

            foreach($loyaltySchemes as $scheme) {
                $discountValue = PosTransactionDetail::where('company_id', $this->company_id)
                    ->whereIn('site_num', $this->site_num)
                    ->whereBetween('finalised_date', [$this->start, $this->end])
                    ->whereIn('command', [3, 7, 38])
                    ->whereIn('command_type', [0, 6])
                    ->where('promotion_guid', $scheme->promotion_guid)

                    ->where('department_guid', $department->guid)
                    ->sum('discount_value');

                if($discountValue != 0) {
                    $data['discounts'][] = [
                        "displayname" => $scheme->displayname,
                        "total" => abs((float) $discountValue)
                    ];
                }
            }

            foreach($discounts as $discount) {
                $discountValue = PosTransactionDetail::where('company_id', $this->company_id)
                    ->whereIn('site_num', $this->site_num)
                    ->whereBetween('finalised_date', [$this->start, $this->end])
                    ->whereIn('command', [3, 7, 38])
                    ->whereIn('command_type', [0, 6])
                    ->where('discount_guid', $discount->CommandUID)
                    ->where('department_guid', $department->guid)
                    ->sum('discount_value');

                if($discountValue != 0) {
                    $data['discounts'][] = [
                        "displayname" => $discount->displayname,
                        "total" => abs((float) $discountValue)
                    ];
                }
            }

            foreach($promotions as $promotion) {
                $promotionValue = PosTransactionDetail::where('company_id', $this->company_id)
                    ->whereIn('site_num', $this->site_num)
                    ->whereBetween('finalised_date', [$this->start, $this->end])
                    ->whereIn('command', [3, 7, 38])
                    ->whereIn('command_type', [0, 6])
                    ->where('promotion_guid', $promotion->CommandUID)
                    ->where('department_guid', $department->guid)
                    ->sum('discount_value');

                if($promotionValue != 0) {
                    $data['discounts'][] = [
                        "displayname" => $promotion->displayname,
                        "total" => abs((float) $promotionValue)
                    ];
                }
            }

            $products = Products::where('department_guid', $department->guid)->pluck('id');

            $wastage = StockTransactions::whereIn('product_id', $products)
                ->whereBetween('created_at', [$this->start, $this->end])
                ->whereIn('summary_type', [5])
                ->sum('costprice');

            $reasons = VoidReasons::where('company_id', $this->company_id)
                ->where('stock', 1)
                ->where('reason_type', 1)
                ->pluck('guid')->toArray();

            $voids = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [5])
                ->where('department_guid', $department->guid)
                ->whereIn('void_reason_guid', $reasons)
                ->sum('cost_price');

            $data['wastage'] = (float) $wastage + (float) $voids;

            if(!empty($data['gross_sales'])) {
                $results[] = $data;
            }
        }

        return $results;
    }
}
