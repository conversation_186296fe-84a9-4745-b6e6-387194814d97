<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\SubDepartment;
use PDF;

class ClerkProductSales extends ReportAbstractController
{
    public $summary;
    public $grouping = 'sub_department';
    public $clerk;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Clerk Product Sales" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Product" => [null, 'displayname', 'string'],
                        "# of Sales" => [null, 'count', 'int'],
                        "Gross Sales Value" => [null, 'sum_net_value', 'decimal'],
                        "% of Clerk Sales" => [null, 'clerk_sales_percentage', 'string']
                    ],
                    "data" => [
                        'column_count' => 8,
                        'resultset' => 'products',
                        'summary_result' => 'summary'
                    ],
                    "summary" => [
                        "# of Sales" => [null, 'count', 'int'],
                        "Gross Sales Value" => [null, 'sum_net_value', 'decimal'],
                        "Discount Value" => [null, 'sum_discount_value', 'decimal'],
                        "Tax Amount" => [null, 'sum_tax_value', 'decimal'],
                        "Unit Cost Price" => [null, 'costprice', 'decimal'],
                        "Total Cost Price" => [null, 'sum_cost_price', 'decimal'],
                        "GP %" => [null, 'gp', 'string']
                    ]
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'groupings',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->clerk = $report_data['clerk'];

        return $this->productSales();
    }

    private function productSales()
    {
        $results = [];

        $subdepartments = SubDepartment::where('company_id', $this->company_id)->withTrashed()->get();

        $clerkTotal = PosTransactionDetail::selectRaw('employee_guid, SUM(net_value) as sum_net_value')
            ->where('employee_guid', $this->clerk['guid'])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0,6])
            ->whereNotIn('command_type', [5])
            ->first();

        $clerkTotal = (float) $clerkTotal['sum_net_value'];

        foreach ($subdepartments as $subdepartment) {
            $products = Products::select(['guid', 'displayname'])
                ->where('company_id', $this->company_id)
                ->where('sub_department_guid', $subdepartment->guid)
                ->orderBy('displayname', 'ASC')
                ->withTrashed()
                ->get()
                ->pluck('guid');

            $query = new PosTransactionDetail();
            $query = $query->select(DB::raw('SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, SUM(qty) as count, SUM(discount_value) as sum_discount_value'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->with(['department' => function ($q) {
                    $q->withTrashed();
                }, 'sub_department' => function ($q) {
                    $q->withTrashed();
                }, 'product' => function ($q) {
                    $q->withTrashed();
                }])
                ->where('employee_guid', $this->clerk['guid'])
                ->whereIn('product_guid', $products)
                ->whereIn('command', [3, 7, 38])
                ->where('command_type', '!=', 5)
                ->orderBy('sum_gross_value', 'DESC')
                ->groupBy('product_guid')
                ->having('sum_gross_value', '>', 0);

            if ($this->terminal != '') {
                $query = $query->whereIn('terminal_num', $this->terminal);
            }

            $query = $query->get();

            if (!$query->isEmpty()) {
                $results['groupings'][$subdepartment->id]['id'] = $subdepartment->id;
                $results['groupings'][$subdepartment->id]['displayname'] = $subdepartment->displayname;
                $results['groupings'][$subdepartment->id]['products'] = [];

                foreach ($query as $k => $q) {
                    $results['groupings'][$subdepartment->id]['products'][] = [
                        'displayname' => $q->product->displayname,
                        'count' => $q->count,
                        'sum_net_value' => $q->sum_net_value
                    ];
                }

                foreach ($results['groupings'][$subdepartment->id]['products'] as $k => $v) {
                    if ($results['groupings'][$subdepartment->id]['products'][$k]['sum_net_value'] > 0) {
                        $results['groupings'][$subdepartment->id]['products'][$k]['clerk_sales_percentage'] = number_format((($results['groupings'][$subdepartment->id]['products'][$k]['sum_net_value'] / $clerkTotal) * 100), 2) . '%';
                    } else {
                        $results['groupings'][$subdepartment->id]['products'][$k]['clerk_sales_percentage'] = '0%';
                    }
                }
            }
        }

        return $results;
    }
}
