<?php

namespace NewbridgeWeb\Http\Controllers\Reports\Stock;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Reports\ReportAbstractController;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\StockTransactions;
use NewbridgeWeb\Repositories\Suppliers;
use PDF;

class PurchaseDetailsByDepartment extends ReportAbstractController
{
    public array $results;
    public string|null $supplier;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Purchase Details Report" => [
                "columns" => [
                    "" => [null, ['name'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Product Name" => [null, 'product_name', 'string'],
                        "Department" => [null, 'department_name', 'string'],
                        "Quantity" => [null, 'quantity', 'string'],
                        "SKU" => [null, 'sku_name', 'string'],
                        "Cost" => [null, 'price', 'decimal'],
                        "Total" => [null, 'total', 'decimal']
                    ],
                    "data" => [
                        'column_count' => 6,
                        'resultset' => 'products',
                        'summary_result' => 'summary'
                    ],
                    "summary" => [
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        "Total" => [null, 'total', 'decimal'],
                        [null, 'blank', 'string']
                    ]
                ],
                "data" => [
                    'column_count' => 1,
                    'resultset' => 'suppliers',
                ]
            ],
            "Supplier Summary" => [
                "columns" => [
                    "Supplier Name" => [null, 'name', 'string'],
                    "Supplier Total" => [null, 'total', 'decimal'],
                ],
                "data" => [
                    'column_count' => 1,
                    'resultset' => 'suppliers_summary',
                    'summary_result' => null
                ]
            ],
            "Department Summary" => [
                "columns" => [
                    "Department Name" => [null, 'name', 'string'],
                    "Department Total" => [null, 'total', 'decimal'],
                ],
                "data" => [
                    'column_count' => 1,
                    'resultset' => 'department_summary',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function __construct()
    {
        $this->results = [
            'suppliers' => [],
            'suppliers_summary' => [],
            'department_summary' => [],
            'summary' => [
                'total' => 0,
                'blank' => ''
            ]
        ];
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->supplier = $report_data['supplier'];

        return $this->supplierOrders();
    }

    private function supplierOrders(): array
    {
        $productOrderDetails = StockTransactions::selectRaw('product_id, price, sum(quantity) as sum_quantity, sum(total) as sum_total, summary_type, summary_id, created_at, status')
            ->whereBetween('created_at', [$this->start, $this->end])
            ->where('summary_type', 2)
            ->where('status', 1)
            ->whereIn('site_num', $this->site_num)
            ->where('company_id', $this->company_id)
            ->with(['product' => function ($q) {
                $q->select('sku_guid', 'id', 'displayname', 'supplier_guid', 'department_guid')
                    ->with(['sku' => function ($q) {
                        $q->select('id', 'guid', 'displayname', 'qty')
                            ->withTrashed();
                    }])
                    ->with(['department' => function ($q) {
                        $q->select('displayname', 'guid', 'id')
                            ->withTrashed();
                    }])
                    ->with(['supplier' => function ($q) {
                        $q->select('id', 'guid', 'name')
                            ->withTrashed();
                    }])->withTrashed();
            }])
            ->with(['summary' => function ($q) {
                $q->with(['supplier' => function ($q) {
                    $q->withTrashed();
                }]);
            }])
            ->groupBy('product_id')
            ->groupBy('summary_id');

        if(!empty($this->supplier)) {
            $supplier = Suppliers::whereGuid($this->supplier)->select(['id'])->value('id');

            $productOrderDetails = $productOrderDetails->whereHas('summary', function ($q) use ($supplier) {
                $q->where('supplier_id', $supplier);
            });
        }

        $productOrderDetails = $productOrderDetails->get()
            ->toArray();

        $departments = Departments::where('company_id', $this->company_id)
            ->withTrashed()
            ->get()
            ->toArray();

        foreach($departments as $department) {
            foreach($productOrderDetails as $productOrderDetail) {
                if (isset($productOrderDetail['summary']['supplier']) && $productOrderDetail['summary']['supplier'] != null) {
                    $this->addSupplierToResults($productOrderDetail['summary']['supplier'], $department);
                } else {
                    $this->addUnknownSupplierToResults($department);
                }
            }
        }

        foreach($productOrderDetails as $productOrderDetail) {
            $this->addProductToSupplier($productOrderDetail);
        }

        $this->calculateSummary();

        return $this->results;
    }

    private function addSupplierToResults(array $supplier, array $department): void
    {
        $this->results['suppliers'][$supplier['id'].':'.$department['id']] = [
            'id' => $supplier['id'],
            'name' => $supplier['name'].' / '.$department['displayname'],
            'products' => []
        ];
    }

    private function addUnknownSupplierToResults(array $department): void
    {
        if(!isset($this->results['suppliers']['unknown'])) {
            $this->results['suppliers']['unknown:'.$department['id']] = [
                'id' => 0,
                'name' => 'Unknown Supplier / '.$department['displayname'],
                'products' => []
            ];
        }
    }

    private function addProductToSupplier(array $orderDetail): void
    {
        $supplier = $orderDetail['summary']['supplier'] != null ? $orderDetail['summary']['supplier']['id'] : 'unknown';
        $department = $orderDetail['product']['department']['id'];

        if(isset($this->results['suppliers'][$supplier.':'.$department]['products'][$orderDetail['product']['id']])) {
            $this->results['suppliers'][$supplier.':'.$department]['products'][$orderDetail['product_id']]['quantity'] += (int) $orderDetail['sum_quantity'] / (int) $orderDetail['product']['sku']['qty'];
            $this->results['suppliers'][$supplier.':'.$department]['products'][$orderDetail['product_id']]['total'] += (float) $orderDetail['sum_total'];
        } else {
            $this->results['suppliers'][$supplier.':'.$department]['products'][$orderDetail['product']['id']] = [
                'id' => $orderDetail['product']['id'],
                'product_name' => $orderDetail['product']['displayname'],
                'department_name' => $orderDetail['product']['department']['displayname'],
                'sku_name' => $orderDetail['product']['sku']['displayname'] ?? 'Unknown',
                'price' => (float) $orderDetail['price'],
                'total' => (float) $orderDetail['sum_total'],
                'quantity' => $orderDetail['product'] != null && $orderDetail['product']['sku'] != null ? (int) $orderDetail['sum_quantity'] / (int) $orderDetail['product']['sku']['qty'] : 0
            ];
        }

        if(isset($this->results['suppliers_summary'][$supplier])) {
            $this->results['suppliers_summary'][$supplier]['total'] += (float) $orderDetail['sum_total'];
        } else {
            $this->results['suppliers_summary'][$supplier] = [
                'name' => $orderDetail['summary']['supplier']['name'] ?? 'Unknown',
                'total' => (float) $orderDetail['sum_total']
            ];
        }

        if(isset($this->results['department_summary'][$department])) {
            $this->results['department_summary'][$department]['total'] += (float) $orderDetail['sum_total'];
        } else {
            $this->results['department_summary'][$department] = [
                'name' => $orderDetail['product']['department']['displayname'],
                'total' => (float) $orderDetail['sum_total']
            ];
        }
    }

    private function calculateSummary(): void
    {
        foreach($this->results['suppliers'] as $key => $supplier) {
            $total = 0;
            if(!empty($supplier['products'])) {
                foreach ($supplier['products'] as $product) {
                    $total += $product['total'];
                }
                $this->results['suppliers'][$key]['summary'] = [
                    'blank' => '',
                    'total' => $total
                ];

                $this->results['summary']['total'] += $total;
            } else {
                unset($this->results['suppliers'][$key]);
            }
        }

        if ($this->supplier != null || count($this->results['suppliers']) === 1) {
            $this->results['summary']['enabled'] = false;
        }
    }
}
