<?php

namespace NewbridgeWeb\Http\Controllers\Reports\Tax;

use NewbridgeWeb\Http\Controllers\Reports\ReportAbstractController;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\PosTransaction;

class ThaiTax extends ReportAbstractController
{
    public bool $onlyAbb;
    private bool $showTopSummary;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Thai Tax Report" => [
                "columns" => [
                    "ABB Number " => [null, 'abb', 'string'],
                    "Check No" => [null, 'order_no', 'string'],
                    "Nett" => [null, 'net', 'decimal'],
                    "Tax" => [null, 'tax', 'decimal'],
                    "Gross" => [null, 'gross', 'decimal'],
                    "Operator" => [null, 'clerk', 'string'],
                ],
                "data" => [
                    'column_count' => 7,
                    'resultset' => 'taxes',
                    'summary_result' => 'tax_summary',
                    'show_top_summary' => true
                ],
                "summary" => [
                    [null, 'blank', 'string'],
                    [null, 'sum_net', 'decimal'],
                    [null, 'sum_tax', 'decimal'],
                    [null, 'sum_gross', 'decimal'],
                    [null, 'blank', 'string']
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data): array
    {
        parent::data($transactions, $report_data);

        $this->onlyAbb = $report_data['only_abb'];
        $this->showTopSummary = $report_data['show_top_summary'] ?? false;

        return $this->taxRateDetails();
    }

    public function taxRateDetails()
    {
        $result = [];
        $result['taxes'] = [];

        $transactionsQuery = PosTransaction::whereBetween('finalised_date', [$this->start, $this->end])
            ->with([
                'taxes' => function ($q) {
                    $q->where('reporting', 1);
                }
            ])
            ->whereHas('taxes', function ($q) {
                $q->where('reporting', 1);
            })
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num);

        if($this->onlyAbb === true) {
            $transactionsQuery->whereNotNull('tax_reference');
        }

        if ($this->terminal != '') {
            $transactionsQuery->whereIn('terminal_num', $this->terminal);
        }

        $transactions = $transactionsQuery->get();
        $sumNet = 0;
        $sumTax = 0;
        $sumGross = 0;

        if (!empty($transactions)) {
            foreach ($transactions as $transaction) {
                $net = $transaction->total - $transaction->taxes->sum('value');
                $tax = $transaction->taxes->sum('value');
                $gross = $transaction->total;

                $result['taxes'][] = [
                    "abb" => $transaction->tax_reference,
                    "order_no" => $transaction->order_number,
                    "net" => $net,
                    "tax" => $tax,
                    "gross" => $gross,
                    "clerk" => $this->getEmployeeName($transaction->employee_guid)
                ];

                $sumNet += $net;
                $sumTax += $tax;
                $sumGross += $gross;
            }
        }

        $result['tax_summary'] = [
            'sum_net' => $sumNet,
            'sum_tax' => $sumTax,
            'sum_gross' => $sumGross,
            'blank' => ''   // For Operator column
        ];

        return $result;
    }

    private function getEmployeeName($guid)
    {
        $clerk = Clerks::select(['guid', 'full_name'])->whereGuid($guid)->withTrashed()->first();
        return $clerk ? $clerk->full_name : 'Unknown';
    }
}
