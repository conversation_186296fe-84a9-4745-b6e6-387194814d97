<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Clerks\Clerks;

class ClerkAttendance extends ReportAbstractController
{
    public $summary = [];
    public $areas = [];

    public function setTables($var = null): array
    {
        $this->currencySymbol = CurrencyHelper::symbol($this->company_id);

        $this->tables = [
            "Attendance Summary" => [
                "columns" => [
                    "Clerk" => [null, 'name', 'string'],
                    "Hours Worked" => [null, 'hours', 'string'],
                    "Wages Total" => [null, 'wages', 'html']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'summary',
                    'summary_result' => 'attendance_summary'
                ],
                "summary" => [
                    "Hours Worked" => [null, 'total_hours', 'string'],
                    "Wages Total" => [null, 'total_wages', 'decimal']
                ]
            ],
            "Clerk Attendance" => [
                "columns" => [
                    "" => [null, ['full_name'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Shift Date" => [null, 'date', 'string'],
                        "From" => [null, 'from', 'string'],
                        "To" => [null, 'to', 'string'],
                        "Hours Worked" => [null, 'hours', 'string'],
                        "Pay Rate" => [null, 'rate', 'html'],
                        "Shift Pay Total" => [null, 'total', 'html']
                    ],
                    "data" => [
                        'column_count' => 6,
                        'resultset' => 'clockings',
                        'summary_result' => null
                    ]
                ],
                "data" => [
                    'column_count' => 6,
                    'resultset' => 'clerks',
                    'summary_result' => null,
                    'has_nested' => true
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->areas = $report_data['areas'];

        $results = $this->attendance();
        $results['summary'] = $this->clerkPaySummary();
        $results['attendance_summary'] = $this->summary;

        return $results;
    }

    public function attendance()
    {
        $query = Clerks::with(['clockings' => function ($q) {
            $q->whereBetween('in', [$this->start, $this->end])
                ->whereNotNull('out')
                ->whereIn('site_num', $this->site_num)
                ->where('is_rota', 0);
        }])
            ->where('company_id', $this->company_id)
            ->where('ishidden', 0)
            ->whereHas('clockings', function ($q) {
                $q->whereBetween('in', [$this->start, $this->end])
                    ->whereNotNull('out')
                    ->whereIn('site_num', $this->site_num)
                    ->where('is_rota', 0);
            })
            ->whereIn('area_guid', $this->areas)
            ->withTrashed()
            ->get();

        foreach ($query as $key => $q) {
            if ($q->clockings->isEmpty()) {
                $query->forget($key);
            }
        }

        $results = [];
        $results['clerks'] = [];

        foreach ($query as $clerk) {
            $results['clerks'][$clerk->id] = [
                'id' => $clerk->id,
                'full_name' => $clerk->full_name
            ];

            foreach ($clerk->clockings as $clock) {
                $results['clerks'][$clerk->id]['clockings'][] = [
                    'id' => $clock->id,
                    'date' => Carbon::parse($clock->in)->setTimezone(TimezoneHelper::getTimezone())->format('D d M'),
                    'from' => Carbon::parse($clock->in)->setTimezone(TimezoneHelper::getTimezone())->format('H:i'),
                    'to' => Carbon::parse($clock->out)->setTimezone(TimezoneHelper::getTimezone())->format('H:i'),
                    'hours' => Carbon::parse($clock->in)->setTimezone(TimezoneHelper::getTimezone())->diff(Carbon::parse($clock->out)->setTimezone(TimezoneHelper::getTimezone()))->format('%D days %H hours %i Minutes'),
                    'rate' => $clerk->salary == 0 ? $this->currencySymbol.number_format($clock->pay_rate, 2) : '<span style="color: darkgoldenrod;">Salary</span>',
                    'total' => $clerk->salary == 0 ? $this->currencySymbol.number_format($clock->total_pay, 2) : '<span style="color: darkgoldenrod;">Salary</span>'
                ];
            }
        }

        return $results;
    }

    public function clerkPaySummary()
    {
        $results = [];
        $this->summary = [
            'blank' => '',
            'total_hours' => 0,
            'total_wages' => 0
        ];

        $clerkHours = Clerks::with(['clockings' => function ($q) {
            $q->whereBetween('in', [$this->start, $this->end])
                ->whereNotNull('out')
                ->whereIn('site_num', $this->site_num)
                ->where('is_rota', 0);
        }])
            ->where('company_id', $this->company_id)
            ->whereHas('clockings', function ($q) {
                $q->whereBetween('in', [$this->start, $this->end])
                    ->whereNotNull('out')
                    ->whereIn('site_num', $this->site_num)
                    ->where('is_rota', 0);
            })
            ->whereIn('area_guid', $this->areas)
            ->where('ishidden', 0)
            ->withTrashed()
            ->get();

        foreach ($clerkHours as $key => $q) {
            if ($q->clockings->isEmpty() && $q->salary == 0) {
                $clerkHours->forget($key);
            }
        }

        foreach ($clerkHours as $clerk) {
            $results[$clerk->id]['name'] = $clerk->full_name;
            $results[$clerk->id]['hours'] = 0;
            $results[$clerk->id]['wages'] = 0;

            foreach ($clerk->clockings as $time) {
                if ($clerk->salary == 0) {
                    $results[$clerk->id]['hours'] += $time->hours_worked;
                    $results[$clerk->id]['wages'] += $time->total_pay;
                } else {
                    $days = Carbon::parse($this->start)->diffInDays(Carbon::parse($this->end));
                    $results[$clerk->id]['hours'] += $time->hours_worked;
                    $results[$clerk->id]['wages'] = ($clerk->rate * $days);
                }
            }

            $this->summary['total_hours'] += $results[$clerk->id]['hours'];
            $this->summary['total_wages'] += $results[$clerk->id]['wages'];

            $results[$clerk->id]['hours'] = number_format($results[$clerk->id]['hours'], 2);
            $results[$clerk->id]['wages'] = number_format($results[$clerk->id]['wages'], 2);
        }

        return $results;
    }
}
