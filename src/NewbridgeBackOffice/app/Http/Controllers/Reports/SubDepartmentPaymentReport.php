<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PaymentTypes;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\PosTransactionPayment;
use NewbridgeWeb\Repositories\Sites;

class SubDepartmentPaymentReport extends ReportAbstractController
{
    public $data;
    public $subdepartment;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Sub Department Sales By Payment Method" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Sub Department" => ['sub_department', 'displayname', 'string'],
                        "Gross Sales Value" => [null, 'sum_net_value', 'decimal'],
                        "# of Sales" => [null, 'count', 'int']
                    ],
                    "data" => [
                        'column_count' => 4,
                        'resultset' => 'subdepartments',
                        'summary_result' => null
                    ]
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'methods',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);
        $this->subdepartment = $report_data['subdepartment'];

        return $this->subDepartmentSales();
    }

    public function paymentMethods()
    {
        $methodTypes = PaymentTypes::get();

        $data = [];

        foreach ($methodTypes as $k => $type) {
            $methods = Payments::where('method_type', $type->id)->where('company_id', $this->company_id)->pluck('CommandUID');

            $data[$k] = [
                'id' => $type->id,
                'displayname' => $type->name,
                'transactions' => []
            ];

            $paymentTransactions = PosTransactionPayment::whereBetween('finalised_date', [$this->start, $this->end])
                ->whereIn('method_guid', $methods)->whereIn('site_num', $this->site_num);

            if ($this->terminal != null) {
                $paymentTransactions = $paymentTransactions->whereIn('terminal_num', $this->terminal);
            }

            $paymentTransactions = $paymentTransactions->pluck('trans_id');

            $data[$k]['transactions'] = $paymentTransactions;
        }

        return $data;
    }

    public function subDepartmentSales()
    {
        $paymentMethods = $this->paymentMethods();

        $result = [
            'methods' => []
        ];

        foreach ($paymentMethods as $method) {
            $result['methods'][$method['id']]['id'] = $method['id'];
            $result['methods'][$method['id']]['displayname'] = $method['displayname'];
            $result['methods'][$method['id']]['subdepartments'] = [];

            $paymentSales = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as count, SUM(discount_value) as sum_discount_value'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereIn('trans_id', $method['transactions'])
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0,6])
                ->where('command_type', '!=', 5)
                ->with(['sub_department' => function ($q) {
                    $q->withTrashed();
                }])
                ->groupBy('sub_department_guid')
                ->get();

            if ($paymentSales != null) {
                $result['methods'][$method['id']]['subdepartments'] = $paymentSales->toArray();
            }
        }

        return $result;
    }
}
