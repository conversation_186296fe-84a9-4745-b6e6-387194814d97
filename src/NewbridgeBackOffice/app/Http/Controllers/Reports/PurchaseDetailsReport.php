<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\StockTransactions;
use NewbridgeWeb\Repositories\Suppliers;
use PDF;

class PurchaseDetailsReport extends ReportAbstractController
{
    public array $results;
    public string|null $supplier;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Purchase Details Report" => [
                "columns" => [
                    "" => [null, ['name'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Product Name" => [null, 'product_name', 'string'],
                        "Department" => [null, 'department_name', 'string'],
                        "Quantity" => [null, 'quantity', 'string'],
                        "SKU" => [null, 'sku_name', 'string'],
                        "Cost" => [null, 'price', 'decimal'],
                        "Total" => [null, 'total', 'decimal']
                    ],
                    "data" => [
                        'column_count' => 6,
                        'resultset' => 'products',
                        'summary_result' => 'summary'
                    ],
                    "summary" => [
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        "Total" => [null, 'total', 'decimal'],
                        [null, 'blank', 'string']
                    ]
                ],
                "data" => [
                    'column_count' => 1,
                    'resultset' => 'suppliers',
                    'summary_result' => 'summary'
                ],
                "summary" => [
                    [null, 'blank', 'string'],
                    [null, 'blank', 'string'],
                    [null, 'blank', 'string'],
                    [null, 'blank', 'string'],
                    "Order Total Value" => [null, 'total', 'decimal'],
                    [null, 'blank', 'string']
                ]
            ]
        ];

        return $this->tables;
    }

    public function __construct()
    {
        $this->results = [
            'suppliers' => [],
            'summary' => [
                'total' => 0,
                'blank' => ''
            ]
        ];
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);
        $this->supplier = $report_data['supplier'];

        return $this->supplierOrders();
    }

    private function supplierOrders(): array
    {
        $productOrderDetails = StockTransactions::selectRaw('product_id, price, sum(quantity) as sum_quantity, sum(total) as sum_total, summary_type, summary_id, created_at, status')
            ->whereBetween('created_at', [$this->start, $this->end])
            ->where('summary_type', 2)
            ->where('status', 1)
            ->whereIn('site_num', $this->site_num)
            ->where('company_id', $this->company_id)
            ->with(['product' => function ($q) {
                $q->select('sku_guid', 'id', 'displayname', 'supplier_guid', 'department_guid')->withTrashed()
                    ->with(['sku' => function ($q) {
                        $q->select('id', 'guid', 'displayname', 'qty')->withTrashed();
                    }])
                    ->with(['department' => function ($q) {
                        $q->select('displayname', 'guid')->withTrashed();
                    }])
                    ->with(['supplier' => function ($q) {
                        $q->select('id', 'guid', 'name')->withTrashed();
                    }]);
            }])
            ->with(['summary' => function ($q) {
                $q->with(['supplier' => function ($q) {
                    $q->select('id', 'guid', 'name')->withTrashed();
                }]);
            }])
            ->groupBy('product_id')
            ->groupBy('summary_id');

        if(!empty($this->supplier)) {
            $supplier = Suppliers::whereGuid($this->supplier)->select(['id'])->value('id');

            $productOrderDetails = $productOrderDetails->whereHas('summary', function ($q) use ($supplier) {
                $q->where('supplier_id', $supplier);
            });
        }

        $productOrderDetails = $productOrderDetails->get()
            ->toArray();

        foreach($productOrderDetails as $productOrderDetail) {
            if(isset($productOrderDetail['summary']['supplier']) && $productOrderDetail['summary']['supplier'] != null) {
                $this->addSupplierToResults($productOrderDetail['summary']['supplier']);
            } else {
                $this->addUnknownSupplierToResults();
            }
        }

        foreach($productOrderDetails as $productOrderDetail) {
            $this->addProductToSupplier($productOrderDetail);
        }

        $this->calculateSummary();

        return $this->results;
    }

    private function addSupplierToResults(array $supplier): void
    {
        $this->results['suppliers'][$supplier['id']] = [
            'id' => $supplier['id'],
            'name' => $supplier['name'],
            'products' => []
        ];
    }

    private function addUnknownSupplierToResults(): void
    {
        if(!isset($this->results['suppliers']['unknown'])) {
            $this->results['suppliers']['unknown'] = [
                'id' => 0,
                'name' => 'Unknown Supplier',
                'products' => []
            ];
        }
    }

    private function addProductToSupplier(array $orderDetail): void
    {
        $supplier = $orderDetail['summary']['supplier'] != null ? $orderDetail['summary']['supplier']['id'] : 'unknown';

        if(isset($this->results['suppliers'][$supplier]['products'][$orderDetail['product']['id']])) {
            $this->results['suppliers'][$supplier]['products'][$orderDetail['product_id']]['quantity'] += (int) $orderDetail['sum_quantity'] / (int) $orderDetail['product']['sku']['qty'];
            $this->results['suppliers'][$supplier]['products'][$orderDetail['product_id']]['total'] += (float) $orderDetail['sum_total'];
        } else {
            $this->results['suppliers'][$supplier]['products'][$orderDetail['product']['id']] = [
                'id' => $orderDetail['product']['id'],
                'product_name' => $orderDetail['product']['displayname'],
                'department_name' => $orderDetail['product']['department']['displayname'],
                'sku_name' => $orderDetail['product']['sku']['displayname'],
                'price' => (float) $orderDetail['price'],
                'total' => (float) $orderDetail['sum_total'],
                'quantity' => (int) $orderDetail['sum_quantity'] / (int) $orderDetail['product']['sku']['qty']
            ];
        }
    }

    private function calculateSummary(): void
    {
        foreach($this->results['suppliers'] as $key => $supplier) {
            $total = 0;

            foreach($supplier['products'] as $product) {
                $total += $product['total'];
            }
            $this->results['suppliers'][$key]['summary'] = [
                'blank' => '',
                'total' => $total
            ];

            $this->results['summary']['total'] += $total;
        }

        if ($this->supplier != null || count($this->results['suppliers']) === 1) {
            $this->results['summary']['enabled'] = false;
        }
    }
}
