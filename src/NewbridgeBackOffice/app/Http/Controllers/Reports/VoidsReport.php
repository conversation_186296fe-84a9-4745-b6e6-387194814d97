<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Sites;
use PDF;

class VoidsReport extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->tables = [
            "Voids Report" => [
                "columns" => [
                    "Date" => [null, 'datetime', 'string'],
                    "Product" => [null, 'displayname', 'string'],
                    "Amount" => [null, 'net_value', 'decimal'],
                    "Reason" => [null, 'void_reason', 'string'],
                    "Clerk" => ['clerk', 'full_name', 'string'],
                    "View Transaction" => [null, 'trans_id', 'transaction', 'View Transaction', '/transactions?transaction_id=']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'voids',
                    'summary_result' => 'summary'
                ],
                "summary" => [
                    "blank" => [null, 'blank', 'string'],
                    "Total" => [null, 'total', 'decimal'],
                    "blank1" => [null, 'blank', 'string'],
                    "blank2" => [null, 'blank', 'string'],
                    "blank3" => [null, 'blank', 'string'],
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->voids();
    }

    public function voids()
    {
        $query = PosTransactionDetail::whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->where('command_type', 5)
            ->with('clerk')
            ->orderBy('datetime', 'DESC')
            ->whereIn('site_num', $this->site_num)
            ->get();

        if (!$query->isEmpty()) {
            $query = $query->toArray();
        }

        $results['voids'] = $query;
        $results['summary'] = [
            'blank' => '',
            'total' => 0
        ];

        foreach ($query as $q) {
            $results['summary']['total'] += $q['net_value'];
        }


        return $results;
    }
}
