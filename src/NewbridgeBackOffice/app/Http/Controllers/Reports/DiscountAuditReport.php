<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CompanyLinks;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\Promotions;
use NewbridgeWeb\Repositories\Sites;
use PDF;

class DiscountAuditReport extends ReportAbstractController
{
    public function setTables($var = null): array
    {
        $this->tables = [
            "Discount Audit Report" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Transaction ID" => [null, 'trans_id', 'string'],
                        "Order #" => ['transaction', 'order_number', 'string'],
                        "Date" => [null, 'created_at', 'date'],
                        "Product Name" => [null, 'displayname', 'string'],
                        "Item Total" => [null, 'gross_value', 'decimal'],
                        "Discount Amount" => [null, 'discount_value', 'decimal'],
                        "Amount Paid" => [null, 'net_value', 'decimal'],
                        "Clerk Name" => ['clerk', 'full_name', 'string'],
                        "View Check" => [null, 'trans_id', 'transaction', 'View Transaction', '/transactions?transaction_id='],
                    ],
                    "data" => [
                        'column_count' => 5,
                        'resultset' => 'transactions',
                        'summary_result' => null
                    ]
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'discounts',
                    'summary_result' => 'discountSummary'
                ],
                "summary" => [
                    "Date" => [null, 'blank', 'string'],
                    "Product Name" => [null, 'blank', 'string'],
                    "Order #" => [null, 'blank', 'string'],
                    "Item Total" => [null, 'sum_gross_value', 'decimal'],
                    "Discount Amount" => [null, 'sum_discount_value', 'decimal'],
                    "Amount Paid" => [null, 'sum_net_value', 'decimal'],
                    "Clerk Name" => [null, 'blank', 'string'],
                    "View Check" => [null, 'blank', 'string'],
                ],
            ],
            "Promotion Audit Report" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Transaction ID" => [null, 'trans_id', 'string'],
                        "Order #" => ['transaction', 'order_number', 'string'],
                        "Date" => [null, 'created_at', 'date'],
                        "Product Name" => [null, 'displayname', 'string'],
                        "Item Total" => [null, 'gross_value', 'decimal'],
                        "Discount Amount" => [null, 'discount_value', 'decimal'],
                        "Amount Paid" => [null, 'net_value', 'decimal'],
                        "Clerk Name" => ['clerk', 'full_name', 'string'],
                        "View Check" => [null, 'trans_id', 'transaction', 'View Transaction', '/transactions?transaction_id=']
                    ],
                    "data" => [
                        'column_count' => 5,
                        'resultset' => 'transactions',
                        'summary_result' => null
                    ]
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'promotions',
                    'summary_result' => 'promotionSummary'
                ],
                "summary" => [
                    "Date" => [null, 'blank', 'string'],
                    "Product Name" => [null, 'blank', 'string'],
                    "Order #" => [null, 'blank', 'string'],
                    "Item Total" => [null, 'sum_promo_gross_value', 'decimal'],
                    "Discount Amount" => [null, 'sum_promo_discount_value', 'decimal'],
                    "Amount Paid" => [null, 'sum_promo_net_value', 'decimal'],
                    "Clerk Name" => [null, 'blank', 'string'],
                    "View Check" => [null, 'blank', 'string'],
                ],
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results['discounts'] = $this->discounts();
        $results['promotions'] = $this->promotions();
        $results['discountSummary'] = $this->discountSummary($results['discounts']);
        $results['promotionSummary'] = $this->promotionSummary($results['promotions']);


        return $results;
    }

    public function discounts()
    {
        $companies = CompanyLinks::links($this->company_id);

        return Discounts::with(['transactions' => function ($q) {
            $q->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereNotIn('command_type', [5,6])
                ->whereIn('site_num', $this->site_num)
                ->where('company_id', $this->company_id)
                ->with('clerk')
                ->with(['transaction' => function ($q) {
                    $q->select('id', 'order_number');
                }]);
            if ($this->terminal != '') {
                $q->whereIn('terminal_num', $this->terminal);
            }
        }])
            ->whereIn('company_id', $companies)
            ->withTrashed()
            ->get();
    }

    public function discountSummary($results)
    {
        $result = [
            'sum_gross_value' => 0,
            'sum_discount_value' => 0,
            'sum_net_value' => 0,
            'blank' => ''
        ];

        foreach ($results as $discount) {
            foreach ($discount['transactions'] as $r) {
                $result['sum_gross_value'] += $r['gross_value'];
                $result['sum_discount_value'] += $r['discount_value'];
                $result['sum_net_value'] += $r['net_value'];
            }
        }

        return $result;
    }

    public function promotions()
    {
        $companies = CompanyLinks::links($this->company_id);

        return Promotions::with(['transactions' => function ($q) {
            $q->whereBetween('finalised_date', [$this->start, $this->end])
                ->whereNotIn('command_type', [5,6])
                ->whereIn('site_num', $this->site_num)
                ->where('company_id', $this->company_id)
                ->with('clerk')
                ->with(['transaction' => function ($q) {
                    $q->select(['id', 'order_number']);
                }]);
            if ($this->terminal != '') {
                $q->whereIn('terminal_num', $this->terminal);
            }
        }])
            ->whereIn('company_id', $companies)
            ->withTrashed()
            ->get();
    }

    public function promotionSummary($results)
    {
        $result = [
            'sum_promo_gross_value' => 0,
            'sum_promo_discount_value' => 0,
            'sum_promo_net_value' => 0,
            'blank' => ''
        ];

        foreach ($results as $promotions) {
            foreach ($promotions['transactions'] as $r) {
                $result['sum_promo_gross_value'] += $r['gross_value'];
                $result['sum_promo_discount_value'] += $r['discount_value'];
                $result['sum_promo_net_value'] += $r['net_value'];
            }
        }

        return $result;
    }

}
