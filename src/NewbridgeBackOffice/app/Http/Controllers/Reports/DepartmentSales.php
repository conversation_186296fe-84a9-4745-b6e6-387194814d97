<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Sites;
use PDF;

class DepartmentSales extends ReportAbstractController
{
    public $department = null;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Department Sales" => [
                "columns" => [
                    "Department" => ['department', 'displayname', 'string'],
                    "Gross Sales Value" => [null, 'sum_net_value', 'decimal'],
                    "# of Sales" => [null, 'count', 'int']
                ],
                "data" => [
                    'column_count' => 4,
                    'resultset' => 'departments',
                    'summary_result' => 'department_summary'
                ],
                "summary" => [
                    "Value" => [null, 'sum_net_value', 'decimal'],
                    "Count" => [null, 'count', 'int'],
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->department = $report_data['department'];

        return $this->departmentSales();
    }

    public function departmentSales()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as count, SUM(discount_value) as sum_discount_value'))
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with(['product' => function ($q) {
                $q->withTrashed();
            }])
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0,6]);

        if ($this->department !== null) {
            $query->whereIn('department_guid', $this->department);
        }

        if ($this->terminal != '') {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        $query = $query->groupBy('department_guid')->get();

        $refundCount = PosTransactionDetail::select(DB::raw('count(*) as refund_count, department_guid, company_id, trans_id, command, command_type'))
            ->where('company_id', $this->company_id)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [6])
            ->where('department_guid', '!=', '')
            ->groupBy('department_guid')
            ->whereIn('site_num', $this->site_num)
            ->get();


        $refunds = [];
        if ($refundCount !== null) {
            foreach ($refundCount as $rc) {
                $refunds[$rc->department_guid] = $rc->refund_count;
            }
        }

        $result = [];
        $result['departments'] = $query;
        $result['department_summary'] = [
            'count' => 0,
            'sum_net_value' => 0,
            'sum_gross_value' => 0
        ];

        foreach ($query as $q) {
            $result['department_summary']['count'] += isset($refunds[$q->department_guid]) ? $q->count - abs($refunds[$q->department_guid]) : $q->count;
            $result['department_summary']['sum_gross_value'] += $q->sum_gross_value;
            $result['department_summary']['sum_net_value'] += $q->sum_net_value;
        }

        return $result;
    }
}
