<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Recipes;
use NewbridgeWeb\Repositories\Sites;

class RecipeCost extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->tables = [
            "Recipe Costs" => [
                "columns" => [
                    "" => [null, ['name'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Ingredient" => [null, 'displayname', 'string'],
                        "Quantity" => [null, 'quantity', 'string'],
                        "Cost" => [null, 'costprice', 'decimal5']
                    ],
                    "data" => [
                        'column_count' => 2,
                        'resultset' => 'products',
                        'summary_result' => 'summary'
                    ],
                    'summary' => [
                        [null, 'blank', 'string'],
                        [null, 'total', 'decimal5']
                    ]
                ],
                "data" => [
                    'column_count' => 2,
                    'resultset' => 'recipes',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->recipes();
    }

    public function recipes()
    {
        $query = Recipes::where('company_id', $this->company_id)
            ->with(['ingredients' => function ($q) {
                $q->with(['product' => function ($q) {
                    $q->with('sku')->withTrashed();
                }])
                    ->with('recipe');
            }])
            ->get();

        $results = [];
        $results['recipes'] = [];

        foreach ($query as $q) {
            $results['recipes'][$q->id] = [
                'name' => $q->name,
                'id' => $q->id
            ];

            $results['recipes'][$q->id]['summary'] = [
                'blank' => '',
                'total' => 0
            ];

            foreach ($q->ingredients as $i) {
                if ($i->product != null) {
                    $displayname = $i->product->deleted_at === null ? $i->product->displayname : $i->product->displayname . ' (Deleted)';

                    $results['recipes'][$q->id]['products'][] = [
                        'id' => $i->id,
                        'quantity' => $i->quantity,
                        'displayname' => $displayname,
                        'costprice' => $i->product->costprice * $i->quantity
                    ];

                    $results['recipes'][$q->id]['summary']['total'] += ($i->product->costprice * $i->quantity);
                } elseif ($i->recipe != null) {
                    $results['recipes'][$q->id]['products'][] = [
                        'id' => $i->recipe->id,
                        'quantity' => $i->quantity,
                        'displayname' => '(Recipe) ' . $i->recipe->name,
                        'costprice' => $i->recipe->cost * $i->quantity
                    ];

                    $results['recipes'][$q->id]['summary']['total'] += ($i->recipe->cost * $i->quantity);
                }
            }

            if (!isset($results['recipes'][$q->id]['products'])) {
                unset($results['recipes'][$q->id]);
            }
        }

        return $results;
    }
}
