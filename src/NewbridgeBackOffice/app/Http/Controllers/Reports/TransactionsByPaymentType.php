<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PaymentTypes;
use NewbridgeWeb\Repositories\PosTransactionPayment;

class TransactionsByPaymentType extends ReportAbstractController
{
    public function setTables($var = null): array
    {
        $this->tables = [
            "Transactions" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Time" => [null, "finalised_date", "date"],
                        "Transaction ID" => ["transaction", "id", "string"],
                        "Order #" => ["transaction", "order_number", "string"],
                        "Site" => [null, "site_num", "int"],
                        "Terminal" => [null, "terminal_num", "int"],
                        "Amount" => [null, "amount", "decimal"]
                    ],
                    "data" => [
                        "column_count" => 4,
                        "resultset" => "transactions",
                        "summary_result" => "transaction_summary"
                    ],
                    "summary" => [
                        [null, 'total', 'decimal']
                    ]
                ],
                "data" => [
                    "column_count" => 8,
                    "resultset" => "methods",
                    "summary_result" => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->getTransactionDetails();
    }

    public function getTransactionDetails(): array
    {
        $results = [];

        $paymentMethods = $this->getPaymentMethods();

        $query = PosTransactionPayment::with("transaction")
            ->where("company_id", $this->company_id)
            ->whereBetween("finalised_date", [$this->start, $this->end])
            ->where("amount", "!=", 0.00);

        if ($this->site_num != null) {
            $query = $query->whereIn("site_num", $this->site_num);
        }

        if ($this->terminal != null) {
            $query = $query->where("terminal_num", $this->terminal);
        }

        $payments = $query->get();

        foreach ($paymentMethods as $paymentMethod) {
            $results['methods'][$paymentMethod['id']]['id'] = $paymentMethod['id'];
            $results['methods'][$paymentMethod['id']]['displayname'] = $paymentMethod['displayname'];
            $results['methods'][$paymentMethod['id']]['transactions'] = [];
            $results['methods'][$paymentMethod['id']]['transaction_summary']['total'] = 0.00;

            foreach ($payments as $payment) {
                $payment->amount = (float) $payment->amount;
                if ($payment->method_type == $paymentMethod['id']) {
                    $results['methods'][$paymentMethod['id']]['transactions'][] = $payment;
                    $results['methods'][$paymentMethod['id']]['transaction_summary']['total'] += $payment->amount;
                }
            }
        }

        return $results;
    }

    public function getPaymentMethods(): array
    {
        $methodTypes = PaymentTypes::all();

        $result = [];

        foreach ($methodTypes as $k => $type) {
            $methods = Payments::where('method_type', $type->id)->where('company_id', $this->company_id)->pluck('CommandUID');

            $result[$k] = [
                'id' => $type->id,
                'guid' => [$methods],
                'displayname' => $type->name,
                'transactions' => []
            ];
        }

        return $result;
    }
}
