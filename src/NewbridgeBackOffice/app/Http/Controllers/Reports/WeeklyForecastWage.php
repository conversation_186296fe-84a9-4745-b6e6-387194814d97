<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Clerks\Clocking;
use NewbridgeWeb\Repositories\Clerks\ShiftTypes;
use NewbridgeWeb\Repositories\CompanyClerkRoles;

class WeeklyForecastWage extends ReportAbstractController
{
    public $days;
    private $currentStart;
    private $currentEnd;
    public $areas;

    public function setTables($var = null): array
    {
        $this->currencySymbol = CurrencyHelper::symbol($this->company_id);

        $this->tables = [
            "Weekly Forecast Wages Report" => [
                "columns" => [
                    "" => [null, ['area_name'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Clerk" => [null, 'full_name', 'string', 'width' => '300px'],
                        "Rate" => [null, 'rate', 'decimal'],
                        "Mon" => [null, 1, 'string'],
                        "Tues" => [null, 2, 'string'],
                        "Weds" => [null, 3, 'string'],
                        "Thur" => [null, 4, 'string'],
                        "Fri" => [null, 5, 'string'],
                        "Sat" => [null, 6, 'string'],
                        "Sun" => [null, 7, 'string'],
                        "Days Worked" => [null, 'days', 'string'],
                        "Total" => [null, 'total', 'string'],
                        "Total ".$this->currencySymbol => [null, 'pay_total', 'string']
                    ],
                    "data" => [
                        'column_count' => 11,
                        'resultset' => 'clerks',
                        'summary_result' => 'summary'
                    ],
                    "summary" => [
                        "BLANK" => [null, 'blank', 'string'],
                        "BLANK1" => [null, 'blank', 'string'],
                        "BLANK11" => [null, 'blank', 'string'],
                        "BLANK2" => [null, 'blank', 'string'],
                        "BLANK3" => [null, 'blank', 'string'],
                        "BLANK4" => [null, 'blank', 'string'],
                        "BLANK5" => [null, 'blank', 'string'],
                        "BLANK6" => [null, 'blank', 'string'],
                        "BLANK9" => [null, 'blank', 'string'],
                        "BLANK7" => [null, 'total', 'string'],
                        "BLANK8" => [null, 'pay_total', 'decimal']
                    ],
                ],
                "data" => [
                    'column_count' => 11,
                    'resultset' => 'area',
                    'summary_result' => 'total_summary'
                ],
                "summary" => [
                    "BLANK" => [null, 'blank', 'string'],
                    "BLANK1" => [null, 'blank', 'string'],
                    "BLANK11" => [null, 'blank', 'string'],
                    "BLANK2" => [null, 'blank', 'string'],
                    "BLANK3" => [null, 'blank', 'string'],
                    "BLANK4" => [null, 'blank', 'string'],
                    "BLANK5" => [null, 'blank', 'string'],
                    "BLANK6" => [null, 'blank', 'string'],
                    "BLANK9" => [null, 'blank', 'string'],
                    "Total Hours" => [null, 'total_hours', 'string'],
                    "Total Wages" => [null, 'total_wages', 'decimal']
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->getRotaShifts();
    }

    private function getRotaShifts()
    {
        $results = [];
        $results['total_summary'] = [
            'blank' => '',
            'total_wages' => 0,
            'total_hours' => 0
        ];

        $clerkAreas = ClerkAreas::where('company_id', $this->company_id)->get();
        $roles = CompanyClerkRoles::where('company_id', $this->company_id)->get();
        $defaultType = ShiftTypes::where('company_id', $this->company_id)
            ->where('isdefault', 1)->first();

        foreach ($clerkAreas as $k => $this->clerkArea) {
            $results['area'][$k]['area_name'] = $this->clerkArea->area_name;
            $results['area'][$k]['guid'] = $this->clerkArea->guid;
            $results['area'][$k]['summary']  = [
                'blank' => '' ,
                'total' => 0,
                'pay_total' => 0
            ];
            $results['area'][$k]['id'] = $this->clerkArea->id;

            foreach ($roles as $role) {
                $clerks = Clerks::where('company_id', $this->company_id)
                    ->with('jobRoles')
                    ->whereIn('area_guid', $this->areas)
                    ->whereHas('clockings', function ($q) use ($role, $defaultType) {
                        $q->where(function ($q1) use ($role, $defaultType) {
                            $q1->whereBetween('in', [$this->start, $this->end])
                                ->whereNotNull('out')
                                ->whereIn('site_num', $this->site_num)
                                ->where('area_guid', $this->clerkArea->guid)
                                ->where('is_rota', 1)
                                ->where('shift_type', $defaultType->id)
                                ->where('role_guid', $role->guid);
                        })
                            ->orWhere(function ($q2) use ($role, $defaultType) {
                                $q2->whereNotNull('out')
                                    ->whereBetween('in', [$this->start, $this->end])
                                    ->whereIn('site_num', $this->site_num)
                                    ->where('area_guid', $this->clerkArea->guid)
                                    ->where('shift_type', '!=', $defaultType->id)
                                    ->where('role_guid', $role->guid)
                                    ->where('is_rota', 1);
                            });
                    })
                    ->get();

                foreach ($clerks as $clerk) {
                    $days = 7;
                    $currentDay = 1;
                    $this->currentStart = Carbon::parse($this->start)->startOfDay();
                    $this->currentEnd = Carbon::parse($this->start)->endOfDay();

                    $clerkResult = [
                        'full_name' => $clerk->full_name . ' ('.$role->displayname.')',
                        'rate' => 0,
                        'id' => $this->clerkArea->id,
                        1 => 0,
                        2 => 0,
                        3 => 0,
                        4 => 0,
                        5 => 0,
                        6 => 0,
                        7 => 0,
                        'total' => 0,
                        'days' => 0,
                        'pay_total' => 0,
                    ];

                    while ($currentDay <= $days) {
                        $dayNum = Carbon::parse($this->currentStart)->dayOfWeekIso;

                        $clockings = Clocking::where(function ($q1) use ($role, $clerk, $defaultType) {
                            $q1->where('employee_guid', $clerk->guid)
                                ->with('role')
                                ->whereBetween('in', [$this->currentStart, $this->currentEnd])
                                ->whereNotNull('out')
                                ->whereIn('site_num', $this->site_num)
                                ->where('is_rota', 1)
                                ->where('shift_type', $defaultType->id)
                                ->where('role_guid', $role->guid);
                        })->orWhere(function ($q2) use ($role, $clerk, $defaultType) {
                            $q2->where('employee_guid', $clerk->guid)
                                ->with('role')
                                ->whereBetween('in', [$this->currentStart, $this->currentEnd])
                                ->whereNotNull('out')
                                ->whereIn('site_num', $this->site_num)
                                ->where('is_rota', 1)
                                ->where('shift_type', '!=', $defaultType->id)
                                ->where('role_guid', $role->guid);
                        })->get();

                        if (!$clockings->isEmpty()) {
                            $clockings = $clockings->toArray();
                            $count = count($clockings);

                            if (count($clockings) > 1) {
                                $i = 1;
                                foreach ($clockings as $clock) {
                                    $clerkResult['pay_total'] += $clock['total_pay'];
                                    $clerkResult['rate'] = $clock['pay_rate'] != null ? $clock['pay_rate'] : $clerk->rate ;

                                    $shiftType = '';
                                    $shiftColor = 'none';
                                    $fontColor = '#000000;';

                                    if ($clock['shift_type'] != null && $clock['shift_type'] != 0) {
                                        $shiftTypes = ShiftTypes::select('short_type', 'color', 'timed')
                                            ->where('company_id', $this->company_id)
                                            ->where('id', $clock['shift_type'])->first();

                                        $shiftType = '(' . $shiftTypes->short_type . ')';
                                        $shiftColor = $shiftTypes->color;
                                        $fontColor = '#ffffff;';

                                        if ($clock['shift_type'] == $defaultType->id) {
                                            if ($i <= $count) {
                                                if (is_numeric($clerkResult[$dayNum])) {
                                                    $clerkResult[$dayNum] += (float) $clock['hours_worked'];
                                                }
                                            }
                                            if ($i == $count) {
                                                $clerkResult['days']++;
                                            }
                                        } elseif ($shiftTypes->timed == 1) {
                                            if (is_numeric($clerkResult[$dayNum])) {
                                                $clerkResult[$dayNum] += (float) $clock['hours_worked'];
                                            }
                                            if ($i == $count) {
                                                $clerkResult[$dayNum] = (float) $clock['hours_worked'].' ('.$shiftTypes->short_type.')';
                                            }
                                        } else {
                                            $clerkResult[$dayNum] = $shiftTypes->short_type;
                                            //                                        $clerkResult['days']--;
                                        }
                                    } else {
                                        if ($i <= $count) {
                                            $clerkResult[$dayNum] += (float) $clock['hours_worked'];
                                        }
                                    }

                                    $i++;
                                }
                            } else {
                                foreach ($clockings as $clock) {
                                    $clerkResult['pay_total'] += $clock['total_pay'];
                                    $clerkResult['rate'] = $clock['pay_rate'] != null ? $clock['pay_rate'] : $clerk->rate ;

                                    $shiftType = '';
                                    $shiftColor = 'none';
                                    $fontColor = '#000000';

                                    if ($clock['shift_type'] != null && $clock['shift_type'] != 0) {
                                        $shiftTypes = ShiftTypes::select('id', 'short_type', 'color', 'timed')
                                            ->where('id', $clock['shift_type'])->first();

                                        $shiftType = '(' . $shiftTypes->short_type . ')';
                                        $shiftColor = $shiftTypes->color;
                                        $fontColor = '#ffffff';

                                        if ($clock['shift_type'] == $defaultType->id) {
                                            $clerkResult[$dayNum] = (float) $clock['hours_worked'];
                                            $clerkResult['days']++;
                                        } elseif ($shiftTypes->timed == 1) {
                                            $clerkResult[$dayNum] = (float) $clock['hours_worked']. ' ('.$shiftTypes->short_type.')';
                                        } else {
                                            $clerkResult[$dayNum] = $shiftTypes->short_type;
                                            //
                                        }
                                    } else {
                                        $clerkResult[$dayNum] = (float) $clock['hours_worked'];
                                    }
                                }
                            }
                        }

                        $this->currentStart = Carbon::parse($this->currentStart)->addDay();
                        $this->currentEnd = Carbon::parse($this->currentEnd)->addDay();
                        $currentDay++;

                        if (is_numeric($clerkResult[$dayNum])) {
                            $clerkResult['total'] += $clerkResult[$dayNum];
                        }
                    }

                    if ($clerk->salary == 1) {
                        $clerkResult['pay_total'] = (($clerk->rate * 365) / 52);
                    }

                    $results['area'][$k]['summary']['total'] += $clerkResult['total'];
                    $results['area'][$k]['summary']['pay_total'] += $clerkResult['pay_total'];


                    $clerkResult['pay_total'] = $this->currencySymbol.number_format($clerkResult['pay_total'], 2);
                    $results['area'][$k]['clerks'][] = $clerkResult;
                }
            }

            $results['total_summary']['total_wages'] += $results['area'][$k]['summary']['pay_total'];
            $results['total_summary']['total_hours'] += $results['area'][$k]['summary']['total'];
        }



        if (isset($results['area'])) {
            foreach ($results['area'] as $k => $result) {
                if (!isset($result['clerks'])) {
                    unset($results['area'][$k]);
                }
            }
        }

        return $results;
    }

    public function getDays($start_date)
    {
        $days = [];

        $no_of_days = 6;
        $current_day = 0;

        while ($current_day <= $no_of_days) {
            $day = Carbon::parse($start_date)->startOfWeek()->addDays($current_day)->format('D');

            $days[] = (string) $day;

            $current_day++;
        }

        return $days;
    }
}
