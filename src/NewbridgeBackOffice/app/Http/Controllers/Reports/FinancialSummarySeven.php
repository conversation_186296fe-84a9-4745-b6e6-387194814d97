<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\PosTransactionPayment;

class FinancialSummarySeven extends ReportAbstractController
{
    public $department;
    public $tableDays;

    public function setTables($days = [1,2,3,4,5,6,7], $var = null): array
    {
        $this->tables = [
            "Department Sales" => [
                "columns" => [
                    "Department" => [null, 'displayname', 'string', 'width' => '250px'],
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
                "data" => [
                    'column_count' => 7,
                    'resultset' => 'daily_departments',
                    'summary_result' => 'daily_departments_summary',
                ],
                "summary" => [
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
            ],
            "General Income" => [
                "columns" => [
                    "Income Type" => [null, 'displayname', 'string', 'width' => '250px'],
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'general_data',
                    'summary_result' => 'general_data_summary',
                ],
                "summary" => [
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
            ],
            "Discounts" => [
                "columns" => [
                    "Discount Type" => [null, 'displayname', 'string', 'width' => '250px'],
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'discounts',
                    'summary_result' => 'discounts_summary',
                ],
                "summary" => [
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
            ],
            "Expenses" => [
                "columns" => [
                    "Expense Type" => [null, 'displayname', 'string', 'width' => '250px'],
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'expenses',
                    'summary_result' => 'expenses_summary',
                ],
                "summary" => [
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
            ],
            "Payment Methods" => [
                "columns" => [
                    "Payment Method" => [null, 'displayname', 'string', 'width' => '250px'],
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'payments',
                    'summary_result' => 'payments_summary',
                ],
                "summary" => [
                    "day1" => [null, $days[0], 'decimal'],
                    "day2" => [null, $days[1], 'decimal'],
                    "day3" => [null, $days[2], 'decimal'],
                    "day4" => [null, $days[3], 'decimal'],
                    "day5" => [null, $days[4], 'decimal'],
                    "day6" => [null, $days[5], 'decimal'],
                    "day7" => [null, $days[6], 'decimal']
                ],

            ],
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        \Log::info($this->start);
        $this->days = $report_data['days'];
        $this->tableDays = $this->days;

        foreach ($this->days as $k => $v) {
            switch($v) {
                case 'Sun':
                    $this->tableDays[$k] = 1;
                    break;
                case 'Mon':
                    $this->tableDays[$k] = 2;
                    break;
                case 'Tue':
                    $this->tableDays[$k] = 3;
                    break;
                case 'Wed':
                    $this->tableDays[$k] = 4;
                    break;
                case 'Thu':
                    $this->tableDays[$k] = 5;
                    break;
                case 'Fri':
                    $this->tableDays[$k] = 6;
                    break;
                case 'Sat':
                    $this->tableDays[$k] = 7;
                    break;
            }
        }

        $results = [];
        $results['daily_departments'] = $this->dailyDepartmentSales();
        $results['general_data'] = $this->generalSales();
        $results['discounts'] = $this->discountSales();
        $results['expenses'] = $this->expenses();
        $results['payments'] = $this->paymentType();
        $results['daily_departments_summary'] = $this->dailyDepartmentSalesSummary();
        $results['general_data_summary'] = $this->generalSalesSummary();
        $results['discounts_summary'] = $this->discountSalesSummary();
        $results['expenses_summary'] = $this->expensesSummary();
        $results['payments_summary'] = $this->paymentTypeSummary();

        return $results;
    }

    public function getDays($start_date)
    {
        $days = [];

        $no_of_days = 6;
        $current_day = 0;

        while ($current_day <= $no_of_days) {
            $day = Carbon::parse($start_date)->addDays($current_day)->format('D');
            $days[] = (string) $day;

            $current_day++;
        }

        return $days;
    }

    private function getDepartments()
    {
        return Departments::where('company_id', $this->company_id)->orderBy('displayname', 'DESC')->get();
    }

    private function paymentType()
    {
        $results = [];

        $paymentTypes = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value, DAYOFWEEK(finalised_date) as day'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->with(['method' => function ($q) {
                $q->withTrashed();
            }])
            ->whereNotIn('method_type', [1,4])
            ->where('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('method_guid')
            ->groupBy('day')
            ->get();

        $cash = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value, DAYOFWEEK(finalised_date) as day'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with(['method' => function ($q) {
                $q->withTrashed();
            }])
            ->where('method_type', 1)
            ->groupBy('method_type')
            ->orderBy('finalised_date', 'DESC')
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->groupBy('day')
            ->get();

        $cashBack = PosTransactionDetail::select(DB::raw('SUM(gross_value) as sum_total, COUNT(*) as count, DAYOFWEEK(finalised_date) as day'))
            ->where('command', 37)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('day')
            ->get()->keyBy('day')->toArray();

        foreach ($paymentTypes as $paymentType) {
            if (!isset($results[$paymentType->method->DisplayName])) {
                $results[$paymentType->method->DisplayName] = [];
                $results[$paymentType->method->DisplayName]['displayname'] = $paymentType->method->DisplayName;
            }
            if (!isset($results[$paymentType->method->DisplayName][$paymentType->day])) {
                $results[$paymentType->method->DisplayName][$paymentType->day] = (float) $paymentType->sales_value;
            } else {
                $results[$paymentType->method->DisplayName][$paymentType->day] += (float) $paymentType->sales_value;
            }
        }

        foreach ($cash as $c) {
            if (!isset($results['Cash'])) {
                $results['Cash'] = [];
                $results['Cash']['displayname'] = 'Cash';
            }
            if (!isset($results['Cash'][$c->day])) {
                $results['Cash'][$c->day] = (float) $c->sales_value;
            } else {
                $results['Cash'][$c->day] += (float) $c->sales_value;
            }
        }

        foreach ($results as $k => $result) {
            if (!isset($results[$k][1])) {
                $results[$k][1] = 0.00;
            }
            if (!isset($results[$k][2])) {
                $results[$k][2] = 0.00;
            }
            if (!isset($results[$k][3])) {
                $results[$k][3] = 0.00;
            }
            if (!isset($results[$k][4])) {
                $results[$k][4] = 0.00;
            }
            if (!isset($results[$k][5])) {
                $results[$k][5] = 0.00;
            }
            if (!isset($results[$k][6])) {
                $results[$k][6] = 0.00;
            }
            if (!isset($results[$k][7])) {
                $results[$k][7] = 0.00;
            }
        }

        if (!empty($results['Cash'])) {
            foreach ($results['Cash'] as $k => $day) {
                if (isset($cashBack[$k])) {
                    $cashback = (float) $cashBack[$k]['sum_total'];
                    $results['Cash'][$k] = ((float) $results['Cash'][$k] - $cashback);
                }
            }
        }

        return $results;
    }

    private function expenses()
    {
        $results = [];

        $expenses = PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count, displayname, DAYOFWEEK(finalised_date) as day'))
            ->where('command', 88)
            ->where('command_type', 1)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('day')
            ->groupBy('displayname')
            ->get();

        foreach ($expenses as $expense) {
            if (!isset($results[$expense->displayname])) {
                $results[$expense->displayname] = [];
                $results[$expense->displayname]['displayname'] = $expense->displayname;
            }
            if (!isset($results[$expense->displayname][$expense->day])) {
                $results[$expense->displayname][$expense->day] = (float) $expense->sum_total;
            } else {
                $results[$expense->displayname][$expense->day] += (float) $expense->sum_total;
            }
        }

        foreach ($results as $k => $result) {
            if (!isset($results[$k][1])) {
                $results[$k][1] = 0.00;
            }
            if (!isset($results[$k][2])) {
                $results[$k][2] = 0.00;
            }
            if (!isset($results[$k][3])) {
                $results[$k][3] = 0.00;
            }
            if (!isset($results[$k][4])) {
                $results[$k][4] = 0.00;
            }
            if (!isset($results[$k][5])) {
                $results[$k][5] = 0.00;
            }
            if (!isset($results[$k][6])) {
                $results[$k][6] = 0.00;
            }
            if (!isset($results[$k][7])) {
                $results[$k][7] = 0.00;
            }
        }

        return $results;
    }

    private function generalSales()
    {
        $results = [];

        $general = PosTransactionDetail::select(DB::raw('pos_transaction_details.command, pos_transaction_details.command_type, sys_int_commands.DisplayName as name, pos_transaction_details.datetime, SUM(pos_transaction_details.gross_value) as sum_gross_value, (SUM(pos_transaction_details.net_value) - sum(pos_transaction_details.tax_value)) as sum_net_value, COUNT(pos_transaction_details.id) as count, DAYOFWEEK(finalised_date) as day'))
            ->whereBetween('pos_transaction_details.finalised_date', [$this->start, $this->end])
            ->where('pos_transaction_details.company_id', $this->company_id)
            ->whereIn('pos_transaction_details.site_num', $this->site_num)
            ->whereIn('pos_transaction_details.command', [61,37,44,46])
            ->where('pos_transaction_details.command_type', '!=', 5)
            ->leftJoin('sys_int_commands', 'sys_int_commands.Command', 'pos_transaction_details.command')
            ->orderBy('pos_transaction_details.finalised_date', 'DESC')
            ->groupBy('day')
            ->groupBy('pos_transaction_details.command')
            ->get();

        foreach ($general as $q) {
            $results[$q->command]['displayname'] = $q->name;

            if ($general != null) {
                $results[$q->command][$q->day] = (float) $q->sum_gross_value;
            } else {
                $results[$q->command][$q->day] = 0.00;
            }
        }

        foreach ($results as $k => $result) {
            if (!isset($results[$k][1])) {
                $results[$k][1] = 0.00;
            }
            if (!isset($results[$k][2])) {
                $results[$k][2] = 0.00;
            }
            if (!isset($results[$k][3])) {
                $results[$k][3] = 0.00;
            }
            if (!isset($results[$k][4])) {
                $results[$k][4] = 0.00;
            }
            if (!isset($results[$k][5])) {
                $results[$k][5] = 0.00;
            }
            if (!isset($results[$k][6])) {
                $results[$k][6] = 0.00;
            }
            if (!isset($results[$k][7])) {
                $results[$k][7] = 0.00;
            }
        }

        return $results;
    }

    private function discountSales()
    {
        $results = [];

        $discountSales = PosTransactionDetail::select(DB::raw('*, COUNT(*) as count, SUM(discount_value) as sum_total, DAYOFWEEK(datetime) as day'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with('discount')
            ->where('discount_value', '<', 0)
            ->whereIn('command', [9,10])
            ->where('command_type', '!=', 5)
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('discount_guid')
            ->groupBy('day')
            ->get();

        foreach ($discountSales as $k => $q) {
            $results[$k]['displayname'] = $q->discount->discount_method_string;

            if ($discountSales != null) {
                $results[$k][$q->day] = (float) $q->sum_total;
            } else {
                $results[$k][$q->day] = 0.00;
            }
        }

        foreach ($results as $k => $result) {
            if (!isset($results[$k][1])) {
                $results[$k][1] = 0.00;
            }
            if (!isset($results[$k][2])) {
                $results[$k][2] = 0.00;
            }
            if (!isset($results[$k][3])) {
                $results[$k][3] = 0.00;
            }
            if (!isset($results[$k][4])) {
                $results[$k][4] = 0.00;
            }
            if (!isset($results[$k][5])) {
                $results[$k][5] = 0.00;
            }
            if (!isset($results[$k][6])) {
                $results[$k][6] = 0.00;
            }
            if (!isset($results[$k][7])) {
                $results[$k][7] = 0.00;
            }
        }

        return $results;
    }

    private function dailyDepartmentSales()
    {
        $departments = $this->getDepartments();
        $results = [];

        foreach ($departments as $k => $department) {
            $query = PosTransactionDetail::select(
                DB::raw('(sum(pos_transaction_details.gross_value) - abs(sum(pos_transaction_details.discount_value))) as total,
            pos_transaction_details.datetime, SUM(pos_transaction_details.cost_price) as sum_cost_price, SUM(pos_transaction_details.tax_value) as sum_tax_value,
            SUM(pos_transaction_details.gross_value) as sum_gross_value, sum(pos_transaction_details.net_value) as net_value_sum, 
            (SUM(pos_transaction_details.net_value) - sum(pos_transaction_details.tax_value)) as sum_net_value,
            SUM(pos_transaction_details.tax_value) as sum_tax_value, COUNT(pos_transaction_details.id) as count, SUM(pos_transaction_details.discount_value) as sum_discount_value, 
            DAYOFWEEK(pos_transaction_summary.finalised_date) as day')
            )
                ->where('pos_transaction_details.company_id', $this->company_id)
                ->leftJoin('pos_transaction_summary', 'pos_transaction_summary.id', 'pos_transaction_details.trans_id')
                ->whereIn('pos_transaction_details.site_num', $this->site_num)
                ->whereIn('pos_transaction_details.command', [3, 7, 38])
                ->whereIn('pos_transaction_details.command_type', [0,6])
                ->whereNotIn('pos_transaction_details.command_type', [5])
                ->whereBetween('pos_transaction_details.finalised_date', [$this->start, $this->end])
                ->where('pos_transaction_details.department_guid', $department->guid)
                ->orderBy('pos_transaction_details.finalised_date', 'DESC')
                ->groupBy('day')
                ->orderBy('day')
                ->get();

            $results[]['displayname'] = $department->displayname;

            foreach ($query as $q) {
                if ($query != null) {
                    $results[$k][$q->day] = (float) $q->net_value_sum;
                } else {
                    $results[$k][$q->day] = 0.00;
                }
            }

            if (!isset($results[$k][1])) {
                $results[$k][1] = 0.00;
            }
            if (!isset($results[$k][2])) {
                $results[$k][2] = 0.00;
            }
            if (!isset($results[$k][3])) {
                $results[$k][3] = 0.00;
            }
            if (!isset($results[$k][4])) {
                $results[$k][4] = 0.00;
            }
            if (!isset($results[$k][5])) {
                $results[$k][5] = 0.00;
            }
            if (!isset($results[$k][6])) {
                $results[$k][6] = 0.00;
            }
            if (!isset($results[$k][7])) {
                $results[$k][7] = 0.00;
            }
        }

        return $results;
    }

    private function dailyDepartmentSalesSummary()
    {
        $results = [];

        $query = PosTransactionDetail::select(
            DB::raw('(sum(pos_transaction_details.gross_value) - abs(sum(pos_transaction_details.discount_value))) as total,
            pos_transaction_details.datetime, SUM(pos_transaction_details.cost_price) as sum_cost_price, SUM(pos_transaction_details.tax_value) as sum_tax_value,
            SUM(pos_transaction_details.gross_value) as sum_gross_value, sum(pos_transaction_details.net_value) as net_value_sum, 
            (SUM(pos_transaction_details.net_value) - sum(pos_transaction_details.tax_value)) as sum_net_value,
            SUM(pos_transaction_details.tax_value) as sum_tax_value, COUNT(pos_transaction_details.id) as count, SUM(pos_transaction_details.discount_value) as sum_discount_value, 
            DAYOFWEEK(pos_transaction_summary.finalised_date) as day')
        )
            ->where('pos_transaction_details.company_id', $this->company_id)
            ->leftJoin('pos_transaction_summary', 'pos_transaction_summary.id', 'pos_transaction_details.trans_id')
            ->whereIn('pos_transaction_details.site_num', $this->site_num)
            ->whereIn('pos_transaction_details.command', [3, 7, 38])
            ->whereIn('pos_transaction_details.command_type', [0,6])
            ->whereNotIn('pos_transaction_details.command_type', [5])
            ->whereBetween('pos_transaction_details.finalised_date', [$this->start, $this->end])
            ->orderBy('pos_transaction_details.finalised_date', 'DESC')
            ->groupBy('day')
            ->get();

        foreach ($query as $q) {
            $results[$q->day] = (float) $q->net_value_sum;
        }

        if (!isset($results[1])) {
            $results[1] = 0.00;
        }
        if (!isset($results[2])) {
            $results[2] = 0.00;
        }
        if (!isset($results[3])) {
            $results[3] = 0.00;
        }
        if (!isset($results[4])) {
            $results[4] = 0.00;
        }
        if (!isset($results[5])) {
            $results[5] = 0.00;
        }
        if (!isset($results[6])) {
            $results[6] = 0.00;
        }
        if (!isset($results[7])) {
            $results[7] = 0.00;
        }

        return $results;
    }

    private function generalSalesSummary()
    {
        $results = [];

        $general = PosTransactionDetail::select(DB::raw('pos_transaction_details.command, sys_int_commands.DisplayName as name, pos_transaction_details.datetime, SUM(pos_transaction_details.gross_value) as sum_gross_value, (SUM(pos_transaction_details.net_value) - sum(pos_transaction_details.tax_value)) as sum_net_value, COUNT(pos_transaction_details.id) as count, DAYOFWEEK(datetime) as day'))
            ->whereBetween('pos_transaction_details.finalised_date', [$this->start, $this->end])
            ->where('pos_transaction_details.company_id', $this->company_id)
            ->whereIn('pos_transaction_details.site_num', $this->site_num)
            ->whereIn('pos_transaction_details.command', [61,37,44,46])
            ->leftJoin('sys_int_commands', 'sys_int_commands.Command', 'pos_transaction_details.command')
            ->orderBy('pos_transaction_details.finalised_date', 'DESC')
            ->groupBy('day')
            ->get();


        foreach ($general as $q) {
            $results[$q->day] = (float) $q->sum_net_value;
        }

        if (!isset($results[1])) {
            $results[1] = 0.00;
        }
        if (!isset($results[2])) {
            $results[2] = 0.00;
        }
        if (!isset($results[3])) {
            $results[3] = 0.00;
        }
        if (!isset($results[4])) {
            $results[4] = 0.00;
        }
        if (!isset($results[5])) {
            $results[5] = 0.00;
        }
        if (!isset($results[6])) {
            $results[6] = 0.00;
        }
        if (!isset($results[7])) {
            $results[7] = 0.00;
        }

        return $results;
    }

    private function discountSalesSummary()
    {
        $results = [];

        $discountSales = PosTransactionDetail::select(DB::raw('*, COUNT(*) as count, SUM(discount_value) as sum_total, DAYOFWEEK(datetime) as day'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with('discount')
            ->where('discount_value', '<', 0)
            ->whereIn('command', [9,10])
            ->where('command_type', '!=', 5)
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('day')
            ->get();


        foreach ($discountSales as $q) {
            $results[$q->day] = (float) $q->sum_total;
        }

        if (!isset($results[1])) {
            $results[1] = 0.00;
        }
        if (!isset($results[2])) {
            $results[2] = 0.00;
        }
        if (!isset($results[3])) {
            $results[3] = 0.00;
        }
        if (!isset($results[4])) {
            $results[4] = 0.00;
        }
        if (!isset($results[5])) {
            $results[5] = 0.00;
        }
        if (!isset($results[6])) {
            $results[6] = 0.00;
        }
        if (!isset($results[7])) {
            $results[7] = 0.00;
        }

        return $results;
    }

    private function expensesSummary()
    {
        $results = [];

        $expenses = PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count, displayname, DAYOFWEEK(datetime) as day'))
            ->where('command', 88)
            ->where('command_type', 1)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('day')
            ->get();

        foreach ($expenses as $q) {
            $results[$q->day] = (float) $q->sum_total;
        }

        if (!isset($results[1])) {
            $results[1] = 0.00;
        }
        if (!isset($results[2])) {
            $results[2] = 0.00;
        }
        if (!isset($results[3])) {
            $results[3] = 0.00;
        }
        if (!isset($results[4])) {
            $results[4] = 0.00;
        }
        if (!isset($results[5])) {
            $results[5] = 0.00;
        }
        if (!isset($results[6])) {
            $results[6] = 0.00;
        }
        if (!isset($results[7])) {
            $results[7] = 0.00;
        }


        return $results;
    }

    private function paymentTypeSummary()
    {
        $results = [];

        $paymentTypes = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value, DAYOFWEEK(trans_date) as day'))
            ->where('company_id', $this->company_id)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with(['method' => function ($q) {
                $q->withTrashed();
            }])
            ->whereNotIn('method_type', [1,4])
            ->where('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('day')
            ->get();


        $cash = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value, DAYOFWEEK(trans_date) as day'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with(['method' => function ($q) {
                $q->withTrashed();
            }])
            ->where('method_type', 1)
            ->groupBy('method_type')
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('day')
            ->get();

        $cashBack = PosTransactionDetail::select(DB::raw('SUM(gross_value) as sum_total, COUNT(*) as count, DAYOFWEEK(datetime) as day'))
            ->where('command', 37)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->orderBy('finalised_date', 'DESC')
            ->groupBy('day')
            ->get()->keyBy('day')->toArray();

        foreach ($paymentTypes as $q) {
            if (!isset($results[$q->day])) {
                $results[$q->day] = (float) $q->sales_value;
            } else {
                $results[$q->day] += (float) $q->sales_value;
            }
        }

        foreach ($cash as $q) {
            if (!isset($results[$q->day])) {
                $results[$q->day] = (float) $q->sales_value;
            } else {
                $results[$q->day] += (float) $q->sales_value;
            }
        }


        if (!isset($results[1])) {
            $results[1] = 0.00;
        }
        if (!isset($results[2])) {
            $results[2] = 0.00;
        }
        if (!isset($results[3])) {
            $results[3] = 0.00;
        }
        if (!isset($results[4])) {
            $results[4] = 0.00;
        }
        if (!isset($results[5])) {
            $results[5] = 0.00;
        }
        if (!isset($results[6])) {
            $results[6] = 0.00;
        }
        if (!isset($results[7])) {
            $results[7] = 0.00;
        }

        foreach ($results as $k => $day) {
            if (isset($cashBack[$k])) {
                $cashback = (float) $cashBack[$k]['sum_total'];
                $results[$k] = (float) $results[$k] - $cashback;
            }
        }

        return $results;
    }
}
