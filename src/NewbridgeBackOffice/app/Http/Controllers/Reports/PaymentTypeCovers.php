<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Sites;
use PDF;

class PaymentTypeCovers extends ReportAbstractController
{
    public $payment_methods = null;
    public $subdepartments = [];

    public function setTables($var = null): array
    {
        $this->tables = [
            "Payment Type & Covers" => [
                "columns" => [
                    "Total Sales Value" => [null, 'total', 'decimal'],
                    "# of Covers" => [null, 'covers', 'int'],
                    "Value of Payments" => [null, 'payments', 'decimal']
                ],
                "data" => [
                    'column_count' => 4,
                    'resultset' => 'salescovers'
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->payment_methods = $report_data['payment_methods'];
        $this->subdepartments = $report_data['subdepartment'];

        $results['salescovers'][0] = $this->departmentSales();

        return $results;
    }

    public function departmentSales()
    {
        $query = PosTransaction::select(DB::raw('id, covers, covers, total, finalised_date, table_number, location_name'))
            ->whereHas('payments', function ($q) {
                if ($this->payment_methods != null) {
                    $q->whereIn('method_guid', $this->payment_methods);
                }
            })
            ->with(['payments' => function ($q) {
                if ($this->payment_methods != null) {
                    $q->whereIn('method_guid', $this->payment_methods);
                }
            }])
            ->whereHas('details', function ($q) {
                if ($this->subdepartments != null) {
                    $q->whereIn('sub_department_guid', $this->subdepartments);
                }
            })
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])->get();

        $results = [
            'covers' => 0,
            'total' => 0,
            'payments' => 0
        ];

        foreach ($query as $transaction) {
            if ($transaction->covers == 0) {
                $numberOfMealsInSubDepartments = PosTransactionDetail::where('trans_id', $transaction->id)
                    ->whereIn('command', [3, 7, 38])
                    ->whereIn('command_type', [0])
                    ->count();

                $covers = $numberOfMealsInSubDepartments == 0 ? 1 : $numberOfMealsInSubDepartments;

                $results['covers'] += $covers;
            }

            $results['total'] += (float) $transaction->total;
            $results['payments'] += (float) $transaction->payments->sum('amount');
        }


        return $results;
    }
}
