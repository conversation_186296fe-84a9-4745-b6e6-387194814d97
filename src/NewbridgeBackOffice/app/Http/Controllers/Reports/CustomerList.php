<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CompanyLinks;
use NewbridgeWeb\Repositories\Customer;

class CustomerList extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->tables = [
            "Customer List" => [
                "columns" => [
                    "Customer name" => [null, ['first_name', 'last_name'], 'string'],
                    "Membership No" => [null, 'membership_no', 'string'],
                    "Points Balance" => [null, 'points_balance', 'int'],
                    "Telephone" => [null, 'telephone', 'string'],
                    "Email" => [null, 'email', 'string'],
                    "Email Marketing" => [null, 'opt_in_text', 'string'],
                    "Registration Date" => [null, 'created_at', 'date', 'd-m-Y'],
                    "Expiry Date" => [null, 'expires', 'date', 'd-m-Y']
                ],
                "data" => [
                    'column_count' => 7,
                    'resultset' => 'customers',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results = [];
        $results['customers'] = $this->customers();

        return $results;
    }

    public function customers()
    {
        $companies = CompanyLinks::links($this->company_id);
        $query = Customer::whereIn('company_id', $companies)
            ->where('customer_type', 0)
            ->get();

        foreach($query as $k => $customer)
        {
            if($customer->opt_in == 0){
                $query[$k]->opt_in_text = 'No';
            } else {
                $query[$k]->opt_in_text = 'Yes';
            }
        }

        return $query;
    }
}
