<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CompanyLinks;
use NewbridgeWeb\Repositories\Customer;

class CustomerNotes extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->tables = [
            "Customer Notes" => [
                "columns" => [
                    "Customer name" => [null, ['first_name', 'last_name'], 'string'],
                    "Membership No" => [null, 'membership_no', 'string'],
                    "Notes" => [null, 'notes', 'string'],
                ],
                "data" => [
                    'column_count' => 6,
                    'resultset' => 'customers',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results = [];
        $results['customers'] = $this->customers();

        return $results;
    }

    public function customers()
    {
        $companies = CompanyLinks::links($this->company_id);
        return Customer::whereIn('company_id', $companies)
            ->where('customer_type', 0)
            ->whereNotNull('notes')
            ->get();
    }
}
