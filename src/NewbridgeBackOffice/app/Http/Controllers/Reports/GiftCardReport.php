<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Customer;

class GiftCardReport extends ReportAbstractController
{
    public function setTables($var = null): array
    {
        $this->tables = [
            "Gift Card Report" => [
                "columns" => [
                    "Registration Date" => [null, 'created_at', 'date', 'd/m/Y'],
                    "Gift Card No" => [null, 'membership_no', 'string'],
                    "Balance" => [null, 'cash_value', 'decimal'],
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'giftcards',
                    'summary_result' => 'summary'
                ],
                'summary' => [
                    [null, null, 'ignore'],
                    [null, 'blank', 'string'],
                    [null, 'balance', 'decimal']
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->customers();
    }

    public function customers()
    {
        $query = Customer::where('company_id', $this->company_id)->where('customer_type', 1)->get();

        $results = [];
        $results['giftcards'] = [];
        $results['summary'] = [
            "balance" => 0,
            "blank" => ''
        ];

        foreach ($query as $q) {
            $results['giftcards'][] = $q;
            $results['summary']['balance'] += $q->cash_value;
        }

        return $results;
    }
}
