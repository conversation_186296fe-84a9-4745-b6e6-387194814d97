<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Clerks\Clocking;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Sites;
use PDF;

class SalesComparison extends ReportAbstractController
{
    public $areas;
    public $times;
    public $start_compare;
    public $end_compare;
    public $hoursWages;
    public $salesTotals;
    public $hoursWagesSummary;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Department Sales" => [
                "columns" => [
                    "Department" => [null, 'displayname', 'string'],
                    "Gross Sales (1)" => [null, 'value0', 'decimal'],
                    "# of Sales (1)" => [null, 'count0', 'int'],
                    "Gross Sales (2)" => [null, 'value1', 'decimal'],
                    "# of Sales (2)" => [null, 'count1', 'int'],
                    "Difference" => [null, 'difference', 'html'],
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'department_sales',
                    'summary_result' => 'department_sales_summary'
                ],
                "summary" => [
                    "Gross Sales (1)" => [null, 'value0', 'decimal'],
                    "# of Sales (1)" => [null, 'count0', 'int'],
                    "Gross Sales (2)" => [null, 'value1', 'decimal'],
                    "# of Sales (2)" => [null, 'count1', 'int'],
                    "Difference" => [null, 'difference', 'html'],
                ]
            ],
            "Sub Department Sales" => [
                "columns" => [
                    "Sub Department" => [null, 'displayname', 'string'],
                    "Gross Sales (1)" => [null, 'value0', 'decimal'],
                    "# of Sales (1)" => [null, 'count0', 'int'],
                    "Gross Sales (2)" => [null, 'value1', 'decimal'],
                    "# of Sales (2)" => [null, 'count1', 'int'],
                    "Difference" => [null, 'difference', 'html'],
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'sub_department_sales',
                    'summary_result' => 'sub_department_sales_summary'
                ],
                "summary" => [
                    "Gross Sales (1)" => [null, 'value0', 'decimal'],
                    "# of Sales (1)" => [null, 'count0', 'int'],
                    "Gross Sales (2)" => [null, 'value1', 'decimal'],
                    "# of Sales (2)" => [null, 'count1', 'int'],
                    "Difference" => [null, 'difference', 'html'],
                ]
            ],
            "Wages By Area Vs Sales" => [
                "columns" => [
                    "Area Name" => [null, 'displayname', 'string'],
                    "Total Wages (1)" => [null, 'total0', 'decimal'],
                    "Percentage of Sales(1)" => [null, 'percentage0', 'percent'],
                    "Total Wages (2)" => [null, 'total1', 'decimal'],
                    "Percentage of Sales (2)" => [null, 'percentage1', 'percent'],
                    "Difference" => [null, 'difference_wages', 'html'],
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'wages_sales',
                    'summary_result' => 'wages_summary'
                ],
                "summary" => [
                    "Total Wages (1)" => [null, 'total0', 'decimal'],
                    "Percentage of Sales(1)" => [null, 'percentage0', 'percent'],
                    "Total Wages (2)" => [null, 'total1', 'decimal'],
                    "Percentage of Sales (2)" => [null, 'percentage1', 'percent'],
                    "Difference" => [null, 'difference', 'html'],
                ]

            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->start_compare = Carbon::createFromFormat('d-m-Y H:i:s', $report_data['start_compare'], TimezoneHelper::getTimezone())
            ->setTimezone('UTC')->toDateTimeString();
        $this->end_compare = Carbon::createFromFormat('d-m-Y H:i:s', $report_data['end_compare'], TimezoneHelper::getTimezone())
            ->setTimezone('UTC')->toDateTimeString();

        $this->times = [];
        $this->times[0] = [$this->start_compare, $this->end_compare];
        $this->times[1] = [$this->start, $this->end];

        $departmentResult = $this->departmentSales();
        $results['department_sales'] = $departmentResult['results'];
        $results['department_sales_summary'] = $departmentResult['summary'];

        $subdepartmentResult = $this->subDepartmentSales();
        $results['sub_department_sales'] = $subdepartmentResult['results'];
        $results['sub_department_sales_summary'] = $subdepartmentResult['summary'];


        $this->getAreas();
        $this->getWagesByArea();
        $this->getWagesVsSalesByArea();
        $this->wagesComparison();

        $results['wages_sales'] = $this->hoursWages;
        $results['wages_summary'] = $this->hoursWagesSummary;


        return $results;
    }

    public function departmentSales()
    {
        $resultSets = [];

        foreach ($this->times as $time) {
            $query = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as count, SUM(discount_value) as sum_discount_value'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', $time)
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0, 6])
                ->with(['department' => function ($q) {
                    $q->withTrashed();
                }])
                ->groupBy('department_guid');

            if ($this->terminal != '') {
                $query = $query->whereIn('terminal_num', $this->terminal);
            }

            $resultSets[] =  $query->get()->toArray();
        }

        $results = [];

        /**
         * Do a for loop for each transaction set
         * in this for loop, make a result with
         * 5 columns for each department, count1, value1, count2, value2, change/difference
         * change / difference is a percentage increase or decrease of sales value
         */
        for ($i = 0; $i < count($this->times); $i++) {
            foreach ($resultSets[$i] as $department) {
                if (!isset($results[$department['department_guid']])) {
                    $results[$department['department_guid']] = [];
                    $results[$department['department_guid']]['displayname'] = $department['department']['displayname'];
                    $results[$department['department_guid']]["value" . $i] = (float) $department['sum_net_value'];
                    $results[$department['department_guid']]["count" . $i] = $department['count'];
                } else {
                    $results[$department['department_guid']]["value" . $i] = (float) $department['sum_net_value'];
                    $results[$department['department_guid']]["count" . $i] = $department['count'];
                }
            }
        }

        $summary = [
            'value0' => 0,
            'value1' => 0,
            'count0' => 0,
            'count1' => 0,
            'difference' => 0
        ];


        /**
         * Loop through the results calculate the diff and fill any missing 0's
         * where the department didnt previously exist.
         * calculate summary row
         */
        foreach ($results as $k => $result) {
            if (!isset($result['value0'])) {
                $results[$k]['value0'] = 0;
                $results[$k]['count0'] = 0;
            }

            if (!isset($result['value1'])) {
                $results[$k]['value1'] = 0;
                $results[$k]['count1'] = 0;
            }

            $results[$k]['difference'] = $this->calculatePercentage($results[$k]['value0'], $results[$k]['value1']);

            $summary['value0'] += $results[$k]['value0'];
            $summary['value1'] += $results[$k]['value1'];
            $summary['count0'] += $results[$k]['count0'];
            $summary['count1'] += $results[$k]['count1'];
        }

        $summary['difference'] = $this->calculatePercentage($summary['value0'], $summary['value1']);

        return ['results' => $results, 'summary' => $summary];
    }

    private function subDepartmentSales()
    {
        $resultSets = [];

        foreach ($this->times as $time) {
            $query = PosTransactionDetail::select(DB::raw('SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as count, SUM(discount_value) as sum_discount_value'))
                ->where('company_id', $this->company_id)
                ->whereIn('site_num', $this->site_num)
                ->whereBetween('finalised_date', $time)
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0, 6])
                ->with(['sub_department' => function ($q) {
                    $q->withTrashed();
                }])
                ->groupBy('sub_department_guid');

            if ($this->terminal != '') {
                $query = $query->whereIn('terminal_num', $this->terminal);
            }

            $resultSets[] = $query->get()->toArray();
        }

        $results = [];

        /**
         * Do a for loop for each transaction set
         * in this for loop, make a result with
         * 5 columns for each department, count1, value1, count2, value2, change/difference
         * change / difference is a percentage increase or decrease of sales value
         */
        for ($i = 0; $i < count($this->times); $i++) {
            foreach ($resultSets[$i] as $department) {
                if (!isset($results[$department['sub_department_guid']])) {
                    $results[$department['sub_department_guid']] = [];
                    $results[$department['sub_department_guid']]['displayname'] = $department['sub_department']['displayname'];
                    $results[$department['sub_department_guid']]["value" . $i] = (float) $department['sum_net_value'];
                    $results[$department['sub_department_guid']]["count" . $i] = $department['count'];
                } else {
                    $results[$department['sub_department_guid']]["value" . $i] = (float) $department['sum_net_value'];
                    $results[$department['sub_department_guid']]["count" . $i] = $department['count'];
                }
            }
        }

        $summary = [
            'value0' => 0,
            'value1' => 0,
            'count0' => 0,
            'count1' => 0,
            'difference' => 0
        ];


        /**
         * TODO: Loop through the results calculate the diff and fill any missing 0's
         * TODO: where the department didnt previously exist.
         * TODO: calculate summary row
         */
        foreach ($results as $k => $result) {
            if (!isset($result['value0'])) {
                $results[$k]['value0'] = 0;
                $results[$k]['count0'] = 0;
            }

            if (!isset($result['value1'])) {
                $results[$k]['value1'] = 0;
                $results[$k]['count1'] = 0;
            }

            $results[$k]['difference'] = $this->calculatePercentage($results[$k]['value0'], $results[$k]['value1']);

            $summary['value0'] += $results[$k]['value0'];
            $summary['value1'] += $results[$k]['value1'];
            $summary['count0'] += $results[$k]['count0'];
            $summary['count1'] += $results[$k]['count1'];
        }

        $summary['difference'] = $this->calculatePercentage($summary['value0'], $summary['value1']);

        //TODO SET SUMMARY INFO AS GLOBAL VAR FOR USE IN WAGES VS AREA CALC

        $this->salesTotals = [
            $summary['value0'],
            $summary['value1']
        ];


        return ['results' => $results, 'summary' => $summary];
    }

    /**
     *
     * 1 - Get Wages total for each area, where in >= START_DATETIME
     * 2 - Get wages total for all areas as summary
     * 3 - Get hours worked for each area where in >= START_TIME
     * 4 - Get hours worked for all areas as summary
     * 5 - Compare wages for each area against total sales
     * 6 - Calculate overall percentage of sales vs wages as a summary
     *
     */
    private function getAreas()
    {
        $this->areas = ClerkAreas::with(['clerks' => function ($q) {
            $q->withTrashed();
        }])->where('company_id', $this->company_id)
            ->withTrashed()
            ->get();
    }

    /**
     * For each of the areas, get 2 sets of results from the
     * sys_clerk_clocking table, see the table for column names
     * and values.
     *
     * Get the list of areas, then, get the GUID of all the clerks in that area,
     * then get the wages total of the clerks clocked in where clerk_guid in array from
     * above.
     *
     * CLERKS WHERE AREA_GUID = XXXX
     *
     * GET WAGES TOTAL: select sum(total_pay) as total from sys_clerk_clocking where employee_guid in (X,X,X,X,X)
     * and {in} between ($this->start, $this->end);
     */
    private function getWagesByArea()
    {
        $results = [];

        foreach ($this->times as $k => $time) {
            $results[$k] = [];

            foreach ($this->areas as $area) {
                $clerks = $area->clerks->pluck('guid');

                $query = Clocking::selectRaw('sum(total_pay) as total, sum(hours_worked) as hours')
                    ->whereIn('employee_guid', $clerks)
                    ->whereBetween('in', $time)
                    ->whereNotNull('out')
                    ->first()
                    ->toArray();

                $results[$k][$area->guid] = $query;
                $results[$k][$area->guid]['displayname'] = $area->area_name;
            }

            // clerks not in an area
            $clerksNoArea = Clerks::whereNull('area_guid')
                ->where('company_id', $this->company_id)
                ->withTrashed()
                ->get()->pluck('guid');

            $query = Clocking::selectRaw('sum(total_pay) as total, sum(hours_worked) as hours')
                ->whereIn('employee_guid', $clerksNoArea)
                ->whereBetween('in', $time)
                ->whereNotNull('out')
                ->first()
                ->toArray();

            $results[$k]['none'] = $query;
            $results[$k]['none']['displayname'] = 'Other';
        }

        $this->hoursWages = $results;
    }

    private function getWagesVsSalesByArea()
    {
        for ($i = 0; $i < 2; $i++) {
            foreach ($this->hoursWages[$i] as  $k => $area) {
                if ((float) $area['total'] == 0 || (float) $this->salesTotals[$i] == 0) {
                    $this->hoursWages[$i][$k]['percentage'] = 0;
                } else {
                    $wagesVsSales = ((float) $area['total'] / ($this->salesTotals[$i]) * 100);
                    $this->hoursWages[$i][$k]['percentage'] = $wagesVsSales;
                }
            }
        }
    }

    /**
     * Spot the difference
     *
     * combine the data sets
     * Loop through the days/times array
     * check if the area exists in the other day (0/1)
     * if it exists merge the 2 sets of data by appending 0/1 to the
     * array key percentage0/percentage1 - total1/total0
     *
     *
    */
    private function wagesComparison()
    {
        $results = [];

        foreach ($this->hoursWages as $k => $hours) {
            foreach ($hours as $k2 => $area) {
                if (!isset($results[$k2])) {
                    $results[$k2] = [];
                    $results[$k2]['displayname'] = $area['displayname'];
                    $results[$k2]['displayname2'] = $area['displayname'] . ' vs All';
                }
                $results[$k2]['total'.$k] =  $area['total'];
                $results[$k2]['percentage'.$k] = $area['percentage'];
                $results[$k2]['hours'.$k] = $area['hours'];
            }
        }

        $this->hoursWagesSummary = [
            'total0' => 0,
            'percentage0' => 0,
            'total1' => 0,
            'percentage1' => 0,
            'difference' => 0
        ];

        foreach ($results as $k  => $result) {
            $results[$k]['difference_wages'] = $this->calculatePercentage($result['total0'], $result['total1']);
            $results[$k]['difference_hours'] = $this->calculatePercentage($result['hours0'], $result['hours1']);
            $results[$k]['difference_percentage'] = $this->calculatePercentage($result['percentage0'], $result['percentage1']);

            $this->hoursWagesSummary['total0'] += $result['total0'];
            $this->hoursWagesSummary['total1'] += $result['total1'];
        }

        $salesTotal = $this->salesTotals[0] + $this->salesTotals[1];

        for ($i = 0; $i < 2; $i++) {
            if ((float) $this->hoursWagesSummary['total'.$i] == 0 || (float) $salesTotal == 0) {
                $this->hoursWagesSummary['percentage'.$i] = 0;
            } else {
                $wagesVsSales = ((float) $this->hoursWagesSummary['total'.$i] / (float) $salesTotal * 100);
                $this->hoursWagesSummary['percentage'.$i] = $wagesVsSales;
            }
        }
        $this->hoursWagesSummary['difference'] = $this->calculatePercentage($this->hoursWagesSummary['total0'], $this->hoursWagesSummary['total1']);

        $this->hoursWages = $results;
    }

    private function calculatePercentage($old, $new, $reverse = false)
    {
        if ($old == 0) {
            $percentage = 100;
        } elseif ($new == 0) {
            $percentage = -100;
        } elseif ($old > $new) {
            $difference = $old - $new;
            $percentage = $difference / $old;
            $percentage = $percentage * 100;
        } elseif ($old < $new) {
            $difference = $new - $old;
            $percentage = $difference / $new;
            $percentage = $percentage * 100;
        }

        if ($reverse) {
            return '<span style="color: green">'.number_format($percentage, 2).'%</span> <i style="color: green;" class="fa fa-chevron-up">';
        } else {
            return '<span style="color: red">' . number_format($percentage, 2) . '%</span> <i style="color: red;" class="fa fa-chevron-down">';
        }
    }
}
