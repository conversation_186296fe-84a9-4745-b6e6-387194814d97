<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\DayParts;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\SubDepartment;
use PDF;

class DayPartsReport extends ReportAbstractController
{
    public array|null $subdepartment = null;
    public array|null $daypart = null;
    public array $summary = [];

    public function setTables($var = null): array
    {
        $this->tables = [
            "Day Part Summary" => [
                "columns" => [
                    "Day Part" => [null, 'name', 'string'],
                    "Gross Sales" => [null, 'total_sales', 'decimal'],
                    "Net Sales" => [null, 'net_sales', 'decimal'],
                    "# of Sales" => [null, 'count', 'int'],
                ],
                "data" => [
                    'column_count' => 4,
                    'resultset' => 'summary'
                ]
            ],
            "Day Part Sales" => [
                "columns" => [
                    "" => [null, ['name'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Product" => [null, 'name', 'string'],
                        "Gross Sales" => [null, 'total_sales', 'decimal'],
                        "Net Sales" => [null, 'net_sales', 'decimal'],
                        "# of Sales" => [null, 'count', 'int'],
                    ],
                    "data" => [
                        'column_count' => 4,
                        'resultset' => 'departments',
                        'summary_result' => null
                    ],
                    "summary" => [
                        "Gross Sales" => [null, 'total_sales', 'decimal'],
                        "Net Sales" => [null, 'net_sales', 'decimal'],
                        "# of Sales" => [null, 'count', 'int'],
                    ]
                ],
                "data" => [
                    'column_count' => 4,
                    'resultset' => 'dayparts'
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->subdepartment = $report_data['subdepartment'];
        $this->daypart = $report_data['daypart'];

        $results['dayparts'] = $this->dayPartSales();
        $results['summary'] = $this->summary;

        return $results;
    }

    public function dayPartSales()
    {
        $results = [];
        $dayParts = DayParts::where('company_id', $this->company_id);

        if ($this->daypart != null) {
            $dayParts = $dayParts->whereIn('short_code', $this->daypart);
        }

        $dayParts = $dayParts->get();

        $subdepartments = SubDepartment::where('company_id', $this->company_id);

        if ($this->subdepartment != null) {
            $subdepartments = $subdepartments->whereIn('guid', $this->subdepartment);
        }

        $subdepartments = $subdepartments->withTrashed()->get();

        foreach ($dayParts as $k => $part) {
            $results[$k] = [
                'id' => $part->id,
                'name' => $part->name,
                'short_code' => $part->short_code,
                'departments' => [],
                'summary' => [
                    'total_sales' => 0,
                    'net_sales' => 0,
                    'count' => 0
                ]
            ];

            foreach ($subdepartments as $s => $sub) {
                $departmentSales = PosTransactionDetail::select(DB::raw('pos_transaction_details.id, SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, 
                product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, 
                COUNT(pos_transaction_details.id) as count, SUM(pos_transaction_details.discount_value) as sum_discount_value, pos_transaction_details.displayname, pos_transaction_summary.day_part'))
                    ->where('pos_transaction_details.company_id', $this->company_id)
                    ->whereIn('pos_transaction_details.site_num', $this->site_num)
                    ->whereBetween('pos_transaction_details.finalised_date', [$this->start, $this->end])
                    ->leftJoin('pos_transaction_summary', 'pos_transaction_summary.id', 'pos_transaction_details.trans_id')
                    ->where('pos_transaction_summary.day_part', $part->short_code)
                    ->where('pos_transaction_details.sub_department_guid', $sub->guid)
                    ->whereIn('pos_transaction_details.command', [3, 7, 38])
                    ->whereIn('pos_transaction_details.command_type', [0, 6])
                    ->groupBy('pos_transaction_details.sub_department_guid');

                if ($this->terminal != '' && $this->terminal != null) {
                    $departmentSales = $departmentSales->whereIn('pos_transaction_details.terminal_num', $this->terminal);
                }

                $departmentSales = $departmentSales->get()->toArray();

                foreach ($departmentSales as $k2 => $department) {
                    $subdepartment = SubDepartment::where('guid', $department['sub_department_guid'])->withTrashed()->first();
                    $results[$k]['departments'][] = [
                        'id' => $department['id'],
                        'name' => $subdepartment['displayname'],
                        'total_sales' => (float) $department['sum_net_value'],
                        'net_sales' => (float) $department['sum_net_value'] - (float) $department['sum_tax_value'],
                        'count' => (float) $department['count']
                    ];

                    $results[$k]['summary']['total_sales'] += (float) $department['sum_net_value'];
                    $results[$k]['summary']['net_sales'] += (float) $department['sum_net_value'] - (float) $department['sum_tax_value'];
                    $results[$k]['summary']['count'] += (float) $department['count'];
                }
            }

            if ($results[$k]['summary']['total_sales'] == 0) {
                unset($results[$k]);
            }
        }

        $this->createSummary($results);

        return $results;
    }

    private function createSummary(array $data): void
    {
        foreach($data as $d) {
            $this->summary[] = [
                "name" => $d['name'],
                "total_sales" => $d['summary']['total_sales'],
                "net_sales" => $d['summary']['net_sales'],
                "count" => $d['summary']['count']
            ];
        }
    }
}
