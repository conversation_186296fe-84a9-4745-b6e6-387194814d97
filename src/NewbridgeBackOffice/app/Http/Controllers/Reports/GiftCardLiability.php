<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\CustomerTransactions;

class GiftCardLiability extends ReportAbstractController
{
    public function setTables($var = null): array
    {
        $this->tables = [
            "Gift Card Liability Report" => [
                "columns" => [
                    "Membership No" => ['customer', 'membership_no', 'string'],
                    "Balance at Date" => [null, 'balance', 'decimal']
                ],
                "data" => [
                    'column_count' => 2,
                    'resultset' => 'transactions',
                    'summary_result' => 'summary'
                ],
                'summary' => [
                    [null, null, 'ignore'],
                    [null, 'balance', 'decimal']
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results = [];
        $results['transactions'] = $this->transactions();
        $results['summary'] = [
            'balance' => 0,
            'blank' => ' '
        ];

        foreach ($results['transactions'] as $k => $entry) {
            if($entry->balance == 0) {
                unset($results['transactions'][$k]);
            }
            $results['summary']['balance'] += $entry->balance;
        }

        return $results;
    }

    public function transactions()
    {
        $date = strtotime($this->end);
        $date = date('Y-m-d H:i:s', $date);

        $customers = Customer::where('company_id', $this->company_id)->where('customer_type', 1)
            ->whereDate('created_at', '<=', $date)
            ->where(function ($q) use ($date) {
                $q->whereDate('expires', '>=', $date)
                    ->orWhereNull('expires');
            })
            ->get()
            ->pluck('guid');

        return CustomerTransactions::select(DB::raw('id, customer_guid, SUM(value) as balance'))
            ->with('customer')->whereIn('customer_guid', $customers)
            ->where('created_at', '<=', $date)
            ->groupBy('customer_guid')
            ->get();
    }
}
