<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\Suppliers;
use PDF;

class PurchasesReport extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->tables = [
            "Purchases Report" => [
                "columns" => [
                    "" => [null, ['name'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Input Date" => [null, 'input_date', 'string'],
                        "Invoice Date" => [null, 'invoice_date', 'string'],
                        "Invoice #" => [null, 'invoice_no', 'string'],
                        "Delivery #" => [null, 'delivery_no', 'string'],
                        "Amount" => [null, 'gross_value', 'decimal'],
                        "View Order" => [null, 'id', 'button', 'View Order', '/stock/orders/edit/']
                    ],
                    "data" => [
                        'column_count' => 7,
                        'resultset' => 'orders',
                        'summary_result' => 'summary'
                    ],
                    "summary" => [
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        [null, 'blank', 'string'],
                        "Total" => [null, 'gross_value', 'decimal'],
                        [null, 'blank', 'string']
                    ]
                ],
                "data" => [
                    'column_count' => 1,
                    'resultset' => 'suppliers',
                    'summary_result' => 'summary'
                ],
                "summary" => [
                    [null, 'blank', 'string'],
                    [null, 'blank', 'string'],
                    [null, 'blank', 'string'],
                    "Total" => [null, 'gross_total', 'decimal'],
                    [null, 'blank', 'string']
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);
        return $this->supplierOrders();
    }

    public function supplierOrders()
    {
        $suppliers = Suppliers::with(['orders' => function ($q) {
            $q->whereBetween('delivery_date', [$this->start, $this->end])
                ->whereIn('site_num', $this->site_num)
                ->with('transactions');
        }])
            ->whereHas('orders', function ($q) {
                $q->whereBetween('delivery_date', [$this->start, $this->end])
                    ->whereIn('site_num', $this->site_num);
            })
            ->where('company_id', $this->company_id)
            ->get();

        $results = [
            'suppliers' => [],
            'summary' => [
                'gross_total' => 0,
                'blank' => ''
            ]
        ];


        foreach ($suppliers as $k => $supplier) {
            $results['suppliers'][$k] = [
                'id' => $supplier->id,
                'name' => $supplier->name,
                'blank' => '',
                'orders' => [],
                'summary' => [
                    'gross_value' => 0,
                    'blank' => ''
                ]
            ];

            foreach ($supplier->orders as $k2 => $order) {
                $total = (float) $order->transactions->sum('total');
                $results['suppliers'][$k]['summary']['gross_value'] += $total;
                $results['summary']['gross_total'] += $total;

                $results['suppliers'][$k]['orders'][$k2] = [
                    'id' => $order->id,
                    'input_date' => $order->created_at,
                    'invoice_date' => $order->delivery_date,
                    'invoice_no' => $order->invoice_no,
                    'delivery_no' => $order->delivery_no,
                    'gross_value' => $total,
                    'blank' => ''
                ];
            }
        }

        return $results;
    }
}
