<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Sites;

class ProductSalesAudit extends ReportAbstractController
{
    public $product = null;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Product Sales Audit (".$var.")" => [
                "columns" => [
                    "Date & Time" => [null, 'datetime', 'date'],
                    "Clerk" => ['clerk', 'full_name', 'string'],
                    "Value" => [null, 'net_value', 'decimal']
                ],
                "data" => [
                    'column_count' => 4,
                    'resultset' => 'audit',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);
        $this->product = isset($report_data['product']) && $report_data['product'] ? Products::find($report_data['product']) : null;

        $results = [];
        $results['audit'] = $this->productAudit();

        return $results;
    }

    public function productAudit()
    {
        if ($this->product != null) {
            $query = PosTransactionDetail::whereIn('site_num', $this->site_num)
                ->where('company_id', $this->company_id)
                ->whereBetween('finalised_date', [$this->start, $this->end])
                ->where('command_type', '!=', 5)
                ->where('product_guid', $this->product->guid)
                ->orderBy('datetime', 'ASC')
                ->with(['clerk' => function ($q) {
                    $q->withTrashed();
                }])
                ->get();

            return $query->toArray();
        } else {
            return [];
        }
    }
}
