<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\PosTransactionPayment;

class FinancialSummary extends ReportAbstractController
{
    public $guid;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Department Sales" => [
                "columns" => [
                    "Department" => [null, 'department_name', 'string'],
                    "Sales Total" => [null, 'sum_gross_value', 'decimal'],
                    "# of Sales" => [null, 'count', 'int'],
                    "Tax " . CurrencyHelper::symbol($this->company_id) => [null, 'sum_tax_value', 'decimal'],
                    "Discount Total" => [null, 'sum_discount_value', 'decimal'],
                    "Gross Total" => [null, 'total', 'decimal'],
                    "Net Total" => [null, 'sum_net_total', 'decimal']
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'departments',
                    'summary_result' => 'sales-totals'
                ],
                'summary' => [
                    [null, 'sum_gross_value', 'decimal'],
                    [null, 'count', 'int'],
                    [null, 'sum_tax_value', 'decimal'],
                    [null, 'sum_discount_value', 'decimal'],
                    [null, 'sum_net_value', 'decimal'],
                    [null, 'sum_net_total', 'decimal']
                ]
            ],
            'Sub Department Sales' => [
                "columns" => [
                    "Sub Department" => [null, 'subdepartment_displayname', 'string'],
                    "Sales Total" => [null, 'sum_gross_value', 'decimal'],
                    "# of Sales" => [null, 'count', 'int'],
                    "Tax " . CurrencyHelper::symbol($this->company_id) => [null, 'sum_tax_value', 'decimal'],
                    "Discount Total" => [null, 'sum_discount_value', 'decimal'],
                    "Gross Total" => [null, 'total', 'decimal'],
                    "Net Total" => [null, 'sum_net_total', 'decimal']
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'subdepartments',
                    'summary_result' => 'sales-totals'
                ],
                'summary' => [
                    [null, 'sum_gross_value', 'decimal'],
                    [null, 'count', 'int'],
                    [null, 'sum_tax_value', 'decimal'],
                    [null, 'sum_discount_value', 'decimal'],
                    [null, 'sum_net_value', 'decimal'],
                    [null, 'sum_net_total', 'decimal']
                ]
            ],
            "General" => [
                "columns" => [
                    "Name" => [null, 'count', 'ignore'],
                    "Count" => [null, 'count', 'int'],
                    "Total " . CurrencyHelper::symbol($this->company_id) => [null, 'sum_total', 'decimal'],
                ],
                "row-titles" => [
                    ["Gratuities", 'gratuities'],
                    ["Cashback", 'cashback'],
                    ["Service Charges", 'service_charges'],
                    ["Gift Card Topup", 'giftcards'],
                    ["Cash Rounding", 'rounding']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => null
                ],
                "summary" => null
            ],
            "Other Discounts" => [
                "columns" => [
                    "Discount" => ['discount', 'displayname', 'string'],
                    "Discount Sales" => [null, 'count', 'int'],
                    "Discount Amount" => [null, 'sum_total', 'decimal']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'discounts',
                    'summary_result' => 'discount_summary'
                ],
                'summary' => [
                    [null, 'count', 'int'],
                    [null, 'sum_total', 'decimal']
                ]
            ],
            "Expenses" => [
                "columns" => [
                    "Name" => [null, 'displayname', 'string'],
                    "Count" => [null, 'count', 'int'],
                    "Total " . CurrencyHelper::symbol($this->company_id) => [null, 'sum_total', 'decimal'],
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'expenses',
                    'summary_result' => 'expenses_summary',
                ],
                'summary' => [
                    [null, 'count', 'int'],
                    [null, 'sum_total', 'decimal']
                ]
            ],
            "Spend Per Head" => [
                "columns" => [
                    "# of Covers" => [null, 'no_covers', 'string'],
                    "Average Spend" => [null, 'average_spend', 'string']
                ],
                "data" => [
                    'column_count' => 2,
                    'resultset' => 'covers',
                    'summary_result' => null
                ]
            ],
            "Payment Methods" => [
                "columns" => [
                    "Payment Type" => [null, 'name', 'string'],
                    "# of Payments" => [null, 'sales_count', 'count'],
                    "Total " . CurrencyHelper::symbol($this->company_id) => [null, 'sales_value', 'decimal']
                ],
                "data" => [
                    'column_count' => 3,
                    'resultset' => 'payments',
                    'summary_result' => 'payments_summary'
                ],
                'summary' => [
                    [null, 'sales_count', 'int'],
                    [null, 'sales_value', 'decimal']
                ]
            ]
        ];


        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->guid = $report_data['guid'];

        $percent = 25 / 6;

        $results = [];

        $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);
        Redis::set(
            'report-' . $this->guid . '-status',
            json_encode(
                [
                    'status' => 'success',
                    'text' => 'Calculating Department Sales',
                    'percent' => $redis['percent'] + $percent
                ]
            )
        );
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));

        $results['departments'] = $this->departmentSales();

        $results['subdepartments'] = $this->subDepartmentSales();

        $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);
        Redis::set(
            'report-' . $this->guid . '-status',
            json_encode(
                ['status' => 'success', 'text' => 'Calculating Sales Totals', 'percent' => $redis['percent'] + $percent]
            )
        );
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));

        $results['sales-totals'] = $this->salesTotals();


        // Payments
        $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);
        Redis::set(
            'report-' . $this->guid . '-status',
            json_encode(
                ['status' => 'success', 'text' => 'Calculating Payments', 'percent' => $redis['percent'] + $percent]
            )
        );
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));
        $results['payments'] = $this->paymentTypeSales();


        $results['cash_payments'] = $this->cashSales();
        $results['payments_summary'] = $this->paymentTypeSalesSummary();

        // General
        $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);
        Redis::set(
            'report-' . $this->guid . '-status',
            json_encode(
                ['status' => 'success', 'text' => 'Calculating Other Income', 'percent' => $redis['percent'] + $percent]
            )
        );
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));
        $results['rounding'] = $this->rounding();
        $results['gratuities'] = $this->gratuities();

        $results['cashback'] = $this->cashback();


        $results['service_charges'] = $this->serviceCharges();
        $results['giftcards'] = $this->giftCardTopup();

        // Discounts
        $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);
        Redis::set(
            'report-' . $this->guid . '-status',
            json_encode(
                [
                    'status' => 'success',
                    'text' => 'Calculating Discounts & Expenses',
                    'percent' => $redis['percent'] + $percent
                ]
            )
        );
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));
        $results['discounts'] = $this->checkDiscounts();
        $results['discount_summary'] = $this->checkDiscountSummary();

        // Expenses
        $results['expenses'] = $this->expenses();
        $results['expenses_summary'] = $this->expensesSummary();

        // Spend per head
        $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);
        Redis::set(
            'report-' . $this->guid . '-status',
            json_encode(
                ['status' => 'success', 'text' => 'Calculating Covers', 'percent' => $redis['percent'] + $percent]
            )
        );
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));
        $results['covers'] = $this->spendPerHead();

        return $results;
    }

    public function gratuities()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(gross_value) as sum_total, COUNT(*) as count'))
            ->where('command', 61)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    private function cashback()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(gross_value) as sum_total, COUNT(*) as count'))
            ->where('command', 37)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    private function giftCardTopup()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count'))
            ->where('command', 46)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    private function serviceCharges()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count'))
            ->where('command', 44)
            ->whereNotIn('command_type', [5])
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    private function rounding()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count'))
            ->where('command', 174)
            ->whereNotIn('command_type', [5])
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    private function expenses()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count, displayname'))
            ->where('command', 88)
            ->where('command_type', 1)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->groupBy('void_reason_guid')
            ->groupBy('displayname')
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->get();
    }

    private function expensesSummary()
    {
        $query = PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count'))
            ->where('command', 88)
            ->where('command_type', 1)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    private function spendPerHead()
    {
        $data = PosTransaction::selectRaw('id, sum(covers) as total_covers, sum(total) as total_sales')
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num])
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('covers', '>', 0)
            ->whereNotNull('covers')
            ->where('table_number', '!=', 0);

        if ($this->terminal != null) {
            $data = $data->whereIn('terminal_num', $this->terminal);
        }

        $data = $data->first();

        $results = [];

        if ($data->total_sales > 0 && $data->total_covers > 0) {
            $results[] = [
                'no_covers' => $data->total_covers,
                'average_spend' => CurrencyHelper::symbol($this->company_id) . number_format(
                    $data->total_sales / $data->total_covers,
                    2
                )
            ];
        } else {
            $results[] = [
                'no_covers' => $data->total_covers,
                'average_spend' => 0.00
            ];
        }

        return $results;
    }

    public function departmentSales()
    {
        $select = 'SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid,';
        $select .= 'sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value,';
        $select .= 'COUNT(pos_transaction_details.id) as count, (SUM(discount_value) * -1) as sum_discount_value, (sum(gross_value) - (SUM(discount_value) * -1)) as total,';
        $select .= '(sum(net_value) - sum(tax_value)) as sum_net_total, plu_department.displayname as department_name';
        $query = PosTransactionDetail::select(DB::raw($select))
            ->where('pos_transaction_details.company_id', $this->company_id)
            ->whereIn('pos_transaction_details.site_num', [$this->site_num])
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->leftJoin('plu_department', 'pos_transaction_details.department_guid', 'plu_department.guid')
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0, 6])
            ->whereNotIn('command_type', [5])
            ->groupBy('department_guid');

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->get();
    }

    public function subDepartmentSales()
    {
        $select = 'SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid,';
        $select .= 'sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value,';
        $select .= 'COUNT(pos_transaction_details.id) as count, (SUM(discount_value) * -1) as sum_discount_value, (sum(gross_value) - (SUM(discount_value) * -1)) as total,';
        $select .= '(sum(net_value) - sum(tax_value)) as sum_net_total, plu_subdepartment.displayname as subdepartment_displayname';

        $query = PosTransactionDetail::select(DB::raw($select))
            ->where('pos_transaction_details.company_id', $this->company_id)
            ->whereIn('pos_transaction_details.site_num', [$this->site_num])
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->leftJoin('plu_subdepartment', 'sub_department_guid', 'plu_subdepartment.guid')
            ->orderBy('plu_subdepartment.id', 'ASC')
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0, 6])
            ->whereNotIn('command_type', [5])
            ->groupBy('sub_department_guid');

        if ($this->terminal != null) {
            $query = $query->whereIn('pos_transaction_details.terminal_num', $this->terminal);
        }

        return $query->get();
    }

    public function salesTotals()
    {
        $select = 'terminal_num,SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid,';
        $select .= 'sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value,';
        $select .= 'COUNT(id) as count, (SUM(discount_value) * -1) as sum_discount_value, (sum(gross_value) - (SUM(discount_value) * -1)) as total,';
        $select .= '(sum(net_value) - sum(tax_value)) as sum_net_total';

        $query = PosTransactionDetail::select(DB::raw($select))
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num])
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->whereIn('command', [3, 38])
            ->whereIn('command_type', [0, 6])
            ->whereNotIn('command_type', [5]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    public function cashSales()
    {
        $query = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('method_type', 1)
            ->groupBy('method_type')
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num])
            ->with([
                'method' => function ($q) {
                    $q->with([
                        'xerolinks' => function ($q2) {
                            $q2->where('company_id', $this->company_id);
                        }
                    ]);
                }
            ]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->get();
    }

    public function checkDiscounts()
    {
        $query = PosTransactionDetail::select(DB::raw('*, COUNT(*) as count, SUM(discount_value) as sum_total'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->groupBy('discount_guid')
            ->with('discount')
            ->where('discount_value', '<', 0)
            ->whereIn('command', [9, 10])
            ->where('command_type', '!=', 5)
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num]);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->get();
    }

    public function checkDiscountSummary()
    {
        $query = PosTransactionDetail::select(DB::raw('*, COUNT(*) as count, SUM(discount_value) as sum_total'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('discount_value', '<', 0)
            ->whereIn('command', [9, 10])
            ->whereIn('command_type', [0, 6])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num])
            ->where('command_type', '!=', 5);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->first();
    }

    public function paymentTypeSales()
    {
        $paymentQuery = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('method_type', '!=', 1)
            ->groupBy('method_guid')
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num])
            ->with([
                'method' => function ($q) {
                    $q->with([
                        'xerolinks' => function ($q2) {
                            $q2->where('company_id', $this->company_id);
                        }
                    ]);
                }
            ]);

        if ($this->terminal != null) {
            $paymentQuery = $paymentQuery->whereIn('terminal_num', $this->terminal);
        }

        $paymentQuery = $paymentQuery->get();

        $cashQuery = $this->cashSales();

        $results = [];

        if (!empty($paymentQuery)) {
            foreach ($paymentQuery as $p) {
                if($p->method === null) {
                    Log::warning('Payment method not found on financial report guid: ' . $p->method_guid);
                }

                $results[] = [
                    'name' => $p->method?->DisplayName ?? 'Payment Method Not Found',
                    'sales_value' => $p->sales_value,
                    'sales_count' => $p->sales_count,
                    'link' => $p->method?->xerolinks ?? 'Links for Method Not Found'
                ];
            }
        }

        $results['cash'] = [
            'name' => 'Cash',
            'sales_value' => 0 - (float) $this->cashback()->sum_total,
            'sales_count' => 0,
            'link' => null
        ];

        if($results['cash']['sales_value'] === 0.0) {
            unset($results['cash']);
        }

        if (!empty($cashQuery)) {
            foreach ($cashQuery as $c) {
                if($c->method === null) {
                    Log::warning('Payment method not found on financial report guid: ' . $c->method_guid);
                }

                $results['cash'] = [
                    'name' => 'Cash',
                    'sales_value' => (float) $c->sales_value - (float) $this->cashback()->sum_total,
                    'sales_count' => $c->sales_count,
                    'link' => $c->method?->xerolinks ?? 'Links for Method Not Found'

                ];
            }
        }


        return $results;
    }

    public function paymentTypeSalesSummary()
    {
        $paymentQuery = PosTransactionPayment::select(DB::raw('COUNT(*) as sales_count, SUM(amount) as sales_value'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$this->site_num])
            ->with('method');

        if ($this->terminal != null) {
            $paymentQuery = $paymentQuery->whereIn('terminal_num', $this->terminal);
        }

        $paymentQuery = $paymentQuery->first();

        $paymentQuery->sales_value = ((float) $paymentQuery->sales_value - (float) $this->cashback()->sum_total);

        return $paymentQuery;
    }
}
