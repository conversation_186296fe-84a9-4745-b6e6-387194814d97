<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\SubDepartment;
use NewbridgeWeb\Repositories\VoidReasons;

class ProductSales extends ReportAbstractController
{
    public $summary;
    public $grouping = 'sub_department';
    public $subdepartment = null;
    public $department = null;
    public $sortBy;
    public $sumTotalSales = 0;
    public $guid;
    public $percentage = 2.5;
    public $sales_type = 'all';

    public function setTables($var = null): array
    {
        $this->currencySymbol = CurrencyHelper::symbol($this->company_id);

        $this->tables = [
            "Product Sales" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "ID" => [null, 'id', 'string'],
                        "Product" => [null, 'displayname', 'string'],
                        "# of Sales" => [null, 'count', 'string'],
                        "# of Refunds" => [null, 'refund_count', 'string'],
                        "Gross Sales Value" => [null, 'sum_net_value', 'string'],
                        "Refund Value" => [null, 'sum_refund_value', 'string'],
                        "Discount Value" => [null, 'sum_discount_value', 'string'],
                        "Tax Amount" => [null, 'sum_tax_value', 'string'],
                        "Unit Cost Price" => [null, 'cost_price', 'string'],
                        "Total Cost Price" => [null, 'sum_cost_price', 'string'],
                        "GP %" => [null, 'gross_profit', 'string'],
                        "GP " . $this->currencySymbol => [null, 'gp_value', 'string'],
                        "% of Sales" => [null, 'percentage_of_sales', 'string']
                    ],
                    "data" => [
                        'column_count' => 8,
                        'resultset' => 'products',
                        'summary_result' => 'summary'
                    ],
                    "summary" => [
                        "BLANK" => [null, 'blank', 'string'],
                        "# of Sales" => [null, 'count', 'string'],
                        "# of Refunds" => [null, 'refund_count', 'string'],
                        "Gross Sales Value" => [null, 'sum_net_value', 'string'],
                        "Refund Value" => [null, 'sum_refund_value', 'string'],
                        "Discount Value" => [null, 'sum_discount_value', 'string'],
                        "Tax Amount" => [null, 'sum_tax_value', 'string'],
                        "Unit Cost Price" => [null, 'costprice', 'string'],
                        "Total Cost Price" => [null, 'sum_cost_price', 'string'],
                        "GP %" => [null, 'gp', 'string'],
                        "GP " . $this->currencySymbol => [null, 'gp_value', 'string'],
                        "% of Sales" => [null, 'percentage_of_sales', 'string']
                    ]
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'groupings'
                ]
            ],
            'Summary' => [
                "columns" => [
                    "# of Sales" => [null, 'count', 'string'],
                    "# of Refunds" => [null, 'refund_count', 'string'],
                    "Gross Sales Value" => [null, 'sum_net_value', 'string'],
                    "Refund Value" => [null, 'sum_refund_value', 'string'],
                    "Discount Value" => [null, 'sum_discount_value', 'string'],
                    "Tax Amount" => [null, 'sum_tax_value', 'string'],
                    "Total Cost Price" => [null, 'sum_cost_price', 'string']
                ],
                "data" => [
                    'column_count' => 7,
                    'resultset' => 'summary'
                ]
            ]

        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->subdepartment = $report_data['subdepartment'];
        $this->department = $report_data['department'];
        $this->sortBy = $report_data['sortBy'];
        $this->guid = $report_data['guid'];
        $this->sales_type = $report_data['sales_type'];

        $this->setSummary();

        $results = $this->productSales();
        $summary = $results['summary'];

        $results['summary'] = [];
        $results['summary'][] = $summary;

        return $results;
    }

    private function getProductSales()
    {
        $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);

        Redis::set(
            'report-' . $this->guid . '-status',
            json_encode(['status' => 'success', 'text' => 'Collating Sales Data', 'percent' => $redis['percent'] + 5])
        );
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));


        $query = new PosTransactionDetail();
        $query = $query->select(
            DB::raw(
                'SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, SUM(qty) as count, SUM(discount_value) as sum_discount_value, cost_price, SUM(cost_price) as sum_costprice'
            )
        )
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0])
            ->whereNotIn('command_type', [5, 6])
            ->groupBy('product_guid')
            ->orderBy('count', 'ASC');

        if ($this->subdepartment != null) {
            $query = $query->whereIn('sub_department_guid', $this->subdepartment);
        }

        if ($this->department != null) {
            $query = $query->whereIn('department_guid', $this->department);
        }

        if ($this->terminal != '') {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        $query = $query->get();


        $this->sumTotalSales = $query->sum('sum_net_value');

        $query = $query->toArray();

        $voidReasons = new VoidReasons();
        $voidReasons = $voidReasons->where('company_id', $this->company_id)
            ->where('reason_type', 0)
            ->where('stock', 1)
            ->pluck('guid');

        $query3 = new PosTransactionDetail();
        $query3 = $query3->select(DB::raw('SUM(qty) as item_count, product_guid'))
            ->where(function ($q2) use ($voidReasons) {
                $q2->where('company_id', $this->company_id)
                    ->whereBetween('finalised_date', [$this->start, $this->end])
                    ->whereIn('site_num', $this->site_num)
                    ->whereIn('command', [3, 7, 38])
                    ->whereIn('command_type', [5])
                    ->whereIn('void_reason_guid', $voidReasons);
            })
            ->orWhere(function ($q2) {
                $q2->where('company_id', $this->company_id)
                    ->whereBetween('finalised_date', [$this->start, $this->end])
                    ->whereIn('site_num', $this->site_num)
                    ->whereIn('command', [107])
                    ->whereNotIn('command_type', [5]);
            })
            ->groupBy('product_guid');


        $query3 = $query3->get()->keyBy('product_guid')->toArray();

        // get cost value of each line
        // as a result of $query3

        $query2 = new PosTransactionDetail();
        $query2 = $query2->select(
            DB::raw(
                'product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, SUM(qty) as count, SUM(discount_value) as sum_discount_value, cost_price, sum(cost_price) as sum_costprice'
            )
        )
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('command_type', 6)
            ->orderBy('displayname', 'ASC')
            ->groupBy('product_guid');

        if ($this->subdepartment != null) {
            $query2 = $query2->whereIn('sub_department_guid', $this->subdepartment);
        }

        if ($this->department != null) {
            $query2 = $query2->whereIn('department_guid', $this->department);
        }

        if ($this->terminal != '') {
            $query2 = $query2->whereIn('terminal_num', $this->terminal);
        }

        $query2 = $query2->get()->toArray();

        $products = [
            'sales' => [],
            'refunds' => [],
            'loss' => $query3
        ];

        foreach ($query as $tran) {
            $products['sales'][$tran['product_guid']] = $tran;
        }

        foreach ($query2 as $tran2) {
            if (!isset($products['sales'][$tran2['product_guid']])) {
                $tran_sale = $tran2;
                $tran_sale['count'] = 0;
                $tran_sale['sum_net_value'] = 0;
                $tran_sale['sum_gross_value'] = 0;
                $tran_sale['sum_tax_value'] = 0;
                $tran_sale['sum_discount_value'] = 0;
                $tran_sale['cost_price'] = 0;
                $tran_sale['sum_costprice'] = 0;

                $products['sales'][$tran2['product_guid']] = $tran_sale;
            }

            $products['refunds'][$tran2['product_guid']] = $tran2;
        }

        return $products;
    }

    public function productSales()
    {
        if ($this->department != null) {
            $this->grouping = 'department';
        } elseif ($this->subdepartment != null) {
            $this->grouping = 'sub_department';
        }

        if ($this->grouping === 'department') {
            $groups = Departments::where('company_id', $this->company_id)->whereIn(
                'guid',
                $this->department
            )->withTrashed()->get();
        }

        if ($this->grouping === 'sub_department') {
            if ($this->subdepartment != null) {
                $groups = SubDepartment::where('company_id', $this->company_id)->whereIn(
                    'guid',
                    $this->subdepartment
                )->withTrashed()->get();
            } else {
                $groups = SubDepartment::where('company_id', $this->company_id)->withTrashed()->get();
            }
        }

        $results = [];

        $results['summary'] = [
            'count' => 0,
            'sum_gross_value' => 0,
            'sum_net_value' => 0,
            'sum_discount_value' => 0,
            'sum_refund_value' => 0,
            'refund_count' => 0,
            'sum_tax_value' => 0,
            'sum_cost_price' => 0
        ];

        $productData = $this->getProductSales();

        foreach ($groups as $group) {
            $products = Products::select(['id', 'guid', 'displayname'])
                ->where('company_id', $this->company_id)
                ->orderBy('displayname', 'ASC')
                ->where($this->grouping . '_guid', $group->guid)
                ->withTrashed()
                ->get()
                ->toArray();

            foreach ($products as $k => $product) {
                if (isset($productData['sales'][$product['guid']])) {
                    $products[$k]['count'] = $productData['sales'][$product['guid']]['count'];
                    $products[$k]['value'] = (float) $productData['sales'][$product['guid']]['sum_net_value'];
                } else {
                    $products[$k]['count'] = 0;
                    $products[$k]['value'] = 0;
                }
            }

            $products = collect($products);

            if ($this->sortBy !== 'displayname') {
                $products = $products->sortByDesc($this->sortBy);
            } else {
                $products = $products->sortBy($this->sortBy);
            }

            $results['groupings'][$group->id]['id'] = $group->id;
            $results['groupings'][$group->id]['displayname'] = $group->displayname;
            $results['groupings'][$group->id]['products'] = [];

            $results['groupings'][$group->id]['summary']['count'] = 0;
            $results['groupings'][$group->id]['summary']['sum_gross_value'] = 0;
            $results['groupings'][$group->id]['summary']['sum_net_value'] = 0;
            $results['groupings'][$group->id]['summary']['sum_discount_value'] = 0;
            $results['groupings'][$group->id]['summary']['sum_refund_value'] = 0;
            $results['groupings'][$group->id]['summary']['refund_count'] = 0;
            $results['groupings'][$group->id]['summary']['sum_tax_value'] = 0;
            $results['groupings'][$group->id]['summary']['costprice'] = 0;
            $results['groupings'][$group->id]['summary']['sum_cost_price'] = 0;
            $results['groupings'][$group->id]['summary']['gp_value'] = 0;
            $results['groupings'][$group->id]['summary']['gp'] = 0;

            $percent = count($products) > 0 ? 25 / count($products) : 0.01;

            foreach ($products as $k => $product) {
                if (isset($productData['sales'][$product['guid']])) {
                    $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);

                    Redis::set(
                        'report-' . $this->guid . '-status',
                        json_encode(
                            [
                                'status' => 'success',
                                'text' => 'Processing: ' . $product['displayname'],
                                'percent' => $redis['percent'] + $percent
                            ]
                        )
                    );
                    Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));

                    $cost_price = (float) $productData['sales'][$product['guid']]['cost_price'];
                    $sum_lost_price = isset($productData['loss'][$product['guid']]) ? ($productData['loss'][$product['guid']]['item_count'] * (float) $productData['sales'][$product['guid']]['cost_price']) : 0.00;
                    $sum_cost_price = $productData['sales'][$product['guid']]['sum_costprice'];

                    $results['groupings'][$group->id]['products'][$product['guid']] = [
                        'id' => $product['id'],
                        'displayname' => $product['displayname'],
                        'count' => $productData['sales'][$product['guid']]['count'],
                        'refund_count' => isset($productData['refunds'][$product['guid']]) ? $productData['refunds'][$product['guid']]['count'] : 0,
                        'sum_gross_value' => $this->currencySymbol . number_format(
                            $productData['sales'][$product['guid']]['sum_gross_value'],
                            2
                        ),
                        'sum_net_value' => $this->currencySymbol . number_format(
                            $productData['sales'][$product['guid']]['sum_net_value'],
                            2
                        ),
                        'sum_discount_value' => $this->currencySymbol . number_format(
                            $productData['sales'][$product['guid']]['sum_discount_value'],
                            2
                        ),
                        'sum_refund_value' => isset($productData['refunds'][$product['guid']]) ? $this->currencySymbol . number_format(
                            abs($productData['refunds'][$product['guid']]['sum_net_value']),
                            2
                        ) : $this->currencySymbol . '0.00',
                        'sum_tax_value' => $this->currencySymbol . number_format(
                            $productData['sales'][$product['guid']]['sum_tax_value'],
                            2
                        ),
                        'cost_price' => $this->currencySymbol . number_format($cost_price, 2),
                        'sum_cost_price' => $this->currencySymbol . number_format($sum_cost_price, 2),
                        'units_lost_count' => isset($productData['loss'][$product['guid']]) ? $productData['loss'][$product['guid']]['item_count'] : 0,
                        'units_lost_cost' => $this->currencySymbol . $sum_lost_price,
                        'percentage_of_sales' => $this->calculatePercentage(
                            $this->sumTotalSales,
                            $productData['sales'][$product['guid']]['sum_gross_value']
                        ) . '%',
                        'gp_value' => $this->currencySymbol . number_format(
                            $productData['sales'][$product['guid']]['sum_net_value'] - $productData['sales'][$product['guid']]['sum_tax_value'] - $sum_cost_price,
                            2
                        ),
                        'gross_profit' => $this->calculateGP(
                            (float) $productData['sales'][$product['guid']]['sum_net_value'],
                            (float) $productData['sales'][$product['guid']]['sum_tax_value'],
                            (float) $sum_cost_price
                        )
                    ];

                    $results['summary']['count'] += $results['groupings'][$group->id]['products'][$product['guid']]['count'];
                    $results['summary']['sum_gross_value'] += (float) $productData['sales'][$product['guid']]['sum_gross_value'];
                    $results['summary']['sum_net_value'] += (float) $productData['sales'][$product['guid']]['sum_net_value'];
                    $results['summary']['sum_refund_value'] += isset($productData['refunds'][$product['guid']]) ? (float) $productData['refunds'][$product['guid']]['sum_net_value'] : 0.00;
                    $results['summary']['refund_count'] += isset($productData['refunds'][$product['guid']]) ? $productData['refunds'][$product['guid']]['count'] : 0;
                    $results['summary']['sum_discount_value'] += (float) $productData['sales'][$product['guid']]['sum_discount_value'];
                    $results['summary']['sum_tax_value'] += (float) $productData['sales'][$product['guid']]['sum_tax_value'];
                    $results['summary']['sum_cost_price'] += (float) $sum_cost_price;

                    $results['groupings'][$group->id]['summary']['blank'] = ' ';
                    $results['groupings'][$group->id]['summary']['count'] += $results['groupings'][$group->id]['products'][$product['guid']]['count'];
                    $results['groupings'][$group->id]['summary']['sum_gross_value'] += (float) $productData['sales'][$product['guid']]['sum_gross_value'];
                    $results['groupings'][$group->id]['summary']['sum_net_value'] += (float) $productData['sales'][$product['guid']]['sum_net_value'];
                    $results['groupings'][$group->id]['summary']['sum_refund_value'] += isset($productData['refunds'][$product['guid']]) ? (float) $productData['refunds'][$product['guid']]['sum_net_value'] : 0.00;
                    $results['groupings'][$group->id]['summary']['refund_count'] += isset($productData['refunds'][$product['guid']]) ? $productData['refunds'][$product['guid']]['count'] : 0;
                    $results['groupings'][$group->id]['summary']['sum_discount_value'] += (float) $productData['sales'][$product['guid']]['sum_discount_value'];
                    $results['groupings'][$group->id]['summary']['sum_tax_value'] += (float) $productData['sales'][$product['guid']]['sum_tax_value'];
                    $results['groupings'][$group->id]['summary']['costprice'] += (float) $cost_price;
                    $results['groupings'][$group->id]['summary']['sum_cost_price'] += (float) $sum_cost_price;
                    $results['groupings'][$group->id]['summary']['percentage_of_sales'] = $this->calculatePercentage(
                        $this->sumTotalSales,
                        $results['groupings'][$group->id]['summary']['sum_gross_value']
                    ) . '%';
                }
            }

            $results['groupings'][$group->id]['summary']['gp_value'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['sum_net_value'] - $results['groupings'][$group->id]['summary']['sum_tax_value'] - $results['groupings'][$group->id]['summary']['sum_cost_price'],
                2
            );
            $results['groupings'][$group->id]['summary']['gp'] = $this->calculateGP(
                (float) $results['groupings'][$group->id]['summary']['sum_net_value'],
                (float) $results['groupings'][$group->id]['summary']['sum_tax_value'],
                (float) $results['groupings'][$group->id]['summary']['sum_cost_price']
            );

            $results['groupings'][$group->id]['summary']['sum_gross_value'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['sum_gross_value'],
                2
            );
            $results['groupings'][$group->id]['summary']['sum_net_value'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['sum_net_value'],
                2
            );
            $results['groupings'][$group->id]['summary']['sum_discount_value'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['sum_discount_value'],
                2
            );
            $results['groupings'][$group->id]['summary']['sum_refund_value'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['sum_refund_value'],
                2
            );
            $results['groupings'][$group->id]['summary']['sum_tax_value'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['sum_tax_value'],
                2
            );
            $results['groupings'][$group->id]['summary']['costprice'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['costprice'],
                2
            );
            $results['groupings'][$group->id]['summary']['sum_cost_price'] = $this->currencySymbol . number_format(
                $results['groupings'][$group->id]['summary']['sum_cost_price'],
                2
            );


            if (empty($results['groupings'][$group->id]['products'])) {
                unset($results['groupings'][$group->id]);
            }
        }

        return $results;
    }

    private function calculatePercentage($total, $productSales)
    {
        if ((float) $productSales > 0 && (float) $total > 0) {
            $percentage = ((float) $productSales / (float) $total) * 100;
        } else {
            $percentage = 0;
        }

        return number_format($percentage, 2);
    }

    private function setSummary()
    {
        $this->summary['count'] = 0;
        $this->summary['sum_gross_value'] = 0;
        $this->summary['sum_net_value'] = 0;
        $this->summary['sum_discount_value'] = 0;
        $this->summary['sum_tax_value'] = 0;
        $this->summary['costprice'] = 0;
        $this->summary['sum_cost_price'] = 0;
        $this->summary['gp'] = 0;
        $this->summary['percentage_of_sales'] = 0;
    }

    private function calculateGP(float $net_value, float $tax_value, float $cost)
    {
        $net = ($net_value - $tax_value);

        if ($net > 0) {
            $result = (($net - $cost) / $net) * 100;
            $gp = number_format($result, 2) . '%';
        } else {
            $gp = number_format(0, 2) . '%';
        }

        return $gp;
    }
}
