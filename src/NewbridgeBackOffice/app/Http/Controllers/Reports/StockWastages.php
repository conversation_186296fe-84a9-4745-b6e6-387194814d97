<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\StockTransactions;

class StockWastages extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->currencySymbol = CurrencyHelper::symbol($this->company_id);

        $this->tables = [
            "Wastage Report" => [
                "columns" => [
                    "Product Name" => ['product', 'displayname', 'string'],
                    "Quantity" => [null, 'sum_quantity', 'string'],
                    "Retail Value" => [null, 'sum_saleprice', 'string'],
                    "Cost Value" => [null, 'cost_value', 'string'],
                    "Reason" => [null, 'reason', 'string'],
                    "Clerk" => ['clerk', 'full_name', 'string'],
                    "User" => ['user', 'name', 'string']
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'wastages',
                    'summary_result' => 'summary'
                ],
                "summary" => [
                    "Product Name" => [null, 'blank', 'string'],
                    "Quantity" => [null, 'quantity', 'string'],
                    "Retail Value" => [null, 'retail_value', 'string'],
                    "Cost Value" => [null, 'cost_value', 'string'],
                    "Reason" => [null, 'blank', 'string'],
                    "Clerk" => [null, 'blank', 'string'],
                    "User" => [null, 'blank', 'string']
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->wastages();
    }

    public function wastages()
    {
        $select = 'company_id, product_id, created_at, costprice,  sum(quantity) as sum_quantity, reason, product_id, CONCAT("'.$this->currencySymbol.'", sum(saleprice)) as sum_saleprice, clerk_guid, user_id, CONCAT("'.$this->currencySymbol.'", FORMAT(((costprice * sum(quantity)) * -1), 2)) as cost_value';
        $query = StockTransactions::selectRaw($select)->where([
            ['company_id', $this->company_id],
            ['site_num', $this->site_num],
            ['summary_type', 5]
        ])
            ->groupBy('product_id')
            ->groupBy('reason')
            ->whereBetween('created_at', [$this->start, $this->end])
            ->with(['product' => function ($q) {
                $q->select('displayname', 'id')->withTrashed();
            }])
            ->with('user')
            ->with(['clerk' => function ($q2) {
                $q2->select('full_name', 'guid')->withTrashed();
            }])
            ->orderBy('created_at', 'ASC')
            ->get()
            ->toArray();

        $result = [];
        $result['wastages'] = $query;

        $result['summary']['blank'] = '';
        $result['summary']['quantity'] = 0;
        $result['summary']['retail_value'] = 0;
        $result['summary']['cost_value'] = 0;

        if (!empty($query)) {
            foreach ($query as $q) {
                $result['summary']['quantity'] += (float) $q['sum_quantity'];
                $result['summary']['retail_value'] += (float) str_replace($this->currencySymbol, '', $q['sum_saleprice']);
                $result['summary']['cost_value'] += (float) str_replace($this->currencySymbol, '', $q['cost_value']);
            }
        }
        $result['summary']['retail_value'] = $this->currencySymbol.$result['summary']['retail_value'];
        $result['summary']['cost_value'] = $this->currencySymbol.$result['summary']['cost_value'];

        return json_decode(json_encode($result), true);
    }
}
