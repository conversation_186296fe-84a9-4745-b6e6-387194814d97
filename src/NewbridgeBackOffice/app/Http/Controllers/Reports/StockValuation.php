<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\StockTransactions;
use NewbridgeWeb\Repositories\SubDepartment;

class StockValuation extends ReportAbstractController
{
    public $data;
    public $grouping = 'table-subdepartment';
    public $departments;
    public $subdepartment;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Stock Valuation" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Product Name" => [null, 'displayname', 'string'],
                        "SKU Quantity" => [null, 'sku_quantity', 'string'],
                        "Unit Quantity" => [null, 'unit_quantity', 'decimal-only'],
                        "Stock Value" => [null, 'stock_value', 'decimal'],
                        "Unit Cost Price" => [null, 'unit_cost_price', 'decimal'],
                        "Tax Amount" => [null, 'vat_amount', 'decimal'],
                        "Selling Price" => [null, 'selling_price', 'decimal'],
                        "Gross Profit %" => [null, 'gp', 'string']
                    ],
                    "data" => [
                        'column_count' => 8,
                        'resultset' => 'products',
                        'summary_result' => 'summary'
                    ],
                    "summary" => [
                        "&nbsp; " => [null, 'blank', 'string'],
                        "&nbsp;" => [null, 'blank', 'string'],
                        "&nbsp;" => [null, 'blank', 'string'],
                        "Total Value" => [null, 'total_stock_value', 'decimal'],
                        "&nbsp;" => [null, 'blank', 'string'],
                        "&nbsp;" => [null, 'blank', 'string'],
                        "&nbsp;" => [null, 'blank', 'string'],
                        "&nbsp;" => [null, 'blank', 'string'],
                    ]
                ],
                "data" => [
                    'column_count' => 8,
                    'resultset' => 'subdepartments',
                    'summary_result' => null
                ]
            ],
            "Stock Valuation Total" => [
                "columns" => [
                    "Total Stock Value" => [null, 'total_stock_value', 'decimal'],
                ],
                "data" => [
                    'column_count' => 1,
                    'resultset' => 'total_stock',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->grouping = $report_data['grouping'];
        $this->departments = $report_data['department'];
        $this->subdepartment = $report_data['subdepartment'];

        return $this->productStock();
    }

    public function productStock()
    {
        $results = [];

        $results['total_stock'] = [];
        $results['total_stock'][] = [
            'total_stock_value' => 0
        ];

        $stockProducts = Products::where('company_id', $this->company_id)
            ->whereHas('sku')
            ->whereNotNull('sku_guid')
            ->get()->pluck('id')->implode(',');

        $productCurrentStock = DB::select("SELECT product_quantity, product_id
                FROM (
                    SELECT st.product_quantity, st.product_id, 
                           ROW_NUMBER() OVER (PARTITION BY st.product_id ORDER BY st.id DESC) AS rn
                    FROM plu_stock_transactions st
                    INNER JOIN (
                        SELECT product_id, MAX(created_at) as max_created_at
                        FROM plu_stock_transactions
                        WHERE status = 1
                            AND created_at <= '$this->end'
                            AND product_id IN ($stockProducts)
                        GROUP BY product_id
                    ) as latest ON st.product_id = latest.product_id 
                                 AND st.created_at = latest.max_created_at
                ) AS ranked
                WHERE rn = 1");

        $productCurrentStock = collect($productCurrentStock);
        $productCurrentStock = $productCurrentStock->keyBy('product_id');

        if ($this->departments == null) {
            $this->departments = Departments::where('company_id', $this->company_id)->pluck('guid');
        }

        if ($this->grouping == 'subdepartment') {
            $groups = SubDepartment::where('company_id', $this->company_id)->with(
                ['products' => function ($q) {
                    $q->with('sku')->whereHas('sku')
                        ->whereNotNull('sku_guid')
                        ->orderBy('displayname', 'ASC')
                        ->whereIn('site_num', [0, $this->site_num]);
                }]
            );

            if ($this->subdepartment != null) {
                $groups = $groups->whereIn('guid', $this->subdepartment);
            }

            $groups = $groups->get();
        } else {
            $groups = Departments::where('company_id', $this->company_id)->with(['products' => function ($q) {
                $q->with('sku')->whereHas('sku')
                    ->whereNotNull('sku_guid')->orderBy('displayname', 'ASC')
                    ->whereIn('site_num', [0, $this->site_num]);
            }])->whereIn('guid', $this->departments)->get();
        }


        foreach ($groups as $sub) {
            $results['subdepartments'][$sub->id]['summary'] = [
                'blank' => '',
                'total_stock_value' => 0
            ];

            $results['subdepartments'][$sub->id]['id'] = $sub->id;
            $results['subdepartments'][$sub->id]['displayname'] = $sub->displayname;
            $results['subdepartments'][$sub->id]['products'] = [];

            foreach ($sub->products as $p) {
                $current_stock = isset($productCurrentStock[$p->id]) ? $productCurrentStock[$p->id]->product_quantity : 0;

                if ($current_stock != 0) {
                    $res = [
                        'displayname' => $p->displayname,
                        'sku_quantity' => $p->sku['qty'] > 0 ? number_format(($current_stock / (float) $p->sku['qty']), 2) . ' (' . $p->sku['displayname'] . ')' : 0,
                        'unit_quantity' => $current_stock,
                        'vat_amount' => $p->vat_amount,
                        'stock_value' => ($current_stock * $p->costprice),
                        'unit_cost_price' => $p->costprice,
                        'selling_price' => $p->selling_price_1,
                        'gp' => $p->costprice > 0 || $p->vat_amount > 0 ? $p->gross_profit : 0
                    ];

                    $results['subdepartments'][$sub->id]['summary']['total_stock_value'] += (float) ($current_stock * (float) $p->costprice);
                    $results['subdepartments'][$sub->id]['products'][] = $res;
                }
            }

            $results['total_stock'][0]['total_stock_value'] += $results['subdepartments'][$sub->id]['summary']['total_stock_value'];

            if (count($results['subdepartments'][$sub->id]['products']) == 0) {
                unset($results['subdepartments'][$sub->id]);
            }
        }

        return $results;
    }
}
