<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\StockTransactions;

class ProductAudit extends ReportAbstractController
{
    public $product = null;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Product Audit (".$var.")" => [
                "columns" => [
                    "Date" => [null, 'date', 'date'],
                    "Type" => [null, 'type', 'string'],
                    "Quantity" => [null, 'quantity', 'string'],
                    "Reference" => [null, 'reference', 'string'],
                    "Value" => [null, 'value', 'decimal'],
                    "Current Stock" => [null, 'current_stock', 'string']
                ],
                "data" => [
                    'column_count' => 6,
                    'resultset' => 'audit',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->product = isset($report_data['product']) && $report_data['product'] ? Products::find($report_data['product']) : null;

        return $this->productAudit();
    }

    public function productAudit()
    {
        if ($this->product != null) {
            $query = StockTransactions::where([
                ['company_id', $this->company_id],
                ['status', 1]
            ])
                ->whereIn('site_num', $this->site_num)
                ->with('transaction')
                ->with('summary')
                ->whereBetween('created_at', [$this->start, $this->end])
                ->where('product_id', $this->product->id)
                ->orderBy('created_at', 'ASC')
                ->orderBy('id', 'ASC')
                ->get();


            $result = [];
            $result['summary'] = [
                'product' => $this->product->displayname,
                'blank' => ' ',
                'total_stock' => 0
            ];

            foreach ($query as $q) {
                $result['audit'][] = [
                    'product' => $this->product->displayname,
                    'date' => $q->created_at,
                    'type' => $q->type_text,
                    'quantity' => in_array($q->summary_type, [4,6,3]) ? $q->qty_entered : $q->quantity,
                    'reference' => $q->summary_type == 1 && $q->transaction ? $q->transaction->id : $q->summary_id,
                    'value' => $q->summary_type == 1 && $q->transaction ? $q->transaction->net_value : $q->total,
                    'current_stock' => $q->product_quantity
                ];

                $result['summary']['total_stock'] += in_array($q->summary_type, [4,6,3]) ? $q->qty_entered : $q->quantity;
            }

            return $result;
        } else {
            return [];
        }
    }
}
