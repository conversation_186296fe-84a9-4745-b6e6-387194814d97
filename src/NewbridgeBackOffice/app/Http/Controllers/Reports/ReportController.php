<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Auth;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\DataTransfer\Export\ReportsExport;
use NewbridgeWeb\Http\Controllers\Xero\XeroAuth;
use NewbridgeWeb\Http\Controllers\Xero\XeroCreateInvoice;
use NewbridgeWeb\Http\Helpers;
use NewbridgeWeb\Http\Helpers\SiteHelper;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Jobs\InstantRunReport;
use NewbridgeWeb\Mail\ReportReady;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\CustomerGroup;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Reports;
use NewbridgeWeb\Repositories\ReportScheduleResults;
use NewbridgeWeb\Repositories\ReportSchedules;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\StockSummary;
use PDF;
use Ramsey\Uuid\Uuid;

class ReportController extends Controller
{
    private $company_id;
    private $site_num;
    private $start;
    private $end;
    private $end_compare;
    private $start_compare;
    private $start_time;
    private $end_time;
    private $options;
    private $site_name;
    private $terminal;
    private $transactions;
    private $report;
    private $report_data;
    private $report_id;
    private $guid;
    private $format;
    private $tables;
    private $save;
    private $data;
    private $supplier;
    private $subdepartment;
    private $department;
    private $clerk;
    private $product;
    private $sendmail;
    private $customer;
    private $payment_method;
    private $comparison_option;
    private $loyalty_group;
    private $sortBy;
    private $grouping;
    private $gross_net;
    private $payment_methods;
    private $daypart;
    private $sales_type = 'all';

    private bool $onlyAbb = false;

    public function index()
    {
        $reports = Reports::orderBy('priority', 'ASC')->get();
        $site = Sites::where('company_id', Auth::user()->company_id)->where('site_num', \Session::get('current_site'))->first();

        $key = session('user.id') . ':fave_reports';
        $favourites = Redis::connection('cache')->zrevrange($key, 0, 9, ['withscores' => true]);

        return view('modules.reports.index', compact("reports", "site", 'favourites'));
    }

    public function form(Request $request)
    {
        $url = $request->segment(2);

        $this->report = Reports::where('url', $url)->first();

        if ($this->report == null) {
            abort(404);
        }

        if (!Helpers\AbilityHelper::ability('newbridge,owner,reseller', $this->report['permission'])) {
            abort(403);
        }

        $company = Company::find(Auth::user()->company_id);

        $options = explode(',', $this->report['options']);

        $this->options = $options;

        $report = [];
        $report['name'] = $this->report->name;
        $report['site'] = null;
        $report['terminal'] = null;
        $report['url'] = $this->report->url;
        $report['id'] = $this->report->id;

        $linkedCompanies = Helpers\CompanyLinks::links($company->id);

        /**
         * Get report filters from the session and if not available
         * set defaults for the company
         */
        if (Session::has('report-filters')) {
            $filters = Session::get('report-filters');
        } else {
            $filters = [
                'site' => null,
                'terminal' => null,
                'start' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->subDay(),
                'end' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone()),
                'start_compare' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->subYear()->startOfDay(),
                'end_compare' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->subYear()->endOfDay(),
                'grouping' => null,
                'stockperiod' => null,
                'offset' => $company->time_offset,
                'start_time' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->subDay()->toTimeString(),
                'end_time' => Carbon::now('UTC')->setTimezone(TimezoneHelper::getTimezone())->toTimeString(),
                'subdepartment' => null,
                'department' => null,
                'daypart' => null,
                'clerk' => Clerks::where('company_id', $company->id)->first(),
                'product' => null,
                'supplier' => null,
                'customer' => null,
                'payment_method' => null,
                'comparison_option' => 0,
                'loyalty_group' => CustomerGroup::whereIn('company_id', $linkedCompanies)->get(),
                'sortBy' => 'displayname',
                'gross_net' => 'gross',
                'payment_methods' => [],
                'sales_type' => 'all',
                'areas' => Auth::check() ? Helpers\AreaHelper::myAreas() : Helpers\AreaHelper::allAreas($company->id),
                'only_abb' => false
            ];
        }

        if (in_array('stockperiod', $options)) {
            $periods = StockSummary::where('company_id', $company->id)
                ->where('summary_type', 3)
                ->where('status', 4)
                ->orderBy('stocktake_date', 'DESC')
                ->limit(2)
                ->get();

            if ($periods->count() == 2) {
                $this->end = Carbon::parse($periods[0]->stocktake_date, 'UTC');
                $this->start = Carbon::parse($periods[1]->stocktake_date, 'UTC');
                $filters['start'] = $this->start;
                $filters['end'] = $this->end;
            } else {
                return 'Sorry, you have not completed enough stock takes to complete a period';
            }
        }

        $has_xero = Company::find($company->id)->has_xero;

        return view('modules.reports.report-results', compact('options', 'report', 'filters', 'has_xero'));
    }

    public function run(Request $request, $save = true, $format = 'html', $sendmail = [])
    {
        $this->sendmail = $sendmail;

        Redis::set('report-'.$this->guid.'-status', json_encode(['status' => 'danger', 'text' => 'Starting...', 'percent' => '10']));
        Redis::expire('report-'.$this->guid.'-status', (config('newbridge.report_expire_time') * 60));

        $form = $request->input();

        if (!isset($form['site'])) {
            $form['site'] = '';
        }

        $this->comparison_option = $form['comparison_option'] ?? 0;
        $this->company_id = $form['company_id'];
        $this->report_id = (int) $form['report_id'];
        $this->sortBy = $form['sortBy'] ?? 'displayname';
        $this->start = $form['start'] ?? Carbon::now('UTC')->subDay();
        $this->end = $form['end'] ?? Carbon::now('UTC');
        $this->site_num = $form['site'] ?? null;
        $this->supplier = isset($form['supplier']) && $form['supplier'] != '' ? $form['supplier'] : null;
        $this->terminal = isset($form['terminal']) && $form['terminal'] != '' ? $form['terminal'] : null;

        if (is_array($this->terminal) && !empty($this->terminal)) {
            $terminal_nums = [];

            foreach ($this->terminal as $term) {
                $terminal = Pos::find($term);
                if($terminal != null) {
                    $terminal_nums[] = $terminal->terminal_num;
                }
            }

            $this->terminal = $terminal_nums;
        }

        $this->customer = isset($form['customer']) && $form['customer'] != '' ? $form['customer'] : null;
        if ($this->customer) {
            $this->customer = Customer::find($this->customer);
        }

        $this->report = Reports::find($this->report_id);
        $this->format = $format;
        $this->sales_type = isset($form['sales_type']) && $form['sales_type'] != '' ? $form['sales_type'] : 'all';

        if ($request->input('guid')) {
            $this->guid = $request->input('guid');
        } else {
            $this->guid = Uuid::uuid4();
        }

        $this->save = (bool) $save;

        $this->start_compare = $form['start_compare'] ?? null;
        $this->end_compare = $form['end_compare'] ?? null;
        $this->start_time = $form['start_time'] ?? null;
        $this->end_time = $form['end_time'] ?? null;
        $this->payment_method = $form['payment_method'] ?? null;
        $this->subdepartment = $form['subdepartment'] ?? null;
        $this->department = $form['department'] ?? null;
        $this->daypart = $form['daypart'] ?? null;
        $this->grouping = $form['grouping'] ?? null;
        $this->gross_net = $form['gross_net'] ?? null;
        $this->payment_methods = $form['payment_methods'] ?? null;
        $this->product = isset($form['product']) ? (int) $form['product'] : null;
        $this->onlyAbb = isset($form['only_abb']) && $form['only_abb'] == 'true';

        if (isset($form['clerk']) && $form['clerk'] != '') {
            $this->clerk = Clerks::find($request->input('clerk'));
        } else {
            $this->clerk = null;
        }

        if (isset($form['loyalty_group']) && $form['loyalty_group'] != '') {
            $this->loyalty_group = $form['loyalty_group'];
        } else {
            $this->loyalty_group = null;
        }

        $site = Sites::where('company_id', $this->company_id)->where('site_num', $form['site'])->first();

        $this->site_name = $site ? $site['site_name'] : 'All Sites';

        // Convert UTC Times
       // if ($this->report->id != 54) {
        $this->convertTimesFromUTC($this->start, $this->end, $this->start_compare, $this->end_compare, $this->start_time, $this->end_time);
        //}

        return $this->createReportObject();
    }

    private function convertTimesFromUTC($start = null, $end =  null, $start_compare = null, $end_compare = null, $start_time = null, $end_time = null): void
    {
        if ($start != null) {
            $this->start = Carbon::parse($start, TimezoneHelper::getTimezone())->setTimezone('UTC');
        }

        if ($end != null) {
            $this->end = Carbon::parse($end, TimezoneHelper::getTimezone())->setTimezone('UTC');
        }

        if ($start_compare != null) {
            $this->start_compare = Carbon::parse($start_compare, TimezoneHelper::getTimezone())->setTimezone('UTC');
        }

        if ($end_compare != null) {
            $this->end_compare = Carbon::parse($end_compare, TimezoneHelper::getTimezone())->setTimezone('UTC');
        }

        if ($start_time != null) {
            $this->start_time = Carbon::createFromFormat('H:i:s', $start_time, TimezoneHelper::getTimezone())
                ->setTimezone('UTC')->format('H:i:s');
        }

        if ($end_time != null) {
            $this->end_time = Carbon::createFromFormat('H:i:s', $end_time, TimezoneHelper::getTimezone())
                ->setTimezone('UTC')->format('H:i:s');
        }
    }

    public function exportOrSave($report_data)
    {
        $this->site_name = Sites::where('company_id', $this->company_id)
            ->where('site_num', $this->site_num)->value('site_name');

        $this->report = Reports::find($report_data['report_id']);

        \Sentry\configureScope(function (\Sentry\State\Scope $scope): void {
            $scope->setTag('reports.id', $this->report['id']);
            $scope->setTag('reports.name', $this->report['name']);
        });

        /**
         * Get the report class and improve
         */
        $this->report_data = $report_data;
        $this->getReportData($report_data);
        $this->guid = $report_data['guid'];

        Redis::set('report-'.$report_data['guid'].'-status', json_encode(['status' => 'success', 'text' => 'Data Loaded', 'percent' => '80']));
        Redis::expire('report-'.$this->guid.'-status', (config('newbridge.report_expire_time') * 60));

        if ($report_data['save'] === true) {
            $this->saveReport($report_data['name'], $this->data, $report_data, $report_data['format']);
        }

        return true;
    }

    private function createReportObject()
    {
        $reportClass = Reports::find($this->report->id);
        $class = new $reportClass['class']();
        $class->company_id = (int) $this->company_id;
        if ($this->product !== null) {
            $product_name = Products::find($this->product);

            if ($product_name) {
                $this->tables = $class->setTables($product_name->displayname);
            } else {
                $this->tables = $class->setTables();
            }
        } elseif (stristr($reportClass->options, 'multi-daterange')) {
            /**
             * TODO: turn this into pretty date string
             */
            $strings = [];
            $strings[0] = 0;
            $strings[1] = 0;

            $falsy = (int) $this->comparison_option === 1;

            if ($falsy === false) {
                $this->start_compare = null;
                $this->end_compare = null;
            }

            $this->tables = $class->setTables($strings, $falsy);
        } else {
            $this->tables = $class->setTables();
        }

        /** Convert all sites to arrays for where in */
        if ($this->site_num == null || $this->site_num == '') {
            $this->site_num = SiteHelper::allSites($this->company_id);
        } else {
            $this->site_num = $this->site_num;
        }

        $report_data = [
            'name' => $this->report->name,
            'start' => $this->start->format('d-m-Y H:i:s'),
            'end' => $this->end->format('d-m-Y H:i:s'),
            'start_compare' => $this->start_compare !== null ? $this->start_compare : null,
            'end_compare'  => $this->end_compare !== null ? $this->end_compare : null,
            'timezone' => TimezoneHelper::getTimezone(),
            'view' => 'modules.reports.results-table.table',
            'url' => $this->report->url,
            'company_id' => (int) $this->company_id,
            'site' => $this->site_num,
            'site_name' => $this->site_name,
            'terminal' => $this->terminal ?: null,
            'format' => $this->format,
            'tables' => $this->tables,
            'guid' => $this->guid,
            'supplier' => $this->supplier,
            'subdepartment' => $this->subdepartment,
            'department' => $this->department,
            'clerk' => $this->clerk,
            'product' => $this->product,
            'save' => $this->save,
            'report_id' => $this->report_id,
            'rotation' => $this->report->rotation,
            'paper_size' => $this->report->paper_size,
            'sendmail' => !empty($this->sendmail),
            'schedule_id' => !empty($this->sendmail) ? $this->sendmail['schedule_id'] : null,
            'is_scheduled' => isset($this->sendmail['is_scheduled']),
            'customer' => $this->customer,
            'payment_method' => $this->payment_method,
            'comparison_option' => $this->comparison_option,
            'loyalty_group' => $this->loyalty_group,
            'sortBy' => $this->sortBy,
            'grouping' => $this->grouping,
            'gross_net' => $this->gross_net,
            'payment_methods' => $this->payment_methods,
            'daypart' => $this->daypart,
            'sales_type' => $this->sales_type,
            'areas' => Auth::check() ? Helpers\AreaHelper::myAreas() : Helpers\AreaHelper::allAreas((int) $this->company_id),
            'currency' => !in_array($this->format, ['xls','csv']) ? Helpers\CurrencyHelper::symbol($this->company_id) : '',
            'only_abb' => $this->onlyAbb
        ];

        if (in_array('week-date', explode(',', $this->report->options))) {
            $this->start = $this->start->setTimezone(TimezoneHelper::getTimezone())->startOfDay();

            $report_data['days'] = $class->getDays($this->start);

            $tableDays = $report_data['days'];

            foreach ($tableDays as $k => $v) {
                switch($v) {
                    case 'Mon':
                        $tableDays[$k] = 2;

                        break;
                    case 'Tue':
                        $tableDays[$k] = 3;

                        break;
                    case 'Wed':
                        $tableDays[$k] = 4;

                        break;
                    case 'Thu':
                        $tableDays[$k] = 5;

                        break;
                    case 'Fri':
                        $tableDays[$k] = 6;

                        break;
                    case 'Sat':
                        $tableDays[$k] = 7;

                        break;
                    case 'Sun':
                        $tableDays[$k] = 1;

                        break;
                }
            }

            $this->tables = $class->setTables($tableDays);
            $report_data['tables'] = $this->tables;
            $report_data['start'] = $this->start->toDateTimeString();
            $report_data['end'] = $this->start->addDays(6)->endOfDay()->toDateTimeString();
        }

        if (in_array('week-date-unrestricted', explode(',', $this->report->options))) {
            $this->start = $this->start->setTimezone(TimezoneHelper::getTimezone())->startOfDay();

            $report_data['days'] = $class->getDays($this->start);

            $tableDays = $report_data['days'];

            foreach ($tableDays as $k => $v) {
                switch($v) {
                    case 'Mon':
                        $tableDays[$k] = 2;

                        break;
                    case 'Tue':
                        $tableDays[$k] = 3;

                        break;
                    case 'Wed':
                        $tableDays[$k] = 4;

                        break;
                    case 'Thu':
                        $tableDays[$k] = 5;

                        break;
                    case 'Fri':
                        $tableDays[$k] = 6;

                        break;
                    case 'Sat':
                        $tableDays[$k] = 7;

                        break;
                    case 'Sun':
                        $tableDays[$k] = 1;

                        break;
                }
            }

            $this->tables = $class->setTables($tableDays);
            $report_data['tables'] = $this->tables;
            $report_data['start'] = $this->start->toDateTimeString();
            $report_data['end'] = $this->start->addDays(6)->endOfDay()->toDateTimeString();
        }

        if (in_array('week-days', explode(',', $reportClass->options)) || in_array('week-date-unrestricted', explode(',', $reportClass->options))) {
            $report_data['days'] = $class->getDays($this->start);
            if ($reportClass->id == 54) {
                unset($report_data['days']);
            }
        }

        // Add Start and End Time to the array
        if ($this->start_time !== null && $this->end_time !== null) {
            $report_data['start_time'] = $this->start_time;
            $report_data['end_time'] = $this->end_time;
        }

        // Add start and end comparison to the array
        if ($this->start_compare != null && $this->end_compare != null) {
            $report_data['start_compare'] = $this->start_compare->setTimezone(TimezoneHelper::getTimezone())->format('d-m-Y H:i:s');
            $report_data['end_compare'] = $this->end_compare->setTimezone(TimezoneHelper::getTimezone())->format('d-m-Y H:i:s');
        }

        $this->report_data = $report_data;

        Redis::set('report-'.$this->guid.'-status', json_encode(['status' => 'warning', 'text' => 'Getting Ready', 'percent' => '35']));
        Redis::expire('report-'.$this->guid.'-status', (config('newbridge.report_expire_time') * 60));

        // set the report data in REDIS using the GUID as the reference field, also set the report status
        Redis::set('report-'.$this->guid, json_encode($report_data));
        Redis::expire('report-'.$this->guid, (config('newbridge.report_expire_time') * 60));

        if ($this->format !== 'xero') {
            $job = new InstantRunReport($this->guid);
            dispatch($job)->delay(1)->onQueue(($report_data['is_scheduled'] ? HorizonQueues::HIGH_MEMORY_BACKGROUND : HorizonQueues::HIGH_MEMORY));

            Redis::set('report-' . $this->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Request Received', 'percent' => '55']));
            Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));

            return $this->guid;
        } else {
            Redis::set('report-' . $this->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Checking Xero', 'percent' => '40']));
            Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));

            return $this->xeroExport();
        }
    }

    private function xeroExport()
    {
        $xero = new XeroAuth();
        $result = $xero->checkCurrentTokenOrStartAuthProcess();

        Redis::set('report-' . $this->guid . '-status', json_encode(['status' => 'warning', 'text' => 'Xero Checked', 'percent' => '45']));
        Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));

        if ($result['status'] === 'login') {
            return ['status' => 'login', 'url' => $result['url'], 'guid' => $this->guid];
        } else {
            // do the export
            $reportClass = Reports::find($this->report->id);
            $class = new $reportClass['class']();

            $reportData = $class->data($this->transactions, $this->report_data);
            $reportData['date'] = $this->report_data['end'];

            $request = new \Illuminate\Http\Request();
            $request->replace(json_decode(json_encode($reportData), true));

            $invoice = new XeroCreateInvoice();
            return $invoice->createInvoice($request);
        }
    }

    private function saveReport($title, $data, $report, $format)
    {
        $this->site_name = Sites::where('company_id', $this->company_id)->where('site_num', $this->site_num)->value('site_name');
        $ref = md5($report['company_id']);

        if ($format == 'xls') {
            $data = ["view" => $report['view'], "compact" => ['results' => $data, 'report' => $report]];

            $file = Excel::store(new ReportsExport($data), '/reports/exports/report-export-'.$this->guid.'.xlsx');

            $filename = '/reports/exports/report-export-'.$this->guid.'.xlsx';

            $file = null;

            while ($file == null) {
                if (Storage::exists($filename)) {
                    $file = Storage::get($filename);
                }
            }

            Redis::set('report-'.$this->guid.'-status', json_encode(['status' => 'complete-download', 'filepath' => $filename]));
            Redis::expire('report-'.$this->guid.'-status', (config('newbridge.report_expire_time') * 60));
        } elseif ($format == 'csv') {
            $data = ['view' => $this->report_data['view'], "compact" => ['results' => $data, 'report' => $report]];

            $file = Excel::store(new ReportsExport($data), '/reports/exports/report-export-'.$this->guid.'.csv');

            $filename = '/reports/exports/report-export-'.$this->guid.'.csv';

            $file = null;

            while ($file == null) {
                if (Storage::exists($filename)) {
                    $file = Storage::get($filename);
                }
            }

            Redis::set('report-'.$this->guid.'-status', json_encode(['status' => 'complete-download', 'filepath' => $filename]));
            Redis::expire('report-'.$this->guid.'-status', (config('newbridge.report_expire_time') * 60));
        } elseif ($format == 'pdf') {
            $file = PDF::loadView($this->report_data['view'], ['results' => $this->data, 'report' => $this->report_data])->setPaper(strtoupper($report['paper_size']), $report['rotation']);

            $filename = '/reports/exports/report-export-'.$this->guid.'.pdf';

            $file = $file->output();

            Storage::put($filename, $file);

            $file = null;

            while ($file == null) {
                if (Storage::exists($filename)) {
                    $file = Storage::get($filename);
                }
            }

            Redis::set('report-'.$this->guid.'-status', json_encode(['status' => 'complete-download', 'filepath' => $filename]));
            Redis::expire('report-'.$this->guid.'-status', (config('newbridge.report_expire_time') * 60));
        } elseif ($format === 'html') {
            $this->site_name = Sites::where('company_id', $this->company_id)->where('site_num', $this->site_num)->value('site_name');

            $result = view($this->report_data['view'], ['results' => $this->data, 'report' => $this->report_data])->render();

            Redis::set('report-'.$this->guid.'-status', json_encode(['status' => 'complete', 'data' => $result]));
            Redis::expire('report-'.$this->guid.'-status', (config('newbridge.report_expire_time') * 60));
        } else {
            abort(400);
        }

        if ($report['sendmail'] === true) {
            $this->mailSavedReport($report['schedule_id'], $ref, $report['guid'], $file, $filename);
        }

        return $report['guid'];
    }

    private function getReportData($report_data)
    {
        $reportClass = Reports::find($report_data['report_id']);
        $class = new $reportClass['class']();

        $this->start = Carbon::parse($report_data['start']);
        $this->end = Carbon::parse($report_data['end']);

        $this->start_time = isset($report_data['start_time']) && $report_data['start_time'] != null ? $report_data['start_time'] : null;
        $this->end_time = isset($report_data['end_time']) && $report_data['end_time'] != null ? $report_data['end_time'] : null;

        if ($report_data['start_compare'] !== null && $report_data['end_compare'] !== null) {
            $this->start_compare = Carbon::parse($report_data['start_compare'])->startOfDay();
            $this->end_compare = Carbon::parse($report_data['end_compare'])->endOfDay();
        }

        $this->start = $this->start->startOfDay();
        $this->end = $this->end->endOfDay();

        $this->data = $class->data(null, $report_data);
    }

    public function download(Request $request)
    {
        $path = $request->input('path');

        if ($path == null) {
            abort(404);
        }

        return  Storage::download($path);
    }

    private function mailSavedReport($report_id, $ref, $guid, $file, $filename)
    {
        $report = ReportSchedules::findOrFail($report_id);
        $company = Company::findOrFail($report->company_id);
        $ext = $this->getExtension($report);

        $result = new ReportScheduleResults();
        $result->schedule_id = $report_id;
        $result->result_datetime = Carbon::now();
        $result->created_at = Carbon::now();
        $result->link =  $filename; // needs changing to variable
        $result->save();

        if ($report->recipients != null && $report->recipients != "" && !empty($report->recipients)) {
            $mailable = [
                'to' => explode(',', str_replace(' ', '', $report->recipients)),
                'subject' => '[' . $company->company_name . '] ' . ucfirst($report->frequency_name) . ' Report: ' . $report['name'],
                'report_name' => $this->report->name,
                'path' => $file, // needs changing to variable
                'link' => config('app.path') . '/reports',
                'file' => $file,
                'format' => $report->format,
                'ext' => $ext
            ];

            $mailable['preHeaderText'] = 'A report you have requested is now available to view, if you did not request this report please contact your manager or check your report settings.';
            $mailable['mainTitle'] = 'Report Ready';
            $mailable['textBlock1'] = "Your scheduled report <b> " . $this->report->name . " </b> is attached to this email.";
            $mailable['buttonUrl'] = $mailable['link'];
            $mailable['buttonText'] = "View More Reports";
            $mailable['lowerBlocks'][] = [
                'background-color' => 'FFFFFF',
                'text-color' => '666666',
                'content' => 'For more detailed reports and analytics, take a look at the reports page on your dashboard. All of the reports can be downloaded in lots of handy formats too!',
                'additional-css' => ''
            ];

            try {
                Mail::to($mailable['to'])->send(new ReportReady($mailable));
            } catch (\Exception $e) {
                // do nothing
            }
        }
    }

    private function getExtension(ReportSchedules $report)
    {
        return match ($report->format) {
            "csv" => "csv",
            "xls" => "xlsx",
            default => "pdf",
        };
    }

    public function checkStatus(string $guid)
    {
        return \Illuminate\Support\Facades\Redis::get('report-' . $guid . '-status');
    }

    public function getTerminals(Request $request)
    {
        $terminals = \NewbridgeWeb\Repositories\Pos::where('company_id', $request->input('company'))
            ->where('site_num', $request->input('site'))->get();

        return response()->json($terminals);
    }
}
