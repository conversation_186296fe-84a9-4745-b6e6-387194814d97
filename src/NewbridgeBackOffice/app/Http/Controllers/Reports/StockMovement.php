<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\StockTransactions;
use NewbridgeWeb\Repositories\SubDepartment;

class StockMovement extends ReportAbstractController
{
    public $supplier;

    public function setTables($var = null): array
    {
        $this->tables = [
            "Stock Movement" => [
                "columns" => [
                    "" => [null, ['displayname'], 'title'],
                ],
                "nested" => [
                    "columns" => [
                        "Product" => [null, 'displayname', 'string'],
                        "Sales Consumption" => [null, 'transactions', 'string'],
                        "Wastage's" => [null, 'wastages', 'string'],
                        "Deliveries" => [null, 'deliveries', 'string'],
                        "Adjustments" => [null, 'adjustments', 'string'],
                        "Current Stock" => [null, 'stock', 'string']
                    ],
                    "data" => [
                        'column_count' => 6,
                        'resultset' => 'stock',
                        'summary_result' => null
                    ]
                ],
                "data" => [
                    'column_count' => 6,
                    'resultset' => 'subdepartments',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);
        $this->supplier = $report_data['supplier'];

        return $this->stockMovement();
    }

    public function stockMovement()
    {
        $products = Products::where([
            ['company_id', $this->company_id]
        ])
            ->whereHas('sku')->with('sku');

        $company = Company::find($this->company_id);

        if ($company->site_specific_products == 1) {
            $products = $products->where('site_num', $this->site_num);
        }

        if ($this->supplier != '') {
            $products = $products->where('supplier_guid', $this->supplier);
        }

        $products = $products->orderBy('displayname', 'ASC')->get();

        $subdepartments = SubDepartment::where('company_id', $this->company_id)->get();

        $movement = StockTransactions::select(DB::raw('product_id, summary_type, sum(quantity) as quantity'))
            ->where('company_id', $this->company_id)
            ->where('site_num', $this->site_num)
            ->whereBetween('created_at', [$this->start, $this->end])
            ->groupBy('summary_type')
            ->groupBy('product_id')
            ->where('status', 1)
            ->get();

        $now = now()->toDateTimeString();
        $productList = $products->pluck('id')->implode(',');

        $productCurrentStock = DB::select("SELECT product_quantity, product_id
                FROM (
                    SELECT st.product_quantity, st.product_id, 
                           ROW_NUMBER() OVER (PARTITION BY st.product_id ORDER BY st.id DESC) AS rn
                    FROM plu_stock_transactions st
                    INNER JOIN (
                        SELECT product_id, MAX(created_at) as max_created_at
                        FROM plu_stock_transactions
                        WHERE status = 1
                            AND created_at <= '$now'
                            AND product_id IN ($productList)
                        GROUP BY product_id
                    ) as latest ON st.product_id = latest.product_id 
                                 AND st.created_at = latest.max_created_at
                ) AS ranked
                WHERE rn = 1");

        $productCurrentStock = collect($productCurrentStock);
        $productCurrentStock = $productCurrentStock->keyBy('product_id');
        $movements = [];

        foreach ($movement as $mov) {
            $movements[$mov->product_id.'-'.$mov->summary_type] = $mov;
        }

        $results = [];

        foreach ($subdepartments as $sub) {
            $results['subdepartments'][$sub->id]['id'] = $sub->id;
            $results['subdepartments'][$sub->id]['displayname'] = $sub->displayname;
            $results['subdepartments'][$sub->id]['stock'] = [];
            $results['subdepartments'][$sub->id]['summary'] = [];

            foreach ($products as $k => $p) {
                if ($p->sub_department_guid == $sub->guid) {
                    $results['subdepartments'][$sub->id]['stock'][$p->id]['displayname'] = $p->displayname;
                    $results['subdepartments'][$sub->id]['stock'][$p->id]['stock'] = 0;
                    $results['subdepartments'][$sub->id]['stock'][$p->id]['transactions'] = isset($movements[$p->id.'-1']) ? number_format((float) $movements[$p->id.'-1']->quantity, 2). ' ('.number_format((float) $movements[$p->id.'-1']->quantity / $p->sku->qty, 2).' '.$p->sku->displayname.')' : 0;
                    $results['subdepartments'][$sub->id]['stock'][$p->id]['deliveries'] = isset($movements[$p->id.'-2']) ? number_format((float) $movements[$p->id.'-2']->quantity, 2). ' ('.number_format((float) $movements[$p->id.'-2']->quantity / $p->sku->qty, 2).' '.$p->sku->displayname.')' : 0;
                    $results['subdepartments'][$sub->id]['stock'][$p->id]['wastages'] = isset($movements[$p->id.'-5']) ? number_format((float) $movements[$p->id.'-5']->quantity, 2). ' ('.number_format((float) $movements[$p->id.'-5']->quantity / $p->sku->qty, 2).' '.$p->sku->displayname.')' : 0;
                    $results['subdepartments'][$sub->id]['stock'][$p->id]['adjustments'] = isset($movements[$p->id.'-4']) ? number_format((float) $movements[$p->id.'-4']->quantity, 2). ' ('.number_format((float) $movements[$p->id.'-4']->quantity / $p->sku->qty, 2).' '.$p->sku->displayname.')' : 0;

                    if (isset($productCurrentStock[$p->id])) {
                        $results['subdepartments'][$sub->id]['stock'][$p->id]['stock'] = $productCurrentStock[$p->id]->product_quantity . ' ('.number_format($productCurrentStock[$p->id]->product_quantity / $p->sku->qty, 2).' '. $p->sku->displayname.')';
                    }
                }
            }
        }

        return $results;
    }
}
