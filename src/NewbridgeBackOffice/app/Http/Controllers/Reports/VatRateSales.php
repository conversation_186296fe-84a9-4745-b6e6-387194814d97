<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosTransactionDetail;

class VatRateSales extends ReportAbstractController
{
    public function setTables($var = null): array
    {
        $this->tables = [
            "Tax Rate Sales" => [
                "columns" => [
                    "Tax Rate" => [null, 'tax_rate', 'string'],
                    "Sales" => [null, 'sum_net_value', 'decimal'],
                    "Tax Amount" => [null, 'sum_tax_value', 'decimal']
                ],
                "data" => [
                    'column_count' => 2,
                    'resultset' => 'vatrates',
                    'summary_result' => 'tax_summary'
                ],
                "summary" => [
                    "Total Sales" => [null, 'sum_sales_value', 'decimal'],
                    "Value" => [null, 'sum_tax_value', 'decimal'],
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->vatRateSales();
    }

    public function vatRateSales()
    {
        $query = PosTransactionDetail::select(DB::raw('tax_rate, SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid, sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value, COUNT(id) as count, SUM(discount_value) as sum_discount_value'))
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with(['product' => function ($q) {
                $q->withTrashed();
            }])
            ->whereIn('command', [3, 7, 38])
            ->whereIn('command_type', [0,6])
            ->groupBy('tax_rate');

        $query = $query->get();

        $result = [];
        $result['vatrates'] = $query;
        $result['tax_summary'] = [
            'blank' => '',
            'sum_sales_value' => 0,
            'sum_tax_value' => 0
        ];

        foreach ($query as $q) {
            $result['tax_summary']['sum_tax_value'] += $q->sum_tax_value;
            $result['tax_summary']['sum_sales_value'] += $q->sum_net_value;
        }

        return $result;
    }
}
