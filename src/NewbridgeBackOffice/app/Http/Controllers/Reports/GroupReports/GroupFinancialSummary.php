<?php

namespace NewbridgeWeb\Http\Controllers\Reports\GroupReports;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\Reports\ReportAbstractController;
use NewbridgeWeb\Http\Helpers\CurrencyHelper;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\PosTransactionPayment;
use NewbridgeWeb\Repositories\Sites;

class GroupFinancialSummary extends ReportAbstractController
{
    public $guid;

    public function setTables($var = null): array
    {
        $this->currencySymbol = CurrencyHelper::symbol($this->company_id);

        $this->tables = [
            "Group Financial Summary" => [
                "columns" => [
                    "Site" => [null, 'site', 'string'],
                    "Gross Sales" => [null, 'gross', 'decimal'],
                    "Net Sales" => [null, 'net', 'decimal'],
                    "TAX " . $this->currencySymbol => [null, 'vat', 'decimal'],
                    "Discounts" => [null, 'discounts', 'decimal'],
                    "Gratuities" => [null, 'gratuities', 'decimal'],
                    "Service Charge" => [null, 'service', 'decimal'],
                    "GC Topup" => [null, 'topups', 'decimal'],
                    "Cashback" => [null, 'cashback', 'decimal'],
                    "Expenses" => [null, 'expenses', 'decimal'],
                    "Payments" => [null, 'payments', 'decimal'],
                    "Covers" => [null, 'covers', 'string'],
                    "Average Spend" => [null, 'avg_spend', 'decimal']

                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'sites',
                    'summary_result' => 'summary'
                ],
                'summary' => [
                    [null, 'gross', 'decimal'],
                    [null, 'net', 'decimal'],
                    [null, 'vat', 'decimal'],
                    [null, 'discounts', 'decimal'],
                    [null, 'gratuities', 'decimal'],
                    [null, 'service', 'decimal'],
                    [null, 'topups', 'decimal'],
                    [null, 'cashback', 'decimal'],
                    [null, 'expenses', 'decimal'],
                    [null, 'payments', 'decimal'],
                    [null, 'covers', 'string'],
                    [null, 'avg_spend', 'decimal']
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $this->guid = $report_data['guid'];

        $results = [];
        $results['sites'] = [];
        $results['summary'] = [
            'gross' => 0,
            'net' => 0,
            'vat' => 0,
            'discounts' => 0,
            'gratuities' => 0,
            'service' => 0,
            'topups' => 0,
            'cashback' => 0,
            'expenses' => 0,
            'payments' => 0,
            'covers' => 0,
            'avg_spend' => 0
        ];

        $sites = Sites::where('company_id', $this->company_id)->get();

        $percent = 25 / $sites->count();

        foreach ($sites as $site) {
            $salesTotals = $this->salesTotals($site->site_num);
            $cashback = $this->getCashback($site->site_num)->sum_total;
            $covers = $this->getCovers($site->site_num)->covers_total;

            $data = [
                'site' => $site->site_name,
                'gross' => (float) $salesTotals['sum_net_value'],
                'net' => (float) $salesTotals['sum_net_total'],
                'vat' => (float) $salesTotals['sum_tax_value'],
                'discounts' => (float) $this->getCheckDiscounts($site->site_num)->sum_total,
                'gratuities' => (float) $this->getGratuities($site->site_num)->sum_total,
                'service' => (float) $this->getServiceCharges($site->site_num)->sum_total,
                'topups' => (float) $this->getGiftCardTopup($site->site_num)->sum_total,
                'cashback' => (float) $cashback,
                'expenses' => (float) $this->expenses($site->site_num)->sum_total,
                'payments' => $this->paymentTypeSalesSummary($site->site_num, $cashback)->sales_value,
                'covers' => $covers,
                'avg_spend' => $covers > 0 && $salesTotals['sum_net_value'] > 0 ? $salesTotals['sum_net_value'] / $covers : 0.00
            ];

            $results['sites'][] = $data;

            $results['summary']['gross'] += $data['gross'];
            $results['summary']['net'] += $data['net'];
            $results['summary']['vat'] += $data['vat'];
            $results['summary']['discounts'] += $data['discounts'];
            $results['summary']['gratuities'] += $data['gratuities'];
            $results['summary']['service'] += $data['service'];
            $results['summary']['topups'] += $data['topups'];
            $results['summary']['cashback'] += $data['cashback'];
            $results['summary']['expenses'] += $data['expenses'];
            $results['summary']['payments'] += $data['payments'];
            $results['summary']['covers'] += $data['covers'];
            $results['summary']['avg_spend'] += $results['summary']['gross'] > 0 && $data['covers'] > 0 ? $results['summary']['gross'] / $data['covers'] : 0.00;

            $redis = json_decode(Redis::get('report-' . $this->guid . '-status'), true);

            Redis::set('report-' . $this->guid . '-status', json_encode(['status' => 'success', 'text' => 'Processing: ' . $site->site_name, 'percent' => $redis['percent'] + $percent]));
            Redis::expire('report-' . $this->guid . '-status', (config('newbridge.report_expire_time') * 60));
        }

        return $results;
    }

    public function getCovers($site_num)
    {
        return PosTransaction::select(DB::raw('SUM(covers) as covers_total'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    public function getGratuities($site_num)
    {
        return PosTransactionDetail::select(DB::raw('SUM(gross_value) as sum_total, COUNT(*) as count'))
            ->where('command', 61)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    private function getCashback($site_num)
    {
        return PosTransactionDetail::select(DB::raw('SUM(gross_value) as sum_total, COUNT(*) as count'))
            ->where('command', 37)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    private function getGiftCardTopup($site_num)
    {
        return PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count'))
            ->where('command', 46)
            ->where('command_type', '!=', 5)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    private function getServiceCharges($site_num)
    {
        return PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count'))
            ->where('command', 44)
            ->whereNotIn('command_type', [5])
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    private function expenses($site_num)
    {
        return PosTransactionDetail::select(DB::raw('SUM(net_value) as sum_total, COUNT(*) as count, displayname'))
            ->where('command', 88)
            ->where('command_type', 1)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    public function salesTotals($site_num)
    {
        $select = 'terminal_num, SUM(cost_price) as sum_cost_price, SUM(tax_value) as sum_tax_value, product_guid, department_guid,';
        $select .= 'sub_department_guid, SUM(gross_value) as sum_gross_value, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value,';
        $select .= 'COUNT(id) as count, SUM(discount_value) as sum_discount_value, (sum(gross_value) - abs(sum(discount_value))) as total,';
        $select .= '(sum(net_value) - abs(sum(tax_value))) as sum_net_total';

        return PosTransactionDetail::select(DB::raw($select))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->whereIn('command', [3, 38])
            ->whereIn('command_type', [0, 6])
            ->whereNotIn('command_type', [5])
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    public function cashSales()
    {
        $query = PosTransactionPayment::select(DB::raw('*, COUNT(*) as sales_count, SUM(amount) as sales_value'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->with(['method' => function ($q) {
                $q->with(['xerolinks' => function ($q2) {
                    $q2->where('company_id', $this->company_id);
                }]);
            }])
            ->where('method_type', 1)
            ->groupBy('method_type')
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', $this->site_num);

        if ($this->terminal != null) {
            $query = $query->whereIn('terminal_num', $this->terminal);
        }

        return $query->get();
    }

    public function getCheckDiscounts($site_num)
    {
        return PosTransactionDetail::select(DB::raw('*, COUNT(*) as count, SUM(discount_value) as sum_total'))
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->where('discount_value', '<', 0)
            ->whereIn('command', [9, 10])
            ->where('command_type', '!=', 5)
            ->where('company_id', $this->company_id)
            ->whereIn('site_num', [$site_num])
            ->first();
    }

    public function paymentTypeSalesSummary($site_num, $cashback)
    {
        $paymentQuery = PosTransactionPayment::select(DB::raw('COUNT(*) as sales_count, SUM(amount) as sales_value'))
            ->where('company_id', $this->company_id)
            ->whereBetween('finalised_date', [$this->start, $this->end])
            ->whereIn('site_num', $site_num)
            ->with('method')
            ->first();

        $paymentQuery->sales_value = ((float) $paymentQuery->sales_value - (float) $cashback);

        return $paymentQuery;
    }
}
