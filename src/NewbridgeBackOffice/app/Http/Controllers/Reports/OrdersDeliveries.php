<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use NewbridgeWeb\Repositories\StockSummary;

class OrdersDeliveries extends ReportAbstractController
{
    public function setTables($var = null): array
    {
        $this->tables = [
            "Orders & Deliveries" => [
                "columns" => [
                    "Date" => [null, 'date', 'date'],
                    "Status" => [null, 'status', 'string'],
                    "Method" => [null, 'method', 'string'],
                    "Delivery Date" => [null, 'delivery_date', 'date'],
                    "Delivery #" => [null, 'delivery_no', 'string'],
                    "Invoice #" => [null, 'invoice_no', 'string'],
                    "View Order" => [null, 'id', 'button', 'View Order', '/stock/orders/edit/']
                ],
                "data" => [
                    'column_count' => 7,
                    'resultset' => 'orders',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        return $this->orders();
    }

    public function orders()
    {
        $query = StockSummary::where([
            ['company_id', $this->company_id],
            ['summary_type', 2],
            ['site_num', $this->site_num]
        ])->whereBetween('created_at', [$this->start, $this->end])->get();

        $results = [];
        $results['orders'] = [];

        foreach ($query as $q) {
            $results['orders'][] = [
                'date' => $q->created_at,
                'status' => $q->status,
                'method' => $q->send_method,
                'delivery_date' => $q->delivery_date,
                'delivery_no' => $q->delivery_no,
                'invoice_no' => $q->invoice_no,
                'id' => $q->id
            ];
        }

        return $results;
    }
}
