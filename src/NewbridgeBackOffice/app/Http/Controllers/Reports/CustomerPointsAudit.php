<?php

namespace NewbridgeWeb\Http\Controllers\Reports;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\CompanyLinks;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\CustomerTransactions;

class CustomerPointsAudit extends ReportAbstractController
{

    public function setTables($var = null): array
    {
        $this->tables = [
            "Customer Points Audit" => [
                "columns" => [
                    "Date/Time" => [null, 'created_at', 'string'],
                    "Customer Name" => ['customer', 'full_name', 'string'],
                    "Points Amount" => [null, 'points', 'string'],
                    "User" => ['user', 'name', 'string'],
                    "Clerk" => ['clerk', 'full_name', 'string'],
                    "Reason" => [null, 'reason_text', 'string']
                ],
                "data" => [
                    'column_count' => 5,
                    'resultset' => 'customers',
                    'summary_result' => null
                ]
            ]
        ];

        return $this->tables;
    }

    public function data($transactions, $report_data)
    {
        parent::data($transactions, $report_data);

        $results = [];
        $results['customers'] = $this->customers();

        return $results;
    }

    public function customers()
    {
        $companies = CompanyLinks::links($this->company_id);
        $customers = Customer::whereIn('company_id', $companies)->where('customer_type', 0)->get();
        return CustomerTransactions::whereIn('customer_guid', $customers->pluck('guid'))
            ->whereBetween('created_at', [$this->start, $this->end])
            ->with('customer', 'user', 'clerk')
            ->orderBy('created_at', 'ASC')
            ->where('points', '!=', 0)
            ->get();
    }
}
