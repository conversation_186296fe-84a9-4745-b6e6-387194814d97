<?php

namespace NewbridgeWeb\Http\Controllers;

use Auth;
use Illuminate\Http\Request;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\PropertyManagement\GuestLineController;
use NewbridgeWeb\Repositories\PMSCredentials;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\RevenueCenters;
use NewbridgeWeb\Repositories\Sites;
use Ramsey\Uuid\Uuid;
use Yajra\DataTables\DataTables;

/**
 * use api classes that are required for the update api
 */
class RevenueCentersController extends Controller
{
    public int $company_id;
    public int $site_num;
    public array $errors = [];

    public function index()
    {
        $usedTerminals = [];

        $revenueCenters = RevenueCenters::where('company_id', Auth::user()->company_id)->get();
        $sites = Sites::where('company_id', Auth::user()->company_id)->get();

        foreach ($revenueCenters as $revenueCenter) {
            $terminals = json_decode($revenueCenter->terminals, true);
            $usedTerminals = array_merge($usedTerminals, $terminals);
        }

        $terminals = Pos::where('company_id', Auth::user()->company_id)->get();

        return response()->view(
            'modules.revenue-centers.table',
            compact('terminals', 'usedTerminals', 'revenueCenters', 'sites')
        );
    }

    /**
     * @param Datatables $dataTables
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Datatables $dataTables)
    {
        $model = RevenueCenters::where('company_id', Auth::user()->company_id);

        return $dataTables->eloquent($model)->setRowId('id')->make(true);
    }

    public function getCreate()
    {
        $usedTerminals = [];

        $revenueCenters = RevenueCenters::where('company_id', Auth::user()->company_id)->get();
        $sites = Sites::where('company_id', Auth::user()->company_id)->get();

        foreach ($revenueCenters as $revenueCenter) {
            $terminals = json_decode($revenueCenter->terminals, true);
            $usedTerminals = array_merge($usedTerminals, $terminals);
        }

        $sites = Sites::where('company_id', Auth::user()->company_id)
            ->with([
                'terminals' => function ($q) use ($usedTerminals) {
                    $q->where('company_id', session('user.company_id'))
                        ->whereNotIn('id', $usedTerminals);
                }
            ])
            ->whereHas('terminals', function ($q) use ($usedTerminals) {
                $q->where('company_id', session('user.company_id'))
                    ->whereNotIn('id', $usedTerminals);
            })->get();

        return view('modules.revenue-centers.create', compact('usedTerminals', 'revenueCenters', 'sites'));
    }

    public function postCreate(Request $request)
    {
        $rules = [
            'name' => 'required'
        ];

        $validated = $request->validate($rules);

        $terminals = [];
        if ($request->has('terminals')) {
            foreach ($request->input('terminals') as $terminal) {
                $terminals[] = $terminal;
            }
        }

        $revenueCenter = RevenueCenters::create([
            'name' => $request->input('name'),
            'company_id' => Auth::user()->company_id,
            'site_num' => 0,
            'guid' => Uuid::uuid4(),
            'terminals' => json_encode($terminals)
        ]);

        $message = ['status' => 'success', 'message' => 'Revenue center added successfully.'];
        \Session::flash('message', $message);

        \Event::dispatch(new UpdaterInsertCrudEvent($revenueCenter));

        return redirect('/company/revenue-centers');
    }

    public function getEdit($id)
    {
        $usedTerminals = [];

        $revenueCenters = RevenueCenters::where('company_id', Auth::user()->company_id)
            ->where('id', '!=', $id)
            ->get();

        foreach ($revenueCenters as $revenueCenter) {
            $terminals = json_decode($revenueCenter->terminals, true);
            $usedTerminals = array_merge($usedTerminals, $terminals);
        }

        $sites = Sites::where('company_id', Auth::user()->company_id)
            ->with([
                'terminals' => function ($q) use ($usedTerminals) {
                    $q->where('company_id', session('user.company_id'))
                        ->whereNotIn('id', $usedTerminals);
                }
            ])
            ->whereHas('terminals', function ($q) use ($usedTerminals) {
                $q->where('company_id', session('user.company_id'))
                    ->whereNotIn('id', $usedTerminals);
            })->get();

        $revenueCenter = RevenueCenters::find($id);

        return view(
            'modules.revenue-centers.edit',
            compact('usedTerminals', 'revenueCenter', 'revenueCenters', 'sites')
        );
    }

    public function postEdit(Request $request, $id)
    {
        $rules = [
            'name' => 'required'
        ];

        $validated = $request->validate($rules);

        $terminals = [];
        if ($request->has('terminals')) {
            foreach ($request->input('terminals') as $terminal) {
                $terminals[] = $terminal;
            }
        }

        RevenueCenters::where('id', $id)->get()->each(function($model) use ($terminals, $request) {
            $model->update([
                'name' => $request->input('name'),
                'terminals' => json_encode($terminals)
            ]);
        });

        $message = ['status' => 'success', 'message' => 'Revenue center updated successfully.'];
        \Session::flash('message', $message);

        $revenueCenter = RevenueCenters::find($id);
        \Event::dispatch(new UpdaterInsertCrudEvent($revenueCenter));

        return redirect('/company/revenue-centers');
    }

    public function delete($id)
    {
        $revenueCenter = RevenueCenters::find($id);

        $credentials = PMSCredentials::where('company_id', $revenueCenter->company_id)->get();

        foreach ($credentials as $credential) {
            $json = json_decode($credential->value7, true);
            if (isset($json['revenue'])) {
                foreach ($json['revenue'] as $k => $revenue) {
                    if ($k == $id) {
                        unset($json['revenue'][$k]);
                    }
                }
            }

            $credential->value7 = json_encode($json);
            $credential->save();
        }

        $revenueCenter->delete();

        $revenueCenter = RevenueCenters::withTrashed()->where('id', $id)->first();
        \Event::dispatch(new UpdaterUpdateCrudEvent($revenueCenter));

        $guestline = new GuestLineController();
        $guestline->createOrUpdateOutletServiceMapping($revenueCenter->company_id);

        $message = ['status' => 'success', 'message' => 'Revenue Center deleted successfully'];
        \Session::flash('message', $message);

        return response()->json('ok');
    }
}
