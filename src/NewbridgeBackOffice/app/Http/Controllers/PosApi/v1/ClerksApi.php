<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1;

use Carbon\Carbon;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\JsonDate;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Http\Helpers\WagesHelper;
use NewbridgeWeb\Repositories\Clerks\ClerkClocking;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Clerks\ShiftTypes;
use NewbridgeWeb\Repositories\Company;

class ClerksApi extends Controller
{
    public $company;
    public $terminal;
    public $site;
    public $content;
    public $employee;
    public $clockin;
    public $defaultShiftType;

    public function __construct(Request $request)
    {
        $auth = $request->header('X-Newbridge-Auth');
        $auth = explode(':', base64_decode($auth));

        // get the terminal UUID
        $company = cache()->rememberForever($auth[0], function () use ($auth) {
            return Company::where('terminal_access_key', $auth[0])->first();
        });

        if ($company && !empty($auth)) {

            $this->company = $company['id'];
            $this->site = $auth[2];
            $this->terminal = $auth[1];

            $this->content = json_decode($request->getContent());

        } else {
            return response()->json(['status' => 'error', 'message' => 'Unable to complete request bad URL or Access key'], 400);
        }
    }

    public function ReceiveClocking(Request $request)
    {
        $this->content = json_decode($request->getContent());

        $this->employee = Clerks::where('guid', $this->content->Employee_Guid)->withTrashed()->first();

        if ($this->employee == null) {
            return response()->json(['status' => 'error', 'message' => 'employee does not exist'], 400);
        }

        $this->clockin = ClerkClocking::where('guid', $this->content->Guid)->first();
        $this->defaultShiftType = ShiftTypes::where('company_id', $this->company)->where('isDefault', 1)->first();

        $timezone = TimezoneHelper::getTimezone($this->site, $this->company);

        if ($this->defaultShiftType == null) {
            $this->defaultShiftType = $this->createDefaultShiftTypeIfNotExists();
        }

        if (!$this->clockin) {
            $this->clockin = new ClerkClocking();
            $this->clockin->guid = $this->content->Guid;
            $this->clockin->site_num = $this->site;
            $this->clockin->role_guid = $this->content->Role_Guid ?? null;
            $this->clockin->employee_guid = $this->content->Employee_Guid;
            $this->clockin->in = $this->content->In != null ? Carbon::parse(JsonDate::parseJsonDate($this->content->In, '0000', 'date', null, $timezone))->toDateTimeString() : null;
            $this->clockin->out = $this->content->Out != null ? Carbon::parse(JsonDate::parseJsonDate($this->content->Out, '0000', 'date', null, $timezone))->toDateTimeString() : null;
            $this->clockin->area_guid = $this->employee['area_guid'];
            $this->clockin->shift_type = $this->defaultShiftType->id;
        } else {
            $this->clockin->out =  $this->content->Out != null ? Carbon::parse(JsonDate::parseJsonDate($this->content->Out, '0000', 'date', null, $timezone))->toDateTimeString() : null;
        }

        $this->clockin = WagesHelper::setRate($this->clockin, $this->employee);
        $this->clockin->save();

        return response()->json(['status' => 'ok'], 200);
    }

    public function createDefaultShiftTypeIfNotExists()
    {
        return ShiftTypes::firstOrCreate(
            ['company_id' => $this->company, 'isDefault' => 1],
            ['type' => 'On Shift', 'short_type' => "OS", 'timed' => 1, 'color' => '#4f8f00']
        );
    }
}
