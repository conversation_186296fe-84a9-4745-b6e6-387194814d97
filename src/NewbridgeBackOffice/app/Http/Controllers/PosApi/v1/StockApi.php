<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1;

use DB;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\StockTransactions;

class StockApi extends Controller
{
    public $company;
    public $terminal;
    public $site;
    public $content;
    public $customer = null;

    public function __construct(Request $request)
    {
        $auth = $request->header('X-Newbridge-Auth');
        $auth = explode(':', base64_decode($auth));

        // get the terminal UUID
        $company = cache()->rememberForever($auth[0], function () use ($auth) {
            return Company::where('terminal_access_key', $auth[0])->first();
        });

        if($company) {
            $this->company = $company['id'];
            $this->site = $auth[2];
            $this->terminal = $auth[1];

            $this->content = json_decode($request->getContent());
        } else {
            return false;
        }
    }

    public function getStock(Request $request)
    {
        $this->content = json_decode($request->getContent());

        $product = Products::where('guid', $this->content->PLU_CommandUID)->first();
        $stock = StockTransactions::select(DB::raw('sum(quantity) as stock'))->where('product_id', $product->id)->groupBy('product_id')->first();

        if ($product != null && $stock != null) {
            return response()->json(['status' => 'ok', 'stock' => $stock['stock']], 200);
        } else {
            return response()->json(['status' => 'error', 'message' => 'Sorry no stock data is available for the product selected'], 200);
        }
    }
}
