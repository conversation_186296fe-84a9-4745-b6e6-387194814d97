<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1;

use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Company;

class ListResources extends Controller
{
    public $company;
    public $terminal;
    public $site;
    public $content;

    public function __construct(Request $request)
    {
        $auth = $request->header('X-Newbridge-Auth');
        $auth = explode(':', base64_decode($auth));

        // get the terminal UUID
        $company = cache()->rememberForever($auth[0], function () use ($auth) {
            return Company::where('terminal_access_key', $auth[0])->first();
        });

        if ($company) {

            $this->company = $company['id'];
            $this->site = $auth[2];
            $this->terminal = $auth[1];

            $this->content = json_decode($request->getContent());

        } else {

            return response()->json(['status' => 'error', 'message' => 'Unable to complete request bad URL or Access key'], 400);

        }
    }

    public function getIntCommands(int|null $int_command = null): \Illuminate\Http\JsonResponse
    {
        $commands = Commands::where('company_id', 0);

        if($int_command != null) {
            $commands = $commands->where('Command', $int_command);
        }

        $commands = $commands->get();

        return response()->json($commands);
    }
}
