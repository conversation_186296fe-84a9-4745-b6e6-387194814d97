<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosSettingHeader;
use NewbridgeWeb\Repositories\PosSettings;
use Ramsey\Uuid\Uuid;

class ReceiveSettingHeadersApi extends Controller
{
    public static function process($content = [], $data = [])
    {
        DB::beginTransaction();

        try {
            $settingHeader = $content;

            $exists = PosSettingHeader::where([
                ['company_id', $data['company_id']],
                ['setting_id', $settingHeader->Setting_ID]
            ])
                ->where('company_id', '!=', 0)
                ->first();

            $guid = $settingHeader->Guid == null ? Uuid::uuid4() : $settingHeader->Guid;

            $newData = [
                'value' => $settingHeader->Value,
                'setting_id' => $settingHeader->Setting_ID,
                'category' => $settingHeader->Category,
                'company_id' => $data['company_id'],
                'received_from' => $data['terminal_num']
            ];

            if ($exists == null) {
                $create = new PosSettingHeader();
                $create->fill($newData);
                $create->guid = $guid;
                $create->save();

                \Event::dispatch(new UpdaterInsertCrudEvent($create));
            } else {
                $update = PosSettings::find($exists['id']);
                $update->fill($newData);
                $update->save();

                \Event::dispatch(new UpdaterUpdateCrudEvent($update));
            }

            DB::commit();

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }
}
