<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Clerks\ClerkRoles;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\CompanyClerkRoles;
use NewbridgeWeb\Repositories\PosButtonLink;

class ReceiveClerksApi extends Controller
{
    public static function process($content = [], $data = [])
    {
        DB::beginTransaction();

        try {
            $company = Company::find($data['company_id']);
            $clerk = $content;
            if (isset($clerk->BiometricData) && !empty($clerk->BiometricData)) {
                $bio = $clerk->BiometricData;
            } else {
                $bio = null;
            }

            $newData = [
                'can_config' => isset($clerk->Can_Config) && $clerk->Can_Config === true ? 1 : 0,
                'can_discount' => isset($clerk->Can_Discount) && $clerk->Can_Discount === true ? 1 : 0,
                'can_no_sale' => isset($clerk->Can_No_Sale) && $clerk->Can_No_Sale === true ? 1 : 0,
                'can_refund' => isset($clerk->Can_Refund) && $clerk->Can_Refund === true ? 1 : 0,
                'can_reopen_tran' => isset($clerk->Can_Reopen_Tran) && $clerk->Can_Reopen_Tran === true ? 1 : 0,
                'can_void' => isset($clerk->Can_Void) && $clerk->Can_Void === true ? 1 : 0,
                'company_id' => $data['company_id'],
                'received_at' => Carbon::now(),
                'default_screen_guid' => $clerk->Default_Screen_GUID,
                'full_name' => $clerk->Full_Name,
                'employee_no' => $clerk->Employee_No,
                'guid' => $clerk->Guid,
                'pin' => $clerk->Pin,
                'short_name' => $clerk->Short_Name,
                'ibutton_num' => $clerk->iButton,
                'ismanager' => isset($clerk->IsManager) && $clerk->IsManager === true ? 1 : 0,
                'ishidden' => $clerk->IsHidden === true ? 1 : 0,
                'istrainee' => $clerk->IsTrainee === true ? 1 : 0,
                'deleted_at' => $clerk->Deleted_At != null ? Carbon::now() : null,
                'received_from' => $data['terminal_num'],
                'accesslevel' => $clerk->AccessLevel ?? 0,
                'biometricdata' => $bio,
                'site_num' => $company->site_specific_clerks == 1 ? $data['site_num'] : 0
            ];

            $newClerk = Clerks::where('guid', $clerk->Guid)->withTrashed()->first();

            if (!empty($newClerk)) {
                $newClerk->fill($newData);
                $newClerk->save();
            } else {
                $newClerk = new Clerks();
                $newClerk->fill($newData);
                $newClerk->save();
            }

            if (isset($clerk->CLK_Clerk_Roles) && !empty($clerk->CLK_Clerk_Roles)) {
                $clerkRoles = json_decode(json_encode($clerk->CLK_Clerk_Roles, true));
                $guids = array_column($clerkRoles, 'Guid');

                ClerkRoles::where('employee_guid', $clerk->Guid)->whereNotIn('guid', $guids)->forceDelete();

                foreach ($clerk->CLK_Clerk_Roles as $cr) {
                    $clerkRole = CompanyClerkRoles::where('guid', $cr->Guid)->first();

                    if ($clerkRole != null) {
                        $role = ClerkRoles::firstOrNew([
                            'company_id' => $data['company_id'],
                            'guid' => $cr->Guid,
                            'employee_guid' => $clerk->Guid,
                            'role_id' => $clerkRole->id
                        ]);
                        $role->company_id = $data['company_id'];
                        $role->guid = $cr->Guid;
                        $role->employee_guid = $clerk->Guid;
                        $role->displayname = $cr->DisplayName;
                        $role->save();
                    }
                }
            }

            if (isset($clerk->CLK_Clerk_Roles) && empty($clerk->CLK_Clerk_Roles)) {
                ClerkRoles::where('employee_guid', $clerk->Guid)->forceDelete();
            }

            PosButtonLink::where('button_guid', $clerk->Guid)->delete();

            if (!empty($clerk->LNK_ButtonLinks)) {
                foreach ($clerk->LNK_ButtonLinks as $link) {
                    $l = new PosButtonLink();
                    $l->company_id = $data['company_id'];
                    $l->button_guid = $clerk->Guid;
                    $l->commandUID = $link->CommandUID;
                    $l->position = $link->Indexer;
                    $l->command_type = 1;
                    $l->type = 1;
                    $l->save();
                }
            }

            DB::commit();

            $newClerk = Clerks::where('guid', $clerk->Guid)->with('ClkClerkRoles', 'lnk_buttonlinks')->withTrashed()->first();

            \Event::dispatch(new UpdaterInsertCrudEvent($newClerk));

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }
}
