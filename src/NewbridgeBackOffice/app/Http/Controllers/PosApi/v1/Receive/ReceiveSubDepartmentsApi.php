<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\SubDepartment;

class ReceiveSubDepartmentsApi extends Controller
{
    public static function process($content = [], $data = [])
    {
        DB::beginTransaction();

        try {
            $subdepartment = $content;

            $exists = SubDepartment::where('guid', $subdepartment->Guid)->first();

            $datetime = Carbon::now();

            $newData = [
                'acc_code' => $subdepartment->Acc_Code,
                'displayname' => $subdepartment->DisplayName,
                'company_id' => $data['company_id'],
                'guid' => $subdepartment->Guid,
                'print_priority' => 0,
                'received_at' => $datetime,
                'updated_at' => $datetime,
                'received_from' => $data['terminal_num']
            ];

            if ($exists == null) {
                $create = new SubDepartment();
                $create->fill($newData);
                $create->save();

                \Event::dispatch(new UpdaterInsertCrudEvent($create));
            } else {
                $update = SubDepartment::find($exists['id']);
                $update->fill($newData);
                $update->save();

                \Event::dispatch(new UpdaterUpdateCrudEvent($update));
            }

            DB::commit();

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }
}
