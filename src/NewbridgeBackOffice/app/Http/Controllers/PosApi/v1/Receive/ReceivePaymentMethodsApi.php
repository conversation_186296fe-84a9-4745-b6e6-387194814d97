<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Payments;

class ReceivePaymentMethodsApi extends Controller
{
    public static function process($content = [], $data = [])
    {

        DB::beginTransaction();

        try {
            $payment = $content;

            $exists = Payments::where('CommandUID', $payment->CommandUID)->first();

            $datetime = Carbon::now();

            $payment = (array) $payment;
            unset($payment['ID']);
            unset($payment['Guid']);
            unset($payment['RecordState']);
            unset($payment['Uploaded']);
            unset($payment['IsModifier']);
            unset($payment['AccessLevel']);
            unset($payment['Created_At']);
            unset($payment['Updated_At']);
            unset($payment['VoucherID']);
            unset($payment['Deleted_At']);
            unset($payment['Company_ID']);
            $prefix_amount = $payment['Prefix_Amount'];
            $method = $payment['Method_Type'];
            unset($payment['Prefix_Amount']);
            unset($payment['Method_Type']);
            unset($payment['Membership_Number']);
            unset($payment['Customer_Type']);
            unset($payment['customer_type']);
            unset($payment['Is_Modifier_for_this_Product']);
            unset($payment['Indexer']);
            unset($payment['IsPMS']);
            unset($payment['EFTConnection']);
            unset($payment['Optional']);
            unset($payment['RFID_UUID']);
            unset($payment['Req_Manager']);
            unset($payment['Selected']);
            unset($payment['Hint']);
            unset($payment['IsDirty']);

            if ($exists == null) {
                $create = new Payments();
                $create->fill($payment);
                $create->prefix_amount = $prefix_amount;
                $create->method_type = $method;
                $create->company_id = $data['company_id'];
                $create->received_at = $datetime;
                $create->received_from = $data['terminal_num'];
                $create->save();

                \Event::dispatch(new UpdaterInsertCrudEvent($create));
            } else {
                $update = Payments::find($exists['id']);
                $update->fill($payment);
                $update->prefix_amount = $prefix_amount;
                $update->method_type = $method;
                $update->received_at = $datetime;
                $update->received_from = $data['terminal_num'];
                $update->save();

                \Event::dispatch(new UpdaterUpdateCrudEvent($update));
            }

            DB::commit();

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }
}
