<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\JsonDate;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\CompanyClerkRoles;

class ReceiveJobRoles extends Controller
{
    public static function process($content = [], $data = [])
    {
        DB::beginTransaction();

        try {
            $role = $content;

            $timezone = TimezoneHelper::getTimezone($data['site_num'], $data['company_id']);

            $deleted_at = $role->Deleted_At !== null ? Carbon::parse(JsonDate::parseJsonDate($role->Deleted_At, '0000', 'date', null, $timezone)) : null;

            $roleData = [
                'guid' => $role->Guid,
                'company_id' => $data['company_id']
            ];

            $roleData = CompanyClerkRoles::firstOrCreate($roleData);

            $roleData->deleted_at = $deleted_at;
            $roleData->displayname = $role->DisplayName;
            $roleData->received_from = $data['terminal_num'];

            $roleData->save();

            DB::commit();

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }
}
