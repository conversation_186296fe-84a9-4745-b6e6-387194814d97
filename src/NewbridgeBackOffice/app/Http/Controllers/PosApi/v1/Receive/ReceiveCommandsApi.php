<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use Newbridge<PERSON>eb\Repositories\Commands;

class ReceiveCommandsApi extends Controller
{
    public static function process($content = [], $data = [])
    {
        DB::beginTransaction();

        try {
            $command = $content;

            $newData = [
                'CommandUID' => $command->CommandUID,
                'company_id' => in_array($command->Command, [1,6,117,113]) ? $data['company_id'] : 0,
                'site_num' => in_array($command->Command, [1,6,117,113]) ? $data['site_num'] : 0,
                'Command' => $command->Command,
                'Link_1' => $command->Link_1,
                'Link_2' => $command->Link_2,
                'Value' => $command->Value,
                'Min' => $command->Min,
                'Max' => $command->Max,
                'DisplayName' => $command->DisplayName,
                'CommandType' => $command->CommandType,
                'Abbreviation' => $command->Abbreviation,
                'AccessLevel' => $command->AccessLevel,
                'received_at' => Carbon::now(),
                'received_from' => $data['terminal_num']
            ];

            $newCommand = Commands::where('CommandUID', $newData['CommandUID'])->first();

            if (!empty($newCommand)) {
                $newCommand->fill($newData);
                $newCommand->save();
                \Event::dispatch(new UpdaterUpdateCrudEvent($newCommand));
            } else {
                $newCommand = new Commands();
                $newCommand->fill($newData);
                $newCommand->company_id = $data['company_id'];
                $newCommand->save();
                \Event::dispatch(new UpdaterInsertCrudEvent($newCommand));
            }

            DB::commit();

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }
}
