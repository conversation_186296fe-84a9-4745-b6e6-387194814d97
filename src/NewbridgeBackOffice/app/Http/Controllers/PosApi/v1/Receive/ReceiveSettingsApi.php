<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosSettings;
use Ramsey\Uuid\Uuid;

class ReceiveSettingsApi extends Controller
{
    public static function process($content = [], $data = [])
    {
        DB::beginTransaction();

        try {
            $setting = $content;

            $exists = PosSettings::where([
                ['company_id', $data['company_id']],
                ['site_num', $data['site_num']],
                ['terminal_num', $data['terminal_num']],
                ['setting_id', $setting->Setting_ID]
            ])
                ->first();

            $datetime = Carbon::now();

            if (!in_array($setting->Setting_ID, ['TerminalNum', 'SiteNum', 'AccessToken'])) {
                $guid = $setting->Guid == null ? Uuid::uuid4() : $setting->Guid;

                $newData = [
                    'value1' => $setting->Value1,
                    'value3' => $setting->Value3,
                    'label' => $setting->Label,
                    'guid' => $guid,
                    'company_id' => $data['company_id'],
                    'site_num' => $data['site_num'],
                    'terminal_num' => $data['terminal_num'],
                    'setting_id' => $setting->Setting_ID,
                    'isreadonly' => $setting->IsReadOnly ? 1 : 0,
                    'description' => $setting->Description,
                    'value2' => $setting->Value2,
                    'value4' => $setting->Value4,
                    'value5' => $setting->Value5,
                    'value6' => $setting->Value6,
                    'value7' => $setting->Value7,
                    'type' => $setting->Type,
                    'category' => $setting->Category,
                    'location' => $setting->Location,
                    'received_at' => $datetime,
                    'updated_at' => $datetime,
                    'ishidden' => $setting->IsHidden ? 1 : 0,
                    'received_from' => $data['terminal_num']
                ];

                if ($newData['type'] == 4) {
                    $newData['terminal_num'] = 0;
                }

                if ($exists == null) {
                    $create = new PosSettings();
                    $create->fill($newData);
                    $create->guid = Uuid::uuid4();
                    $create->save();

                    if ($create->type != 4) {
                        \Event::dispatch(new UpdaterInsertCrudEvent($create));
                    }
                } else {
                    $update = PosSettings::find($exists['id']);
                    $update->fill($newData);
                    $update->save();

                    if ($update->type != 4) {
                        \Event::dispatch(new UpdaterUpdateCrudEvent($update));
                    }
                }
            }

            DB::commit();

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();
            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }
}
