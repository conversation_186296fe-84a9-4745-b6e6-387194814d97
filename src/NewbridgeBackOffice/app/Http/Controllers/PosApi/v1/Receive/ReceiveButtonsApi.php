<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Receive;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\ButtonLinks;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PosButtonLink;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PosButtonStyle;
use NewbridgeWeb\Repositories\Products;

class ReceiveButtonsApi extends Controller
{
    public static function process($content = [], $data = [])
    {
        $updateButtons = [];
        $updateButtons['inserts'] = [];
        $updateButtons['updates'] = [];

        DB::beginTransaction();

        try {
            $buttons = $content;

            foreach ($buttons as $button) {
                $exists = PosButtons::with('lnk_buttonlinks', 'pos_button_style')->where('guid', $button->Guid)->first();

                $sitenum = $button->ViewModel == 5 ? 0 : $data['site_num'];

                if ($button->ColumnSpan != '' && $button->RowSpan != '') {
                    $newData = [
                        'company_id' => $data['company_id'],
                        'site_num' => $sitenum,
                        'guid' => $button->Guid,
                        "column" => $button->Column,
                        "row" => $button->Row,
                        "columnspan" => $button->ColumnSpan,
                        "rowspan" => $button->RowSpan,
                        "displayname" => $button->DisplayName,
                        "viewmodel" => $button->ViewModel,
                        "buttontype" => $button->ButtonType,
                        "received_at" => Carbon::now(),
                        "received_from" => $data['terminal_num'],
                        "visibility" => $button->Visibility,
                        "imagepath" => $button->ImagePath ?? null,
                        "plu_product_page_guid" => $button->plu_product_page_guid,
                        "pos_button_style_guid" => $button->pos_button_style_guid
                    ];

                    if (!empty($button->POS_Button_Style)) {
                        $newStyleData = [
                            'guid' => $button->POS_Button_Style->Guid,
                            'resourcestyle' => $button->POS_Button_Style->ResourceStyle,
                            'color1' => strlen($button->POS_Button_Style->Color1) == 9 ? str_replace('#FF', '#', $button->POS_Button_Style->Color1) : $button->POS_Button_Style->Color1,
                            'color2' => strlen($button->POS_Button_Style->Color2) == 9 ? str_replace('#FF', '#', $button->POS_Button_Style->Color2) : $button->POS_Button_Style->Color2,
                            'color3' => strlen($button->POS_Button_Style->Color3) == 9 ? str_replace('#FF', '#', $button->POS_Button_Style->Color3) : $button->POS_Button_Style->Color3,
                            'color4' => strlen($button->POS_Button_Style->Color4) == 9 ? str_replace('#FF', '#', $button->POS_Button_Style->Color4) : $button->POS_Button_Style->Color4,
                            'cornerradius' => $button->POS_Button_Style->CornerRadius,
                            'foreground' => strlen($button->POS_Button_Style->Foreground) == 9 ? str_replace('#FF', '#', $button->POS_Button_Style->Foreground) : $button->POS_Button_Style->Foreground,
                            'fontsize' => $button->POS_Button_Style->FontSize,
                            'typeface' => $button->POS_Button_Style->TypeFace,
                            'alignment' => $button->POS_Button_Style->Alignment,
                            "received_at" => Carbon::now(),
                        ];
                    }

                    if ($exists != null) {
                        ButtonLinks::where('button_guid', $button->Guid)->delete();
                        $exists->fill($newData);
                        $exists->save();

                        if (!empty($button->POS_Button_Style)) {
                            $style = PosButtonStyle::where('guid', $newStyleData['guid'])->first();
                            $style->fill($newStyleData);
                            $style->save();
                        }
                    } else {
                        $exists = new PosButtons();
                        $exists->fill($newData);

                        if (!empty($button->POS_Button_Style)) {
                            $style = new PosButtonStyle();
                            $style->fill($newStyleData);
                            $style->save();

                            $exists->pos_button_style_guid = $style->guid;
                        }

                        $exists->save();

                    }

                    if (!empty($button->LNK_ButtonLinks)) {
                        foreach ($button->LNK_ButtonLinks as $link) {
                            if ($link->CommandUID != null) {
                                $newLink = [
                                    'company_id' => $data['company_id'],
                                    'site_id' => 0,
                                    "button_guid" => $link->Button_Guid,
                                    "commandUID" => $link->CommandUID,
                                    "command_type" => ReceiveButtonsApi::getCommandType($link->CommandUID),
                                    "position" => $link->Indexer,
                                    "received_at" => Carbon::now()
                                ];

                                $link = new PosButtonLink();
                                $link->fill($newLink);
                                $link->save();

                                $exists->lnk_buttonlinks->push($link);
                            }
                        }
                    }

                    if ($exists && $exists->wasRecentlyCreated) {
                        $updateButtons['inserts'][] = $exists;
                    } elseif ($exists && !$exists->wasRecentlyCreated) {
                        $updateButtons['updates'][] = $exists;
                    }
                }
            }

            DB::commit();

            foreach ($updateButtons['inserts'] as $insert) {
                $button = PosButtons::with(['lnk_buttonlinks', 'plu_product_page', 'pos_button_style'])->where('guid', $insert->guid)->first();
                \Event::dispatch(new UpdaterInsertCrudEvent($button));
            }

            foreach ($updateButtons['updates'] as $update) {
                $button = PosButtons::with(['lnk_buttonlinks', 'plu_product_page', 'pos_button_style'])->where('guid', $update->guid)->first();
                \Event::dispatch(new UpdaterUpdateCrudEvent($button));
            }

            return response()->json(['status' => 'ok'], 200);
        } catch (\Exception $e) {
            DB::rollback();

            // This is because POS is stupid DO NOT REMOVE
            throw new \Exception($e);

            return response()->json(['status' => 'error', 'message' => 'There was an issue processing this update.'], 400);
        }
    }

    public static function getCommandType($CommandUID)
    {
        $isProduct = Products::where('CommandUID', $CommandUID)->count() > 0;
        $isFunction = Commands::where('CommandUID', $CommandUID)->count() > 0;
        $isPayment = Payments::where('CommandUID', $CommandUID)->count() > 0;
        $isDiscount = Discounts::where('CommandUID', $CommandUID)->count() > 0;

        if ($isProduct) {
            return 0;
        } elseif ($isFunction) {
            return 1;
        } elseif ($isPayment) {
            return 2;
        } elseif ($isDiscount) {
            return 3;
        } else {
            return 0;
        }
    }
}
