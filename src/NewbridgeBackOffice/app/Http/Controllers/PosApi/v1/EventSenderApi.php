<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\ServiceBusHelper;

class EventSenderApi extends Controller
{
    public function sendEvent(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'Topic' => 'required|string',
            'Subject' => 'required|string',
            'EventType' => 'required|string',
            'Data' => 'required|array'
        ]);

        try {
            ServiceBusHelper::sendEventToTopic($validated['Topic'], $validated['Subject'], $validated['EventType'], json_encode($validated['Data']), true);

            return response()->json(['status' => 'ok'], 200);

        } catch (\Exception $e) {
            report($e);
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}