<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Transactions\Process;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\PosApi\v1\Transactions\Transactions;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\PosTransactionTaxDetails;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Tax\TaxRates;

class ProcessDetails extends Controller
{

    public function calculateGrossTotal($rrp, $commandType){
        if ($commandType == 6) {
            return abs($rrp) * -1;
        }
        return $rrp;
    }

    public function calculateNetTotal($rrp, $salePrice, $commandType){
        return $salePrice;
    }

    public function calculateDiscountValue($rrp, $salePrice, $commandType){
        $discountValue = $rrp - $salePrice;

        if($commandType === 6){
            return $discountValue * -1;
        }

        return $discountValue != 0 ? $discountValue * -1 : 0;
    }

    public function productSale($data, $company_data, $transaction)
    {
        $product = Products::where('guid', $data->PLU_CommandUID)
            ->where('company_id', $company_data['company_id'])
            ->withTrashed()
            ->first();

        if ($product) {
            $detail = new PosTransactionDetail();

            if ($data->Command == 107) {
                $data->SalePrice = $data->RRP;
            }

            $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);
            $detail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

            // company/terminal
            $detail->site_num = $company_data['site_num'];
            $detail->terminal_num = $company_data['terminal_num'];
            $detail->added_terminal_num = $data->Terminal_Num ?? 0;
            $detail->company_id = $company_data['company_id'];
            $detail->datetime = $data->Date;

            /**
             * Add split reference
             */
            $detail->splitparentreference = $data->SplitParentReferenceKey ?? null;

            // employee
            $detail->employee_guid = $data->EmployeeGUID;

            // finance
            $detail->gross_value = $this->calculateGrossTotal($data->RRP, $data->CommandType);
            $detail->net_value = $this->calculateNetTotal($data->RRP, $data->SalePrice, $data->CommandType);

            if ($data->Tax_Rate == null && $product->tax_guid != null) {
                $data->Tax_Rate = TaxRates::where('guid', $product->tax_guid)->withTrashed()->first()->rate;
            }

            $detail->tax_value = Transactions::calculateVat($data->SalePrice, $data->Tax_Rate);

            $detail->tax_rate = $data->Tax_Rate;

            $detail->command_type = $data->CommandType;
            $detail->command = $product->recipe_guid != null && $data->Command != 107 ? 38 : $data->Command;
            $detail->cost_price = $detail->splitparentreference != null ? 0 : $product->costprice;

            if ($product->soldbyweight && $data->ProductWeight > 0) {
                $detail->qty = $data->ProductWeight;
            } else {
                $detail->qty = $detail->splitparentreference != null ? 0 : 1;
            }

            // discount
            if ($data->Command != 107) {
                $detail->discount_guid = $data->PLUDiscount_Guid;
                $detail->promotion_guid = $data->PromoUID ?? null;
                $detail->discount_value = $this->calculateDiscountValue($detail->gross_value, $detail->net_value, $data->CommandType);
            }

            // Department / Sub Department / Product Link
            $detail->product_guid = $data->PLU_CommandUID;
            $detail->sub_department_guid = $data->SubDepartment_Guid;
            $detail->department_guid = $data->Department_Guid;
            $detail->displayname = $data->DisplayName;

            if ($data->VoidReason != null) {
                $detail->void_reason = $data->VoidReason;
                $detail->void_reason_guid = $data->VoidGuid ?? null;
                $detail->void_amount = $detail->gross_value;
            }

            $detail->transaction()->associate($transaction);
            $detail->save();

            if (isset($data->TaxDetails) && !empty($data->TaxDetails)) {
                $taxTotal = 0;
                foreach ($data->TaxDetails as $tax) {
                    $rate = TaxRates::where('guid', $tax->RateGuid)->withTrashed()->first();

                    $taxDetail = new PosTransactionTaxDetails();
                    $taxDetail->name = $tax->RateName;
                    $taxDetail->rate_guid = $tax->RateGuid;
                    $taxDetail->rule_guid = $tax->RuleGuid;
                    $taxDetail->rate = $tax->Rate;
                    $taxDetail->value = $tax->Value;
                    $taxDetail->reporting = $rate->reporting;
                    $taxDetail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

                    if ($rate->reporting == 1) {
                        $taxTotal += $taxDetail->value;
                    }

                    $taxDetail->transaction()->associate($transaction);
                    $taxDetail->detail()->associate($detail);

                    $taxDetail->save();
                }

                $detail->tax_value = $taxTotal;
                $detail->save();
            }

            return $detail;

        }
    }

    /**
     *
     * Process Modifiers
     *
     * @param $data
     * @param $company_data
     * @param $item
     * @param $transaction
     * @return PosTransactionDetail
     */
    public function modifier($data, $company_data, $item, $transaction)
    {
        $product = Products::where('guid', $data->PLU_CommandUID)
            ->where('company_id', $company_data['company_id'])
            ->withTrashed()
            ->first();

        if ($product != null) {

            $detail = new PosTransactionDetail();

            $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);
            $detail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

            // company/terminal
            $detail->site_num = $company_data['site_num'];
            $detail->terminal_num = $company_data['terminal_num'];
            $detail->added_terminal_num = $data->Terminal_Num ?? 0;
            $detail->company_id = $company_data['company_id'];
            $detail->datetime = (string) $data->Date;

            // employee
            $detail->employee_no = $data->EmployeeNo;
            $detail->employee_guid = $data->EmployeeGUID;

            $detail->splitparentreference = $item->splitparentreference ?? null;

            // finance
            $detail->gross_value = $data->CommandType != 6 ? $data->RRP : abs($data->RRP) * -1;
            $detail->net_value = $data->CommandType != 6 ? $data->SalePrice : abs($data->SalePrice) * -1;

            if ($data->Tax_Rate == null && $product->tax_guid != null) {
                $data->Tax_Rate = TaxRates::where('guid', $product->tax_guid)->first()->rate;
            }

            $detail->tax_value = Transactions::calculateVat($data->SalePrice, $data->Tax_Rate);

            $detail->tax_rate = $data->Tax_Rate;

            $detail->command_type = $item->command_type == 5 ? 5 : $data->CommandType;
            $detail->command = $product->recipe_guid == null ? 3 : 38;

            // discount
            $detail->discount_guid = $data->PLUDiscount_Guid;

            $detail->discount_value = abs((abs($data->SalePrice) - abs($data->RRP))) * -1;
            if ($data->CommandType == 6) {
                if($detail->gross_value < $detail->net_value){
                    $detail->discount_value = abs($detail->discount_value) * -1;
                } else {
                    $detail->discount_value = abs($detail->discount_value);
                }
            }

            // Department / Sub Department / Product Link
            $detail->product_guid = $data->PLU_CommandUID;
            $detail->sub_department_guid = $data->SubDepartment_Guid;
            $detail->department_guid = $data->Department_Guid;
            $detail->displayname = $data->DisplayName;

            $detail->cost_price = $detail->splitparentreference != null ? 0 : $product->costprice;

            if ($data->VoidReason != null) {
                $detail->void_reason = $data->VoidReason;
                $detail->void_reason_guid = $data->VoidGuid ?? null;
                $detail->void_amount = $detail->gross_value;
            }

            if ($product->soldbyweight && $data->ProductWeight > 0) {
                $detail->qty = $data->ProductWeight;
            } else {
                $detail->qty = $detail->splitparentreference != null ? 0 : 1;
            }

            $detail->detail()->associate($item);
            $detail->transaction()->associate($transaction);

            $detail->save();

            if (isset($data->TaxDetails) && !empty($data->TaxDetails)) {
                $taxTotal = 0;
                foreach ($data->TaxDetails as $tax) {
                    $rate = TaxRates::where('guid', $tax->RateGuid)->withTrashed()->first();

                    $taxDetail = new PosTransactionTaxDetails();
                    $taxDetail->name = $tax->RateName;
                    $taxDetail->rate_guid = $tax->RateGuid;
                    $taxDetail->rule_guid = $tax->RuleGuid;
                    $taxDetail->rate = $tax->Rate;
                    $taxDetail->value = $tax->Value;
                    $taxDetail->reporting = $rate->reporting;
                    $taxDetail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

                    if ($rate->reporting == 1) {
                        $taxTotal += $taxDetail->value;
                    }

                    $taxDetail->transaction()->associate($transaction);
                    $taxDetail->detail()->associate($detail);

                    $taxDetail->save();
                }
                $detail->tax_value = $taxTotal;
                $detail->save();
            }

            return $detail;
        }
    }

    /**
     *
     * Process free text field as detail item
     *
     * @param $data
     * @param $company_data
     * @param $item
     * @param $transaction
     * @return PosTransactionDetail
     *
     */
    public function freeText($data, $company_data, $item, $transaction)
    {
        $detail = new PosTransactionDetail();

        // company data
        $detail->site_num = $company_data['site_num'];
        $detail->terminal_num = $company_data['terminal_num'];
        $detail->added_terminal_num = $data->Terminal_Num ?? 0;
        $detail->company_id = $company_data['company_id'];

        $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);
        $detail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

        $detail->datetime = $data->Date;
        $detail->employee_no = $data->EmployeeNo;
        $detail->employee_guid = $data->EmployeeGUID;
        $detail->discount_value = $data->SalePrice;
        $detail->command_type = $data->CommandType;

        // financial (shouldn't be any)
        $detail->gross_value = 0;
        $detail->net_value = 0;
        $detail->discount_value = 0;
        $detail->tax_value = 0;
        $detail->tax_id = 0;
        $detail->tax_rate = 0;

        $detail->command = $data->Command;
        $detail->product_guid = null;

        // save display name
        $detail->displayname = $data->DisplayName;

        if ($item) {
            $detail->detail()->associate($item);
        }
        $detail->transaction()->associate($transaction);
        $detail->save();

        return $detail;
    }

    /**
     *
     * Process giftcards, cashback, gratuity
     *
     * @param $data
     * @param $company_data
     * @param $transaction
     * @return PosTransactionDetail
     */
    public function service($data, $company_data, $transaction)
    {
        $detail = new PosTransactionDetail();
        $detail->site_num = $company_data['site_num'];
        $detail->terminal_num = $company_data['terminal_num'];
        $detail->added_terminal_num = $data->Terminal_Num ?? 0;
        $detail->company_id = $company_data['company_id'];

        $detail->datetime = $data->Date;
        $detail->employee_no = $data->EmployeeNo;
        $detail->employee_guid = $data->EmployeeGUID;

        $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);
        $detail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

        // financial
        $detail->gross_value = $data->CommandType == 6 ? (abs($data->RRP) * -1) : $data->RRP;
        $detail->net_value = $data->CommandType == 6 ? (abs($data->SalePrice) * -1) : $data->SalePrice;

        $detail->tax_value = 0;
        $detail->tax_id = 0;
        $detail->tax_rate = 0;

        $detail->command = $data->Command;
        $detail->command_type = $data->CommandType;

        // product & department
        $detail->product_guid = null;

        // discount
        $detail->discount_guid = null;
        $detail->discount_value = null;

        $detail->displayname = $data->DisplayName;

        if ($data->Command == 46 && $data->SalePrice > 0 && $data->CommandType != 5) {
            Transactions::topUpOrCreateGiftCard($data->Membership_Number, $data->SalePrice, $transaction->id, $company_data['company_id'], false);
        }

        if ($data->CommandType == 6 && $data->Command == 46) {
            // TODO : Refund a topped up giftcard and remove the value from the card
            Transactions::topUpOrCreateGiftCard($data->Membership_Number, $data->SalePrice, $transaction->id, $company_data['company_id'], true);
        }

        if ($data->VoidReason != null) {
            $detail->void_reason = $data->VoidReason;
            $detail->void_amount = $detail->gross_value;
        }

        $detail->transaction()->associate($transaction);
        $detail->save();

        return $detail;
    }

    /**
     *
     * Process giftcards, cashback, gratuity
     *
     * @param $data
     * @param $company_data
     * @param $transaction
     * @return PosTransactionDetail
     *
     */
    public function giftCardSpend($data, $company_data, $transaction)
    {
        if ($data->Command == 76 && $data->CommandType != 5) {
            Transactions::reconcileGiftCardTransaction($data->Membership_Number, $transaction->id, $transaction->reference, $data->SalePrice, $company_data['company_id']);
        }
    }

    /**
     *
     * Process giftcards, cashback, gratuity
     *
     * @param $data
     * @param $company_data
     * @param $transaction
     * @return PosTransactionDetail
     */
    public function expenses($data, $company_data, $transaction)
    {
        $detail = new PosTransactionDetail();
        $detail->site_num = $company_data['site_num'];
        $detail->terminal_num = $company_data['terminal_num'];
        $detail->added_terminal_num = $data->Terminal_Num ?? 0;
        $detail->company_id = $company_data['company_id'];

        $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);
        $detail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

        $detail->datetime = $data->Date;
        $detail->employee_no = $data->EmployeeNo;
        $detail->employee_guid = $data->EmployeeGUID;

        // financial
        $detail->gross_value = abs($data->RRP) * -1;
        $detail->net_value = abs($data->SalePrice) * -1;

        $detail->tax_value = 0;
        $detail->tax_id = 0;
        $detail->tax_rate = 0;

        $detail->command = $data->Command;
        $detail->command_type = $data->CommandType;

        // product & department
        $detail->product_guid = null;

        // discount
        $detail->discount_guid = null;
        $detail->discount_value = null;

        $detail->displayname = $data->DisplayName;

        $detail->void_reason = $data->VoidReason;
        $detail->void_reason_guid = $data->VoidGuid ?? null;

        $detail->transaction()->associate($transaction);
        $detail->save();

        return $detail;
    }

    /**
     *
     * Process check discounts command 9 & 10
     *
     * @param $data
     * @param $company_data
     * @param $transaction
     */
    public function checkDiscount($data, $company_data, $transaction)
    {
        $detail = new PosTransactionDetail();
        $detail->site_num = $company_data['site_num'];
        $detail->terminal_num = $company_data['terminal_num'];
        $detail->added_terminal_num = $data->Terminal_Num ?? 0;
        $detail->company_id = $company_data['company_id'];
        $detail->datetime = $data->Date;
        $detail->employee_no = $data->EmployeeNo;
        $detail->employee_guid = $data->EmployeeGUID;

        $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);
        $detail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

        $detail->gross_value = $data->SalePrice;
        $detail->net_value = $data->SalePrice;
        $detail->discount_value = $data->SalePrice;
        $detail->tax_value = 0;
        $detail->tax_id = 0;
        $detail->tax_rate = 0;

        // command identifiers
        $detail->command = $data->Command;
        $detail->command_type = $data->CommandType;

        $detail->product_guid = null;
        $detail->displayname = $data->DisplayName;
        $detail->discount_guid = $data->PLU_CommandUID;

        if ($data->VoidReason != null) {
            $detail->void_reason = $data->VoidReason;
            $detail->void_reason_guid = $data->VoidGuid ?? null;
            $detail->void_amount = $detail->gross_value;
        }

        $detail->transaction()->associate($transaction);
        $detail->save();
    }

    /**
     *
     * Process a no sale detail
     *
     * @param $data
     * @param $company_data
     * @param $transaction
     */
    public function noSale($data, $company_data, $transaction)
    {
        $detail = new PosTransactionDetail();
        $detail->site_num = $company_data['site_num'];
        $detail->terminal_num = $company_data['terminal_num'];
        $detail->added_terminal_num = $data->Terminal_Num ?? 0;
        $detail->company_id = $company_data['company_id'];
        $detail->datetime = $data->Date;
        $detail->employee_no = $data->EmployeeNo;
        $detail->employee_guid = $data->EmployeeGUID;
        $detail->gross_value = $data->SalePrice;
        $detail->net_value = $data->SalePrice;
        $detail->discount_value = $data->SalePrice;
        $detail->command_type = $data->CommandType;
        $detail->tax_value = 0;
        $detail->tax_id = 0;
        $detail->tax_rate = 0;
        $detail->command = $data->Command;
        $detail->product_guid = null;
        $detail->displayname = $data->DisplayName;

        $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);
        $detail->finalised_date = Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC');

        $detail->transaction()->associate($transaction);
        $detail->save();

        return $detail;
    }
}
