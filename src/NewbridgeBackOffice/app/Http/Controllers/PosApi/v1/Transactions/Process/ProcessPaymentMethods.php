<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Transactions\Process;

use Carbon\Carbon;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PosTransactionPayment;

class ProcessPaymentMethods extends Controller
{
    public function create($data, $company_data, $transaction)
    {
        $data = json_decode(json_encode($data), true);

        $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);

        // check if payment method exists before adding to the transaction
        $paymentData = [
            'company_id' => $company_data['company_id'],
            'site_num' => $company_data['site_num'],
            'terminal_num' => $company_data['terminal_num'],
            'trans_date' => $data['TimeStamp'],
            'method_guid' => $data['Method']['CommandUID'],
            'method_type' => $data['Method']['Method_Type'],
            'amount' => $data['Amount'],
            'finalised_date' => Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC')
        ];

        $payment = new PosTransactionPayment();
        $payment->fill($paymentData);
        $payment->transaction()->associate($transaction);
        $payment->save();

        return $payment;
    }

    public function resDiaryDeposits($item, $company_data, $transaction)
    {
        $payment_method = Payments::firstOrCreate([
            'DisplayName' => 'Res Diary Deposits',
            'company_id' => $company_data['company_id'],
            'CommandUID' => $company_data['company_id'].'-RES-DIARY-DEPOSITS',
            'prefix_amount' => 0.00,
            'EFTConnection' => 0,
            'Command' => 11,
            'CommandType' => 0,
            'AccessLevel' => 0,
            'InDrawerValue' => 0,
            'method_type' => 3 // other
        ]);

        $timezone = TimezoneHelper::getTimezone($company_data['site_num'], $company_data['company_id']);

        // check if payment method exists before adding to the transaction
        $paymentData = [
            'company_id' => $company_data['company_id'],
            'site_num' => $company_data['site_num'],
            'terminal_num' => $company_data['terminal_num'],
            'trans_date' => $item->Date,
            'method_guid' => $payment_method->CommandUID,
            'method_type' => 3,
            'amount' => ($item->RRP * -1),
            'finalised_date' => Carbon::parse($transaction->finalised_date, $timezone)->setTimezone('UTC')
        ];

        $payment = new PosTransactionPayment();
        $payment->fill($paymentData);
        $payment->transaction()->associate($transaction);
        $payment->save();

        return $payment;
    }
}
