<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v1\Updates;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Events\ButtonQueueEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Colors;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\CompanyClerkRoles;
use NewbridgeWeb\Repositories\DayParts;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\Orders\Orders;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PosSettingHeader;
use NewbridgeWeb\Repositories\PosSettings;
use NewbridgeWeb\Repositories\PosUpdates;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Promotions;
use NewbridgeWeb\Repositories\ReceiptLayouts;
use NewbridgeWeb\Repositories\Recipes;
use NewbridgeWeb\Repositories\RedisRepositoryLists;
use NewbridgeWeb\Repositories\RevenueCenters;
use NewbridgeWeb\Repositories\SKU;
use NewbridgeWeb\Repositories\SubDepartment;
use NewbridgeWeb\Repositories\Suppliers;
use NewbridgeWeb\Repositories\Tables;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use NewbridgeWeb\Repositories\Tax\TaxRules;
use NewbridgeWeb\Repositories\VoidReasons;
use Ramsey\Uuid\Uuid;

class UpdatesSlim extends Controller
{
    public $lastUpdateDate;
    public $updateID;
    public $updateData = [];
    public $company_id;
    public $site_num;
    public $terminal_num;
    public $initial;
    public $log;
    public $module;
    public $reason_code;
    public $updateCount;
    public $data;
    public $terminal;
    public $version = 1;
    public array $modules = [
        "revenue_centers",
        "sys_tax_rules",
        "sys_day_parts",
        "orders",
        "sys_job_roles",
        "pos_settings_tab_header",
        "site_tables",
        "sys_clerk_areas",
        "sys_reasons",
        "sys_void_reasons",
        "sys_color",
        "sys_receiptFormat",
        "pos_settings",
        "sys_tax_rates",
        "plu_discounts",
        "plu_promotions",
        "sys_clerks",
        "sys_payments",
        "sys_int_commands",
        "pos_buttons",
        "pos_screens",
        "plu_modifier_groups",
        "plu_recipes",
        "plu_sku",
        "plu_suppliers",
        "plu_subdepartment",
        "plu_department",
        "plu_products"
    ];

    public function getUpdates(Request $request, $module)
    {
        $data['module'] = $module;
        $this->module = $module;

        $auth = $request->header('X-Newbridge-Auth');
        $auth = explode(':', base64_decode($auth));

        $this->version = $request->hasHeader('nb-version') ? $request->header('nb-version') : 1;

        // get the terminal UUID
        $company = cache()->rememberForever($auth[0], function () use ($auth) {
            return Company::where('terminal_access_key', $auth[0])->first();
        });

        $this->initial = (bool)$request->header('X-Newbridge-Initial');

        $this->company_id = $company['id'];
        $this->terminal_num = $auth[1];
        $this->site_num = $auth[2];

        $this->terminal = cache()->remember(
            'pos-' . $this->company_id . '-' . $auth[2] . '-' . $auth[1],
            3600,
            function () use ($auth, $company) {
                return Pos::where('company_id', $this->company_id)->where('site_num', $auth[2])->where(
                    'terminal_num',
                    $auth[1]
                )->limit(1)->first();
            }
        );

        $data['company_id'] = $this->company_id;
        $data['site_num'] = $auth[2];
        $data['terminal_num'] = $auth[1];

        /**
         *
         * Delete all pending updates for the current terminal,
         * so as to prevent corruption if the terminal has existed
         * before initialisation.
         *
         */
        if ($this->initial && $module == 'taxrates') {
            $key = '*t-' . $this->terminal->id . '*';
            $deleteKeys = Redis::connection('cache')->keys($key);

            if ($deleteKeys) {
                foreach ($deleteKeys as $k) {
                    Redis::connection('cache')->del($k);
                }
            }
        }

        $data['ignore_date'] = $request->header('X-Newbridge-Initial') != null;
        $this->lastUpdateDate = $this->initial ? '1970-01-01 00:00:00' : Carbon::now()->toDateTimeString();

        if ($module == null) {
            return response()->json(['status' => 'error', 'message' => 'Sorry please check the module in your url'],
                404);
        }

        $this->data = $data;
        $this->initial = $this->lastUpdateDate == '1970-01-01 00:00:00';
        $this->updateID = $this->getRedisIncrement();
        $this->updateData = $this->getUpdateData($module);
        $this->reason_code = -1;

        return response()->json($this);
    }

    private function getRedisIncrement()
    {
        $exists = Redis::connection('cache')->exists('lastUpdateID');

        if ($exists == 1) {
            $last = (int)Redis::connection('cache')->get('lastUpdateID');

            $next = $last + 1;
            Redis::connection('cache')->set('lastUpdateID', $next);

            return $next;
        } else {
            Redis::connection('cache')->set('lastUpdateID', 100000);

            return 100000;
        }
    }

    public function checkUpdates(Request $request)
    {
        $data['module'] = 'check-updates';

        $auth = $request->header('X-Newbridge-Auth');
        $auth = explode(':', base64_decode($auth));

        // get the terminal UUID
        $company = cache()->rememberForever($auth[0], function () use ($auth) {
            return Company::where('terminal_access_key', $auth[0])->first();
        });

        $this->company_id = $company['id'];
        $this->terminal_num = $auth[1];
        $this->site_num = $auth[2];
        $this->module = $data['module'];

        $this->terminal = cache()->remember(
            'pos-' . $company['id'] . '-' . $auth[2] . '-' . $auth[1],
            60,
            function () use ($auth, $company) {
                return Pos::where('company_id', $company['id'])->where('site_num', $auth[2])->where(
                    'terminal_num',
                    $auth[1]
                )->first();
            }
        );

        return $this->checkForAvailableChanges();
    }

    public function getUpdateData($module)
    {
        switch ($module) {
            case 'products':
                if ($this->initial) {
                    return $this->getProducts();
                } else {
                    $ids = $this->getUpdatesForModule('plu_products', 'plu_products', false);

                    if ($ids != null) {
                        return $this->getProducts($ids);
                    } else {
                        return null;
                    }
                }
            case 'departments':
                if ($this->initial) {
                    return $this->getDepartments();
                } else {
                    $ids = $this->getUpdatesForModule('plu_departments', 'plu_department', false);
                    if ($ids != null) {
                        return $this->getDepartments($ids);
                    } else {
                        return null;
                    }
                }
            case 'subdepartments':
                if ($this->initial) {
                    return $this->getSubDepartments();
                } else {
                    $ids = $this->getUpdatesForModule('plu_sub_departments', 'plu_subdepartment', false);
                    if ($ids != null) {
                        return $this->getSubDepartments($ids);
                    } else {
                        return null;
                    }
                }
            case 'suppliers':
                if ($this->initial) {
                    return $this->getSuppliers();
                } else {
                    $ids = $this->getUpdatesForModule('plu_suppliers', 'plu_suppliers', false);
                    if ($ids != null) {
                        return $this->getSuppliers($ids);
                    } else {
                        return null;
                    }
                }
            case 'skus':
                if ($this->initial) {
                    return $this->getSkus();
                } else {
                    $ids = $this->getUpdatesForModule('plu_sku', 'plu_skus', false);
                    if ($ids != null) {
                        return $this->getSkus($ids);
                    } else {
                        return null;
                    }
                }
            case 'recipes':
                if ($this->initial) {
                    return $this->getRecipes();
                } else {
                    $ids = $this->getUpdatesForModule('plu_recipes', 'plu_recipes', false);
                    if ($ids != null) {
                        return $this->getRecipes($ids);
                    } else {
                        return null;
                    }
                }
            case 'modifiers':
                if ($this->initial) {
                    return $this->getModifierGroups();
                } else {
                    $ids = $this->getUpdatesForModule('plu_modifier_groups', 'mod_groups', false);
                    if ($ids != null) {
                        return $this->getModifierGroups($ids);
                    } else {
                        return null;
                    }
                }
            case 'modifier-links':
                return [];
            case 'pages':
                if ($this->initial) {
                    return $this->getPages();
                } else {
                    $ids = $this->getUpdatesForModule('pos_screens', 'pos_screens', true);
                    if ($ids != null) {
                        return $this->getPages($ids);
                    } else {
                        return null;
                    }
                }
            case 'buttons':
                if ($this->initial) {
                    return $this->getButtons();
                } else {
                    $ids = $this->getUpdatesForModule('pos_buttons', 'pos_button', 'both');
                    if ($ids != null) {
                        return $this->getButtons($ids);
                    } else {
                        return null;
                    }
                }
            case 'intcommands':
                if ($this->initial) {
                    return $this->getIntCommands();
                } else {
                    // most likely not going to be used
                    $ids = $this->getUpdatesForModule('sys_int_commands', 'sys_int_commands', false);
                    if ($ids != null) {
                        return $this->getIntCommands($ids);
                    } else {
                        return null;
                    }
                }
            case 'payment-types':
                if ($this->initial) {
                    return $this->getPaymentTypes();
                } else {
                    $ids = $this->getUpdatesForModule('sys_payments', 'sys_payment', false);
                    if ($ids != null) {
                        return $this->getPaymentTypes($ids);
                    } else {
                        return null;
                    }
                }
            case 'clerks':
                if ($this->initial) {
                    return $this->getClerks();
                } else {
                    $ids = $this->getUpdatesForModule('sys_clerks', 'sys_clerks', false);
                    if ($ids != null) {
                        return $this->getClerks($ids);
                    } else {
                        return null;
                    }
                }
            case 'promotions':
                if ($this->initial) {
                    return $this->getPromotions();
                } else {
                    $ids = $this->getUpdatesForModule('plu_promotions', 'plu_promotion', true);
                    if ($ids != null) {
                        return $this->getPromotions($ids);
                    } else {
                        return null;
                    }
                }
            case 'discounts':
                if ($this->initial) {
                    return $this->getDiscounts();
                } else {
                    $ids = $this->getUpdatesForModule('plu_discounts', 'plu_discount', false);
                    if ($ids != null) {
                        return $this->getDiscounts($ids);
                    } else {
                        return null;
                    }
                }
            case 'taxrates':
                if ($this->initial) {
                    return $this->getTaxRates();
                } else {
                    $ids = $this->getUpdatesForModule('sys_tax_rates', 'sys_tax_rates', false);
                    if ($ids != null) {
                        return $this->getTaxRates($ids);
                    } else {
                        return null;
                    }
                }
            case 'pos-settings':
                if ($this->initial) {
                    return $this->getPosSettings();
                } else {
                    $ids = $this->getUpdatesForModule('pos_settings', 'pos_settings', true);
                    if ($ids != null) {
                        return $this->getPosSettings($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'receipts':
                if ($this->initial) {
                    return $this->getReceiptLayouts();
                } else {
                    $ids = $this->getUpdatesForModule('sys_receiptFormat', 'pos_receipt_layout', true);
                    if ($ids != null) {
                        return $this->getReceiptLayouts($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'colors':
                if ($this->initial) {
                    return $this->getColors();
                } else {
                    $ids = $this->getUpdatesForModule('sys_color', 'sys_color', false, null);
                    if ($ids != null) {
                        return $this->getColors($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'void-reasons':
                if ($this->initial) {
                    return $this->getVoidReasons();
                } else {
                    $ids = $this->getUpdatesForModule('sys_void_reasons', 'sys_reasons', false);
                    if ($ids != null) {
                        return $this->getVoidReasons($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'reasons':
                if ($this->initial) {
                    return $this->getReasons();
                } else {
                    $ids = $this->getUpdatesForModule('sys_reasons', 'sys_reasons', false);
                    if ($ids != null) {
                        return $this->getReasons($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'clerk-areas':
                if ($this->initial) {
                    return $this->getClerkAreas();
                } else {
                    $ids = $this->getUpdatesForModule('sys_clerk_areas', 'sys_clerk_areas', false);
                    if ($ids != null) {
                        return $this->getClerkAreas($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'tables':
                if ($this->initial) {
                    return $this->getTables();
                } else {
                    $ids = $this->getUpdatesForModule('site_tables', 'site_tables', true);
                    if ($ids != null) {
                        return $this->getTables($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'setting-headers':
                if ($this->initial) {
                    return $this->getSettingHeaders();
                } else {
                    $ids = $this->getUpdatesForModule('pos_settings_tab_header', 'pos_settings_tab_header', false);
                    if ($ids != null) {
                        return $this->getSettingHeaders($ids);
                    } else {
                        return null;
                    }
                }

                break;
            case 'clerk-roles':
                if ($this->initial) {
                    return $this->getClerkRoles();
                } else {
                    $ids = $this->getUpdatesForModule('sys_job_roles', 'sys_job_roles', false);
                    if ($ids != null) {
                        return $this->getClerkRoles($ids);
                    } else {
                        return null;
                    }
                }
            // no break
            case 'orders':
                return $this->getUpdatesForModule('orders', 'orders', false);

                break;
            case 'day-parts':
                if ($this->initial) {
                    return $this->getDayParts();
                } else {
                    $ids = $this->getUpdatesForModule('sys_day_parts', 'sys_day_parts', false);
                    if ($ids != null) {
                        return $this->getDayParts($ids);
                    } else {
                        return null;
                    }
                }
            // no break
            case 'tax-rules':
                if ($this->initial) {
                    return $this->getTaxRules();
                } else {
                    $ids = $this->getUpdatesForModule('sys_tax_rules', 'sys_tax_rules', false);
                    if ($ids != null) {
                        return $this->getTaxRules($ids);
                    } else {
                        return null;
                    }
                }
            // no break
            case 'revenue-centers':

                if ($this->initial) {
                    return $this->getRevenueCenters();
                } else {
                    $ids = $this->getUpdatesForModule('revenue_centers', 'revenue_centers', false);
                    if ($ids != null) {
                        return $this->getRevenueCenters($ids);
                    } else {
                        return null;
                    }
                }
            // no break
            default:
                return response()->json(['status' => 'error', 'message' => 'Sorry this api does not exist'], 404);
        }
    }

    private function checkForAvailableChanges(): array
    {
        $updates = [
            'modules' => [],
            'count' => 0
        ];

        $hasChanges = Redis::connection('cache')->exists(
            'c-' . $this->company_id . ':t-' . $this->terminal->id . ':check-updates'
        );

        if ($hasChanges) {
            $modules = Redis::connection('cache')->get(
                'c-' . $this->company_id . ':t-' . $this->terminal->id . ':check-updates'
            );
            $modules = json_decode($modules, true);

            if (is_array($modules)) {
                $updates['modules'] = $modules;
                $updates['count'] = count($modules);
            }
        }

        return $updates;
    }

    private function getUpdatesForModule($module, $table_name, $site_specific = false, $company = false)
    {
        if ($company === false) {
            $company = $this->company_id;
        }

        $keys = [];
        $keys[] = 'c-' . $company . ':t-' . $this->terminal->id . ':updates:' . $module;
        $keys[] = 'c-' . $company . ':t-' . $this->terminal->id . ':inserts:' . $module;

        $this->markUpdatesAsCollected($company, $table_name);

        $results = [];

        /**
         * Get Updates for the terminal
         */
        $key = 'c-' . $this->company_id . ':t-' . $this->terminal->id . ':';
        $key .= 'updates:' . $table_name;

        /** get the updates */
        $results1 = RedisRepositoryLists::retrieve($key, $this->updateID, true);

        /**
         * Get inserts for the terminal
         */
        $key = 'c-' . $this->company_id . ':t-' . $this->terminal->id . ':';
        $key .= 'inserts:' . $table_name;

        /** get the results */
        $results2 = RedisRepositoryLists::retrieve($key, $this->updateID, true);

        $results = array_merge($results1, $results2);

        if ($module == 'orders') {
            if ($this->version == 1) {
                $results['orders'] = RedisRepositoryLists::retrieve(
                    'c-' . $this->company_id . ':s-' . $this->site_num . ':orders',
                    $this->updateID,
                    true
                );

                foreach ($results['orders'] as $order) {
                    Orders::where('order_id', $order->OrderNumber)->where('company_id', $this->company_id)->get()->each(
                        function ($model) {
                            $model->update(['pos_status' => 1]);
                        }
                    );
                }
            } elseif ($this->version == 2) {
                $results['orders'] = [];

                $orders = Orders::where('company_id', $this->company_id)->where('pos_status', 0)->limit(20)->get()->pluck('transaction_pos');

                if (!$orders->isEmpty()) {
                    foreach ($orders as $order) {
                        $results['orders'][] = json_decode($order);
                    }
                }
            } else {
                $results['orders'] = RedisRepositoryLists::retrieve(
                    'c-' . $this->company_id . ':s-' . $this->site_num . ':orders',
                    $this->updateID,
                    true
                );
            }
        }

        if (empty($results)) {
            $results = null;
        }

        return $results;
    }

    /**
     * Get all products based on the company ID
     * filter by dates and if initial get all active records,
     * if not initial get all records including deletes.
     *
     * @param null $ids
     * @return array
     * @throws \Exception
     */
    public function getProducts($ids = null)
    {
        try {
            $products = new Products();

            $company = Company::find($this->company_id);

            if ($ids != null) {
                if ($company->site_specific_products === 0) {
                    $result['updates']['plu_products'] = $products->where([
                        ['company_id', $this->company_id]
                    ])
                        ->whereIn('id', $ids)
                        ->with('groups')
                        ->withTrashed()
                        ->get();
                } else {
                    $result['updates']['plu_products'] = $products->where([
                        ['company_id', $this->company_id],
                        ['site_num', '=', $this->site_num]
                    ])
                        ->whereIn('id', $ids)
                        ->with('groups')
                        ->withTrashed()
                        ->get();
                }
            } else {
                if ($company->site_specific_products === 0) {
                    $result['inserts']['plu_products'] = $products->where([
                        ['company_id', $this->company_id]
                    ])->with('groups')
                        ->get();
                } else {
                    $result['inserts']['plu_products'] = $products->where([
                        ['company_id', $this->company_id],
                        ['site_num', '=', $this->site_num]
                    ])->with('groups')
                        ->get();
                }
            }


            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getDepartments($ids = null)
    {
        DB::beginTransaction();

        try {
            $departments = new Departments();
            Departments::where('company_id', $this->company_id)->update(['site_num' => $this->site_num]);

            if ($ids != null) {
                $result['updates']['plu_departments'] = $departments->where('company_id', $this->company_id)
                    ->whereIn('id', $ids)
                    ->withTrashed()
                    ->get();

                Departments::where('company_id', $this->company_id)->update(['site_num' => 0]);

                DB::commit();

                return $result;
            } else {
                $result['inserts']['plu_departments'] = $departments->where([
                    ['company_id', $this->company_id],
                    ['created_at', '>=', $this->lastUpdateDate]
                ])
                    ->get();

                Departments::where('company_id', $this->company_id)->update(['site_num' => 0]);

                DB::commit();

                return $result;
            }
        } catch (\Exception $e) {
            DB::rollback();
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getSubDepartments($ids = null)
    {
        $subdepartment = new SubDepartment();

        try {
            if ($ids != null) {
                $result['updates']['plu_sub_departments'] = $subdepartment->where('company_id', $this->company_id)
                    ->whereIn('id', $ids)
                    ->withTrashed()
                    ->get();
            } else {
                $result['inserts']['plu_sub_departments'] = $subdepartment->where('company_id', $this->company_id)->get();
            }


            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getSuppliers($ids = null)
    {
        $suppliers = new Suppliers();

        try {
            if ($ids != null) {
                $result['updates']['plu_suppliers'] = $suppliers->whereIn('id', $ids)
                    ->withTrashed()
                    ->get();
            } else {
                $result['inserts']['plu_suppliers'] = $suppliers->where('company_id', $this->company_id)
                    ->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getSkus($ids = null)
    {
        $skus = new SKU();

        try {
            if ($ids != null) {
                $result['updates']['plu_sku'] = $skus->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['plu_sku'] = $skus->where('company_id', $this->company_id)->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getRecipes($ids = null)
    {
        $recipes = new Recipes();

        try {
            if ($ids != null) {
                $result['updates']['plu_recipes'] = $recipes->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['plu_recipes'] = $recipes->where('company_id', $this->company_id)->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getModifierGroups($ids = null)
    {
        try {
            $commands = new Commands();
            $company = Company::find($this->company_id);
            $sites = $company->site_specific_products === 1 ? [$this->site_num] : [
                0,
                1,
                2,
                3,
                4,
                5,
                6,
                7,
                8,
                9,
                10,
                11,
                12,
                13,
                14,
                15
            ];

            if ($ids != null) {
                $result['updates']['plu_modifier_groups'] = $commands->with(['group_links'])
                    ->whereIn('id', $ids)
                    ->withTrashed()
                    ->get();
            } else {
                $result['inserts']['plu_modifier_groups'] = $commands->where([
                    ['Command', 6],
                    ['company_id', $this->company_id]
                ])
                    ->whereIn('site_num', $sites)
                    ->with(['group_links'])->get();
            }


            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getPages($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['pos_screens'] = Commands::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['pos_screens'] = Commands::where([
                    ['Command', 1, 117],
                    ['company_id', $this->company_id],
                    ['site_num', '=', (int)$this->site_num]
                ])->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getColors($ids = null)
    {
        try {
            if ($ids != null) {
                $result['inserts']['sys_color'] = Colors::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_color'] = Colors::all();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getButtons($ids = null)
    {
        try {
            $sites = [0, $this->site_num];

            if ($ids != null) {
                $posButtons = new PosButtons();

                $result['updates']['pos_buttons'] = $posButtons->with([
                    'lnk_buttonlinks' => function ($q) {
                        $q->orderBy('position')
                            ->orderBy('created_at');
                    },
                    'pos_button_style',
                    'plu_product_page' => function ($q) use ($sites) {
                        $q->whereIn('site_num', $sites)->withTrashed();
                    }
                ])->whereIn('id', $ids)
                    ->withTrashed()
                    ->get();
            } else {
                // get the first 10 pages
                $pages = Commands::select('CommandUID')->where('company_id', $this->company_id)
                    ->whereIn('Command', [1, 117, 6])
                    ->whereIn('site_num', $sites)
                    ->offset(0)
                    ->limit(5)
                    ->get()->pluck('CommandUID');

                $posButtons = new PosButtons();

                $result['inserts']['pos_buttons'] = $posButtons->where([
                    ['company_id', $this->company_id]
                ])
                    ->with([
                        'lnk_buttonlinks' => function ($q) {
                            $q->orderBy('position')
                                ->orderBy('created_at');
                        },
                        'pos_button_style',
                        'plu_product_page' => function ($q) use ($sites) {
                            $q->whereIn('site_num', $sites);
                        }
                    ])
                    ->whereIn('site_num', $sites)
                    ->whereIn('plu_product_page_guid', $pages)
                    ->get();

                $remainingPages = Commands::where([
                    ['company_id', $this->company_id],
                    ['created_at', '>=', $this->lastUpdateDate]
                ])
                    ->whereIn('site_num', $sites)
                    ->whereIn('Command', [1, 117, 6])
                    ->offset(5)
                    ->limit(99999)
                    ->get();

                if ($remainingPages->count() > 0) {
                    $remainingPages = $remainingPages->toArray();

                    foreach ($remainingPages as $page) {
                        $page['terminal_id'] = $this->terminal;
                        \Event::dispatch(new ButtonQueueEvent($page));
                    }
                }
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getIntCommands($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['sys_int_commands'] = Commands::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_int_commands'] = Commands::where([
                    ['company_id', 0]
                ])
                    ->orWhere(function ($q) {
                        $q->where('company_id', $this->company_id)
                            ->whereIn('Command', [113, 89]);
                    })
                    ->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getPaymentTypes($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['sys_payments'] = Payments::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_payments'] = Payments::where([
                    ['company_id', $this->company_id]
                ])->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getClerks($ids = null)
    {
        try {
            $company = Company::find($this->company_id);

            if ($ids != null) {
                $result['updates']['sys_clerks'] = Clerks::whereIn('id', $ids)
                    ->with('ClkClerkRoles')
                    ->with('lnk_buttonlinks')
                    ->withTrashed()
                    ->get();
            } else {
                if ($company->site_specific_clerks == 1) {
                    $result['inserts']['sys_clerks'] = Clerks::where([
                        ['company_id', $this->company_id],
                        ['created_at', '>=', $this->lastUpdateDate],
                        ['site_num', $this->site_num]
                    ])
                        ->whereIn('site_num', [0, (int)$this->site_num])
                        ->with('ClkClerkRoles')
                        ->with('lnk_buttonlinks')
                        ->get();
                } else {
                    $result['inserts']['sys_clerks'] = Clerks::where([
                        ['company_id', $this->company_id],
                        ['created_at', '>=', $this->lastUpdateDate]
                    ])
                        ->with('ClkClerkRoles')
                        ->with('lnk_buttonlinks')
                        ->get();
                }
            }

            if (isset($result['inserts']['sys_clerks']) && !empty($result['inserts']['sys_clerks'])) {
                foreach ($result['inserts']['sys_clerks'] as $clerk) {
                    $clerk->DisplayName = $clerk->full_name;
                }
            }

            if (isset($result['updates']['sys_clerks']) && !empty($result['updates']['sys_clerks'])) {
                foreach ($result['updates']['sys_clerks'] as $clerk) {
                    $clerk->DisplayName = $clerk->full_name;
                }
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getPromotions($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['plu_promotions'] = Promotions::with([
                    'discount',
                    'promotion_bins' => function ($q) {
                        $q->with([
                            'promotion_items' => function ($q) {
                                $q->with('discount');
                            }
                        ]);
                    }
                ])
                    ->withTrashed()
                    ->whereIn('id', $ids)
                    ->get();
            } else {
                $result['inserts']['plu_promotions'] = Promotions::with([
                    'discount',
                    'promotion_bins' => function ($q) {
                        $q->with([
                            'promotion_items' => function ($q) {
                                $q->with('discount');
                            }
                        ]);
                    }
                ])->where('company_id', $this->company_id)
                    ->whereIn('site_num', [$this->site_num, 0])
                    ->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getDiscounts($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['plu_discounts'] = Discounts::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['plu_discounts'] = Discounts::where([
                    ['auto', 0],
                    ['company_id', $this->company_id]
                ])
                    ->whereIn('site_num', [$this->site_num, 0])
                    ->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getTaxRates($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['sys_tax_rates'] = TaxRates::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_tax_rates'] = TaxRates::where('company_id', $this->company_id)->get();
            }


            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getPosSettings($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['pos_settings'] = PosSettings::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['pos_settings'] = PosSettings::where([
                    ['company_id', $this->company_id],
                    ['site_num', $this->site_num]
                ])
                    ->whereIn('terminal_num', [$this->terminal_num, 0])
                    ->whereNotIn('setting_id', ['TerminalNum', 'SiteNum', 'AccessToken'])
                    ->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getSettingHeaders($ids = null)
    {
        try {
            if ($ids != null) {
                $result['updates']['pos_settings_tab_header'] = PosSettingHeader::whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['pos_settings_tab_header'] = PosSettingHeader::where(
                    [['company_id', $this->company_id]]
                )
                    ->whereNotIn('company_id', [0])
                    ->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getReceiptLayouts($ids = null)
    {
        try {
            $receiptLayouts = new ReceiptLayouts();

            if ($ids != null) {
                $result['inserts']['sys_receiptFormat'] = $receiptLayouts->whereIn('id', $ids)->get();
            } else {
                $result['inserts']['sys_receiptFormat'] = $receiptLayouts->where([
                    ['company_id', $this->company_id],
                    ['created_at', '>=', $this->lastUpdateDate],
                    ['site_num', $this->site_num]
                ])->get();
            }

            if (isset($result['inserts']) && !$result['inserts']['sys_receiptFormat']->isEmpty()) {
                foreach ($result['inserts']['sys_receiptFormat'] as $r) {
                    if ($r->reciepttext == null) {
                        $r->receipttext = '           ';
                    }
                }
            }

            if (isset($result['updates']) && !$result['updates']['sys_receiptFormat']->isEmpty()) {
                foreach ($result['updates']['sys_receiptFormat'] as $r) {
                    if ($r->reciepttext == null) {
                        $r->receipttext = '           ';
                    }
                }
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getVoidReasons($ids = null)
    {
        try {
            $voidReasons = new VoidReasons();

            if ($ids != null) {
                $result['updates']['sys_void_reasons'] = $voidReasons->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_void_reasons'] = $voidReasons->where('company_id', $this->company_id)->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getReasons($ids = null)
    {
        try {
            $voidReasons = new VoidReasons();

            if ($ids != null) {
                $result['updates']['sys_void_reasons'] = $voidReasons->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_void_reasons'] = $voidReasons->where('company_id', $this->company_id)->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getClerkAreas($ids = null)
    {
        try {
            $clerkAreas = new ClerkAreas();

            if ($ids != null) {
                $result['updates']['sys_clerk_areas'] = $clerkAreas->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_clerk_areas'] = $clerkAreas->where('company_id', $this->company_id)->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    private function markUpdatesAsCollected(int $company, string $module): void
    {
        $totalLength = 0;
        $keys = [];
        $keys[] = 'c-' . $company . ':t-' . $this->terminal->id . ':updates:' . $module;
        $keys[] = 'c-' . $company . ':t-' . $this->terminal->id . ':inserts:' . $module;
        foreach ($keys as $key) {
            try {
                $totalLength += Redis::connection('cache')->lLen($key);
            } catch (\Exception $e) {
            }
        }

        if ($totalLength == 0) {
            $key = "c-$company:t-" . $this->terminal->id . ":check-updates";
            $exists = Redis::connection('cache')->exists($key);

            if ($exists) {
                $data = Redis::connection('cache')->get($key);
                $decoded = json_decode($data, true);

                if (is_array($decoded)) {
                    foreach ($decoded as $k => $name) {
                        if ($name === $module) {
                            unset($decoded[$k]);
                        }
                    }

                    if (empty($decoded)) {
                        Redis::connection('cache')->delete($key);
                    } else {
                        Redis::connection('cache')->set($key, json_encode(array_values($decoded)));
                    }
                }
            }
        }
    }

    public function getTables($ids = null)
    {
        try {
            $tables = new Tables();

            if ($ids != null) {
                $result['updates']['site_tables'] = $tables->whereIn('id', $ids)->withTrashed()->with([
                    'tablelocation' => function ($q) {
                        $q->withTrashed();
                    }
                ])->get();
            } else {
                $result['inserts']['site_tables'] = $tables->where([
                    ['company_id', $this->company_id],
                    ['site_num', $this->site_num]
                ])->with('tablelocation')->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getClerkRoles($ids = null)
    {
        try {
            $companyClerkRoles = new CompanyClerkRoles();

            if ($ids != null) {
                $result['updates']['sys_job_roles'] = $companyClerkRoles->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_job_roles'] = $companyClerkRoles->where('company_id', $this->company_id)->get();
            }


            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getDayParts($ids = null)
    {
        try {
            $parts = new DayParts();
            if ($ids != null) {
                $result['updates']['sys_day_parts'] = $parts->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['sys_day_parts'] = $parts->where([
                    ['company_id', $this->company_id]
                ])->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getTaxRules($ids = null)
    {
        try {
            $parts = new TaxRules();
            if ($ids != null) {
                $result['updates']['sys_tax_rules'] = $parts->whereIn('id', $ids)->with('links')->withTrashed()->get();
            } else {
                $result['inserts']['sys_tax_rules'] = $parts->where([
                    ['company_id', $this->company_id]
                ])->with('links')->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    public function getRevenueCenters($ids = null)
    {
        try {
            $revenueCenters = new RevenueCenters();
            if ($ids != null) {
                $result['updates']['revenue_centers'] = $revenueCenters->whereIn('id', $ids)->withTrashed()->get();
            } else {
                $result['inserts']['revenue_centers'] = $revenueCenters->where([
                    ['company_id', $this->company_id]
                ])->get();
            }

            return $result;
        } catch (\Exception $e) {
            $this->logError($e->getMessage());

            throw new \Exception($e);
        }
    }

    private function convertToReadableSize($size)
    {
        $base = log($size) / log(1024);
        $suffix = array("Bytes", "KB", "MB", "GB", "TB");
        $f_base = floor($base);

        return round(pow(1024, $base - floor($base)), 1) . ' ' . $suffix[$f_base];
    }

    private function logError($e)
    {
        $dataSize = $this->convertToReadableSize(strlen(json_encode($this->updateData)));

        $data = [
            'update_count' => '',
            'update_data' => json_encode($this->updateData),
            'company_id' => $this->company_id,
            'site_num' => $this->site_num,
            'terminal_num' => $this->terminal_num,
            'data_size' => $dataSize,
            'module' => $this->module,
            'initial' => $this->initial ? 1 : 0,
            'status' => 2,
            'error' => $e
        ];

        $update = new PosUpdates();
        $update->fill($data);
        $update->save();
    }
}
