<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v2;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\ButtonLinks;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\Products;

class ModifierProducts extends Controller
{
    public function get($company_id, $site_num)
    {
        try {
            $results = [];

            $pages = Commands::where('company_id', $company_id)
                ->where('site_num', $site_num)
                ->where('command', 6)
                ->get();

            $products = Products::where('company_id', $company_id)
                ->where('site_num', $site_num)->get()->pluck('guid')->toArray();

            foreach ($pages as $page) {
                $buttons = PosButtons::where('plu_product_page_guid', $page->CommandUID)->get()->pluck('guid')->toArray();

                $links = ButtonLinks::whereIn('button_guid', $buttons)->whereIn('commandUID', $products)->get()->pluck('commandUID')->toArray();

                $linkProducts = Products::whereIn('guid', $links)->get();

                foreach ($linkProducts as $lp) {
                    $lp->modifier_group_guid = $page->CommandUID;
                    $results[] = $lp;
                }
            }

            return response()->json($results);
        } catch (\Exception $e) {
            report($e);

            return response()->json(['error' => $e->getMessage()], $e->getCode());
        }
    }
}
