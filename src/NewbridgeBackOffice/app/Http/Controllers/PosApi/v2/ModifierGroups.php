<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v2;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Commands;

class ModifierGroups extends Controller
{
    public function get($company_id, $site_num)
    {
        try {
            $result = Commands::where('company_id', $company_id)
                ->where('site_num', $site_num)->where('command', 6)
                ->get();

            return response()->json($result);
        } catch (\Exception $e) {
            report($e);

            return response()->json(['error' => $e->getMessage()], $e->getCode());
        }
    }
}
