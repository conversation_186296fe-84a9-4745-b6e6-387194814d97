<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\v2;

use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\Sites;

class SitesApi extends Controller
{
    public function get($company_id)
    {
        try {
            $result = Sites::where('company_id', $company_id)->get();

            return response()->json($result);
        } catch (\Exception $e) {
            report($e);

            return response()->json(['error' => $e->getMessage()], $e->getCode());
        }
    }
}
