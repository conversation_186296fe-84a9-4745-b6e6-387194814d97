<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\App\Ordering;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\PosApi\App\PaymentMethods;
use NewbridgeWeb\Http\Controllers\PosApi\v1\Transactions\Transactions;
use NewbridgeWeb\Repositories\Orders\OrdersActivity;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\Products;
use Ramsey\Uuid\Uuid;
use stdClass;

/**
 * use api classes that are required for the update api
 */
class Orders extends Controller
{
    public $settings;
    public $transaction;
    public $activity = [];

    public function ordersForTill($id = null, $type = 'transaction', $company_id = null)
    {
        if ($type === 'order') {
            $this->transaction = \NewbridgeWeb\Repositories\Orders\Orders::where('company_id', $company_id)->where('order_id', $id)->first();

            $this->transaction->updated_at = Carbon::now();
            $this->transaction->save();

            $transactionData = json_decode($this->transaction->transaction_web);
        }

        if (!isset($transactionData)) {
            return response()->json(['status' => 'error', 'message' => 'Transaction not found.'], 404);
        }

        $this->activity[] = [
            'order_id' =>  $transactionData->order_number,
            'activity' => 'Creating summary for the POS',
            'step' => 5,
            'created_at' => Carbon::now()
        ];

        $order = $this->createSummary($transactionData);

        $this->activity[] = [
            'order_id' =>  $transactionData->order_number,
            'activity' => 'Created summary for the POS',
            'step' => 6,
            'created_at' => Carbon::now()
        ];

        Redis::connection('cache')->rpush('c-'.$this->transaction->company_id.':s-'.$this->transaction->site_num.':orders', json_encode($order));

        $this->activity[] = [
            'order_id' =>  $transactionData->order_number,
            'activity' => 'Added the order to redis',
            'step' => 7,
            'created_at' => Carbon::now()
        ];

        OrdersActivity::insert($this->activity);

        $this->transaction->transaction_pos = json_encode($order);
        $this->transaction->save();

        return response()->json(['status' => 'success', 'message' => 'Order sent to tills successfully']);
    }

    private function createSummary($summary)
    {
        $order = new stdClass();

        $order->IsTraining = false;

        // Presets
        $order->Covers = $summary->covers;
        $order->CurrentCover = 0;
        $order->Created_At = $summary->trans_datetime;
        $order->Updated_At = $summary->trans_datetime;
        $order->Deleted_At = null;
        $order->Uploaded = null;
        $order->PAC = $summary->payment_id;

        $order->DoNotPromote = true;

        // More PRESETS
        $order->Is_Reloaded = false;
        $order->CoursePrinter = 0;
        $order->IsRefund = false;
        $order->IsWastage = false;
        $order->Course = 1;
        $order->RecordState = 1;
        $order->IsLoyaltyCard = false;
        $order->RoomNumber = 0;
        $order->Split = 1;
        $order->CustomerID = $summary->customer->customer_name; // customer name
        $order->CustomerIDIsSet = true;
        $order->DiscountAmount = 0;
        $order->ID = 0;
        $order->Timeoffset = 0;
        $order->Discount_Method = 0;
        $order->HasGiftCard = false;
        $order->GiftCard = 0;
        $order->ServiceCharge = 0;
        $order->Refund = 0;
        $order->Gratuity = 0;
        $order->Cash = 0;
        $order->Change = 0;

        // TABLE & LOCATION
        $order->TableNumber = $summary->table_number;
        $order->TableLocation = $summary->table_location;
        $order->TableLocationName = $summary->location_name;
        $order->TableGuid = $summary->table_guid;
        $order->DeliveryMethod = $summary->delivery_method;

        // TRANSACTION IDENTITY
        $order->Reference = $summary->reference;
        $order->Historic_Reference = null;
        $order->ReferenceHash = 0;

        $order->Order_Status = 0;// new status

        // FINALISE
        $order->Finalised_Date = $summary->finalised_date;
        $order->Finalized = $order->Finalised_Date == null ? false : true;
        $order->Date = $summary->trans_datetime;

        // clerk data
        $order->EmployeeNo = $summary->employee_no;
        $order->EmployeeGUID = $summary->employee_guid;

        // CUSTOMER DATA
        $order->DueTime = $summary->duetime;

        $order->customer = new StdClass();
        $order->customer->CustomerName = $summary->customer->customer_name;
        $order->customer->CustomerAddress1 = $summary->customer->customer_address1;
        $order->customer->CustomerAddress2 = $summary->customer->customer_address2;
        $order->customer->CustomerAddress3 = $summary->customer->customer_address3;
        $order->customer->CustomerAddress4 = $summary->customer->customer_address4;
        $order->customer->CustomerAddressPostcode = $summary->customer->customer_postcode;

        $order->customer->CustomerReserved1 = $summary->notes;
        $order->customer->CustomerReserved2 = null;
        $order->customer->CustomerReserved3 = null;
        $order->customer->CustomerReserved4 = null;
        $order->customer->CustomerReserved5 = null;

        $order->customer->CustomerEmail = $summary->customer->customer_email;
        $order->customer->CustomerPhone = $summary->customer->customer_phone;

        $order->Membership_No = $summary->membership_no;

        // Total amounts
        $order->TotalToPay = $summary->total;
        $order->SubTotal = $summary->subtotal;
        $order->CashBack = $summary->cashback;
        $order->Gratuity = $summary->gratuity;

        // cash and change in here
        $order->Cash =  0;
        $order->Change = 0;

        // discounts and methods // need to tidy this up so that only one field is used.
        $order->IsDiscounted = false;
        $order->Discount = 0;
        $order->OrderNumber = $summary->order_number;
        $order->RoomNumber = 0;

        // MORE FLAGS AND STUFF
        $order->KickCashDrawer = true;
        $order->IsNoSale = false;
        $order->IsDiscounted = false;
        $order->IsSelected = false;
        $order->RRP = null;
        $order->Z_Report = false;
        $order->IsRecalled = false;
        $order->FileName = '';
        $order->LockedBy = 0;
        $order->IsTableStore = false;
        $order->IsAutoTab = false;
        $order->TableState = 1;

        $order->items = new Collection();
        $order->PaymentMethods = new Collection();

        foreach ($summary->details as $k => $detail) {
            $orderDetail = $this->createDetail($detail, $summary);
            $order->items->push($orderDetail);
            if (isset($detail->details)) {
                $order->items[$k]->Modifiers = new Collection();

                foreach ($detail->details as $k2 => $detail) {
                    $mod = $this->createDetail($detail, $summary);
                    $order->items[$k]->Modifiers->push($mod);
                }
            }
        }

        if (isset($summary->payments)) {
            foreach ($summary->payments as $k2 => $payment) {
                $paymentDetail = $this->createPaymentDetail($summary, $payment);
                $order->items->push($paymentDetail);

                $paymentMethod = $this->createPayment($summary, $payment);
                $order->PaymentMethods->push($paymentMethod);
            }
        }

        return $order;
    }

    public function createDetail($detail, $summary)
    {
        $product = Products::where('guid', $detail->product_guid)->first();

        $orderDetail = new stdClass();

        $orderDetail->EmployeeNo = $summary->employee_no;
        $orderDetail->EmployeeGUID = $summary->employee_guid;
        $orderDetail->Date = $detail->datetime;

        // finance
        $orderDetail->RRP = $detail->rrp;
        $orderDetail->SalePrice = $detail->net_value;
        $orderDetail->Tax_Rate = $detail->tax_rate;

        $orderDetail->CommandType = $detail->command_type;
        $orderDetail->Command = $detail->command;

        $orderDetail->PLUDiscount_Guid = null;
        $orderDetail->PromoUID = null;

        // Department / Sub Department / Product Link
        $orderDetail->PLU_CommandUID = $detail->product_guid;
        $orderDetail->SubDepartment_Guid = $detail->sub_department_guid;
        $orderDetail->Department_Guid = $detail->department_guid;
        $orderDetail->DisplayName = $detail->displayname;
        $orderDetail->Short_Desc = $detail->displayname;

        $orderDetail->VoidReason = $detail->void_reason;
        $orderDetail->VoidGuid = $detail->void_reason_guid;

        $orderDetail->Barcode = null;
        $orderDetail->HasPrintedCustomerBill = false;
        $orderDetail->UID = Uuid::uuid4();
        $orderDetail->Index = 0;
        $orderDetail->Print_Priority = 0;
        $orderDetail->Tax_Guid = $detail->tax_guid;
        $orderDetail->Tax_Rate = $detail->tax_rate;
        $orderDetail->MyProperty = 0;
        $orderDetail->Membership_Number = null;
        $orderDetail->customer_type = 0;
        $orderDetail->PMSResult = null;
        $orderDetail->PLU_CommandUID = $detail->product_guid;
        $orderDetail->Split = null;
        $orderDetail->ConsolidationString = $detail->displayname;
        $orderDetail->ConsolidationValue = $detail->consolidationvalue;

        $orderDetail->Discount = null;
        $orderDetail->Discount_Method = 0;
        $orderDetail->DiscountAmount = 0;
        $orderDetail->txtDiscountAmount = 0;
        $orderDetail->PLUDiscount_Guid = null;
        $orderDetail->DiscountPrice = number_format($detail->net_value, 2);

        $orderDetail->ProductWeight = 0;
        $orderDetail->TareWeight = 0;
        $orderDetail->LDText = null;
        $orderDetail->HasBeenSavedAlready = false;
        $orderDetail->IsRefund = false;
        $orderDetail->ByWeightPrice = null;

        $orderDetail->KP_Target = $product != null ? $product->KP_Target : $detail->KP_Target;
        $orderDetail->KP_Target_Mask = 0;
        $orderDetail->KP_Category = $product?->kp_category;
        $orderDetail->KP_Category_Order = 0;

        $orderDetail->CourseID = 1;
        $orderDetail->CourseGUID = '00000000-0000-0000-0000-000000000000';
        $orderDetail->Follow_Course_Separator = false;
        $orderDetail->Cover = 0;
        $orderDetail->CommandType = 0;
        $orderDetail->Descriptor = null;

        $orderDetail->Attribute1 = $detail->Attribute1;
        $orderDetail->Attribute2 = $detail->Attribute2;
        $orderDetail->Attribute3 = $detail->Attribute3;
        $orderDetail->Attribute4 = $detail->Attribute4;
        $orderDetail->Attribute5 = '';
        $orderDetail->Attribute6 = '';
        $orderDetail->Attribute7 = '';

        /**
         * Promotions
         */
        $orderDetail->PromoUID = $detail->PromoUID;
        $orderDetail->PromoBinGuid = $detail->PromoBinGuid;
        $orderDetail->PromoMask = $detail->PromoMask;
        $orderDetail->PromoBinMask = $detail->PromoBinMask;

        $orderDetail->VoidGuid = '';
        $orderDetail->VoidReason = '';
        $orderDetail->IsVoid = false;

        $orderDetail->Color = '#ffffffff';
        $orderDetail->Is_Wasteable = false;
        $orderDetail->Non_Stock = null;
        $orderDetail->IsEnabled = true;
        $orderDetail->IsVisable = 0;

        $orderDetail->WasAutoSelected = false;
        $orderDetail->CommandUID = $detail->product_guid;
        $orderDetail->ProductID = 0;
        $orderDetail->LUID = '';

        $orderDetail->GroupColor = '';
        $orderDetail->IsModifier = false;
        $orderDetail->IsCommitedToCheck = false;
        $orderDetail->HasModifiers = 0;
        $orderDetail->Allergens = 0;


        return $orderDetail;
    }

    public function createPaymentDetail($summary, $payment)
    {
        $orderDetail = new stdClass();

        if ($payment->payment_method === "eoho") {
            $method = Payments::where('CommandUID', $summary->company_id.'-EAT-OUT-HELP-OUT')->first();
        }

        if ($payment->payment_method === 0) {
            $method = Payments::where('CommandUID', $summary->company_id.'-APP-PAYMENT')->first();
        }

        $orderDetail->EmployeeNo = $summary->employee_no;
        $orderDetail->EmployeeGUID = $summary->employee_guid;
        $orderDetail->Date = Carbon::now()->toDateTimeString();
        $orderDetail->DiscountPrice = ($payment->amount * -1);
        $orderDetail->RRP = ($payment->amount * -1);
        $orderDetail->Index = 0;
        $orderDetail->Print_Priority = 0;
        $orderDetail->Tax_Rate = 0;
        $orderDetail->MyProperty = 0;
        $orderDetail->customer_type = 0;
        $orderDetail->PLU_CommandUID = $method->CommandUID;
        $orderDetail->Split = null;
        $orderDetail->ConsolidationString = $method->DisplayName;
        $orderDetail->ConsolidationValue = 0;
        $orderDetail->Discount = 0;
        $orderDetail->ProductWeight = 0;
        $orderDetail->TareWeight = 0;
        $orderDetail->KP_Target = 0;
        $orderDetail->KP_Target_Mask = 0;
        $orderDetail->HasBeenSavedAlready = false;
        $orderDetail->IsRefund = false;
        $orderDetail->DiscountAmount = 0;
        $orderDetail->txtDiscountAmount = 0;
        $orderDetail->CourseID = 1;
        $orderDetail->CourseGUID = "00000000-0000-0000-0000-000000000000";
        $orderDetail->Cover = 0;
        $orderDetail->CommandType = 3;
        $orderDetail->DisplayName = $method->DisplayName;
        $orderDetail->Discount_Method = 0;
        $orderDetail->SalePrice = number_format(round(($payment->amount * -1), 2), 2);
        $orderDetail->ByWeightPrice = 0;
        $orderDetail->IsVoid = false;
        $orderDetail->IsPriceOverride = false;
        $orderDetail->Color = "#FFFF00FF";
        $orderDetail->Non_Stock = null;
        $orderDetail->Is_Wasteable = false;
        $orderDetail->IsSelected = false;
        $orderDetail->KP_Category_Order = 0;
        $orderDetail->WasAutoSelected = false;
        $orderDetail->ModifiersVisible = false;
        $orderDetail->CommandUID = $method->CommandUID;
        $orderDetail->ProductID = 0;
        $orderDetail->Command = 11;
        $orderDetail->IsModifier = false;
        $orderDetail->IsCommitedToCheck = false;
        $orderDetail->HasModifiers = false;
        $orderDetail->IsPromoItem = false;
        $orderDetail->PromoMask = 0;
        $orderDetail->PromoBinMask = 0;
        $orderDetail->IsDiscounted = false;
        $orderDetail->ProductColor = "#FF701760";
        $orderDetail->IsNew = true;
        $orderDetail->Follow_Course_Separator = false;
        $orderDetail->Allergens = 0;

        return $orderDetail;
    }

    public function createPayment($summary, $payment)
    {
        if ($payment->payment_method === "eoho") {
            $method = Payments::where('CommandUID', $summary->company_id.'-EAT-OUT-HELP-OUT')->first();
        }

        if ($payment->payment_method === 0) {
            $method = Payments::where('CommandUID', $summary->company_id.'-APP-PAYMENT')->first();
        }

        $paymentMethod = new stdClass();
        $paymentMethod->Amount = $payment->amount;
        $paymentMethod->TimeStamp = $summary->trans_datetime;
        $paymentMethod->Tendered = $payment->amount;

        $paymentMethod->Method = new stdClass();
        $paymentMethod->Method->DisplayName = $method->DisplayName;
        $paymentMethod->Method->Indexer = 0;
        $paymentMethod->Method->Selected = false;
        $paymentMethod->Method->Optional = false;
        $paymentMethod->Method->CommandUID = $method->CommandUID;
        $paymentMethod->Method->Command = $method->Command;
        $paymentMethod->Method->OpperationMode = 0;
        $paymentMethod->Method->CommandType = $method->CommandType;
        $paymentMethod->Method->AccessLevel = 0;
        $paymentMethod->Method->IsPMS = false;
        $paymentMethod->Method->IsDirty = false;
        $paymentMethod->Method->Is_Modifier_for_this_Product = false;
        $paymentMethod->Method->Created_At = $method->created_at;
        $paymentMethod->Method->Updated_At = $method->updated_at;
        $paymentMethod->Method->Deleted_At = null;
        $paymentMethod->Method->Uploaded  = $method->created_at;
        $paymentMethod->Method->Company_ID = $method->company_id;
        $paymentMethod->Method->EFTConnection = 0;
        $paymentMethod->Method->InDrawervalue1 = 0;
        $paymentMethod->Method->InDrawervalue2 = 0;
        $paymentMethod->Method->InDrawervalue = 0.00;
        $paymentMethod->Method->Guid = $method->CommandUID;
        $paymentMethod->Method->Value = null;
        $paymentMethod->Method->Partial_Tender = 0;
        $paymentMethod->Method->Over_Tender = 0;
        $paymentMethod->Method->Req_Manager = 0;
        $paymentMethod->Method->Prefix_Amount = 0.00;
        $paymentMethod->Method->Method_Type = $method->method_type; // Other
        $paymentMethod->Method->customer_type = 0;

        return $paymentMethod;
    }
}
