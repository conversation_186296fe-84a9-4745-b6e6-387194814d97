<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\App\Ordering;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Log;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Http\Controllers\PosApi\App\Ordering\Orders as ApiOrders;
use NewbridgeWeb\Http\Controllers\PosApi\v1\Transactions\Transactions;
use NewbridgeWeb\Jobs\WebOrders\ProcessWebOrder;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\Orders\Orders;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\RedisRepository;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\Tables;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use Ramsey\Uuid\Uuid;
use stdClass;

/**
 * use api classes that are required for the update api
 */
class WebOrders extends Controller
{
    /**
     * Request ID
     *
     * @var
     */
    public $requestId;

    /**
     * Used for the order ID
     *
     * @var
     */
    public $subjectId;

    /**
     * Define the incoming hook type
     *
     * @var string
     */
    public $type;

    /**
     * Store the data that was received
     *
     * @var array
     */
    public $data;

    /**
     * What is the store ID sending this transaction?
     * @var integer
     */
    public $store;

    /**
     * What does the transaction contain,
     * after being built for Newbridge
     *
     * @var object
     */
    public $transaction;

    /**
     * Store the company Information
     *
     * @var object
     */
    public $company;

    /**
     * Store an array of settings for the ordoo integration
     *
     * @var array
     */
    private $settings;
    public $activity = [];

    /**
     * The order
     *
     * @var object
     */
    public $order;
    public $current_detail = null;
    public $traits = [];
    public $clerk = null;
    public $orderProducts = [];
    public $productsNum = 1;
    public $productsAndTraits = [];
    public $user = '';
    public $site;
    public $site_num = 1;
    public $payment_method;
    public $payment_method_eoho;
    public $errors = [];
    public $platform = 'newbridge';
    public $currentConsolidation = 1;

    public int $orderId = 0;

    public function receive(Request $request, int $site_num, $platform = 'newbridge')
    {
        $this->platform = $request->hasHeader('platform') ? $request->header('platform') : $platform;

        try {
            $this->data = json_decode($request->getContent());
        } catch (\Exception $e) {
            report($e);

            return response()->json(
                [
                    'status' => 'errors',
                    'messages' =>
                        ['The data provided was not of the correct format to be decoded, please check your data and try again. JSON keys with empty values are not accepted, please replace with null.']
                ],
                422
            );
        }

        if (!is_object($this->data)) {
            return response()->json(
                [
                    'status' => 'errors',
                    'messages' =>
                        ['The data provided was not of the correct format to be decoded, please check your data and try again. JSON with empty values for a key are not accepted, please replace with null.']
                ],
                422
            );
        }

        $this->company = $request->input('company');
        $this->site = Sites::where('company_id', $this->company['id'])
            ->where('site_num', $site_num)
            ->first();


        /**
         * Specific Processing for Butlr
         *
         * Find an associate a table location, default table GUID or other settings
         */
        if ($platform != 'newbridge' && $platform != 'orderpay') {
            $date = Carbon::now()->toDateTimeString();
            $table = Tables::where('company_id', $this->company['id'])
                ->where('site_num', $site_num)
                ->where('tablenumber', $this->data->delivery->table->guid)
                ->whereHas('tablelocation', function ($q) use ($date) {
                    $q->where('Segment_From_Date', '<=', $date)
                        ->where('Segment_To_Date', '>=', $date);
                })
                ->first();

            if ($table == null) {
                $table = Tables::where('company_id', $this->company['id'])
                    ->whereHas('tablelocation', function ($q) use ($date) {
                        $q->where('Segment_From_Date', '<=', $date)
                            ->where('Segment_To_Date', '>=', $date);
                    })
                    ->where('site_num', $site_num)->first();
            }

            $this->data->delivery->table->guid = $table->guid;
        }

        if ($this->data->delivery->method != 'eat-in' && ($this->data->delivery->table->guid == null || $this->data->delivery->table->guid == "")) {
            if ($this->data->delivery->method == 'collection') {
                $table = Tables::where('company_id', $this->company['id'])->where('site_num', $site_num)->where(
                    'tablenumber',
                    config('newbridge.app_collection_table_number')
                )->first();
            }

            if ($table == null) {
                $table = Tables::where('company_id', $this->company['id'])->where('site_num', $site_num)->first();
            }

            $this->data->delivery->table->guid = $table->guid;
        }

        if ($this->platform == 'newbridge') {
            $existing = Orders::where(function($q) {
                $q->where('order_id', $this->data->order_number)
                    ->orWhere('reference', $this->data->order_number);
            })
                ->where('company_id', $this->company['id'])
                ->where('site_num', $this->site->site_num)
                ->count();
        } else {
            $existing = Orders::where('order_id', $this->data->order_number)
                ->orWhere('reference', $this->data->order_number)
                ->count();
        }

        $processing = RedisRepository::checkExists('processing_weborder_'.$this->data->order_number);

        if($existing > 0 || $processing){
            Log::error('[WebOrders] '.$this->data->order_number.' Order already exists and is a duplicate');
            return response()->json(['status' => 'duplicate'], 200);
        } else {
            RedisRepository::store('processing_weborder_'.$this->data->order_number, 'processing');
        }

        ProcessWebOrder::dispatch($this->data, $site_num, $this->company, $this->site, $this->platform);

        return response()->json(['status' => 'ok'], 200);
    }

    public function createOrder(): void
    {
        $this->orderId = self::newOrderId(
            $this->company['id'],
            $this->site->site_num
        );

        DB::beginTransaction();
        $this->order = null;

        $this->createSummary();

        $this->addProducts();

        $this->addPromotionProducts();

        $this->fireGratuityService();

        $this->order->site_num = $this->site->site_num;
        $this->order->company_id = $this->site->company_id;
        $this->order->transaction_web = json_encode($this->transaction);
        $this->order->integration_id = 3;
        $this->order->status = 0;
        $this->order->order_datetime = Carbon::parse($this->order->duetime);
        $this->order->save();

        if (!empty($this->errors)) {
            DB::rollBack();
        }

        $ApiOrder = new ApiOrders();
        $ApiOrder->ordersForTill($this->order->order_id, 'order', $this->company['id']);

        DB::commit();
    }

    private function createSummary(): void
    {
        $this->order = Orders::firstOrNew([
            'order_id' => $this->orderId,
            'platform' => $this->platform,
            'next_try' => Carbon::now()->addSeconds(env(strtoupper($this->platform) . '_DELAY_ACCEPT', 60))
        ]);

        $this->order->reference = !isset($this->data->order_number) ? $this->data->order_id : $this->data->order_number;

        $this->order->company_id = $this->company['id'];

        $hasClerk = Clerks::where('guid', $this->company['id'] . '-WEBAPP-CLERK')->first();

        if ($hasClerk == null) {
            $this->clerk = Clerks::create([
                'full_name' => 'Web App Orders',
                'short_name' => 'Web App Orders',
                'company_id' => $this->company['id'],
                'pin' => 'WEBAPP123',
                'site_num' => 0,
                'default_screen_guid' => null,
                'employee_no' => ************,
                'guid' => $this->company['id'] . '-WEBAPP-CLERK',
                'ishidden' => 1,
                'ismanager' => 1,
                'can_config' => 1,
                'can_no_sale' => 1,
                'can_void' => 1,
                'can_refund' => 1,
                'can_discount' => 1,
                'istrainee' => 0,
                'accesslevel' => 0
            ]);

            \Event::dispatch(new UpdaterUpdateCrudEvent($this->clerk));
        } else {
            $this->clerk = $hasClerk;
        }

        $hasPayment = Payments::where('CommandUID', $this->company['id'] . '-APP-PAYMENT')->first();

        if ($hasPayment == null) {
            $this->payment_method = Payments::create([
                'DisplayName' => 'Paid Online',
                'company_id' => $this->company['id'],
                'CommandUID' => $this->company['id'] . '-APP-PAYMENT',
                'prefix_amount' => 0.00,
                'EFTConnection' => 0,
                'Command' => 11,
                'CommandType' => 0,
                'AccessLevel' => 0,
                'InDrawerValue' => 0,
                'method_type' => 3 // other
            ]);

            \Event::dispatch(new UpdaterUpdateCrudEvent($this->payment_method));
        } else {
            $this->payment_method = $hasPayment;
        }

        $hasPaymentEoHo = Payments::where('CommandUID', $this->company['id'] . '-EAT-OUT-HELP-OUT')->first();

        if ($hasPaymentEoHo == null) {
            $this->payment_method_eoho = Payments::firstOrCreate([
                'DisplayName' => 'Eat Out Help Out',
                'company_id' => $this->company['id'],
                'CommandUID' => $this->company['id'] . '-EAT-OUT-HELP-OUT',
                'prefix_amount' => 0.00,
                'EFTConnection' => 0,
                'Command' => 11,
                'CommandType' => 0,
                'AccessLevel' => 0,
                'InDrawerValue' => 0,
                'method_type' => 3 // other
            ]);

            \Event::dispatch(new UpdaterUpdateCrudEvent($this->payment_method_eoho));
        } else {
            $this->payment_method_eoho = $hasPaymentEoHo;
        }

        $this->transaction = new PosTransaction();

        $this->data->sale_total = str_replace(',', '', $this->data->sale_total);

        $this->transaction->duetime = isset($this->data->duetime) ? Carbon::parse(
            $this->data->duetime
        )->toDateTimeString() : Carbon::parse($this->data->due_date)->toDateTimeString();
        $this->transaction->payment_id = $this->data->payment_id ?? null;

        // company data
        $this->transaction->company_id = $this->company['id'];
        $this->transaction->site_num = $this->site->site_num;
        $this->transaction->terminal_num = 1;

        $this->transaction->notes = $this->data->notes ?? '';

        // unique transaction identifiers
        $this->transaction->reference = Uuid::uuid4();
        $this->transaction->historic_reference = null;

        // date formating from json_date to Carbon
        $this->transaction->trans_datetime = isset($this->data->datetime) ? Carbon::parse(
            $this->data->datetime
        ) : Carbon::parse($this->data->order_date);
        $this->transaction->finalised_date = null;

        // clerk data
        $this->transaction->employee_no = $this->clerk->employee_no;
        $this->transaction->employee_guid = $this->clerk->guid;

        // do covers
        $this->transaction->covers = $this->data->covers ?? 0;

        // membership data
        $this->transaction->membership_no = null;

        // Total amounts
        $this->transaction->total = (float) $this->data->sale_total > 0 ? (float) $this->data->sale_total : 0;
        $this->transaction->subtotal = (float) $this->data->sale_total > 0 ? (float) $this->data->sale_total : 0;
        $this->transaction->cashback = 0;
        $this->transaction->gratuity = 0;

        // cash and change in here
        $this->transaction->cash = 0;
        $this->transaction->change = 0;

        // discounts and methods // need to tidy this up so that only one field is used.
        $this->transaction->is_discounted = 0;
        $this->transaction->discount_amount = 0;
        $this->transaction->total_discount_value = 0;

        if ($this->data->delivery->method == 'eat-in') {
            $this->transaction->delivery_method = 1;

            $table = \NewbridgeWeb\Repositories\Tables::with([
                'tablelocation' => function ($q) {
                    $q->withTrashed();
                }
            ])
                ->where('guid', $this->data->delivery->table->guid)->first();

            $this->transaction->table_location = $table->tablelocation->table_location_id;
            $this->transaction->location_name = $table->tablelocation->displayname;
            $this->transaction->table_number = $table->tablenumber;
            $this->transaction->table_guid = $table->guid;
        } elseif ($this->data->delivery->method == 'takeaway') {
            $this->transaction->delivery_method = 3;

            $table = \NewbridgeWeb\Repositories\Tables::with('tablelocation')
                ->where('guid', $this->data->delivery->table->guid)->first();

            $this->transaction->table_location = $table->tablelocation->table_location_id;
            $this->transaction->location_name = $table->tablelocation->displayname;
            $this->transaction->table_number = $table->tablenumber;
            $this->transaction->table_guid = $table->guid;
        } elseif ($this->data->delivery->method == 'collection') {
            $this->transaction->delivery_method = 2;

            $table = \NewbridgeWeb\Repositories\Tables::with('tablelocation')
                ->where('guid', $this->data->delivery->table->guid)->first();

            $this->transaction->table_location = $table->tablelocation->table_location_id;
            $this->transaction->location_name = $table->tablelocation->displayname;
            $this->transaction->table_number = $table->tablenumber;
            $this->transaction->table_guid = $table->guid;
        }

        if ($this->data->membership_no != null) {
            $this->transaction->membership_no = $this->data->membership_no;

            $customer = Customer::where('company_id', $this->company['id'])
                ->where('membership_no', $this->data->membership_no)
                ->with('address')
                ->first();

            if ($customer != null) {
                $this->transaction->customer = new \StdClass();
                $this->transaction->customer->customer_name = $customer->full_name;
                $this->transaction->customer->customer_email = $customer->email;
                $this->transaction->customer->customer_phone = $customer->telephone;
                $this->transaction->customer->customer_address1 = isset($customer->address->address_line1) ? $customer->address->address_line1 : null;
                $this->transaction->customer->customer_address2 = isset($customer->address->address_line2) ? $customer->address->address_line2 : null;
                $this->transaction->customer->customer_address4 = isset($customer->address->county) ? $customer->address->county : null;
                $this->transaction->customer->customer_postcode = isset($customer->address->postcode) ? $customer->address->postcode : null;
            } else {
                Log::error('[WebOrders] '.$this->data->order_number.' You have provided a membership_no that does not exist, please check and try again. Null is an acceptable value if the membership_no is now known or used.');
                $this->errors[] = 'error';
            }
        }

        if (isset($this->data->customer) && $this->data->membership_no == null) {
            $this->transaction->customer = new \StdClass();
            $this->transaction->customer->customer_name = $this->data->customer->full_name;
            $this->transaction->customer->customer_email = $this->data->customer->email;
            $this->transaction->customer->customer_phone = $this->data->customer->phone;
            $this->transaction->customer->customer_address1 = $this->data->customer->address->address_line1 ?? null;
            $this->transaction->customer->customer_address2 = $this->data->customer->address->address_line2 ?? null;
            $this->transaction->customer->customer_address3 = $this->data->customer->address->address_line3 ?? null;
            $this->transaction->customer->customer_address4 = $this->data->customer->address->address_line4 ?? null;
            $this->transaction->customer->customer_postcode = $this->data->customer->address->postcode ?? null;
        } else {
            $this->transaction->customer = new \StdClass();
            $this->transaction->customer->customer_name = '';
            $this->transaction->customer->customer_email = '';
            $this->transaction->customer->customer_phone = '';
            $this->transaction->customer->customer_address1 = '';
            $this->transaction->customer->customer_address2 = '';
            $this->transaction->customer->customer_address3 = '';
            $this->transaction->customer->customer_address4 = '';
            $this->transaction->customer->customer_postcode = '';
        }


        $this->transaction->order_number = $this->order->order_id;

        $this->transaction->room_number = null;

        $this->transaction->payments = new Collection();

        foreach ($this->data->payments as $payment) {
            $payment->amount = str_replace(',', '', $payment->amount);

            $paymentData = new \StdClass();
            $paymentData->payment_method = $payment->method_type;
            $paymentData->amount = (float) $payment->amount;

            $this->transaction->payments->push($paymentData);
        }

        $this->transaction->details = new Collection();
    }

    private function addProducts(): void
    {
        foreach ($this->data->products as $k => $product) {
            // get product from the database that matches the external third party ID
            $detail = new PosTransactionDetail();

            if ($product->product_guid != null) {
                $productData = Products::withTrashed()->where('guid', $product->product_guid)->first();

                if ($productData == null) {
                    if (!isset($this->data->meta->catch_all_id)) {
                        Log::error('[WebOrders] '.$this->data->order_number.' Please provide a catch all product ID or a valid product ID/GUID');
                        $this->errors[] = 'error';
                        return;
                    }

                    $productData = Products::withTrashed()->where('id', $this->data->meta->catch_all_id)->first();
                }
            } elseif ($product->app_product_id != null) {
                $productData = Products::where('company_id', $this->company['id'])->whereIn(
                    'site_num',
                    [$this->site->site_num, 0]
                )
                    ->where('third_party_id', $product->app_product_id)
                    ->first();

                if ($productData == null) {
                    if (!isset($this->data->meta->catch_all_id)) {
                        Log::error('[WebOrders] '.$this->data->order_number.' Please provide a catch all product ID or a valid product ID/GUID');
                        $this->errors[] = 'error';
                        return;
                    }

                    $productData = Products::withTrashed()->where('id', $this->data->meta->catch_all_id)->first();
                }
            } else {
                /**
                 * belongs to default product - by ID
                 */
                if (!isset($this->data->meta->catch_all_id)) {
                    Log::error('[WebOrders] '.$this->data->order_number.' Please provide a catch all product ID or a valid product ID/GUID');
                    $this->errors[] = 'error';
                    return;
                }

                $productData = Products::withTrashed()->where('id', $this->data->meta->catch_all_id)->first();
            }

            if ($productData == null) {
                Log::error('[WebOrders] '.$this->data->order_number.' Please provide a catch all product ID or a valid product ID/GUID');
                $this->errors[] = 'error';
                return;
            }

            $tax = $productData->tax_guid != null ? TaxRates::where('guid', $productData->tax_guid)->withTrashed(
            )->first() : null;

            $detail->finalised_date = null;

            // company data
            $this->transaction->company_id = $this->company['id'];
            $this->transaction->site_num = $this->site->site_num;
            $this->transaction->terminal_num = 1;

            $detail->KP_Target = $productData->KP_Target;

            $detail->datetime = $product->date;

            // employee
            // $detail->employee_no = $data->EmployeeNo;
            $detail->employee_no = null; // get from configuration
            $detail->employee_guid = null; // get from configuration

            // finance
            $detail->gross_value = $product->sale_value;
            $detail->net_value = $product->sale_value;
            $detail->tax_value = Transactions::calculateVat($product->sale_value, (float) $tax->rate);
            $detail->tax_rate = (float) $tax->rate;
            $detail->tax_guid = $productData->tax_guid;

            $detail->rrp = (float) abs($detail->net_value);

            $detail->command_type = 0;
            $detail->command = 3;
            $detail->cost_price = $productData->costprice;

            $detail->qty = 1;

            /**
             * Promotions
             */
            $detail->PromoUID = '';
            $detail->PromoBinGuid = '';
            $detail->PromoMask = 0;
            $detail->PromoBinMask = 0;
            $detail->Attribute1 = '';
            $detail->Attribute2 = '';
            $detail->Attribute3 = '';
            $detail->Attribute4 = '';

            // Department / Sub Department / Product Link
            $detail->product_guid = $productData->guid;
            $detail->sub_department_guid = $productData->sub_department_guid;
            $detail->department_guid = $productData->department_guid;
            $detail->displayname = $productData->displayname;

            $detail->void_reason = '';
            $detail->void_reason_guid = null;
            $detail->void_amount = 0;

            $consolidation_string = $productData->guid . $productData->selling_price_1;

            // check for modifiers and add to the order
            if (isset($product->modifiers) && count($product->modifiers) > 0) {
                $detail->details = new Collection();

                foreach ($product->modifiers as $mod) {
                    $modifier = $this->addModifier($mod);

                    $consolidation_string .= $mod->displayname;

                    if ($modifier != false) {
                        $detail->details->push($modifier);
                    }
                }
            }

            $detail->consolidationvalue = $this->currentConsolidation;
            $this->currentConsolidation++;

            $this->transaction->details->push($detail);
        }
    }

    public function fireGratuityService(): void
    {
        if (isset($this->data->gratuity)) {
            if ($this->data->gratuity && $this->data->gratuity > 0) {
                $this->addGratuityAndService('gratuity');
            }
        }
        if (isset($this->data->service_charge)) {
            if ($this->data->service_charge && $this->data->service_charge > 0) {
                $this->addGratuityAndService('service_charge');
            }
        }
    }

    private function addGratuityAndService($type): void
    {
        $command = null;
        if ($type == 'gratuity') {
            $command = Commands::where('DisplayName', 'Gratuity')->where('company_id', 0)->first();
        }
        if ($type == 'service_charge') {
            $command = Commands::where('DisplayName', 'Service Charge')->where('company_id', 0)->first();
        }

        if ($command) {
            // get product from the database that matches the external third party ID
            $detail = new PosTransactionDetail();

            $detail->finalised_date = null;

            // company data
            $this->transaction->company_id = $this->company['id'];
            $this->transaction->site_num = $this->site->site_num;
            $this->transaction->terminal_num = 1;

            $detail->KP_Target = 1;

            $detail->datetime = Carbon::now()->toDateTimeString();

            // employee
            $detail->employee_no = null; // get from configuration
            $detail->employee_guid = null; // get from configuration

            // finance
            $detail->gross_value = $this->data->$type;
            $detail->net_value = $this->data->$type;
            $detail->tax_value = 0;
            $detail->tax_rate = 0;
            $detail->tax_guid = null;

            $detail->rrp = $this->data->$type;

            $detail->command_type = 0;
            $detail->command = $command->Command;
            $detail->cost_price = 0;

            $detail->qty = 1;

            /**
             * Promotions
             */
            $detail->PromoUID = '';
            $detail->PromoBinGuid = '';
            $detail->PromoMask = 0;
            $detail->PromoBinMask = 0;
            $detail->Attribute1 = '';
            $detail->Attribute2 = '';
            $detail->Attribute3 = '';
            $detail->Attribute4 = '';

            // Department / Sub Department / Product Link
            $detail->product_guid = $command->CommandUID;
            $detail->sub_department_guid = null;
            $detail->department_guid = null;
            $detail->displayname = $command->DisplayName;

            $detail->void_reason = '';
            $detail->void_reason_guid = null;
            $detail->void_amount = 0;

            $detail->consolidationvalue = $this->currentConsolidation;
            $this->currentConsolidation++;

            $this->transaction->details->push($detail);
        }

    }

    private function addModifier($orderItem)
    {
        $newbridgeProduct = Products::withTrashed()->where('guid', $orderItem->guid)->first();

        if ($newbridgeProduct !== null) {

            $detail = new PosTransactionDetail();
            $tax = $newbridgeProduct->tax_guid != null ? TaxRates::where(
                'guid',
                $newbridgeProduct->tax_guid
            )->withTrashed()->first() : null;

            if ($tax == null) {
                Log::error('[WebOrders] '. $this->data->order_number .'The product ' . $newbridgeProduct->displayname . ' is missing correct tax data');
                $this->errors[] = 'error';
                return;
            }

            $detail->finalised_date = null;

            // company data
            $this->transaction->company_id = $this->company['id'];
            $this->transaction->site_num = 1;
            $this->transaction->terminal_num = 1;

            $detail->KP_Target = $newbridgeProduct->KP_Target;

            $detail->datetime = $this->transaction->trans_datetime;

            // employee
            $detail->employee_no = null; // get from configuration
            $detail->employee_guid = null; // get from configuration

            // finance
            $detail->gross_value = $orderItem->sale_value ? (float) $orderItem->sale_value : 0;
            $detail->net_value = $orderItem->sale_value != 0 ? (float) $orderItem->sale_value : 0;
            $detail->tax_value = Transactions::calculateVat($detail->net_value, (float) $tax['rate']);
            $detail->tax_rate = (float) $tax['rate'];
            $detail->tax_guid = $tax['guid'];

            $detail->rrp = $newbridgeProduct->selling_price_1;

            $detail->Attribute1 = '';
            $detail->Attribute2 = '';
            $detail->Attribute3 = '';
            $detail->Attribute4 = '';
            $detail->PromoUID = null;
            $detail->PromoBinGuid = null;
            $detail->PromoMask = 0;
            $detail->PromoBinMask = 0;

            $detail->command_type = 0;
            $detail->command = 3;
            $detail->cost_price = $newbridgeProduct->costprice;

            $detail->qty = 1;

            // Department / Sub Department / Product Link
            $detail->product_guid = $newbridgeProduct->guid;
            $detail->sub_department_guid = $newbridgeProduct->sub_department_guid;
            $detail->department_guid = $newbridgeProduct->department_guid;
            $detail->displayname = $newbridgeProduct->displayname;

            $detail->consolidationvalue = $this->currentConsolidation;
            $this->currentConsolidation++;

            $detail->void_reason = '';
            $detail->void_reason_guid = null;
            $detail->void_amount = 0;

            return $detail;
        }
    }

    private function addPromotionProducts()
    {
        if (isset($this->data->promotions)) {
            foreach ($this->data->promotions as $k => $promotion) {
                foreach ($promotion->items as $k2 => $product) {
                    // get product from the database that matches the external third party ID
                    $detail = new PosTransactionDetail();

                    $productData = Products::withTrashed()->where('guid', $product->product_guid)->first();

                    $tax = $productData->tax_guid != null ? TaxRates::where(
                        'guid',
                        $productData->tax_guid
                    )->withTrashed()->first() : null;

                    $detail->finalised_date = null;

                    // company data
                    $this->transaction->company_id = $this->company['id'];
                    $this->transaction->site_num = $this->site->site_num;
                    $this->transaction->terminal_num = 1;

                    $detail->KP_Target = $productData->KP_Target;

                    $detail->datetime = $product->date;

                    // employee
                    // $detail->employee_no = $data->EmployeeNo;
                    $detail->employee_no = null; // get from configuration
                    $detail->employee_guid = null; // get from configuration

                    // finance
                    $detail->gross_value = $product->sale_value;
                    $detail->net_value = $product->sale_value;
                    $detail->tax_value = Transactions::calculateVat($product->sale_value, (float) $tax->rate);
                    $detail->tax_rate = (float) $tax->rate;
                    $detail->tax_guid = $tax->guid;

                    $detail->rrp = $productData->selling_price_1;

                    $detail->command_type = 0;
                    $detail->command = 3;
                    $detail->cost_price = $productData->costprice;

                    /**
                     * Promotion specific codes
                     */
                    $detail->PromoUID = $promotion->guid;
                    $detail->PromoBinGuid = $product->bin_guid;
                    $detail->Attribute1 = $promotion->discount_type != '' ? $promotion->discount_type : 'SET';
                    $detail->Attribute2 = $promotion->short_text;
                    $detail->Attribute3 = $promotion->discount_type != '' ? $promotion->discount_type : 'SET';
                    $detail->Attribute4 = $promotion->discount_type != '' ? $promotion->discount_type : 'SET';

                    $detail->rrp = $productData->selling_price_1;

                    $detail->PromoMask = $promotion->mask_total;
                    $detail->PromoBinMask = $product->mask;

                    $detail->qty = 1;

                    // Department / Sub Department / Product Link
                    $detail->product_guid = $productData->guid;
                    $detail->sub_department_guid = $productData->sub_department_guid;
                    $detail->department_guid = $productData->department_guid;
                    $detail->displayname = $productData->displayname;

                    $consolidation_string = $detail->product_guid.$product->sale_value;

                    $detail->void_reason = '';
                    $detail->void_reason_guid = null;
                    $detail->void_amount = 0;


                    // check for modifiers and add to the order
                    if (isset($product->modifiers) && count($product->modifiers) > 0) {
                        $detail->details = new Collection();

                        foreach ($product->modifiers as $mod) {
                            $consolidation_string .= $mod->guid;

                            $modifier = $this->addModifier($mod);
                            $detail->details->push($modifier);
                        }
                    }


                    if (count($detail->details) > 0) {
                        foreach ($detail->details as $d) {
                            $consolidation_string .= $d->consolidationvalue;
                        }
                    }

                    $detail->consolidationvalue = $this->currentConsolidation;
                    $this->currentConsolidation++;

                    $this->transaction->details->push($detail);
                }
            }
        }

    }

    public static function newOrderId($company_id, $site_num)
    {
        $shop = Sites::where('company_id', $company_id)->where('site_num', $site_num)->first();

        $shop->order_id = ($shop->order_id + 1);
        $shop->save();

        return $company_id . $site_num . $shop->order_id;
    }
}
