<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\App;

use Carbon\Carbon;
use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\TableLocations;

/**
 * use api classes that are required for the update api
 */
class Tables extends Controller
{
    public $company;
    public $data;

    public function get(Request $request, $site_num = 1)
    {
        $this->company = $request->input('company');
        $this->company = $this->company['id'];

        $data = [];

        $data['tables'] = TableLocations::with(['tables' => function ($q) {
            $q->orderBy('displayname');
        }])
            ->where('company_id', $this->company)
            ->where('site_num', $site_num)
            ->where('Segment_From_date', '<=', Carbon::now())
            ->where('Segment_To_date', '>=', Carbon::now())
            ->where('app_available', 1)
            ->get()
            ->toArray();

        if (empty($data['tables'])) {
            return response()->json(['status' => 'error', 'messages' => ['No table locations available for this company']], 422);
        }

        $tables = [];

        foreach ($data['tables'] as $location) {
            $tables[$location['displayname']] = [];

            foreach ($location['tables'] as $table) {
                $tables[$location['displayname']][] = [
                    'table_number' => $table['tablenumber'],
                    'displayname' => $table['displayname'],
                    'guid' => $table['guid']
                ];
            }
        }

        return response()->json($tables);
    }
}
