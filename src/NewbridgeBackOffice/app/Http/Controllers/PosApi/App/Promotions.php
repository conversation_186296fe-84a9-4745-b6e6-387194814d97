<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\App;

use Illuminate\Http\Request;
use NewbridgeWeb\Http\Controllers\Controller;

class Promotions extends Controller
{
    public $company;
    public $data;
    public $site_num = 1;

    public function get(Request $request, $site_num = 1)
    {
        $this->site_num = $site_num;
        $this->company = $request->input('company');
        $this->company = $this->company['id'];

        $data = \NewbridgeWeb\Repositories\Promotions::with(['discount', 'promotion_bins' => function ($q) {
            $q->with('discount')->with(['promotion_items' => function ($q) {
                $q->with(['product' => function ($q2) {
                    $q2->select('id', 'guid', 'sub_department_guid');
                }])->whereIn('site_num', [0, $this->site_num]);
            }]);
        }])
            ->where('company_id', $this->company)
            ->where('isloyalty', 0)
            ->where('app_available', 1)
            ->get()
            ->keyBy('CommandUID')
            ->toArray();

        return response()->json($data);
    }
}
