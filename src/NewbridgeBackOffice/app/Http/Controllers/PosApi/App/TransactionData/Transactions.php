<?php

namespace NewbridgeWeb\Http\Controllers\PosApi\App\TransactionData;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use NewbridgeWeb\Http\Controllers\Controller;
use NewbridgeWeb\Repositories\PosTransaction;

/**
 * use api classes that are required for the update api
 */
class Transactions extends Controller
{
    public function transactions(Request $request, $date)
    {
        $this->validator(['date_value' => $date])->validate();

        $start = Carbon::parse($date)->startOfDay();
        $end = Carbon::parse($date)->endOfDay();

        $transactions = PosTransaction::with(['details' => function ($q) {
            $q->select(['id', 'displayname', 'qty as quantity', 'net_value as sale_value', 'tax_rate', 'tax_value', 'discount_value', 'trans_id', 'product_guid', 'sub_department_guid', 'department_guid', 'employee_guid', 'splitparentreference'])
                ->where('command_type', '!=', 5)
                ->whereNotIn('command', [107])
                ->with(['product' => function ($q) {
                    $q->select('id', 'guid', 'barcode', 'stock_code', 'third_party_id', 'DisplayName');
                }])
                ->with(['clerk' => function ($q) {
                    $q->select('guid', 'employee_no', 'full_name', 'short_name');
                }]);
        }])
            ->with(['payments' => function ($q) {
                $q->select('id', 'trans_id', 'method_type', 'method_guid', 'amount')->with(['type' => function ($q) {
                    $q->select('id', 'name');
                }])->with(['method' => function ($q) {
                    $q->select(['id', 'CommandUID', 'DisplayName', 'method_type']);
                }]);
            }])
            ->with(['clerk' => function ($q) {
                $q->select('guid', 'employee_no', 'full_name', 'short_name');
            }])
            ->where('company_id', $request['company']['id'])
            ->select(['id', 'site_num', 'terminal_num', 'order_number', 'total', 'trans_datetime as open_time', 'finalised_date', 'day_part', 'covers', 'parentreferencekey', 'membership_no', 'employee_guid'])
            ->whereBetween('finalised_date', [$start, $end])
            ->whereHas('details', function ($q) {
                $q->where('command_type', '!=', 5)
                    ->whereNotIn('command', [107]);
            })
            ->get();

        return response()->json($transactions);
    }

    protected function validator(array $data)
    {
        $message = [
            'date_value.date' => 'invalid date'
        ];

        return Validator::make($data, [
            'date_value' => 'date',
        ], $message);
    }
}
