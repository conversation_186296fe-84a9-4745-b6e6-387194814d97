<?php

namespace NewbridgeWeb\Http\Traits;

use NewbridgeWeb\Http\Helpers\ServiceBusHelper;
use function Illuminate\Events\queueable;

trait SendsToServiceBus {

    protected static function bootSendsToServiceBus(): void
    {
        if(config('newbridge.send_to_service_bus') === false) {
            return;
        }

        static::created(queueable(function ($model) {
            $tableName = str_replace("sys-", "", str_replace('_', '-', $model->getTable()));
            ServiceBusHelper::sendEventToTopic($tableName, $model->source_string, 'created', $model->toArray());
        }));

        static::updated(queueable(function ($model) {
            $tableName = str_replace("sys-", "", str_replace('_', '-', $model->getTable()));
            ServiceBusHelper::sendEventToTopic($tableName, $model->source_string, 'updated', $model->toArray());
        }));

        static::deleted(queueable(function ($model) {
            $tableName = str_replace("sys-", "", str_replace('_', '-', $model->getTable()));
            ServiceBusHelper::sendEventToTopic($tableName, $model->source_string, 'deleted', $model->toArray());
        }));

        if (method_exists(SendsToServiceBus::class, 'softDeleted')) {
            static::softDeleted(function ($model) {
                $tableName = str_replace("sys-", "", str_replace('_', '-', $model->getTable()));
                ServiceBusHelper::sendEventToTopic($tableName, $model->source_string, 'softdeleted', $model->toArray());
            });
        }
    }
}