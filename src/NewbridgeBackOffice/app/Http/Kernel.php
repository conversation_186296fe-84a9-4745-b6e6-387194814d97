<?php

namespace NewbridgeWeb\Http;

use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Foundation\Http\Kernel as HttpKernel;
use Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Foundation\Http\Middleware\ValidatePostSize;
use Illuminate\Http\Middleware\HandleCors;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use NewbridgeWeb\AppInsights\Middleware\TrackRequest;
use NewbridgeWeb\Http\Middleware\AbilityMiddleware;
use NewbridgeWeb\Http\Middleware\AccountsAuthMiddleware;
use NewbridgeWeb\Http\Middleware\ApiDeviceAuthorisation;
use NewbridgeWeb\Http\Middleware\BeamerMiddleware;
use NewbridgeWeb\Http\Middleware\CompanyStatusMiddleware;
use NewbridgeWeb\Http\Middleware\EncryptCookies;
use NewbridgeWeb\Http\Middleware\FavouriteReportsMiddleware;
use NewbridgeWeb\Http\Middleware\HttpsProtocol;
use NewbridgeWeb\Http\Middleware\Maintenance;
use NewbridgeWeb\Http\Middleware\MemoryPeakMiddleware;
use NewbridgeWeb\Http\Middleware\ModuleControlMiddleware;
use NewbridgeWeb\Http\Middleware\RoleMiddleware;
use NewbridgeWeb\Http\Middleware\SessionDataUpdate;
use NewbridgeWeb\Http\Middleware\StockRequisitionMiddleware;
use NewbridgeWeb\Http\Middleware\TrimStrings;
use NewbridgeWeb\Http\Middleware\TrustProxies;
use NewbridgeWeb\Http\Middleware\TwoFactorMiddleware;
use NewbridgeWeb\Http\Middleware\VerifyCsrfToken;

class Kernel extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array
     */
    protected $middleware = [
        HandleCors::class,
        MemoryPeakMiddleware::class
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array
     */
    protected $middlewareGroups = [

        'web' => [
            ValidatePostSize::class,
            TrimStrings::class,
            ConvertEmptyStringsToNull::class,
            TrustProxies::class,
            EncryptCookies::class,
            AddQueuedCookiesToResponse::class,
            StartSession::class,
            AuthenticateSession::class,
            ShareErrorsFromSession::class,
            SubstituteBindings::class,
            Maintenance::class,
            SessionDataUpdate::class,
            TrackRequest::class,
            HttpsProtocol::class,
            StockRequisitionMiddleware::class,
            ModuleControlMiddleware::class,
            VerifyCsrfToken::class,
            BeamerMiddleware::class,
            FavouriteReportsMiddleware::class
        ],

        'stateless' => [
            TrackRequest::class
        ],

        'api' => [
            CheckForMaintenanceMode::class,
            ValidatePostSize::class,
            TrimStrings::class,
            ConvertEmptyStringsToNull::class,
            TrackRequest::class,
            'maintenance' => Maintenance::class,
            ApiDeviceAuthorisation::class,
            'proxies' => TrustProxies::class,
            "throttle:999999,120",
            'bindings',
            'api-https',
        ]

    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array
     */
    protected $routeMiddleware = [
        'auth' => \Illuminate\Auth\Middleware\Authenticate::class,
        'auth.basic' => \Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'bindings' => \Illuminate\Routing\Middleware\SubstituteBindings::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \NewbridgeWeb\Http\Middleware\RedirectIfAuthenticated::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'csrf' => \NewbridgeWeb\Http\Middleware\VerifyCsrfToken::class,
        'api-device' => ApiDeviceAuthorisation::class,
        'api-https' => \NewbridgeWeb\Http\Middleware\ApiHttpsProtocol::class,
        'role' => RoleMiddleware::class,
        'permission' => \NewbridgeWeb\Http\Middleware\PermissionMiddleware::class,
        'ability' => AbilityMiddleware::class,
        'auth.accounts' => AccountsAuthMiddleware::class,
        '2fa' => TwoFactorMiddleware::class,
        'company.status' => CompanyStatusMiddleware::class
    ];
}
