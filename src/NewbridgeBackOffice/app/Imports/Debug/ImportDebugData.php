<?php

namespace NewbridgeWeb\Imports\Debug;

use Maatwebsite\Excel\Concerns\SkipsUnknownSheets;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use NewbridgeWeb\Imports\Debug\Sheets\DebugCompanyImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugCustomerGroupsImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugCustomersImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugCustomerTransactionsImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugDepartmentImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugPermissionsImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugPosSettingsImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugProductsImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugRecipeLinksImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugRecipesImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugRolePermissionsImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugRolesImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugSitesImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugSkusImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugStockSummaryImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugStockTransactionImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugSubDepartmentImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugTaxRatesImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugTerminalsImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugTransactionDetailImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugTransactionPaymentDetailImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugTransactionSummaryImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugUserImport;
use NewbridgeWeb\Imports\Debug\Sheets\DebugUserRolesImport;

class ImportDebugData implements WithMultipleSheets, WithHeadingRow, SkipsUnknownSheets
{
    public function sheets(): array
    {

        return [
            'Company' => new DebugCompanyImport(),
            'Sites' => new DebugSitesImport(),
            "Terminals" => new DebugTerminalsImport(),
            "Terminal Settings" => new DebugPosSettingsImport(),
            'Products' => new DebugProductsImport(),
            'Departments' => new DebugDepartmentImport(),
            'Sub Departments' => new DebugSubDepartmentImport(),
            'Payment Methods' => new DebugSubDepartmentImport(),
            'Tax Rates' => new DebugTaxRatesImport(),
            'SKUs' => new DebugSkusImport(),
            'Stock Summary' => new DebugStockSummaryImport(),
            'Stock Transactions' => new DebugStockTransactionImport(),
            'Users' => new DebugUserImport(),
            'User Roles' => new DebugUserRolesImport(),
            'Roles' => new DebugRolesImport(),
            'Recipes' => new DebugRecipesImport(),
            'Recipe Links' => new DebugRecipeLinksImport(),
            'Permissions' => new DebugPermissionsImport(),
            'Role Permissions' => new DebugRolePermissionsImport(),
            'Transaction Summary' => new DebugTransactionSummaryImport(),
            'Transaction Details' => new DebugTransactionDetailImport(),
            'Transaction Payments' => new DebugTransactionPaymentDetailImport(),
            'Customers' => new DebugCustomersImport(),
            'Customer Transactions' => new DebugCustomerTransactionsImport(),
            'Customer Groups' => new DebugCustomerGroupsImport()
        ];
    }

    public function onUnknownSheet($sheetName)
    {
        // E.g. you can log that a sheet was not found.
        info("Sheet {$sheetName} was skipped");
    }
}
