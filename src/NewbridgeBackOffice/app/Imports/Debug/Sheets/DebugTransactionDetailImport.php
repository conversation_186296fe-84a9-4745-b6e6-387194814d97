<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\PosTransactionDetail;

class DebugTransactionDetailImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row): void
    {
        $row = $row->toArray();
        PosTransactionDetail::unguard();
        PosTransactionDetail::where('id', $row['id'])->forceDelete();
        PosTransactionDetail::create($row);
        PosTransactionDetail::reguard();
    }
}
