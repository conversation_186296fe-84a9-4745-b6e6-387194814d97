<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\Tax\TaxRates;

class DebugTaxRatesImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row): void
    {
        $row = $row->toArray();
        TaxRates::unguard();
        TaxRates::where('id', $row['id'])->forceDelete();
        TaxRates::create($row);
        TaxRates::reguard();
    }
}
