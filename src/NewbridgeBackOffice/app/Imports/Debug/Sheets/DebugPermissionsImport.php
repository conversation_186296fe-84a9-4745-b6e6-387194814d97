<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use Spatie\Permission\Models\Permission;

class DebugPermissionsImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row): void
    {
        $row = $row->toArray();
        Permission::unguard();
        Permission::where('id', $row['id'])->forceDelete();
        Permission::insertOrIgnore($row);
        Permission::reguard();
    }
}
