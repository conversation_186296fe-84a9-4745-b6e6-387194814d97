<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\SKU;

class DebugSkusImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row)
    {
        $row = $row->toArray();
        SKU::unguard();
        SKU::where('id', $row['id'])->forceDelete();
        SKU::create($row);
        SKU::reguard();
    }
}
