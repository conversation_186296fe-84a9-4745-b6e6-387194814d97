<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\Pos;

class DebugTerminalsImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row)
    {
        $row = $row->toArray();
        $row['terminal_type'] = $row['terminal_type'] != null ? $row['terminal_type'] : 0;
        Pos::unguard();
        Pos::where('id', $row['id'])->forceDelete();
        Pos::create($row);
        Pos::reguard();
    }
}
