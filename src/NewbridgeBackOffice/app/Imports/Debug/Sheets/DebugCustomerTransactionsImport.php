<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\CustomerTransactions;

class DebugCustomerTransactionsImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row)
    {
        $row = $row->toArray();
        CustomerTransactions::unguard();
        CustomerTransactions::where('id', $row['id'])->forceDelete();
        CustomerTransactions::create($row);
        CustomerTransactions::reguard();
    }
}
