<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\SubDepartment;

class DebugSubDepartmentImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row)
    {
        $row = $row->toArray();
        SubDepartment::unguard();
        SubDepartment::where('id', $row['id'])->forceDelete();
        SubDepartment::create($row);
        SubDepartment::reguard();
    }
}
