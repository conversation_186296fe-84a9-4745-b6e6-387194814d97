<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\Payments;

class DebugPaymentMethodsImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row): void
    {
        $row = $row->toArray();
        Payments::unguard();
        Payments::where('id', $row['id'])->forceDelete();
        Payments::create($row);
        Payments::reguard();
    }
}
