<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\Repositories\StockSummary;

class DebugStockSummaryImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row): void
    {
        $row = $row->toArray();

        StockSummary::unguard();
        StockSummary::where('id', $row['id'])->forceDelete();
        StockSummary::create($row);
        StockSummary::reguard();
    }
}
