<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use NewbridgeWeb\User;

class DebugUserImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row): void
    {
        $row = $row->toArray();
        User::where('id', $row['id'])->delete();
        $user = User::create($row);
        $newPassword = 'password';
        $user->password = bcrypt($newPassword);
        $user->save();
    }
}
