<?php

namespace NewbridgeWeb\Imports\Debug\Sheets;

use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Row;
use Spatie\Permission\Models\Role;

class DebugRolesImport implements OnEachRow, WithHeadingRow
{
    public function onRow(Row $row): void
    {
        $row = $row->toArray();
        Role::unguard();
        Role::where('id', $row['id'])->forceDelete();
        Role::insertOrIgnore($row);
        Role::reguard();
    }
}
