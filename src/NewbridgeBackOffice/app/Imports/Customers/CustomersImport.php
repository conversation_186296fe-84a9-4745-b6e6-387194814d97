<?php

namespace NewbridgeWeb\Imports\Customers;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Customer;
use NewbridgeWeb\Repositories\CustomerGroup;
use NewbridgeWeb\Repositories\CustomerTypes;
use Ramsey\Uuid\Uuid;
use stdClass;

class CustomersImport implements ToCollection, withHeadingRow, SkipsEmptyRows
{
    private $company;
    public $data;
    private $guid;
    private $customers;
    private $errors;
    public const TAG = "customers-import-sheet";

    public function __construct(Company $company)
    {
        $this->company = $company;
        $this->data = new StdClass();
        $this->errors = [];
        $this->customers = [];
        $this->guid = Uuid::uuid4();
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        $errors = [];

        $customerGroups = CustomerGroup::where('company_id', $this->company->id)->keyBy('displayname')->get()->toArray();
        $customerTypes = CustomerTypes::where('company_id', $this->company->id)->keyBy('displayname')->get()->toArray();

        foreach ($collection as $k => $rec) {
            if (!isset($rec['first_name']) || $rec['first_name'] == '') {
                $errors[] = ['type' => 'required', 'section' => 'Customers', 'row' => $k + 2, 'col' => 'First Name', 'message' => 'First Name is a required field and is missing in row ' . $k];
            } elseif (!isset($rec['last_name']) || $rec['last_name'] == '') {
                $errors[] = ['type' => 'required', 'section' => 'Customers', 'row' => $k + 2, 'col' => 'Last Name', 'message' => 'Last Name is a required field and is missing in row ' . $k];
            } elseif (!isset($rec['email']) || $rec['email'] == '') {
                $errors[] = ['type' => 'required', 'section' => 'Customers', 'row' => $k + 2, 'col' => 'Email', 'message' => 'Email is a required field and is missing in row ' . $k];
            } elseif (!isset($rec['membership_no']) || $rec['membership_no'] == '') {
                $errors[] = ['type' => 'required', 'section' => 'Customers', 'row' => $k + 2, 'col' => 'Membership Number', 'message' => 'Membership Number is a required field and is missing in row ' . $k];
            } else {
                $customer = Customer::firstOrNew([
                    'first_name' => $rec['first_name'],
                    'last_name' => $rec['last_name'],
                    'membership_no' => $rec['membership_no'],
                    'company_id' => $this->company->id
                ]);
                $customer->company_id = $this->company->id;
                $customer->group_guid =
                $customer->received_at = '1970-01-01 00:00:00';
                $customer->received_from = 0;
                $customer->save();
            }
        }
        $this->setErrors($errors);

        return $this;
    }

    public function getRedisString()
    {
        return $this->company->id . "-" . self::TAG . "-" . $this->guid;
    }

    public function setErrors($errors)
    {
        Redis::set($this->getRedisString(), json_encode($errors), 'EX', 120);
    }

    public function getErrors()
    {
        return Redis::get($this->getRedisString()) ?
            json_decode(Redis::get($this->getRedisString())) :
            [] ;
    }

    public function deleteErrors()
    {
        Redis::del($this->getRedisString());
    }

    public function getRecipes()
    {
        return $this->recipes;
    }
}
