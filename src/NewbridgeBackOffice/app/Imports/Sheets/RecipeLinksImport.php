<?php

namespace NewbridgeWeb\Imports\Sheets;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\RecipeLinks;
use Ramsey\Uuid\Uuid;

class RecipeLinksImport implements ToCollection, withHeadingRow, SkipsEmptyRows
{
    private $company;
    public $data;
    private $guid;
    public const TAG = "recipe-links-import-sheet";

    public function __construct(Company $company, $data)
    {
        $this->company = $company;
        $this->data = $data;
        $this->guid = Uuid::uuid4();
    }

    /**
    * @param Collection $collection
     *
    */
    public function collection(Collection $collection)
    {
        $errors = [];

        foreach ($collection as $k => $lnk) {
            if (!isset($this->data->getRecipes()[trim(strtolower($lnk['recipe_id']))])) {
                $errors[] = ['type' => 'required', 'section' => 'Recipe Links', 'row' => $k + 2, 'col' => 'Recipe ID', 'message' => 'The recipe is missing from the data for the link on line ' . $k];
            }

            $product = Products::where('displayname', $lnk['product_id'])->where('company_id', $this->company->id)->first();

            if ($product === null) {
                $errors[] = ['type' => 'required', 'section' => 'Recipe Links', 'row' => $k + 2, 'col' => 'Product ID', 'message' => 'No product called '.$lnk['product_id'].' exists for the recipe link on line '. $k];
            } else {
                $recipeLink = RecipeLinks::firstOrNew(['recipe_guid' => $this->data->getRecipes()[trim(strtolower($lnk['recipe_id']))]->guid, 'product_guid' => $product->guid]);
                $recipeLink->quantity = $lnk['quantity'];
                $recipeLink->guid != null && $recipeLink->guid != '' ? $recipeLink->guid : Uuid::uuid4();
                $recipeLink->received_at = '1970-01-01 00:00:00';
                $recipeLink->received_from = 0;
                $recipeLink->save();
            }
        }

        $this->setErrors($errors); // errors make it this far

        return $this;
    }

    public function getRedisString()
    {
        return $this->company->id . "-" . self::TAG . "-" . $this->guid;
    }

    public function setErrors($errors)
    {
        Redis::set($this->getRedisString(), json_encode($errors), 'EX', 120);
    }

    public function getErrors()
    {
        return Redis::get($this->getRedisString()) ?
            json_decode(Redis::get($this->getRedisString())) :
            [] ;
    }

    public function deleteErrors()
    {
        Redis::del($this->getRedisString());
    }
}
