<?php

namespace NewbridgeWeb\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewUserRegistration extends Mailable
{
    use Queueable;
    use SerializesModels;
    public $data;

    /**
     * Create a new message instance.
     *
     * @return void
     * @param $data
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $data = $this->data;

        return $this->view('emails.template', compact('data'))->subject($this->data['subject']);
    }
}
