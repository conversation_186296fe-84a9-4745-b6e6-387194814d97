<?php

namespace NewbridgeWeb\Mail\BugReports;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class StatusUpdatedResponse extends Mailable
{
    use Queueable;
    use SerializesModels;
    public $data;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->data['subject'] = 'We have updated the issue you reported';
        $this->data['preHeaderText'] = 'Your issue report has been received.';
        $this->data['mainTitle'] = 'Issue Update';
        $this->data['textBlock1'] = 'Hello '.$this->data['user']->name.', <br /><br />
        This is an email response to inform you that the '.$this->data['issue']['type'].' you requested us to look into on 
        '.substr($this->data['issue']['date_reported'], 0, 10).' has had its status updated to '.$this->data['issue']['status'].'. ';
        $this->data['buttonUrl'] = '';
        $this->data['buttonText'] = "";
        $this->data['lowerBlocks'][] = [
            'background-color' => 'FFFFFF',
            'text-color' => '888888',
            'content' => 'Have a great day! <br /><br />',
            'additional-css' => ''
        ];
        $this->data['lowerBlocks'][] = [
            'background-color' => 'FFFFFF',
            'text-color' => '666666',
            'content' => 'If there is anything else we can do, please contact the team on <strong>02920 003273</strong> or <a href="mailto:<EMAIL>">email us</a> and we will get back to you as soon as we can.',
            'additional-css' => ''
        ];

        return $this->view('emails.template')->subject($this->data['subject']);
    }
}
