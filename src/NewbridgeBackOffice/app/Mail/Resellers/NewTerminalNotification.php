<?php

namespace NewbridgeWeb\Mail\Resellers;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewTerminalNotification extends Mailable
{
    use Queueable;
    use SerializesModels;
    public $data;

    /**
     *      The recipient of this email is the reseller
     */

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $data = $this->data;

        return $this->view('emails.template', compact('data'))->subject($this->data['subject']);
    }
}
