<?php

namespace NewbridgeWeb\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ReportReady extends Mailable
{
    use Queueable;
    use SerializesModels;
    public $data;

    /**
     * ReportReady email constructor.
     * @param $data
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->attachData($this->data['file'], $this->data['report_name'] . '.' . $this->data['ext'], [
            'mime' => $this->getMimeType($this->data['format']),
        ]);

        $data = $this->data;

        return $this->view('emails.template', compact('data'))->subject($this->data['subject']);
    }

    public function getMimeType(string $format)
    {
        /**
         *  This function uses $format not $ext
         */

        switch ($format) {
            case "csv":
                return "text/csv";
            case "xls":
                return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            default:
                return "application/pdf";
        }
    }
}
