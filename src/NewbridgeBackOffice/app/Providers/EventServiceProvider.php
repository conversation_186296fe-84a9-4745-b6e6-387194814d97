<?php

namespace NewbridgeWeb\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Laravel\Horizon\Events\MasterSupervisorOutOfMemory;
use Laravel\Horizon\Events\SupervisorOutOfMemory;
use NewbridgeWeb\Events\BackgroundImportEvent;
use NewbridgeWeb\Events\ButtonQueueEvent;
use NewbridgeWeb\Events\ProductUpdatedEvent;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Listeners\BackgroundImportEventListener;
use NewbridgeWeb\Listeners\ButtonQueueListener;
use NewbridgeWeb\Listeners\HorizonMemoryEventListener;
use NewbridgeWeb\Listeners\HorizonSupervisorMemoryEventListener;
use NewbridgeWeb\Listeners\ProductUpdatedEventListener;
use NewbridgeWeb\Listeners\UpdaterInsertCrudEventListener;
use NewbridgeWeb\Listeners\UpdaterUpdateCrudEventListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        'NewbridgeWeb\Events\TransactionDetailCreatedEvent' => [
            'NewbridgeWeb\Listeners\TransactionDetailCreatedEventListener',
        ],
        'NewbridgeWeb\Events\TransactionCreatedEvent' => [
            'NewbridgeWeb\Listeners\TransactionCreatedEventListener',
        ],
        'NewbridgeWeb\Events\ReSyncEvent' => [
            'NewbridgeWeb\Listeners\ReSyncListener',
        ],
        UpdaterInsertCrudEvent::class => [
            UpdaterInsertCrudEventListener::class
        ],
        UpdaterUpdateCrudEvent::class => [
            UpdaterUpdateCrudEventListener::class
        ],
        ProductUpdatedEvent::class => [
            ProductUpdatedEventListener::class
        ],
        ButtonQueueEvent::class => [
            ButtonQueueListener::class
        ],
       /* TerminalSettingUpdatedVersion::class => [
            TerminalSettingUpdatedVersionListener::class,
        ],*/
        BackgroundImportEvent::class => [
            BackgroundImportEventListener::class
        ],
        MasterSupervisorOutOfMemory::class => [
            HorizonMemoryEventListener::class
        ],
        SupervisorOutOfMemory::class => [
            HorizonSupervisorMemoryEventListener::class
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
