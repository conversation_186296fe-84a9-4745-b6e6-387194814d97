<?php

namespace NewbridgeWeb\Providers;

use Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use NewbridgeWeb\Observers\SitesObserver;
use NewbridgeWeb\Repositories\Sites;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(255);

        Blade::if('ability', function ($roles, $permissions = '') {

            $roles = explode(',', $roles);
            $permissions = explode(',', $permissions);
            $allowed = false;

            foreach ($roles as $role) {
                if (auth()->user()->hasRole($role)) {
                    $allowed = true;
                }
            }

            $userPerms = auth()->user()->getAllPermissions()->pluck('name')->toArray();

            foreach ($permissions as $permission) {

                foreach ($userPerms as $userPerm) {
                    if (stristr($permission, '*')) {
                        $permission = str_replace('*', '', $permission);

                        if (stristr($userPerm, $permission)) {
                            $allowed = true;
                        }
                    }
                    if ($userPerm == $permission) {
                        $allowed = true;
                    }
                }
            }

            return $allowed;
        });

        Blade::if('role', function ($roles) {
            $allowed = false;
            if (!is_array($roles)) {
                $roles = [$roles];
            }

            foreach ($roles as $role) {
                if (Auth::user()->hasRole($role)) {
                    $allowed = true;
                }
            }

            return $allowed;
        });

        \URL::forceRootUrl(\config('app.url'));
        \URL::forceScheme('https');

        // Register observers
        Sites::observe(SitesObserver::class);
    }

    public function register()
    {
    }
}
