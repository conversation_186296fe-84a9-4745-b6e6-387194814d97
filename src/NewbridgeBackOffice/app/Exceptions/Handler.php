<?php

namespace NewbridgeWeb\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Validation\ValidationException;
use Log;
use Sentry\Laravel\Integration;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        AuthenticationException::class,
        AuthorizationException::class,
    ];

    /**
     * Render an exception into an HTTP response.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     * @throws Throwable
     */
    public function render($request, Throwable $exception)
    {
        return parent::render($request, $exception);
    }

    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            Integration::captureUnhandledException($e);
        });
    }

    /**
     * Convert an authentication exception into an unauthenticated response.
     *
     */
    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->expectsJson()) {
            return response()->json(['status' => 'error', 'message' => 'Your session has expired, please log in again.'], 401);
        }

        return redirect()->guest('login');
    }

    protected function invalidJson($request, ValidationException $exception)
    {
        Log::error('Request data is invalid '.$request->url(), $exception->errors());

        return response()->json([
            'data' => [],
            'meta' => [
                'message' => 'The given data is invalid',
                'errors' => $exception->errors()
            ]
        ], $exception->status);
    }
}
