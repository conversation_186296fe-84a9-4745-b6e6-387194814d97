<?php

namespace NewbridgeWeb\DTO\Search\BuildersDTO;

use Illuminate\Contracts\Pagination\CursorPaginator as CursorPaginatorContract;
use Illuminate\Contracts\Pagination\Paginator as PaginatorContract;
use Illuminate\Pagination\AbstractCursorPaginator;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Enumerable;
use Illuminate\Support\LazyCollection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\CursorPaginatedDataCollection;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\PaginatedDataCollection;

class ProductsResultDTO extends Data
{
    public function __construct(
        public int     $id,
        public string  $guid,
        #[MapInputName('displayname')]
        public string  $displayName,
        public ?string  $barcode,
        public ?string  $ean13,
        #[MapInputName('department_displayname')]
        public ?string $departmentName,
        #[MapInputName('subdeparment_displayname')]
        public ?string $subDepartmentName,
    )
    {
    }

    public static function collect(mixed $items, ?string $into = null): array|DataCollection|PaginatedDataCollection|CursorPaginatedDataCollection|Enumerable|AbstractPaginator|PaginatorContract|AbstractCursorPaginator|CursorPaginatorContract|LazyCollection|Collection
    {
        $flattened = $items->map(function ($item) {
            $item['barcode'] = $item['barcode'] ?? 'None';
            $item['ean13'] = $item['ean13'] ?? 'None';

            $item['department_displayname'] = $item['department']['displayname'] ?? 'No department';
            unset($item['department']);

            $item['subdeparment_displayname'] = $item['subdepartment']['displayname'] ?? 'No subdepartment';
            unset($item['subdepartment']);

            return $item;
        });

        return static::factory()->collect($flattened, $into);
    }
}