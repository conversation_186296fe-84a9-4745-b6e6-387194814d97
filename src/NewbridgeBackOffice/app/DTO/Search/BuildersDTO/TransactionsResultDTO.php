<?php

namespace NewbridgeWeb\DTO\Search\BuildersDTO;

use Illuminate\Contracts\Pagination\CursorPaginator as CursorPaginatorContract;
use Illuminate\Contracts\Pagination\Paginator as PaginatorContract;
use Illuminate\Pagination\AbstractCursorPaginator;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Enumerable;
use Illuminate\Support\LazyCollection;
use Spatie\LaravelData\Attributes\MapInputName;
use Spatie\LaravelData\CursorPaginatedDataCollection;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\DataCollection;
use Spatie\LaravelData\PaginatedDataCollection;

class TransactionsResultDTO extends Data
{
    public function __construct(
        public int     $id,
        #[MapInputName('site_name')]
        public ?string $siteName,
        #[MapInputName('terminal_num')]
        public string  $terminalNum,
        #[MapInputName('clerk_full_name')]
        public ?string $clerkFullName,
        #[MapInputName('trans_datetime')]
        public string  $transDatetime,
        public float   $total
    )
    {
    }

    public static function collect(mixed $items, ?string $into = null): array|DataCollection|PaginatedDataCollection|CursorPaginatedDataCollection|Enumerable|AbstractPaginator|PaginatorContract|AbstractCursorPaginator|CursorPaginatorContract|LazyCollection|Collection
    {
        $flattened = $items->map(function ($item) {
            $item['site_name'] = $item['site']['site_name'] ?? 'No specific site';
            unset($item['site']);

            $item['clerk_full_name'] = $item['clerk']['full_name'] ?? 'No clerk';
            unset($item['clerk']);

            return $item;
        });

        return static::factory()->collect($flattened, $into);
    }
}