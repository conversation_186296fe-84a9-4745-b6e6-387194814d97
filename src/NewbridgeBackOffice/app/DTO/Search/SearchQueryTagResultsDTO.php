<?php

namespace NewbridgeWeb\DTO\Search;

use Illuminate\Support\Collection;
use NewbridgeWeb\DTO\Search\BuildersDTO\CustomersResultDTO;
use NewbridgeWeb\DTO\Search\BuildersDTO\ExtensionsResultDTO;
use NewbridgeWeb\DTO\Search\BuildersDTO\ProductsResultDTO;
use NewbridgeWeb\DTO\Search\BuildersDTO\TransactionsResultDTO;
use Spatie\LaravelData\Data;

class SearchQueryTagResultsDTO extends Data
{
    public function __construct(
        public int $tagId,
        public string $tagName,
        /** @var Collection<int, ExtensionsResultDTO|TransactionsResultDTO|CustomersResultDTO|ProductsResultDTO> */
        public ?Collection $results = null
    )
    {}
}