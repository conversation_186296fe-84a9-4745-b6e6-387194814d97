<?php

namespace NewbridgeWeb\DTO\Search;

use NewbridgeWeb\Enums\SearchTags;
use <PERSON><PERSON>\LaravelData\Attributes\Validation\ArrayType;
use Spatie\LaravelData\Attributes\Validation\Uuid;
use Spatie\LaravelData\Attributes\WithCast;
use Spatie\LaravelData\Casts\EnumCast;
use Spatie\LaravelData\Data;

class SearchQueryDTO extends Data
{
    public function __construct(
        public string $query,
        public string $url,
        public string $userAgent,
        #[Uuid()]
        public string $searchRequestUuid,
        /** @var SearchTags[] */
        public array $tags,
        public int $siteId,
        public int $userId,
    )
    {}
}