<?php

namespace NewbridgeWeb\Models;

use NewbridgeWeb\Repositories\NewbridgeRepository;

class NewbridgeModel extends NewbridgeRepository
{
    public function getTable()
    {
        return $this->table;
    }

    public function scopeExists($query)
    {
        return $query->where('deleted_at', '=', null);
    }

    public static function replicateWithRelationships(int $id)
    {
        if (!is_a(static::class, "NewbridgeWeb\Repositories\Abstracts\Replicable", true)) {
            throw new \Exception("This class does not implement the Replicable Interface");
        }

        $replicable = self::find($id);
        $replicate = $replicable->replicate();
        $replicate->save();

        foreach ($replicable->getReplicableRelationships() as $method => $values) {
            if (method_exists($replicable, $method)) {
                $replicate->$method()->sync($values);
            }
        }

        foreach ($replicable->getRequiredAttributeReplacements() as $key => $value) {
            if (isset($replicate->$key)) {
                $replicate->$key = $value;
            }
        }

        $replicate->save();

        return $replicate;
    }
}
