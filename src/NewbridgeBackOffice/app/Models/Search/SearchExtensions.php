<?php

namespace NewbridgeWeb\Models\Search;

use <PERSON><PERSON>\Scout\Searchable;
use Newbridge<PERSON>eb\Enums\SearchExtensionTypes;
use NewbridgeWeb\Models\NewbridgeModel;

class SearchExtensions extends NewbridgeModel
{
    use Searchable;

    protected $table = 'search_extensions';
    protected $casts = [
      'keywords' => 'array'
    ];

    protected $fillable = [
        'uuid',
        'title',
        'info',
        'type',
        'url',
        'keywords',
    ];

    public function searchableAs(): string
    {
        return 'search_extensions';
    }

    public function toSearchableArray(): array
    {
        return [
            'title' => $this->title,
            'info' => $this->info,
            'type' => strtolower(SearchExtensionTypes::from($this->type)?->name),
            'url' => $this->url,
            'keywords' => $this->keywords ?? [],
        ];
    }
}