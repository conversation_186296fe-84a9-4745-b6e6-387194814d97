<?php

namespace NewbridgeWeb\Models\Search;

use Illuminate\Database\Eloquent\Relations\HasOne;
use NewbridgeWeb\Models\NewbridgeModel;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\User;

class SearchAnalytics extends NewbridgeModel
{
    protected $table = 'search_analytics';
    protected $casts = [
      'conditions' => 'array',
      'results' => 'array',
      'clicked' => 'array'
    ];

    protected $fillable = [
        'uuid',
        'user_id',
        'site_id',
        'query',
        'conditions',
        'site_url',
        'device_agent',
        'results',
        'clicked',
        'is_admin'
    ];

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    public function site(): HasOne
    {
        return $this->hasOne(Sites::class, 'id', 'site_id');
    }

    public function scopeTopQueries($query, $limit = 10)
    {
        return $query->select('query')
            ->selectRaw('COUNT(*) as total')
            ->groupBy('query')
            ->orderByDesc('total')
            ->limit($limit);
    }

    public function scopeNoResultQueries($query, $limit = 10)
    {
        return $query->select('query')
            ->selectRaw('COUNT(*) as total')
            ->whereRaw('JSON_LENGTH(results) = 0')
            ->groupBy('query')
            ->orderByDesc('total')
            ->limit($limit);
    }

    public function scopeTopSiteUrls($query, $limit = 10)
    {
        return $query->select('site_url')
            ->selectRaw('COUNT(*) as total')
            ->groupBy('site_url')
            ->orderByDesc('total')
            ->limit($limit);
    }

}