<?php

namespace NewbridgeWeb\RealRepositories\MerlinAPI;

use Exception;
use Illuminate\Support\Facades\Http;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrations;

abstract class MerlinApiRepository
{
    public function connection(SiteIntegrations $integration, array $configuration): ?array
    {
        if ($configuration['site_id'] === null || $configuration['username'] === null || $configuration['password'] === null || $configuration['url'] === null) {
            return null;
        }

        $headers = [
            'Content-Type' => 'application/json',
            'Accepts' => 'application/json',
            'Authorization' => 'Basic ' . base64_encode($configuration['username'] . ':' . $configuration['password'] . ':' . $configuration['site_id'])
        ];

        try {
            ///webapi/api/token
            $response = Http::withHeaders($headers)->post($configuration['url'].'/webapi/api/token');

            if ($response->status() === 200) {
                $headers['Authorization'] = 'JWT ' . json_decode($response->body())->token;

                return $headers;
            }

            return null;
        } catch (Exception $e) {

            \Session::flash('message', ['status' => 'danger', 'message' => 'Integration failed to connect.'. $e->getMessage()]);

            return null;
        }
    }
}
