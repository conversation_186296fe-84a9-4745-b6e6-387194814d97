<?php

namespace NewbridgeWeb\RealRepositories\MerlinAPI;

use Exception;
use Illuminate\Support\Facades\Http;
use NewbridgeWeb\DTO\MappingsDTO;
use NewbridgeWeb\DTO\MappingsEntitieDTO;
use NewbridgeWeb\DTO\MappingsEntitiesGroupDTO;
use NewbridgeWeb\RealRepositories\Interfaces\MappingsEntitiesAPIInterface;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrations;

class MerlinEntitiesRepository extends MerlinApiRepository implements MappingsEntitiesAPIInterface
{
    public function getEntitiesFromAPI(SiteIntegrations $integration, array $configuration): ?MappingsDTO
    {
        $auth = $this->connection($integration, $configuration);

        if ($auth === null) {
            return null;
        }

        try {
            $response = Http::withHeaders($auth)->get($configuration['url'] . '/webapi/api/pos/GetPOSSetups');

            $result = json_decode($response->body(), true);

            $entityGroups = [];


            foreach ($result['POSItemRevenueGroups'] as $code) {
                if (!isset($codeLists['accountCodeList'][$code['GlCategory']])) {
                    $entityGroups[$code['GlCategory']] = [
                        "id" => null,
                        "name" => $code['GlCategory'],
                        "entities" => []
                    ];
                }
            }

            foreach ($entityGroups as $k => $group) {
                foreach ($result['POSItemRevenueGroups'] as $c => $code) {
                    if ($code['GlCategory'] == $group['name']) {
                        $entityGroups[$k]['entities'][$code['ItemCode']] = [
                            'id' => $code['ItemCode'],
                            'name' => $code['POSItemRevenueGroup']
                        ];
                    }
                }
            }

            $entities = collect((object) $entityGroups);

            $groupDTO = $entities->map(function ($group) {
                $entitiesDTO = MappingsEntitieDTO::collection(
                    collect($group['entities'])->map(function ($entity) {
                        return new MappingsEntitieDTO(
                            id: $entity['id'],
                            name: $entity['name'],
                        );
                    })
                );

                return new MappingsEntitiesGroupDTO(
                    id: null,
                    name: $group['name'],
                    entities: $entitiesDTO
                );
            });

            return MappingsDTO::from(["collection" => $groupDTO]);
        } catch (Exception $e) {

            \Session::flash(
                'message',
                ['status' => 'danger', 'message' => 'Integration failed to get mappings data.' . $e->getMessage()]
            );

            return null;
        }
    }
}
