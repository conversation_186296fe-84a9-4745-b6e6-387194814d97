<?php

namespace NewbridgeWeb\RealRepositories\Interfaces;

use NewbridgeWeb\DTO\Search\SearchQueryDTO;
use NewbridgeWeb\DTO\Search\SearchQueryTagResultsDTO;
use NewbridgeWeb\Enums\SearchTags;

interface SearchQueriesInterface
{
    public function __construct(SearchTags $tag, SearchQueryDTO $searchQueryDTO);

    public function __invoke(): SearchQueryTagResultsDTO;

    public function query(): SearchQueryTagResultsDTO;

    public function beforeQuery(): bool;

    public function afterQuery(SearchQueryTagResultsDTO $dto): SearchQueryTagResultsDTO;
}
