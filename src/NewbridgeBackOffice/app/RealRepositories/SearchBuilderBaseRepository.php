<?php

namespace NewbridgeWeb\RealRepositories;

use Auth;
use Newbridge<PERSON>eb\DTO\Search\SearchClickedDTO;
use Newbridge<PERSON>eb\DTO\Search\SearchQueryDTO;
use NewbridgeWeb\DTO\Search\SearchQueryResponseDTO;
use NewbridgeWeb\Enums\SearchTags;
use NewbridgeWeb\Models\Search\SearchAnalytics;
use NewbridgeWeb\RealRepositories\Interfaces\SearchBuilderBaseInterface;

class SearchBuilderBaseRepository implements SearchBuilderBaseInterface
{
    public function search(SearchQueryDTO $searchQueryDTO): SearchQueryResponseDTO
    {
        $result = new SearchQueryResponseDTO($searchQueryDTO->searchRequestUuid);
        $analyticResult = [];

        $tags = empty($searchQueryDTO->tags) ? SearchTags::cases() : $searchQueryDTO->tags;

        foreach ($tags as $tag) {
            if (class_exists($tag->getQueryClass()) === false) {
                continue;
            }

            $class = $tag->getQueryClass();
            $repository = (new $class($tag, $searchQueryDTO))->__invoke();

            if ($repository->results?->isNotEmpty()) {
                $result->results[] = $repository;
                $analyticResult[$tag->value] = $repository->results?->pluck('id');
            }
        }

        $this->logAnalytics($searchQueryDTO, $analyticResult);

        return $result;
    }

    public function clicked(SearchClickedDTO $searchClickedDTO): bool
    {
        $searchAnalytics = SearchAnalytics::where('uuid', $searchClickedDTO->searchRequestUUID)
            ->first();

        if (!$searchAnalytics) {
            return false;
        }

        $clickedData = $searchAnalytics->clicked ?? [];
        $clickedTag = $searchClickedDTO->tag->value;
        $clickedId = $searchClickedDTO->resourceId;

        if (!isset($clickedData[$clickedTag])) {
            $clickedData[$clickedTag] = [];
        }

        if (!in_array($clickedId, $clickedData[$clickedTag])) {
            $clickedData[$clickedTag][] = $clickedId;
        }

        $searchAnalytics->clicked = $clickedData;

        return $searchAnalytics->save();
    }

    public function logAnalytics(SearchQueryDTO $searchQueryDTO, $analyticResult): bool
    {
        $model = SearchAnalytics::create([
            'query' => $searchQueryDTO->query,
            'uuid' => $searchQueryDTO->searchRequestUuid,
            'site_id' => $searchQueryDTO->siteId,
            'user_id' => $searchQueryDTO->userId,
            'conditions' => ['tags' => $searchQueryDTO->tags ?? []],
            'is_admin' => Auth::user()->hasRole('newbridge'),
            'site_url' => $searchQueryDTO->url ?? null,
            'device_agent' => $searchQueryDTO->userAgent,
            'results' => $analyticResult
        ]);

        return $model->exists;
    }
}