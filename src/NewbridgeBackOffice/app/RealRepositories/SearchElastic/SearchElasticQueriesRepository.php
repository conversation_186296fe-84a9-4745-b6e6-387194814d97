<?php

namespace NewbridgeWeb\RealRepositories\SearchElastic;

use Newbridge<PERSON>eb\DTO\Search\SearchQueryDTO;
use NewbridgeWeb\DTO\Search\SearchQueryTagResultsDTO;
use NewbridgeWeb\Enums\SearchTags;
use NewbridgeWeb\RealRepositories\Interfaces;

abstract class SearchElasticQueriesRepository implements Interfaces\SearchQueriesInterface
{
    public SearchQueryTagResultsDTO $dto;

    public function __construct(public SearchTags $tag, public SearchQueryDTO $searchQueryDTO)
    {
        $this->dto = new SearchQueryTagResultsDTO($tag->value, $tag->name);
    }

    public function __invoke(): SearchQueryTagResultsDTO
    {
        if($this->beforeQuery()) {
            return $this->dto;
        }

        $query = $this->query();

        return $this->afterQuery($query);
    }

    abstract public function query(): SearchQueryTagResultsDTO;

    public function beforeQuery(): bool
    {
        if ($this->tag !== SearchTags::LINK && !\Auth::user()->hasRole('super-admin')) {
            return true;
        }

        return false;
    }

    public function afterQuery(SearchQueryTagResultsDTO $dto): SearchQueryTagResultsDTO
    {
        // Future needed logic to not duplicate code
        return $dto;
    }
}
