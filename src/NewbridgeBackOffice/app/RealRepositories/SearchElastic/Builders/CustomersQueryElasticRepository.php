<?php

namespace NewbridgeWeb\RealRepositories\SearchElastic\Builders;

use Auth;
use Illuminate\Database\Eloquent\Builder;
use NewbridgeWeb\DTO\Search\BuildersDTO\CustomersResultDTO;
use NewbridgeWeb\DTO\Search\SearchQueryTagResultsDTO;
use NewbridgeWeb\RealRepositories\SearchElastic\SearchElasticQueriesRepository;
use NewbridgeWeb\Repositories\Customer;

class CustomersQueryElasticRepository extends SearchElasticQueriesRepository
{
    public function query(): SearchQueryTagResultsDTO
    {
        $this->dto->results = CustomersResultDTO::collect(Customer::search($this->searchQueryDTO->query)
                ->where('company_id', Auth::user()->company_id)
                ->query(fn(Builder $query) => $query->with(['group' => function ($query) {
                    $query->select(['id', 'guid', 'displayname']);
                },
                    'type' => function ($query) {
                        $query->select(['id', 'guid', 'displayname']);
                    }]))
                ->get());

        return $this->dto;
    }
}