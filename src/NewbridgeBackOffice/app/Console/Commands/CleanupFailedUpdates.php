<?php

namespace NewbridgeWeb\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Repositories\RedisRepositoryLists;

class CleanupFailedUpdates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'updates:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add failed updates back to the queue';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $failedUpdates = Redis::connection('cache')->lrange('updatelog', 0, 100);

        foreach ($failedUpdates as $update) {
            $update = json_decode($update, true);

            if ($update != null) {
                $time = Carbon::parse($update['time']);

                if (Carbon::now()->diffInMinutes($time) > 3) {
                    RedisRepositoryLists::reQueue($update);

                    $this->info('Re-Queued Update ' . $update['update_id']);
                }
            }
        }

        $this->line('Done!');
    }
}
