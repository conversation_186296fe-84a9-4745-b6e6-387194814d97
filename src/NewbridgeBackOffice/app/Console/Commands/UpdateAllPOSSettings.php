<?php

namespace NewbridgeWeb\Console\Commands;

use Illuminate\Console\Command;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Repositories\PosSettings;

class UpdateAllPOSSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:possettings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        //           $settings = PosSettings::where('setting_id', 'Idle_Page')->get();
        //
        //           foreach($settings as $setting){
        //               $this->info('Idle Screen - '.$setting->id);
        //
        //               $setting->value1 = 'True';
        //               $setting->received_from = 0;
        //               $setting->save();
        //
        //               \Event::dispatch(new UpdaterUpdateCrudEvent($setting));
        //           }

        $settings2 = PosSettings::where('setting_id', 'TerminalDisplayName')->whereIn('company_id', [316, 312])
            ->get();

        foreach ($settings2 as $setting2) {
            $this->info('Terminal Name');
            $setting2->received_from = 0;
            $setting2->save();
            \Event::dispatch(new UpdaterUpdateCrudEvent($setting2));
        }
    }
}
