<?php

namespace NewbridgeWeb\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use NewbridgeWeb\Repositories\ButtonLinks;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PosButtonStyle;
use NewbridgeWeb\Repositories\StockSummary;
use NewbridgeWeb\Repositories\StockTransactions;

// Replace YourModel with the appropriate model

class CleanupDeletedRecords extends Command
{
    protected $signature = 'cleanup:deleted-records';
    protected $description = 'Clean up deleted records from the table';
    private int $limit = 50000;

    public function handle()
    {
        // Specify the threshold date for deletion (30 days ago)
        $thresholdDate = now()->subDays(30);

        $this->cleanupStockTransactions($thresholdDate);
        $this->cleanupStockSummary($thresholdDate);
        $this->cleanupButtonLinks($thresholdDate);
        $this->cleanupButtons($thresholdDate);
        $this->cleanupButtonStyles($thresholdDate);
        $this->cleanupPages($thresholdDate);

    }

    public function cleanupStockTransactions(Carbon $thresholdDate): void
    {
        StockTransactions::whereNotNull('deleted_at')
            ->where('deleted_at', '<=', $thresholdDate)
            ->limit($this->limit)
            ->forceDelete();
    }

    public function cleanupStockSummary(Carbon $thresholdDate): void
    {
        StockSummary::whereNotNull('deleted_at')
            ->where('deleted_at', '<=', $thresholdDate)
            ->limit($this->limit)
            ->forceDelete();
    }

    public function cleanupButtonLinks(Carbon $thresholdDate): void
    {
        ButtonLinks::whereNotNull('deleted_at')
            ->where('deleted_at', '<=', $thresholdDate)
            ->limit($this->limit)
            ->forceDelete();
    }

    public function cleanupButtons(Carbon $thresholdDate): void
    {
        PosButtons::whereNotNull('deleted_at')
            ->where('deleted_at', '<=', $thresholdDate)
            ->limit($this->limit)
            ->forceDelete();
    }

    public function cleanupButtonStyles(Carbon $thresholdDate): void
    {
        PosButtonStyle::whereNotNull('deleted_at')
            ->where('deleted_at', '<=', $thresholdDate)
            ->limit($this->limit)
            ->forceDelete();
    }

    public function cleanupPages(Carbon $thresholdDate): void
    {
        Commands::whereNotNull('deleted_at')
            ->where('deleted_at', '<=', $thresholdDate)
            ->limit($this->limit)
            ->forceDelete();
    }
}