<?php

namespace NewbridgeWeb\Console\Commands;

use DB;
use Illuminate\Console\Command;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;

class Lochinver extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lochinver';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get consumption data and upload to idraught ftp server';
    public int $chunk = 0;

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $result = DB::select(DB::raw('select summary_id, DATE(date) as date from lochinver group by summary_id, DATE(date)'));


        foreach ($result as $res) {
            $this->makeTransactions($res);
        }

        $this->chunk++;

        $this->info('chunk: '.$this->chunk);
    }

    public function makeTransactions($trans)
    {
        $details = DB::table('lochinver')->where('summary_id', $trans->summary_id)->where('date', 'LIKE', '%'.$trans->date.'%')->get();

        $transaction = new PosTransaction();

        // company data
        $transaction->company_id = 236;
        $transaction->site_num = 1;
        $transaction->terminal_num = 1;

        // unique transaction identifiers
        $transaction->reference = \Ramsey\Uuid\Uuid::uuid4();
        $transaction->historic_reference = null;

        $transaction->finalised_date = \Carbon\Carbon::parse($trans->date);
        $transaction->trans_datetime = \Carbon\Carbon::parse($trans->date);


        // clerk data
        // TODO:: get this from table
        $transaction->employee_no = '123';
        $transaction->employee_guid = '436ae506-42d2-4cbe-907a-a2a398d3e2d3';

        // do covers
        $transaction->covers = 0;

        // membership data
        $transaction->membership_no = null;
        //        $transaction->customer_type = null;

        // Total amounts
        $transaction->total = 0; // calc below
        $transaction->subtotal = 0; // calc below
        $transaction->cashback = 0;
        $transaction->gratuity = 0;

        // cash and change in here
        $transaction->cash = 0;
        $transaction->change = 0;

        // discounts and methods // TODO:: need to tidy this up so that only one field is used.
        $transaction->is_discounted = 0;
        $transaction->discount_amount = 0;
        $transaction->total_discount_value = 0;

        // table and order data
        $transaction->table_location = null;
        $transaction->location_name = null;
        $transaction->table_number = null;
        $transaction->order_number = 'HISTORY:' . $trans->summary_id . ':' . \Carbon\Carbon::parse($trans->date)->toDateString();

        $transaction->room_number = null;

        $transaction->save();

        $productTotal = 0; // total calc

        foreach ($details as $sale) {
            $clerk = Clerks::where('full_name', 'Clerk ' . $sale->employee_id)->first();
            // validation of totals

            $productTotal += (float) $sale->price;
            //does the product exist?
            $exists = Products::where('displayname', $sale->product)->withTrashed()->first();
            if ($exists) {
                $detail = new PosTransactionDetail();

                // company/terminal
                $detail->company_id = 236;
                $detail->site_num = 1;
                $detail->terminal_num = 1;
                $detail->datetime = $sale->date;
                $detail->finalised_date = \Carbon\Carbon::parse($sale->date);

                // employee
                $detail->employee_no = $clerk->employee_no;
                $detail->employee_guid = $clerk->guid;

                // finance
                $detail->gross_value = $sale->price;
                $detail->tax_value = $sale->tax;
                $detail->net_value = (float) $sale->price - (float) $sale->tax;
                $detail->tax_rate = $exists->vat_rate;

                $detail->command_type = 0;
                $detail->command = 3;
                $detail->cost_price = $exists->costprice;
                $detail->qty = 1;

                $detail->discount_guid = null;
                $detail->promotion_guid = null;
                $detail->discount_value = 0;

                // Department / Sub Department / Product Link
                $detail->product_guid = $exists->guid;
                $detail->sub_department_guid = $exists->sub_department_guid;
                $detail->department_guid = $exists->department_guid;
                $detail->displayname = $exists->displayname;

                $detail->void_reason = null;
                $detail->void_reason_guid = null;
                $detail->void_amount = null;

                $detail->trans_id = $transaction->id;
                $transaction->total = $productTotal;
                $transaction->subtotal = $productTotal;
                $transaction->save();
                $detail->save();
            } else {
                DB::rollback();

                return response()->json(['error' => 'The product ' . $sale->product . ' could not be found.'], 400);
            }
        }

        $this->info('Completed: ' . $transaction->order_number);
    }
}
