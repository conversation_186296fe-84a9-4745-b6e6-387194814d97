<?php

namespace NewbridgeWeb\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Orders\Orders;
use NewbridgeWeb\Repositories\PosButtonLink;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PosReceiptLayout;
use NewbridgeWeb\Repositories\SiteOpening;
use NewbridgeWeb\Repositories\SiteOpeningTimes;
use NewbridgeWeb\Repositories\StockSummary;
use NewbridgeWeb\Repositories\StockTransactions;

class DatabaseCleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:dbtables';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cleans up old database tables';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // clean up non reporting tables after X months.

        PosButtons::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Pos Buttons > 6 Months Soft Deleted');
        PosButtonLink::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Pos Button Links > 6 Months Soft Deleted');
        Orders::where('created_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Web/App Orders > 6 Months Old');
        SiteOpening::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        SiteOpeningTimes::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Opening Times > 6 Months Soft Deleted');
        StockTransactions::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Stock Transactions > 6 Months Soft Deleted');
        StockSummary::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Stock Summaries > 6 Months Soft Deleted');
        PosReceiptLayout::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Receipt Layouts  > 6 Months Soft Deleted');
        Commands::where('deleted_at', '!=', null)->where('deleted_at', '<', Carbon::now()->subMonths(6))->forceDelete();
        $this->info('Done: Deleted Pages and Commands > 6 Months Soft Deleted');
    }
}
