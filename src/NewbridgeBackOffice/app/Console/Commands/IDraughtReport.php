<?php

namespace NewbridgeWeb\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use NewbridgeWeb\Jobs\IDraughtJob;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Integrations\IntegrationSettings;

class IDraughtReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'idraught:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get consumption data and upload to idraught ftp server';

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $idraughtCompanies = Company::where('active_integrations', '&', 1)->get();

        foreach ($idraughtCompanies as $company) {

            $settings = IntegrationSettings::where('company_id', $company->id)
                ->where('integration_id', 1)
                ->get()
                ->keyBy('setting_id')
                ->toArray();

            $this->info('company_id: '.$company->id);

            if($this->notRunToday((isset($settings['last_run']) && $settings['last_run']['value']))) {
                $now = Carbon::now();
                $runTime = Carbon::parse($now->toDateString().' '.$settings['time']['value']);
                if($now > $runTime && config('app.env') === 'production') {
                    dispatch(new IDraughtJob($company));
                }


            }

        }

    }

    private function notRunToday(string $last_run): bool
    {
        $date = Carbon::parse($last_run);
        $start = Carbon::now()->startOfDay();
        $end = Carbon::now()->endOfDay();

        if(!$date->between($start, $end) && $date->lt($start)) {
            return true;
        }

        return false;
    }
}
