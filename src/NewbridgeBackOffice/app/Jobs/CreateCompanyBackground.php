<?php

namespace NewbridgeWeb\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Newbridge<PERSON>eb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\Company\GenerateDefaultPages;
use NewbridgeWeb\Repositories\Company;

class CreateCompanyBackground implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $company;
    public $errors = [];

    public function __construct(Company $company)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);

        $this->company = $company;
    }

    public function handle()
    {
        GenerateDefaultPages::copyDefaultSkus($this->company->id);
        GenerateDefaultPages::copyDefaultPaymentMethods($this->company->id, 1);
        $defaultPages = GenerateDefaultPages::copyDefaultPagesAndLinkCommands($this->company->id, 1);
        GenerateDefaultPages::copyDefaultClerks($this->company->id, $defaultPages[2]['CommandUID']);

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}
