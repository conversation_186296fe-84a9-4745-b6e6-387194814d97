<?php

namespace NewbridgeWeb\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\DataTransfer\Export\IDraught\IDraughtExport;
use NewbridgeWeb\Repositories\Integrations\IntegrationSettings;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\StockTransactions;

class IDraughtJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $company;
    public $integrationSettings;
    public $canRead;

    public function __construct($company)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);

        $this->company = $company;

        $this->integrationSettings = IntegrationSettings::where('company_id', $this->company['id'])
            ->where('integration_id', 1)
            ->get()->keyBy('setting_id')->toArray();
    }

    public function handle()
    {
        $this->runReportUpload($this->company);

        MemorySpikeCheckAction::execute(self::class, 512);
    }

    private function runReportUpload()
    {
        $data = [];

        $productList = new Products();
        $productList = $productList->select('id')->where('company_id', $this->company['id'])
            ->whereNotNull('third_party_reference')
            ->whereNotNull('ml_amount')
            ->pluck('id')->toArray();


        $consumptions = StockTransactions::selectRaw('plu_stock_transactions.company_id, plu_stock_transactions.created_at,  plu_stock_transactions.quantity , plu_stock_transactions.product_id, plu_products.displayname, plu_products.third_party_reference, sum(plu_products.ml_amount) as ml_amount_sum, plu_products.displayname')
            ->leftJoin('plu_products', 'plu_stock_transactions.product_id', 'plu_products.id')
            ->where('plu_stock_transactions.company_id', $this->company['id'])
            ->whereIn('plu_stock_transactions.summary_type', [1,5])
            ->where('plu_stock_transactions.status', 1)
            ->whereIn('plu_stock_transactions.product_id', $productList)
            ->where('plu_stock_transactions.created_at', '>', $this->integrationSettings['last_run']['value'])
            ->groupBy('plu_stock_transactions.created_at', 'plu_products.third_party_reference', 'plu_products.displayname')
            ->get()
            ->toArray();


        foreach ($consumptions as $k => $consumption) {
            $data[$k]['site_id'] = $this->integrationSettings['site_id']['value'];
            $data[$k]['date_time'] = Carbon::parse($consumption['created_at'])->format('d/m/Y H:i:s') ;
            $data[$k]['upc'] = $consumption['third_party_reference'];
            $data[$k]['pd'] = $consumption['displayname'];
            $data[$k]['vol'] = abs($consumption['quantity'] * $consumption['ml_amount_sum']);
        }

        if(!empty($data)) {
            $this->convertToCSV($data);

            $updateSettings = new IntegrationSettings();
            $updateSettings = $updateSettings->where('company_id', $this->company['id'])
                ->where('integration_id', 1)
                ->where('setting_id', 'last_run')
                ->first();

            $updateSettings->value = Carbon::now()->toDateTimeString();
            $updateSettings->save();
        }

        return 'done';

    }

    public function convertToCSV($data)
    {
        $path = '/idraught/';
        $filename = $this->company['company_name'].'.csv';

        Excel::store(new IDraughtExport($data), $path.$filename);
        $file = Storage::get($path.$filename);

        //Change this to use settings
        Config::set('filesystems.disks.idraught', [
            'driver' => 'ftp',

            'host' => $this->integrationSettings['ftp_host']['value'],

            'username' => $this->integrationSettings['ftp_user']['value'],

            'password' => $this->integrationSettings['ftp_password']['value']
        ]);

        Storage::disk('idraught')->put(''.$filename, $file);

        return 'done';
    }
}
