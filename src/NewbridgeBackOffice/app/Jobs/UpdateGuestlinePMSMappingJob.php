<?php

namespace NewbridgeWeb\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Newbridge<PERSON>eb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\PropertyManagement\GuestLineController;

class UpdateGuestlinePMSMappingJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(public int $company_id)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);
    }

    public function handle()
    {
        $glController = new GuestLineController();
        $glController->createOrUpdateOutletServiceMapping($this->company_id);

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}
