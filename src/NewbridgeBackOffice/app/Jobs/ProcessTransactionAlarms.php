<?php

namespace NewbridgeWeb\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Newbridge<PERSON>eb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\AlarmResults;
use NewbridgeWeb\Repositories\Alarms;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\RedisRepository;

class ProcessTransactionAlarms implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $transaction;

    public function __construct(PosTransaction $transaction)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);

        $this->transaction = $transaction->toArray();
    }

    public function handle()
    {
        /**
         * Which Transaction?
         */
        $transaction = PosTransaction::with('clerk')->where('id', $this->transaction['id'])
            ->first();


        if ($transaction != null) {
            /**
             * Get Company Alarms to Test Against
             */
            $alarms = new Alarms();
            $alarms = $alarms->with('type')
                ->where('company_id', $transaction['company_id'])
                ->get();

            if ($alarms !== null) {
                /**
                 * Check the alarms and ring the bell if necessary!
                 */
                foreach ($alarms as $alarm) {
                    $key = 'alarm_' . $alarm->id;

                    if (in_array($alarm->type->trigger_type, [0, 1])) {
                        /**
                         * Is the alarm a period or single transactions alarm?
                         * alarm period 1 = single trans
                         * alarm period > 1 - period alarm
                         */
                        if ($alarm->type->record_type == 1 && $alarm->alarm_period === 1) {
                            $this->singleTransactionAlarm($transaction, $alarm);
                        } elseif ((int) $alarm->type->record_type === 1 && (int) $alarm->alarm_period > 1) {
                            // get the transactions details for the transaction
                            $details = $this->getTransactionDetails($alarm, $transaction);

                            // Check Redis for an entry
                            $check = RedisRepository::checkExists($key);

                            if ($check === true) {
                                $alarm_details = RedisRepository::getByKey($key);

                                /**
                                 * If now is less than the end_date in the redis key
                                 * increment the required values and cehck if we are going to
                                 * trigger the alarm or reset it back to 0 and change the dates
                                 */
                                if (Carbon::now()->lt(Carbon::parse($alarm_details['end_date']))) {
                                    $alarm_details['current_value'] += (float) $details;
                                    $alarm_details['period_total'] += (float) $details;
                                    array_push($alarm_details['transactions'], $this->transaction['id']);

                                    RedisRepository::replace($key, $alarm_details);

                                    if ($alarm_details['current_value'] >= (float) $alarm->value) {
                                        $this->triggerAlarm($transaction, $alarm, abs($alarm_details['current_value']), $alarm_details['transactions']);
                                        $this->incrementTriggerCountRedis($alarm_details, $key);
                                    }
                                } elseif ((Carbon::now()->gt(Carbon::parse($alarm_details['end_date'])))) {
                                    $alarm_details = $this->getDateRanges($alarm->period, $alarm_details);

                                    $alarm_details['current_value'] = (float) $details;
                                    $alarm_details['period_total'] = 0;
                                    $alarm_details['trigger_count'] = 0;
                                    $alarm_details['transactions'] = [];

                                    if ($alarm_details['current_value'] >= (float) $alarm->value) {
                                        RedisRepository::replace($key, $alarm_details);
                                    }
                                }
                            } elseif ($check === null) {
                                $alarm_details = [
                                    'start_date' => null,
                                    'end_date' => null,
                                    'current_value' => 0,
                                    'period_total' => 0,
                                    'trigger_count' => 0,
                                    'transactions' => []
                                ];
                                $alarm_details = $this->getDateRanges($alarm->period, $alarm_details);

                                $alarm_details['current_value'] = (float) $details;
                                $alarm_details['period_total'] = (float) $details;
                                $alarm_details['transactions'][] = $this->transaction['id'];
                                RedisRepository::store($key, $alarm_details);

                                if ($alarm_details['current_value'] >= (int) $alarm->value) {
                                    $this->triggerAlarm($transaction, $alarm, abs($alarm_details['current_value']), $alarm_details['transactions']);
                                    $this->incrementTriggerCountRedis($alarm_details, $key);
                                }
                            }
                        }
                    }
                }
            }
        }

        MemorySpikeCheckAction::execute(self::class, 512);
    }

    /**
     * Update the redis entry
     *
     * @param $alarm_details
     * @param $key
     */
    private function incrementTriggerCountRedis($alarm_details, $key)
    {
        $alarm_details['trigger_count'] += 1;
        $alarm_details['current_value'] = 0;
        $alarm_details['transactions'] = [];
        RedisRepository::replace($key, $alarm_details);
    }

    /**
     * Get details from the pos_transaction_details
     * table and do count/sum
     *
     * @param $alarm
     * @param $transaction
     * @return mixed
     */
    private function getTransactionDetails($alarm, $transaction)
    {
        $commandOrType = $alarm->type->command == null ? 'command_type' : 'command';

        $details = new PosTransactionDetail();
        if ($alarm->value_type === 1) {
            $transaction_value = $details->where('trans_id', $transaction['id'])
                ->where($commandOrType, $alarm->type->$commandOrType)
                ->count();
        } elseif ($alarm->value_type === 0) {
            $transaction_value = $details->selectRaw('SUM(net_value) as value')->where('trans_id', $transaction['id'])
                ->where($commandOrType, $alarm->type->$commandOrType)
                ->groupBy('trans_id')
                ->value('value');
        }


        return $transaction_value;
    }

    /**
     * Set the date ranges that will be
     * stored in redis with the alarm details
     *
     * @param $period
     * @param $alarm_details
     * @return mixed
     */
    private function getDateRanges($period, $alarm_details)
    {
        switch ($period['id']) {
            case 2:
                $alarm_details['start_date'] = Carbon::now()->startOfDay()->toDateTimeString();
                $alarm_details['end_date'] = Carbon::now()->endOfDay()->toDateTimeString();

                break;
            case 3:
                $alarm_details['start_date'] = Carbon::now()->startOfWeek()->toDateTimeString();
                $alarm_details['end_date'] = Carbon::now()->endOfWeek()->toDateTimeString();

                break;
            case 4:
                $alarm_details['start_date'] = Carbon::now()->startOfMonth()->toDateTimeString();
                $alarm_details['end_date'] = Carbon::now()->endOfMonth()->toDateTimeString();

                break;
            case 5:
                $alarm_details['start_date'] = Carbon::now()->startOfYear()->toDateTimeString();
                $alarm_details['end_date'] = Carbon::now()->endOfYear()->toDateTimeString();

                break;
        }

        return $alarm_details;
    }

    /**
     * Check and fire a single transaction alarm
     *
     * @param $transaction
     * @param $alarm
     */
    private function singleTransactionAlarm($transaction, $alarm)
    {
        $commandOrType = $alarm->type->command == null ? 'command_type' : 'command';
        $trigger = $this->getTriggerType($alarm->type->trigger_type);
        $details = new PosTransactionDetail();
        $details = $details->selectRaw('SUM(net_value) as value')->where('trans_id', $transaction['id'])
            ->where($commandOrType, $alarm->type->$commandOrType)
            ->groupBy('trans_id')
            ->havingRaw('ABS(SUM(net_value)) ' . $trigger . ' ' . (float) $alarm->value)
            ->value('value');

        if ($details !== null) {
            $this->triggerAlarm($transaction, $alarm, abs($details));
        }
    }

    private function getTriggerType($type)
    {
        switch ($type) {
            case 0:
                return '>=';

                break;
            case 1:
                return '<=';

                break;
            default:
                return '>=';

                break;
        }
    }

    private function triggerAlarm($transaction, $alarm, $value, $transactions = null)
    {
        // Record the result
        $result = new AlarmResults();
        $result->company_id = $transaction['company_id'];
        $result->transaction_id = $transaction['id'];
        $result->alarm_id = $alarm->id;

        if ($transactions !== null) {
            $result->transactions = implode(",", $transactions);
        }


        // Build the alert
        $alert_message = $alarm->message_string;
        if ($transaction->clerk === null) {
            $alert_message = str_replace('#CLERK', 'UNKNOWN CLERK', $alert_message);
        } else {
            $alert_message = str_replace('#CLERK', $transaction->clerk->displayname, $alert_message);
        }
        $alert_message = str_replace('#VALUE', number_format($value, 2), $alert_message);

        $result->alarm_text = $alert_message;

        $result->save();

        return $result;
    }
}
