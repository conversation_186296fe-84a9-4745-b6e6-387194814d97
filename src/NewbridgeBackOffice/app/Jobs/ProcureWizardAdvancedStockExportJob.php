<?php

namespace NewbridgeWeb\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Exports\ProcureWizard\AdvancedStockCostSalesExport;
use NewbridgeWeb\Exports\ProcureWizard\BasicRetailSalesExport;
use NewbridgeWeb\Http\Helpers\IntegrationsLoggingHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationSites;
use NewbridgeWeb\Repositories\Sites;

class ProcureWizardAdvancedStockExportJob implements \Illuminate\Contracts\Queue\ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    private Carbon $start;
    private Carbon $end;
    private SiteIntegrationSites $integrationSettings;
    private string $path = 'ProcureWizard/AdvancedStockExport/';
    private string $filename;
    private IntegrationsLoggingHelper $logger;
    private array $config;
    private int $site_id;

    public function __construct(SiteIntegrationSites $site, IntegrationsLoggingHelper $logger, null|Carbon $startDate = null)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);

        $this->integrationSettings = $site;

        // TODO: set timezone based on company or site information
        $company = Company::find($this->integrationSettings->company_id);

        if($startDate == null) {
            $this->start = Carbon::yesterday()->startOfDay()->addHours($company->time_offset);
        } else {
            $this->start = $startDate->startOfDay()->addHours($company->time_offset);
        }
        $this->end = Carbon::parse($this->start)->addHours(24);
        $this->config = $this->integrationSettings->configuration;

        $site = Sites::whereCompanyId($this->integrationSettings->company_id)
            ->whereSiteNum($this->integrationSettings->site_num)
            ->first();

        $this->site_id = $site->id;

        $this->logger = $logger;
    }

    public function handle(): void
    {
        $this->logger->setDescription('ProcureWizardAdvancedStockExportJob picked up by queue');
        $this->logger->write();

        try {
            $this->runReport();
            $this->uploadReport();
        } catch (\Exception $e) {
            $this->logger->setException($e);
        } finally {
            $this->logger->setDescription('ProcureWizardAdvancedStockExportJob ran');
            $this->logger->write();
        }

        MemorySpikeCheckAction::execute(self::class, 512);
    }

    private function setFilename(): void
    {
        $this->filename = Carbon::parse($this->start)->toDateString()
            . '-' . $this->integrationSettings->company_id
            . '-' . $this->site_id . '.csv';
    }

    private function runReport(): void
    {
        $site = Sites::where('company_id', $this->integrationSettings->company_id)
            ->where('site_num', $this->integrationSettings->site_num)
            ->first();

        $this->site_id = $site->id;

        $this->setFilename();

        if($this->config['export_format'] == '2') {
            Excel::store(new AdvancedStockCostSalesExport([
                'company_id' => $this->integrationSettings->company_id,
                'site_num' => $this->integrationSettings->site_num,
                'start' => $this->start,
                'end' => $this->end
            ], $this->logger, $this->site_id), $this->path . $this->filename);
        }
        if($this->config['export_format'] == '4') {
            Excel::store(new BasicRetailSalesExport([
                'company_id' => $this->integrationSettings->company_id,
                'site_num' => $this->integrationSettings->site_num,
                'start' => $this->start,
                'end' => $this->end
            ], $this->logger, $this->site_id), $this->path . $this->filename);
        }

        $this->logger->setResource(Storage::url($this->path . $this->filename));

        if(Storage::exists($this->path . $this->filename)) {
            $this->logger->setDescription('ProcureWizardAdvancedStockExportJob wrote file to disk');
            $this->logger->write();
        }
    }

    private function uploadReport(): void
    {
        $file = Storage::get($this->path . $this->filename);

        Config::set('filesystems.disks.procurewizard', [
            'driver' => 'sftp',
            'host' => 'ftp.procurewizard.com',
            'username' => $this->config['username'],
            'password' => $this->config['password'],
            'port' => 22,
            'timeout' => 30,
            'throw' => true
        ]);

        $result = false;

        try {
            $result = Storage::disk('procurewizard')->put('' . $this->filename, $file);
        } catch (\Exception $e) {
            $this->logger->setException($e);
        } finally {
            if ($result) {
                $this->logger->setDescription('ProcureWizardAdvancedStockExportJob file uploaded successfully');
            } else {
                $this->logger->setDescription('ProcureWizardAdvancedStockExportJob file upload unsuccessful');
            }
            $this->logger->write();
        }

    }
}
