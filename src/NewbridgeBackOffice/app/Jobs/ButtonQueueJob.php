<?php

namespace NewbridgeWeb\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Newbridge<PERSON>eb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Events\UpdaterInsertCrudEvent;
use NewbridgeWeb\Repositories\PosButtons;

class ButtonQueueJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $page;
    public $terminal;

    public function __construct($data)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);

        $this->page = json_decode(json_encode($data), true)['data'];
    }

    public function handle()
    {
        $this->queuePageButtons();

        MemorySpikeCheckAction::execute(self::class, 600);
    }

    public function queuePageButtons()
    {
        PosButtons::where('plu_product_page_guid', $this->page['CommandUID'])
            ->get()
            ->each(function($model){
                $model->update(['received_from' => 0]);
            });

        $buttons = PosButtons::with(['pos_button_style', 'lnk_buttonlinks', 'plu_product_page'])
            ->where('plu_product_page_guid', $this->page['CommandUID'])
            ->get();

        $terminal_id = $this->page['terminal_id'] ?? null;

        $this->putInQueue($buttons, $terminal_id);
    }

    private function putInQueue($buttons, $terminal_id)
    {
        foreach ($buttons as $button) {
            if ($terminal_id != null) {
                $button->terminal_id = $terminal_id;
            }

            \Event::dispatch(new UpdaterInsertCrudEvent($button));
        }

        return true;
    }
}
