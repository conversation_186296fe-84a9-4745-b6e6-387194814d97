<?php

namespace NewbridgeWeb\Jobs;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Exports\Debug\DebugExport;
use NewbridgeWeb\Http\Controllers\Newbridge\Debug\ImportDebug;
use NewbridgeWeb\Repositories\JobStatus\JobStatus;

class DebugExportJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    protected int $company_id;
    protected int $site_num;
    protected string $uuid;
    protected string $dateTimeString;

    public function __construct(string $uuid, int $company_id, int $site_num, string $dateTimeString)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);

        $this->company_id = $company_id;
        $this->site_num = $site_num;
        $this->uuid = $uuid;
        $this->dateTimeString = $dateTimeString;
    }

    public function handle(): string
    {
        $export = new DebugExport($this->uuid, $this->company_id, $this->site_num);
        $dateTime = Carbon::now()->toDateTimeString();

        $debug = new ImportDebug();
        $debug->updatejobStatus(['uuid' => $this->uuid, 'status' => JobStatus::STATUS_PROCESSING]);

        $fileStore = $export->store('/debug-export-files/'.$this->uuid.'.xlsx');

        $debug = new ImportDebug();
        $debug->updatejobStatus(['uuid' => $this->uuid, 'end' => Carbon::now()->toDateTimeString(), 'status' => JobStatus::STATUS_COMPLETE]);

        MemorySpikeCheckAction::execute(self::class, 400);

        return $fileStore;
    }

    public function failed($exception)
    {
        ImportDebug::updatejobStatus([
            'uuid' => $this->uuid,
            'status' => JobStatus::STATUS_FAILED,
            'exception' => $exception->getMessage()
        ]);
    }
}
