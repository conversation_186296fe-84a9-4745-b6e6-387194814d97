<?php

namespace NewbridgeWeb\Jobs\Company\Site;

use Carbon\Carbon;
use DB;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NewbridgeWeb\Http\Controllers\Company\AvonDataController;
use NewbridgeWeb\Http\Controllers\Company\GenerateDefaultPages;
use NewbridgeWeb\Http\Controllers\Company\SitesController;
use NewbridgeWeb\Http\Controllers\Company\TerminalController;
use NewbridgeWeb\Http\Helpers\EventGridHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\User;

class CreateTerminalJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public int $tries = 1;

    /**
     * Create a new job instance
     *
     * @return void
     */
    public function __construct(public Company $company, public User $creatingUser, public int $siteNum, public int $terminalAmount)
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $curnum = Pos::where('company_id', $this->company->id)->orderBy('terminal_num', 'DESC')->first();

        if ($curnum != null) {
            $num = $curnum->terminal_num + 1;
        } else {
            $num = 1;
        }

        for ($i = 0; $i < $this->terminalAmount; $i++) {
            $terminal = [
                'terminal_num' => $num + $i,
                'company_id' => $this->company->id,
                'site_num' => $this->siteNum,
                'name' => 'Terminal ' . ($num + $i),
                'last_ping' => Carbon::now(),
                'ip_address' => '',
                'terminal_type' => ''
            ];

            $pos = new Pos();
            $pos->fill($terminal);
            $pos->save();

            TerminalController::createDefaultSettings($terminal, $this->creatingUser);
        }
    }
}