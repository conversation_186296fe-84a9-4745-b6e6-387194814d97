<?php

namespace NewbridgeWeb\Jobs\Company;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Log;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Http\Controllers\Company\CompanyController;
use NewbridgeWeb\Mail\Exports\ExportFailure;
use NewbridgeWeb\Mail\Exports\ExportFailureAdminNotification;
use NewbridgeWeb\Mail\Exports\ExportSuccess;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Export;
use NewbridgeWeb\User;

class CreateUserJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public int $tries = 1;

    public function __construct(public array $newUserData, public Company $company, public User $creatingUser, public int $terminalAmount)
    {

    }

    public function handle()
    {
        $reseller = Company::find($this->creatingUser->real_company_id);

        // generate a random 8 character password
        $plain_password = Str::random(8);

        // correct the user data and encrypt the random password
        $newUserDetails = [
            'name' => $this->newUserData['contact_name'],
            'email' => $this->newUserData['email'],
            'password' => bcrypt($plain_password),
            'company_id' => $this->company->id,
            'username' => $this->newUserData['username'],
            'real_company_id' => $this->company->id
        ];

        // save the user to the database
        $newUser = new User($newUserDetails);
        $newUser->save();

        $newUser->syncRoles('owner');

        $emailData = [
            'email' => $newUser->email,
            'name' => $newUser->name,
            'username' => $newUser->username,
            'password' => $plain_password,
            'terminal_qty' => $this->terminalAmount,
            'terminal_access_key' => $this->company->terminal_access_key,
            'company' => $this->company->company_name,
            'reseller' => $reseller
        ];

        if ($this->creatingUser->hasRole('reseller')) {
            CompanyController::buildResellerNewCustomerWelcomeEmail($emailData);
            CompanyController::buildResellerNewCustomerNotificationEmail($emailData);
            CompanyController::buildResellerNewCustomerNewbridgeNotificationEmail($emailData);
        } else {
            CompanyController::CompanyWelcomeEmail($emailData);
        }

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}