<?php

namespace NewbridgeWeb\Jobs\Company;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\Company\CompanyController;
use NewbridgeWeb\Http\Controllers\Company\GenerateDefaultPages;
use NewbridgeWeb\Http\Controllers\Company\SitesController;
use NewbridgeWeb\Http\Controllers\ReceiptController;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\RedisRepositoryLists;
use NewbridgeWeb\User;

class GenerateDefaultDataForSiteJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public int $tries = 2;

    public function __construct(public User $user, public int $company_id, public int $site_num)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);
    }

    public function handle(): void
    {
        DB::transaction(function() {
            GenerateDefaultPages::copyDefaultPagesAndLinkCommands($this->company_id, $this->site_num, [], $this->user);

            $siteController = new SitesController();
            $siteController->createColoursIfNotExists($this->company_id, $this->site_num);

            $data = [
                'reseller' => Company::find($this->user->real_company_id),
                'company' => Company::find($this->company_id)
            ];
            ReceiptController::generateDefaultReceipts($this->company_id, $this->site_num);
            Log::info('Added default receipt layout to Company '.$this->company_id.' Site '.$this->site_num);
            CompanyController::buildNewbridgeNewSiteNotificationEmail($data);
        });

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}