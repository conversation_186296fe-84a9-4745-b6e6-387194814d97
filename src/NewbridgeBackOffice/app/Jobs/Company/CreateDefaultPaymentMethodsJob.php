<?php

namespace NewbridgeWeb\Jobs\Company;

use DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\User;
use Ramsey\Uuid\Uuid;

class CreateDefaultPaymentMethodsJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public int $tries = 1;

    public function __construct(public Company $company, public User $creatingUser)
    {

    }

    public function handle()
    {
        $reseller = $this->creatingUser->hasRole('reseller') ? Company::find($this->creatingUser->real_company_id) : null;
        $default_company_id = $this->creatingUser->hasRole('reseller') && $reseller->template_company_id != 0 ? $reseller->template_company_id : config('newbridge.default_company_id');

        if (empty($paymentMethods)) {
            $paymentMethods = Payments::where('company_id', $default_company_id)->get();
        } else {
            $paymentMethods = Payments::where('company_id', $default_company_id)->whereIn('DisplayName', $paymentMethods)->get();
        }

        DB::beginTransaction();

        try {
            foreach ($paymentMethods as $paymentMethod) {
                $data = [
                    'company_id' => $this->company->id,
                    'Descriptor' => $paymentMethod->Descriptor != null ? $paymentMethod->Descriptor : null,
                    'DisplayName' => $paymentMethod->DisplayName,
                    'prefix_amount' => $paymentMethod->prefix_amount,
                    'EFTConnection'  => $paymentMethod->EFTConnection,
                    'InDrawerValue' => $paymentMethod->InDrawerValue,
                    'CommandUID' => Uuid::uuid4(),
                    'Command' => $paymentMethod->Command,
                    'OpperationMode' => $paymentMethod->OpperationMode != null ? $paymentMethod->OpperationMode : 0,
                    'CommandType' => $paymentMethod->CommandType != null ? $paymentMethod->CommandType : 0,
                    'Abbreviation' => $paymentMethod->Abbreviation != null ? $paymentMethod->Abbreviation : null,
                    'AccessLevel' => $paymentMethod->AccessLevel != null ? $paymentMethod->AccessLevel : 0,
                    'value' => $paymentMethod->value != null ? $paymentMethod->value : null,
                    'Partial_Tender' => $paymentMethod->Partial_Tender != null ? $paymentMethod->Partial_Tender : null,
                    'Over_Tender' => $paymentMethod->Over_Tender != null ? $paymentMethod->Over_Tender : null,
                    'Req_Manager' => $paymentMethod->Req_Manager != null ? $paymentMethod->Req_Manager : null,
                    'method_type' => $paymentMethod->method_type,
                    'InDrawervalue1' =>  $paymentMethod->InDrawerValue1 != null ? $paymentMethod->InDrawerValue1 : 0,
                    'InDrawervalue2' => $paymentMethod->InDrawerValue2 != null ? $paymentMethod->InDrawerValue2 : 0,

                ];

                $payment = new Payments();
                $payment->fill($data);
                $payment->received_at = '1970-01-01 00:00:00';
                $payment->received_from = 0;
                $payment->save();
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();

            throw new \Exception($e);
        }

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}