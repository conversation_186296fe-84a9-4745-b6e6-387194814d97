<?php

namespace NewbridgeWeb\Jobs;

use Carbon\Carbon;
use DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\CustomerGroup;
use NewbridgeWeb\Repositories\CustomerGroupProducts;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\PromotionBins;
use NewbridgeWeb\Repositories\PromotionItems;
use NewbridgeWeb\Repositories\Promotions;
use NewbridgeWeb\Repositories\RedisRepository;
use Ramsey\Uuid\Uuid;
use Log;

class LoyaltyGroupJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;


    public function __construct(public string|Uuid $uuid)
    {
        $this->onQueue(HorizonQueues::HIGH_PRIORITY);
    }

    public function handle()
    {
        $data = RedisRepository::getByKey('customer-group-'.$this->uuid);

        DB::beginTransaction();

        try {
            $input = $data['data'];

            $times = [
                'days' => $input['days'],
                'start_time' => $input['start_time'],
                'end_time' => $input['end_time']
            ];

            $id = $input['id'];

            $group = CustomerGroup::find($id);
            $group->displayname = $input['displayname'];

            if ($group->discount_guid != null) {
                Discounts::where('CommandUID', $group->discount_guid)->forceDelete();
            }

            // reset values
            $group->price_level = null;
            $group->gives_points = 0;
            $group->points_per_amount = 0;
            $group->points_spending_value = 0;
            $group->discount_guid = null;

            if ($input['group_type'] == 'discount' && $input['discountmethod'] == 3) {
                $group->price_level = $input['price_level'];
            }

            if ($input['earn_points'] == 'yes') {
                CustomerGroupProducts::where('group_guid', $group->guid)->forceDelete();

                $group->gives_points = 1;
                $group->points_per_amount = $input['points_earned'];
                $group->points_spending_value = $input['points_value'];
                $group->min_spend = $input['min_spend'];
                $group->minimum_balance = $input['minimum_balance'];

                if (!empty($input['products'])) {
                    RedisRepository::store('customer-group-' . $this->uuid, ['status' => 'processing', 'message' => 'Adding loyalty points products']);

                    foreach ($input['products'] as $k => $prods) {
                        foreach ($prods as $p) {
                            $link = new CustomerGroupProducts();
                            $link->company_id = $group->company_id;
                            $link->product_guid = $p;
                            $link->group_guid = $group->guid;
                            $link->save();
                        }
                    }
                }

                RedisRepository::store('customer-group-' . $this->uuid, ['status' => 'processing', 'message' => 'Added loyalty points products.']);
            }

            if ($input['group_type'] == 'discount') {
                RedisRepository::store('customer-group-' . $this->uuid, ['status' => 'processing', 'message' => 'Creating the Discount']);

                $data_new = [
                    'company_id' => $data['company_id'],
                    'site_num' => 0,
                    'displayname' => $group->displayname . ' ' . $this->chooseDiscountName($input['discountmethod']),
                    'receipttext' => $group->displayname . ' ' . $this->chooseDiscountName($input['discountmethod']),
                    'discountmethod' => $input['discountmethod'],
                    'value' => $input['discountmethod'] < 3 ? $input['value'] : $input['price_level'],
                    'auto' => 0,
                    'isloyaltycard' => 1,
                    'received_from' => 0
                ];

                $discount = new Discounts();
                $discount->fill($data_new);
                $discount->CommandUID = Uuid::uuid4();
                $discount->save();

                if (in_array($input['discountmethod'], [0, 1, 2])) {
                    RedisRepository::store('customer-group-' . $this->uuid, ['status' => 'processing', 'message' => 'Adding discount items.'], 10);

                    $promotion = $this->updatePromotion($group, $discount, $input['discount_products'], $times);
                    $group->promotion_guid = $promotion->CommandUID;
                } elseif ($input['discountmethod'] == 3) {
                    $group->discount_guid = $discount->CommandUID;
                }
            }

            $group->save();

            DB::commit();

            RedisRepository::store('customer-group-' . $this->uuid, ['status' => 'done', 'data' => $input]);

        } catch(\Exception $e) {
            DB::rollback();

            RedisRepository::store('customer-group-' . $this->uuid, ['status' => 'error', 'message' => $e->getMessage()]);

            throw($e);
        }

        MemorySpikeCheckAction::execute(self::class, 512);
    }

    private function chooseDiscountName($type)
    {
        switch($type) {
            case 0:
                return 'Percentage Discount';

                break;
            case 1:
                return 'Amount Discount';

                break;
            case 2:
                return 'Set Price Discount';

                break;
            case 3:
                return 'Price Level Discount';

                break;
            default:
                return 'Loyalty Discount';

                break;
        }
    }

    private function updatePromotion($group, $discount, $items, $times)
    {
        $daySum = 0;

        if (count($times['days']) > 0) {
            foreach ($times['days'] as $d) {
                $daySum += $d;
            }
        }

        $discount = Discounts::find($discount->id);

        $promotion = Promotions::where('CommandUID', $group->promotion_guid)->first();

        if ($promotion == null) {
            $promotion = new Promotions();
            $promotion->CommandUID = Uuid::uuid4();
        }

        $promotion->discount_guid = $discount->CommandUID;
        $promotion->displayname = 'promotion_' . $discount->displayname;
        $promotion->promotiontype = 1;
        $promotion->receipttext = $group->displayname;
        $promotion->short_desc = '';
        $promotion->site_num = $group->site_num;
        $promotion->is_final = 0;
        $promotion->company_id = $discount->company_id;
        $promotion->startdate = Carbon::now()->startOfDay();
        $promotion->enddate = Carbon::now()->addYears(20)->endOfDay();
        $promotion->starttime = $times['start_time'];
        $promotion->endtime = $times['end_time'];
        $promotion->daysofweek = $daySum;
        $promotion->received_at = Carbon::parse('1979-01-01 00:00:00');
        $promotion->received_from = 0;
        $promotion->mask = 1;
        $promotion->isloyalty = 1;

        // create a bin
        $bin = PromotionBins::where('plu_promotion_guid', $promotion->CommandUID)->first();

        if ($bin == null) {
            $bin = new PromotionBins();
            $bin->guid = Uuid::uuid4();
            $bin->plu_promotion_guid = $promotion->CommandUID;
        }

        $bin->displayname = 'Promotion Bin';
        $bin->mask = 1;
        $bin->discount_guid = $discount->CommandUID;
        $bin->save();

        $promotion->save();

        $inserts = [];

        PromotionItems::where('plu_promotion_guid', $promotion->CommandUID)->where('company_id', $promotion->company_id)->forceDelete();

        if (!empty($items)) {
            $itemcount = count($items[$promotion->company_id]);

            foreach ($items[$promotion->company_id] as $k => $product) {
                RedisRepository::store('customer-group-'.$this->uuid, json_encode(['status' => 'processing', 'message' => 'Item '.($k + 1).' of '.$itemcount]), 10);

                $insert = [
                    'guid' => Uuid::uuid4(),
                    'company_id' => $promotion->company_id,
                    'PLU_CommandUID' => $product,
                    'plu_promotion_guid' => $promotion->CommandUID,
                    'plu_promotion_bin_guid' => $bin->guid,
                    'discount_guid' => $discount->CommandUID,
                    'mask' => 1

                ];
                PromotionItems::insert($insert);
            }
        }


        return $promotion;
    }
}
