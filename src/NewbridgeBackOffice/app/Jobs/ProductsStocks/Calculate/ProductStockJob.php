<?php

namespace NewbridgeWeb\Jobs\ProductsStocks\Calculate;

use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;
use NewbridgeWeb\Http\Helpers\WooCommerce\WooCommerceHelper;
use NewbridgeWeb\Jobs\WooCommerce\WooCommerceProductJob;
use NewbridgeWeb\Repositories\RedisRepositoryLists;
use Ramsey\Uuid\Uuid;

class ProductStockJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use Batchable;

    public int $tries = 0;

    public function __construct(
        public int     $productId,
        public int     $siteNumber,
        public ?string $fromDate = null,
        public bool    $validateMode = false
    )
    {
        $this->onQueue(HorizonQueues::STOCK);
    }

    public function handle(): void
    {
        try {
            $processUuid = Uuid::uuid4();

            if ($this->checkForLocks() && DB::table('job_batches')
                    ->where('name', '=', 'calculate-' . $this->productId . '-' . $this->siteNumber)
                    ->where('finished_at', null)
                    ->exists()) {

                return;
            }

            $this->setLocks();

            $companyId = DB::table('plu_products')
                ->where('id', $this->productId)
                ->whereIn('site_num', [0, $this->siteNumber])
                ->where('deleted_at', null)
                ->select('company_id')
                ->value('company_id');

            if ($companyId === null) {
                $this->releaseLocks();

                return;
            }

            $transactionBatchSize = config('newbridge.stock_calculation_chunk_size');

            $transactionCount = DB::table('plu_stock_transactions')
                ->where('product_id', $this->productId)
                ->where('site_num', $this->siteNumber)
                ->where('status', 1)
                ->where('deleted_at', null);

            $latestBeforeFromDate = null;

            if ($this->fromDate) {

                $latestBeforeFromDate = DB::table('plu_stock_transactions')
                    ->select('id', 'created_at')
                    ->where('product_id', $this->productId)
                    ->where('site_num', $this->siteNumber)
                    ->where('status', 1)
                    ->where('deleted_at', null)
                    ->where('created_at', '<', $this->fromDate)
                    ->orderByDesc('created_at')
                    ->orderByDesc('id')
                    ->first();

                $transactionCount = $transactionCount->where('created_at', '>=', $this->fromDate);
            }

            $transactionCount = $transactionCount->orderBy('created_at')
                ->count();

            if ($latestBeforeFromDate) {
                $transactionCount++;
            }

            if ($transactionCount === 0) {
                $this->releaseLocks();

                return;
            }

            if ($transactionCount < $transactionBatchSize) {
                $transactionBatchSize = $transactionCount;
            }

            $batchJobs = [];

            $transactionIterations = ceil($transactionCount / $transactionBatchSize);

            for ($j = 0; $j < $transactionIterations; $j++) {
                $transactionOffset = $j * $transactionBatchSize;

                $batchJobs[] = new ProductStockTransactionsBatchJob(
                    $this->productId,
                    $this->siteNumber,
                    $transactionOffset,
                    $latestBeforeFromDate?->created_at ?? null,
                    $this->validateMode
                );
            }

            $productId = $this->productId;
            $siteNumber = $this->siteNumber;
            $fromDate = $this->fromDate;

            Bus::batch([$batchJobs])
                ->then(function (Batch $batch) use ($productId, $siteNumber, $companyId, $fromDate) {
                    Cache::forget('stock-calculation-' . $productId . '-' . $siteNumber);
                    RedisRepositoryLists::removeFromList('product-' . $productId, ['id' => 0, 'date' => $fromDate]);
                })->finally(function (Batch $batch) use ($productId, $siteNumber, $companyId) {
                    Cache::forget('stock-calculation-' . $productId . '-' . $siteNumber);

                    WooCommerceHelper::SendEvent(
                        $productId,
                        $siteNumber
                    );

                    if (!RedisRepositoryLists::hasKey('product-' . $productId)) {
                        RedisRepositoryLists::removeFromList('products_stock_updates_list',
                            ['id' => $productId, 'site' => $siteNumber]);
                    }
                })->name('calculate-' . $productId . '-' . $siteNumber)->onQueue(HorizonQueues::STOCK)->dispatch();
        } catch (\Throwable $e) {
            $this->releaseLocks();

            report($e);

            throw $e;
        }
    }

    private function checkForLocks(): bool
    {
        return Cache::has('stock-calculation-' . $this->productId . '-' . $this->siteNumber);
    }

    private function setLocks(): void
    {
        Cache::put('stock-calculation-' . $this->productId . "-" . $this->siteNumber, true);
    }

    private function releaseLocks(): void
    {
        Cache::forget('stock-calculation-' . $this->productId . "-" . $this->siteNumber);
    }
}
