<?php

namespace NewbridgeWeb\Jobs\ProductsStocks\Calculate;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\RedisRepositoryLists;

class ProcessStockScheduler implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public int $tries = 1;

    public function __construct()
    {
        $this->onQueue(HorizonQueues::STOCK);
    }

    public function handle()
    {
        $productsToRecalculate = RedisRepositoryLists::get('products_stock_updates_list');

        if($productsToRecalculate->isEmpty()) {
            unset($productsToRecalculate);

            return true;
        }

        foreach ($productsToRecalculate as $product) {
            if (!DB::table('job_batches')
                ->where('finished_at', null)
                ->where('name', '=', 'calculate-' . $product->id . '-' . $product->site)
                ->exists()) {

                $productList = RedisRepositoryLists::get('product-' . $product->id);

                $isProductNotDeleted = DB::table('plu_products')
                ->where('id', $product->id)
                ->whereIn('site_num', [0, $product->site])
                ->where('deleted_at', null)
                ->exists();

                if(!$productList->isEmpty() && $isProductNotDeleted) {
                    $oldestDate = Carbon::now('UTC');
                    foreach ($productList as $productItem) {
                        if (Carbon::parse($productItem->date)->lt($oldestDate)) {
                            $oldestDate = Carbon::parse($productItem->date);
                        } else {
                            RedisRepositoryLists::removeFromList('product-' . $product->id, $productItem);
                        }
                    }

                    ProductStockJob::dispatch($product->id, $product->site, $oldestDate, false)->delay(5)->onQueue(HorizonQueues::STOCK);

                } else {
                    RedisRepositoryLists::removeFromList('products_stock_updates_list', $product);
                }
            }
        }

        unset($productsToRecalculate);
        return true;
    }

}