<?php

namespace NewbridgeWeb\Jobs\ProductsStocks\ProcessTransactions;

use Carbon\Carbon;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use Log;
use NewbridgeWeb\Http\Helpers\IntegrationsHelper;
use NewbridgeWeb\Http\Helpers\TimezoneHelper;
use NewbridgeWeb\Http\Helpers\WooCommerce\WooCommerceHelper;
use NewbridgeWeb\Jobs\WooCommerce\WooCommerceProductJob;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Recipes;
use NewbridgeWeb\Repositories\RedisRepository;
use NewbridgeWeb\Repositories\RedisRepositoryLists;
use NewbridgeWeb\Repositories\RedisSortedSets;
use NewbridgeWeb\Repositories\Sites;
use NewbridgeWeb\Repositories\StockSummary;
use NewbridgeWeb\Repositories\StockTransactions;

class StockTransactionProcessJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use Batchable;
    use SerializesModels;

    public PosTransaction $transactionModel;
    public null|PosTransactionDetail $transactionDetailModel;
    public Products $product;
    public Sites $site;
    public string $datetime;

    public function __construct(public int $transactionDetailsId, public int $company_id, public int $site_num)
    {
        $this->onQueue('stock');
    }

    public function handle()
    {
        $this->transactionDetailModel = PosTransactionDetail::withTrashed()->find($this->transactionDetailsId);

        if($this->transactionDetailModel === null){
            $key = 'transaction-details-' . $this->transactionDetailsId . '-retries';
            if(RedisRepository::checkExists($key)){
                $retries = RedisRepository::getByKey($key);
                if($retries >= 10){
                    RedisRepository::delete($key);
                    $setKey = 'transaction-details-' . $this->company_id . '-' . $this->site_num;
                    RedisSortedSets::removeById($setKey, (int) $this->transactionDetailsId);
                    
                    return true;
                }
                RedisRepository::increment($key);
            } else {
                RedisRepository::store($key, 1);
            }

            return true;
        }

        if($this->transactionDetailModel->deleted_at != null) {
            $key = 'transaction-details-' . $this->transactionDetailModel->company_id . '-' . $this->transactionDetailModel->site_num;
            $object = ['id' => (int) $this->transactionDetailModel->id, 'date' => $this->transactionDetailModel->datetime];
            RedisSortedSets::remove($key, $object);

            return true;
        }

        $retryKey = 'transaction-details-' . $this->transactionDetailsId . '-retries';
        if(RedisRepository::checkExists($retryKey)){
            RedisRepository::delete($retryKey);
        }

        $transaction = PosTransaction::find($this->transactionDetailModel->trans_id);

        if($transaction === null) {
            $key = 'transaction-details-' . $this->transactionDetailModel->company_id . '-' . $this->transactionDetailModel->site_num;
            $object = ['id' => (int) $this->transactionDetailModel->id, 'date' => $this->transactionDetailModel->datetime];
            RedisSortedSets::remove($key, $object);

            return true;
        }

        $timezone = TimezoneHelper::getTimezone($this->site_num, $this->company_id);
        $this->transactionModel = $transaction;
        $this->datetime =  Carbon::parse($this->transactionDetailModel->datetime, $timezone)->setTimezone('UTC')->toDateTimeString();

        if ($this->transactionModel->finalised_date == null) {

            $key = 'transaction-details-' . $this->transactionDetailModel->company_id . '-' . $this->transactionDetailModel->site_num;
            $object = ['id' => (int) $this->transactionDetailModel->id, 'date' => $this->transactionDetailModel->datetime];
            RedisSortedSets::remove($key, $object);
            
            return true;
        }

        if (isset($this->transactionDetailModel->splitparentreference) && $this->transactionDetailModel->splitparentreference != null) {

            $key = 'transaction-details-' . $this->transactionDetailModel->company_id . '-' . $this->transactionDetailModel->site_num;
            $object = ['id' => (int) $this->transactionDetailModel->id, 'date' => $this->transactionDetailModel->datetime];
            RedisSortedSets::remove($key, $object);

            return true;
        }

        $this->product = Products::where('guid', $this->transactionDetailModel->product_guid)
            ->with(['parent' => function ($q) {
                $q->with('parent');
            }])
            ->withTrashed()
            ->first();

        $this->site = Sites::where([
            'company_id' => $this->company_id,
            'site_num' => $this->site_num,
        ])->first();

        switch ($this->transactionDetailModel->command) {
            case 3: // PLU
                $this->processProduct();
                $this->sendUpdatedStockEvent();
                break;
            case 5:
                $this->processProduct();
                $this->sendUpdatedStockEvent();
                break;
            case 38: // recipes
                $this->processRecipe();
                break;
            case 107: // wastage
                $this->processWastage();
                $this->sendUpdatedStockEvent();
                break;
            default: // anything else, is unhandled
                return true;
        }

        $key = 'transaction-details-' . $this->transactionDetailModel->company_id . '-' . $this->transactionDetailModel->site_num;
        $object = ['id' => (int) $this->transactionDetailModel->id, 'date' => $this->transactionDetailModel->datetime];
        RedisSortedSets::remove($key, $object);

        return true;
    }

    public function checkAndQueueRecalculation(int $productId, Carbon|string $datetime, int $siteNum)
    {
        if(!$this->site->hasStockModuleEnabled()) {
            return;
        }

        $timezone = TimezoneHelper::getTimezone($this->site_num, $this->company_id);

        $transaction = StockTransactions::where('product_id', $productId)
            ->where('created_at', '>', Carbon::parse($datetime, $timezone)->setTimezone('UTC')->toDateTimeString())
            ->where('site_num', $siteNum)
            ->where('status', 1)
            ->first();

        if($transaction !== null)
        {
            // schedule a recalculation from this date.
            if (!RedisRepositoryLists::has('products_stock_updates_list',
                ['id' => (int) $productId, 'site' => (int) $siteNum])) {
                RedisRepositoryLists::store('products_stock_updates_list',
                    ['id' => (int) $productId, 'site' => (int) $siteNum]);
            }

            RedisRepositoryLists::store('product-' . (int) $productId,
                ['id' => 0, 'date' => Carbon::parse($datetime, $timezone)->setTimezone('UTC')->toDateTimeString()]);
        }
    }

    private function getPreviousStockForProduct(int $productId, string|Carbon $createdAt)
    {
        $previousQuantity = StockTransactions::select(['product_quantity', 'created_at'])
            ->where('product_id', $productId)
            ->where('status', 1)
            ->where('deleted_at', null)
            ->where('created_at', '<=', $createdAt)
            ->orderBy('created_at', 'desc')
            ->orderBy('id', 'desc')
            ->limit(1)
            ->first();
        
        return $previousQuantity != null ? $previousQuantity->product_quantity : 0;
    }

    private function processProduct()
    {
        $refund = $this->transactionDetailModel->command_type == 6;

        $hasAlreadyRun = StockTransactions::where('transaction_detail_id', $this->transactionDetailModel->id)->where(function ($q) {
            $q->where('isTransaction', 1)
                ->orWhere('summary_type', 1);
        })->count();

        if ($hasAlreadyRun > 0) {
            return true;
        }

        /**
         * If the product has a parent guid
         * get that product and remove stock from it
         */
        if (!empty($this->product)) {
            if ($this->product->sku_guid != null) {
                $previousStock = $this->getPreviousStockForProduct($this->product->id, $this->datetime);
                $data = [
                    'company_id' => $this->transactionDetailModel->company_id,
                    'site_num' => $this->transactionDetailModel->site_num,
                    'product_id' => $this->product->id,
                    'quantity' => $refund === true ? abs((float)$this->transactionDetailModel->qty) : abs(((float)$this->transactionDetailModel->qty)) * -1,
                    'product_quantity' => $refund === true ? ($previousStock + abs((float)$this->transactionDetailModel->qty)) : ($previousStock + (abs((float)$this->transactionDetailModel->qty) * -1)),
                    'summary_type' => 1,
                    'transaction_detail_id' => $this->transactionDetailModel->id,
                    'status' => 1,
                    'sku_guid' => $this->product->sku_guid,
                    'isTransaction' => 1,
                    'created_at' => $this->datetime
                ];

                $transaction = new StockTransactions();
                $transaction->fill($data);
                $transaction->save();

                $this->checkAndQueueRecalculation($this->product->id, $this->transactionDetailModel->datetime, $this->transactionDetailModel->site_num);
            } else {
                if ($this->product->parent != null && $this->product->parent->parent != null && $this->product->parent->parent->sku_guid != null) {
                    $previousStock = $this->getPreviousStockForProduct($this->product->parent->parent->id, $this->datetime);
                    $data = [
                        'company_id' => $this->transactionDetailModel->company_id,
                        'site_num' => $this->transactionDetailModel->site_num,
                        'product_id' => $this->product->parent->parent->id,
                        'quantity' => $refund === true ? $this->product->sku_quantity : ($this->product->sku_quantity * -1),
                        'product_quantity' => $refund === true ? ($previousStock + $this->product->sku_quantity) : ($previousStock + ($this->product->sku_quantity * -1)),
                        'summary_type' => 1,
                        'transaction_detail_id' => $this->transactionDetailModel->id,
                        'status' => 1,
                        'sku_guid' => $this->product->parent->parent->sku_guid,
                        'isTransaction' => 1,
                        'created_at' => $this->datetime
                    ];

                    $transaction = new StockTransactions();
                    $transaction->fill($data);
                    $transaction->save();

                    $this->checkAndQueueRecalculation($this->product->parent->parent->id, $this->transactionDetailModel->datetime, $this->transactionDetailModel->site_num);
                } else {
                    /**
                     * CHILD PRODUCT
                     */
                    if ($this->product->parent != null && $this->product->parent->sku_guid != null) {
                        if ($this->product->sku_quantity == null) {
                            return false;
                        }
                        $previousStock = $this->getPreviousStockForProduct($this->product->parent->id, $this->datetime);
                        $data = [
                            'company_id' => $this->transactionDetailModel->company_id,
                            'site_num' => $this->transactionDetailModel->site_num,
                            'product_id' => $this->product->parent->id,
                            'quantity' => $refund === true ? $this->product->sku_quantity : ($this->product->sku_quantity * -1),
                            'product_quantity' => $refund === true ? ($previousStock + $this->product->sku_quantity) : ($previousStock + ($this->product->sku_quantity * -1)),
                            'summary_type' => 1,
                            'transaction_detail_id' => $this->transactionDetailModel->id,
                            'status' => 1,
                            'sku_guid' => $this->product->parent->sku_guid,
                            'isTransaction' => 1,
                            'created_at' => $this->datetime
                        ];

                        $transaction = new StockTransactions();
                        $transaction->fill($data);
                        $transaction->save();

                        $this->checkAndQueueRecalculation($this->product->parent->id, $this->transactionDetailModel->datetime, $this->transactionDetailModel->site_num);
                    }
                }
            }
        } else {
            if ($this->transactionDetailModel->displayname == 'Paid Out') {
                return true;
            }
        }

        return true;
    }

    private function processRecipe()
    {
        $refund = $this->transactionDetailModel->command_type == 6;

        // get the product with the recipe
        $products = new Products();

        $soldProduct = $products->query()->where('guid', $this->transactionDetailModel->product_guid)->with(['recipe' => function ($q) {
            $q->with(['ingredients' => function ($q) {
                $q->withTrashed();
            }])->withTrashed();
        }])->withTrashed()->first();

        $hasAlreadyRun = StockTransactions::where('transaction_detail_id', $this->transactionDetailModel->id)->where(function ($q) {
            $q->where('isTransaction', 1)
                ->orWhere('summary_type', 1);
        })->count();

        if($soldProduct->recipe != null) {
            if ($hasAlreadyRun > ($soldProduct->recipe_qty * $soldProduct->recipe->ingredients->count())) {
                return true;
            }

            for ($x = 0; $x < $soldProduct->recipe_qty; $x++) {
                foreach ($soldProduct->recipe->ingredients as $ingredient) {
                    $ingredientProduct = Products::where('guid', $ingredient->product_guid)->withTrashed()->first();
                    $ingredientRecipe = Recipes::where('guid', $ingredient->product_guid)->with('ingredients')->withTrashed()->first();

                    if ($ingredientRecipe) {
                        for ($x = 0; $x < $soldProduct->recipe_qty; $x++) {
                            foreach ($ingredientRecipe->ingredients as $ingredient2) {
                                $ingredientProduct2 = Products::where('guid', $ingredient2->product_guid)->withTrashed()->first();

                                if ($ingredientProduct2 !== null) {
                                    $ingredientProduct2PreviousStock = $this->getPreviousStockForProduct($ingredientProduct2->id, $this->datetime);

                                    if ($ingredientProduct2) {
                                        $data = [
                                            'company_id' => $this->transactionDetailModel->company_id,
                                            'site_num' => $this->transactionDetailModel->site_num,
                                            'product_id' => $ingredientProduct2['id'],
                                            'quantity' => !$refund ? ($ingredient2->quantity * -1) : $ingredient2->quantity,
                                            'product_quantity' => !$refund ? ($ingredientProduct2PreviousStock + ($ingredient2->quantity * -1)) : ($ingredientProduct2PreviousStock + $ingredient2->quantity),
                                            'summary_type' => 1,
                                            'transaction_detail_id' => $this->transactionDetailModel->id,
                                            'status' => 1,
                                            'sku_guid' => $ingredientProduct2['sku_guid'],
                                            'isTransaction' => 1,
                                            'created_at' => $this->datetime
                                        ];

                                        $transaction = new StockTransactions();
                                        $transaction->fill($data);
                                        $transaction->save();

                                        $this->checkAndQueueRecalculation($ingredientProduct2->id, $this->transactionDetailModel->datetime, $this->transactionDetailModel->site_num);
                                    }
                                }
                            }
                        }
                    } elseif ($ingredientProduct) {
                        $ingredientProductPreviousStock = $this->getPreviousStockForProduct($ingredientProduct->id, $this->datetime);
                        $data = [
                            'company_id' => $this->transactionDetailModel->company_id,
                            'site_num' => $this->transactionDetailModel->site_num,
                            'product_id' => $ingredientProduct['id'],
                            'quantity' => !$refund ? ($ingredient->quantity * -1) : $ingredient->quantity,
                            'product_quantity' => !$refund ? ($ingredientProductPreviousStock + ($ingredient->quantity * -1)) : ($ingredientProductPreviousStock + $ingredient->quantity),
                            'summary_type' => 1,
                            'transaction_detail_id' => $this->transactionDetailModel->id,
                            'status' => 1,
                            'sku_guid' => $ingredientProduct['sku_guid'],
                            'isTransaction' => 1,
                            'created_at' => $this->datetime
                        ];

                        $transaction = new StockTransactions();
                        $transaction->fill($data);
                        $transaction->save();

                        $this->checkAndQueueRecalculation($ingredientProduct['id'], $this->transactionDetailModel->datetime, $this->transactionDetailModel->site_num);
                    }
                }
            }
        }
        return true;
    }

    private function processWastage()
    {
        if ($this->transactionDetailModel->command_type === 5) {
            return false;
        }

        /**
         * If the product has a parent guid
         * get that product and remove stock from it
         */
        $soldProduct = Products::where('guid', $this->transactionDetailModel->product_guid)
            ->with(['parent'])
            ->with(['recipe' => function ($q) {
                $q->with('ingredients');
            }])
            ->withTrashed()
            ->first();

        if (!empty($soldProduct)) {
            /**
             * check that a summary exists for this transaction summary
             */
            $exists = Redis::exists('wastage-summary-' . $this->transactionDetailModel->trans_id);

            if ($exists === 0) {
                $summaryData = [
                    'company_id' => $this->transactionDetailModel->company_id,
                    'site_num' => $this->transactionDetailModel->site_num,
                    'user_id' => 0,
                    'summary_type' => 5,
                    'send_method' => null,
                    'status' => 4,
                    'notes' => 'Created by POS Terminal',
                    'stocktake_date' => $this->transactionModel->finalised_date
                ];

                $summary = new StockSummary();
                $summary->fill($summaryData);
                $summary->save();

                Redis::set('wastage-summary-' . $this->transactionDetailModel->trans_id, $summary->id);
                Redis::expire('wastage-summary-' . $this->transactionDetailModel->trans_id, 60 * 60);

                $wastageSummaryID = $summary->id;
            } else {
                $wastageSummaryID = Redis::get('wastage-summary-' . $this->transactionDetailModel->trans_id);
            }

            if ($soldProduct->recipe_guid != null && $soldProduct->recipe != null) {
                // loop through the recipe quantity
                for ($x = 0; $x < $soldProduct->recipe_qty; $x++) {
                    $salePrice = ($this->transactionDetailModel->gross_value / $soldProduct->recipe_qty);
                    $ingredientCount = $soldProduct->recipe->ingredients->count();
                    if($ingredientCount == 0){
                        return false;
                    }
                    $salePrice = ($salePrice / $ingredientCount);

                    // loop through the ingredients
                    foreach ($soldProduct->recipe->ingredients as $ingredient) {
                        $ingredientProduct = Products::where('guid', $ingredient->product_guid)->withTrashed()->first();
                        if ($ingredientProduct) {
                            $ingredientProductPreviousStock = $this->getPreviousStockForProduct($ingredientProduct->id, $this->datetime);
                            $data = [
                                'company_id' => $this->transactionDetailModel->company_id,
                                'site_num' => $this->transactionDetailModel->site_num,
                                'product_id' => $ingredientProduct['id'],
                                'quantity' => $ingredient->quantity * -1,
                                'product_quantity' => ($ingredientProductPreviousStock + ($ingredient->quantity * -1)),
                                'summary_type' => 5,
                                'summary_id' => $wastageSummaryID,
                                'status' => 1,
                                'sku_guid' => $ingredientProduct->sku_guid,
                                'isTransaction' => 0,
                                'transaction_detail_id' => $this->transactionDetailModel->id,
                                'reason' => $this->transactionDetailModel->void_reason,
                                'clerk_guid' => $this->transactionDetailModel->employee_guid,
                                'costprice' => ($ingredientProduct->costprice * $ingredient->quantity),
                                'costprice_total' => $soldProduct->costprice,
                                'saleprice' => $salePrice,
                                'created_at' => $this->datetime
                            ];

                            $transaction = new StockTransactions();
                            $transaction->fill($data);
                            $transaction->save();
                        }
                    }
                }

                return true;
            } elseif ($soldProduct->sku_guid !== null) {
                $qty = -1;
                $previousStock = $this->getPreviousStockForProduct($soldProduct->id, $this->datetime);
                $data = [
                    'company_id' => $this->transactionDetailModel->company_id,
                    'site_num' => $this->transactionDetailModel->site_num,
                    'product_id' => $soldProduct['id'],
                    'quantity' => $qty,
                    'product_quantity' => ($previousStock + $qty),
                    'summary_type' => 5,
                    'summary_id' => $wastageSummaryID,
                    'status' => 1,
                    'sku_guid' => $soldProduct->sku_guid,
                    'isTransaction' => 0,
                    'transaction_detail_id' => $this->transactionDetailModel->id,
                    'reason' => $this->transactionDetailModel->void_reason,
                    'clerk_guid' => $this->transactionDetailModel->employee_guid,
                    'costprice' => $soldProduct->costprice,
                    'costprice_total' => $soldProduct->costprice,
                    'saleprice' => $this->transactionDetailModel->gross_value,
                    'created_at' => $this->datetime
                ];

                $transaction = new StockTransactions();
                $transaction->fill($data);
                $transaction->save();

                return true;
            } elseif ($soldProduct->plu_parent_guid !== null) {
                $previousStock = $this->getPreviousStockForProduct($soldProduct->parent->id, $this->datetime);
                $data = [
                    'company_id' => $this->transactionDetailModel->company_id,
                    'site_num' => $this->transactionDetailModel->site_num,
                    'product_id' => $soldProduct->parent->id,
                    'quantity' => ($soldProduct->sku_quantity * -1),
                    'product_quantity' => ($previousStock + ($soldProduct->sku_quantity * -1)),
                    'summary_type' => 5,
                    'summary_id' => $wastageSummaryID,
                    'status' => 1,
                    'sku_guid' => $soldProduct->parent->sku_guid,
                    'isTransaction' => 0,
                    'transaction_detail_id' => $this->transactionDetailModel->id,
                    'reason' => $this->transactionDetailModel->void_reason,
                    'clerk_guid' => $this->transactionDetailModel->employee_guid,
                    'costprice' => $soldProduct->costprice,
                    'costprice_total' => $soldProduct->costprice,
                    'saleprice' => $this->transactionDetailModel->gross_value,
                    'created_at' => $this->datetime
                ];

                $transaction = new StockTransactions();
                $transaction->fill($data);
                $transaction->save();

                return true;
            }
        }

        return false;
    }

    public function sendUpdatedStockEvent()
    {
        WooCommerceHelper::SendEvent($this->product->id, $this->site_num);
    }
}
