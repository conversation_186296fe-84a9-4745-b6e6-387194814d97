<?php

namespace NewbridgeWeb\Jobs\ProductsStocks\ProcessTransactions;

use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Repositories\RedisRepositoryLists;
use NewbridgeWeb\Repositories\RedisSortedSets;

class ProcessStockTransactionList implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(public int $companyId, public int $siteNum)
    {
        $this->onQueue('stock');
    }

    public function handle()
    {
        if (DB::table('job_batches')
            ->where('finished_at', null)
            ->where('name', '=', 'stock-transaction-processing-' . $this->companyId . '-' . $this->siteNum)
            ->exists()) {
            return;
        }

        $key = 'transaction-details-' . $this->companyId . '-' . $this->siteNum;
        $transactionDetailList = RedisSortedSets::get($key, 0, 500);

        if($transactionDetailList->isEmpty()) {
            self::clearCompanyFromList($this->companyId, $this->siteNum);

            return;
        }

        foreach ($transactionDetailList as $i => $transaction) {
            $batchJobs[] = new StockTransactionProcessJob(
                $transaction->id,
                $this->companyId,
                $this->siteNum
            );
        }

        if (empty($batchJobs)) {
            self::clearCompanyFromList($this->companyId, $this->siteNum);

            return;
        }

        $companyId = $this->companyId;
        $siteNum = $this->siteNum;

        Bus::batch([$batchJobs])
            ->finally(function (Batch $batch) use ($companyId, $siteNum) {
                self::clearCompanyFromList($companyId, $siteNum);
            })
            ->name('stock-transaction-processing-' . $this->companyId . '-' . $this->siteNum)->dispatch();

        MemorySpikeCheckAction::execute(self::class, 300);
    }

    private static function clearCompanyFromList(int $companyId, int $siteNum): void
    {
        $key = 'transaction-details-' . $companyId . '-' . $siteNum;

        if (!RedisSortedSets::hasSet($key)) {
            RedisRepositoryLists::removeFromList('sites_with_stock_updates_list',
                ['company_id' => $companyId, 'site_num' => $siteNum]);
        }
    }
}
