<?php

namespace NewbridgeWeb\Jobs\ProductsStocks\ProcessTransactions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\RedisRepositoryLists;

class ProcessStockTransactionScheduler implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public int $tries = 1;

    public function __construct()
    {
        $this->onQueue(HorizonQueues::STOCK);
    }

    public function handle()
    {
        $sitesToHaveStockRun = RedisRepositoryLists::get('sites_with_stock_updates_list');

        if ($sitesToHaveStockRun->isEmpty()) {
            unset($sitesToHaveStockRun);

            return true;
        }

        foreach ($sitesToHaveStockRun as $site) {
            if (!DB::table('job_batches')
                ->where('finished_at', null)
                ->where('name', '=', 'stock-transaction-processing-' . $site->company_id . '-' . $site->site_num)
                ->exists()) {

                ProcessStockTransactionList::dispatch($site->company_id, $site->site_num)->onQueue(HorizonQueues::STOCK);
            }
        }

        MemorySpikeCheckAction::execute(self::class, 300);

        unset($sitesToHaveStockRun);
        return true;
    }

}