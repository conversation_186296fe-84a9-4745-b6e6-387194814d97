<?php

namespace NewbridgeWeb\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use Newbridge<PERSON>eb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\DataTransfer\Import\ImportController;
use NewbridgeWeb\Repositories\Import;

class BackgroundImportJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $document;
    public $tries = 5;

    public function __construct(Import $document)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY_BACKGROUND);

        ini_set('max_execution_time', 3600);
        ini_set('memory_limit', '1G');
        set_time_limit(3500);

        $this->document = $document;
    }

    public function handle()
    {
        $this->runImport();

        MemorySpikeCheckAction::execute(self::class, 600);
    }

    private function runImport()
    {
        Redis::set(
            'import-' .
            $this->document->guid . '-status',
            json_encode(['status' => 'warning',
                'text' => 'Queued Job Picked Up',
                'percent' => '55'])
        );
        $run = new ImportController();

        return $run->processImportedSheet($this->document);
    }

    public function failed($exception)
    {
        Redis::set('import-' . $this->document->guid . '-status', json_encode(['status' => 'error', 'text' => $exception->getMessage(), 'percent' => '100']));
        $this->document->upload_status = Import::FAILED;
        $this->document->save();
    }
}
