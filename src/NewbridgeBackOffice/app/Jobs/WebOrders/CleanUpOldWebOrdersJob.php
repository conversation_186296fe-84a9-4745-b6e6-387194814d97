<?php

namespace NewbridgeWeb\Jobs\WebOrders;

use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\Orders\Orders;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\PosTransactionPayment;
use NewbridgeWeb\Repositories\StockTransactions;

class CleanUpOldWebOrdersJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 1;

    public function __construct()
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);
    }

    public function handle()
    {
        Orders::where('created_at', '<', now()->subDays(7))
            ->whereIn('platform', [null, 'orderpay', 'newbridge'])
            ->forceDelete();

        Orders::where('created_at', '<', now()->subDays(30))
            ->whereNotIn('platform', [null, 'orderpay', 'newbridge'])
            ->forceDelete();

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}