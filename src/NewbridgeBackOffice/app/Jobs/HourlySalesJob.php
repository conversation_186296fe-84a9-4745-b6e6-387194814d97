<?php

namespace NewbridgeWeb\Jobs;

use Carbon\Carbon;
use DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\DataTransfer\Export\HourlySales\HourlySalesExport;
use NewbridgeWeb\Repositories\Integrations\IntegrationSettings;
use NewbridgeWeb\Repositories\PosTransactionDetail;
use NewbridgeWeb\Repositories\Sites;

class HourlySalesJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $company;
    public $integrationSettings;
    public $canRead;
    public $sites;

    public function __construct($company)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);

        $this->company = $company;

        $this->integrationSettings = IntegrationSettings::where('company_id', $this->company['id'])
            ->where('integration_id', 8)
            ->get()
            ->keyBy('setting_id')
            ->toArray();

        $this->sites = Sites::where('company_id', $this->company->id)->get();
    }

    public function handle()
    {
        $this->runReportUpload($this->company);

        MemorySpikeCheckAction::execute(self::class, 512);
    }

    private function runReportUpload()
    {
        $results = [];

        $maxHours = 24;
        $currentHour = 0;
        $currentHourFormat = 00;

        $sites = Sites::where('company_id', $this->company['id'])->get()->keyBy('site_num')->toArray();

        while ($currentHour < $maxHours) {
            $query = PosTransactionDetail::select(DB::raw('department_guid, site_num, SUM(net_value) as sum_net_value, SUM(tax_value) as sum_tax_value'))
                ->where('company_id', $this->company['id'])
                ->whereBetween('finalised_date', [Carbon::now()->subDay()->startOfDay()->addHours($currentHour), Carbon::now()->subDay()->startOfDay()->addHours($currentHour + 1)])
                ->whereIn('command', [3, 7, 38])
                ->whereIn('command_type', [0,6])
                ->with(['department' => function ($q) {
                    $q->withTrashed();
                }])
                ->groupBy('department_guid')
                ->groupBy('site_num')
                ->get();



            foreach ($query as $k => $q) {
                if ($q->sum_net_value != 0) {
                    $results[] = [
                        'Site Identifier' => $sites[$q->site_num]['site_name'],
                        'Date' => Carbon::now()->subDay()->format('d/m/y'),
                        'Revenue Key' => $q->department->acc_code,
                        'Hour' => $currentHourFormat,
                        'Value' => $q->sum_net_value - $q->sum_tax_value
                    ];
                }
            }

            $currentHour++;
            $currentHourFormat = Carbon::now()->subDay()->startOfDay()->addHours($currentHour)->format('H');
        }

        $this->convertToCSV($results);

        $updateSettings = new IntegrationSettings();
        $updateSettings = $updateSettings->where('company_id', $this->company['id'])
            ->where('integration_id', 8)
            ->where('setting_id', 'last_run')
            ->first();

        $updateSettings->value = Carbon::now()->toDateTimeString();
        $updateSettings->save();

        return 'done';
    }

    public function convertToCSV($data)
    {
        $path = '/accessupload/';
        $filename = 'cash_'.$this->company['company_name'].'_'.Carbon::now()->toDateString().'.csv';

        Excel::store(new HourlySalesExport($data), $path.$filename);
        $file = Storage::get($path.$filename);

        //Change this to use settings

        Config::set('filesystems.disks.accesspeople', [
            'driver' => 'ftp',

            'host' => $this->integrationSettings['ftp_host']['value'],

            'port' => 21,

            'ssl' => true,

            'username' => $this->integrationSettings['ftp_user']['value'],

            'password' => $this->integrationSettings['ftp_password']['value']
        ]);

        Storage::disk('accesspeople')->put(''.$filename, $file);

        return 'done';
    }
}
