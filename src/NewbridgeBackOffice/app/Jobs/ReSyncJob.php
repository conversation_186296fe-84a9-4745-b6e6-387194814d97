<?php

namespace NewbridgeWeb\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Events\ButtonQueueEvent;
use NewbridgeWeb\Events\UpdaterUpdateCrudEvent;
use NewbridgeWeb\Repositories\Clerks\ClerkAreas;
use NewbridgeWeb\Repositories\Clerks\ClerkRoles;
use NewbridgeWeb\Repositories\Clerks\Clerks;
use NewbridgeWeb\Repositories\Colors;
use NewbridgeWeb\Repositories\Commands;
use NewbridgeWeb\Repositories\Departments;
use NewbridgeWeb\Repositories\Discounts;
use NewbridgeWeb\Repositories\Payments;
use NewbridgeWeb\Repositories\Pos;
use NewbridgeWeb\Repositories\PosButtons;
use NewbridgeWeb\Repositories\PosSettings;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\Promotions;
use NewbridgeWeb\Repositories\ReceiptLayouts;
use NewbridgeWeb\Repositories\SKU;
use NewbridgeWeb\Repositories\SubDepartment;
use NewbridgeWeb\Repositories\Suppliers;
use NewbridgeWeb\Repositories\Tables;
use NewbridgeWeb\Repositories\Tax\TaxRates;
use NewbridgeWeb\Repositories\VoidReasons;

class ReSyncJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $data;
    public $key;

    public function __construct($data)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);

        $this->data = json_decode(json_encode($data), true)['data'];
        $this->key = 'resync:' . $this->data['company_id'] . ':' . $this->data['update_id'];
    }

    public function handle()
    {
        switch ($this->data['update_id']) {
            case 'sys_tax_rates':
                $this->sys_tax_rates();
                break;
            case 'pos_receipt_layout':
                $this->pos_receipt_layout();
                break;
            case 'sys_reasons':
                $this->sys_reasons();
                break;
            case 'sys_payment':
                $this->sys_payment();
                break;
            case 'sys_int_commands':
                $this->sys_int_commands();
                break;
            case 'sys_color':
                $this->sys_color();
                break;
            case 'sys_clerks':
                $this->sys_clerks();
                break;
            case 'sys_clerk_areas':
                $this->sys_clerk_areas();
                break;
            case 'site_tables':
                $this->site_tables();
                break;
            case 'pos_settings':
                $this->pos_settings();
                break;
            case 'pos_screens':
                $this->pos_screens();
                break;
            case 'pos_button':
                $this->pos_button();
                break;
            case 'plu_suppliers':
                $this->plu_suppliers();
                break;
            case 'plu_subdepartment':
                $this->plu_subdepartment();
                break;
            case 'plu_skus':
                $this->plu_skus();
                break;
            case 'plu_recipes':
                $this->plu_recipes();
                break;
            case 'plu_promotion':
                $this->plu_promotion();
                break;
            case 'plu_products':
                $this->plu_products();
                break;
            case 'mod_groups':
                $this->mod_groups();
                break;
            case 'plu_discount':
                $this->plu_discount();
                break;
            case 'plu_department':
                $this->plu_department();
                break;
            case 'sys_job_roles':
                $this->sys_job_roles();
                break;
            default:
                $status = json_decode(Redis::get($this->key), true);
                $status['status'] = 'failed';
                Redis::set($this->key, json_encode($status));
                Redis::expire($this->key, (config('newbridge.report_expire_time') * 60));

                break;
        }

        MemorySpikeCheckAction::execute(self::class, 300);
    }

    public function sys_tax_rates()
    {
        $data = TaxRates::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function putInQueue($data)
    {
        $status = json_decode(Redis::get($this->key), true);

        if (!isset($status['queued'])) {
            $status['queued'] = 0;
        }

        if (!isset($statusp['records'])) {
            $status['status'] = 'in-progress';
            $status['records'] = $status['records'] + $data->count();
        } else {
            $status['records'] = $status['records'] + $data->count();
        }

        Redis::set($this->key, json_encode($status));
        Redis::expire($this->key, (config('newbridge.report_expire_time') * 60));

        $pushed = 0;
        $current = 0;

        if ($this->data['update_id'] === 'pos_button') {
            foreach ($data as $page) {
                $buttonCount = PosButtons::where('plu_product_page_guid', $page->CommandUID)->count();

                $terminals = Pos::where('company_id', $this->data['company_id'])
                    ->where('site_num', $this->data['site_num'])
                    ->get();

                foreach ($terminals as $pos) {
                    $page->terminal_id = $pos->id;
                    \Event::dispatch(new ButtonQueueEvent($page));
                }

                $pushed = $pushed + $buttonCount;
                $current++;
                if ($current == 25) {
                    $status['status'] = 'queued';
                    $status['queued'] = $status['queued'] + $pushed;

                    Redis::set($this->key, json_encode($status));
                    Redis::expire($this->key, (config('newbridge.report_expire_time') * 60));
                    $current = 0;
                }
            }
        } else {
            foreach ($data as $d) {
                $d->received_from = 0;
                \Event::dispatch(new UpdaterUpdateCrudEvent($d));
                $pushed++;

                if ($current == 50) {
                    $status['status'] = 'queued';
                    $status['queued'] = $status['queued'] + $pushed;

                    Redis::set($this->key, json_encode($status));
                    Redis::expire($this->key, (config('newbridge.report_expire_time') * 60));
                    $current = 0;
                }
            }
        }

        $status['queued'] = $status['queued'] + $pushed;
        $status['status'] = 'awaiting-collection';

        Redis::set($this->key, json_encode($status));
        Redis::expire($this->key, (config('newbridge.report_expire_time') * 60));
    }

    public function sys_job_roles()
    {
        $data = ClerkRoles::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function pos_receipt_layout()
    {
        $data = ReceiptLayouts::where('company_id', $this->data['company_id'])->whereIn('site_num', $this->data['site_num'])->get();

        $this->putInQueue($data);
    }

    public function sys_reasons()
    {
        $data = VoidReasons::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function sys_payment()
    {
        $data = Payments::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function sys_int_commands()
    {
        $data = Commands::where('company_id', 0)->get();

        $this->putInQueue($data);
    }

    public function sys_color()
    {
        $data = Colors::all();

        $this->putInQueue($data);
    }

    public function sys_clerks()
    {
        $data = Clerks::where('company_id', $this->data['company_id'])
            ->with('ClkClerkRoles')
            ->with('lnk_buttonlinks')
            ->whereIn('site_num', $this->data['site_num'])
            ->get();

        $this->putInQueue($data);
    }

    public function sys_clerk_areas()
    {
        $data = ClerkAreas::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function site_tables()
    {
        $data = Tables::with('tablelocation')
            ->where('company_id', $this->data['company_id'])
            ->whereIn('site_num', $this->data['site_num'])
            ->get();

        $this->putInQueue($data);
    }

    public function pos_settings()
    {
        $data = PosSettings::where('company_id', $this->data['company_id'])
            ->whereIn('site_num', $this->data['site_num'])
            ->get();

        $this->putInQueue($data);
    }

    public function pos_screens()
    {
        $data = Commands::where('company_id', $this->data['company_id'])
            ->whereIn('site_num', $this->data['site_num'])
            ->where('Command', 1)->get();

        $this->putInQueue($data);
    }

    public function pos_button()
    {
        $data = Commands::where('company_id', $this->data['company_id'])
            ->whereIn('site_num', $this->data['site_num'])
            ->whereIn('Command', [1, 117, 6])
            ->get();

        $this->putInQueue($data);
    }

    public function plu_suppliers()
    {
        $data = Suppliers::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function plu_subdepartment()
    {
        $data = SubDepartment::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function plu_skus()
    {
        $data = SKU::where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function plu_recipes()
    {
        $data = Departments::with(['products'])
            ->where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function plu_promotion()
    {
        $data = Promotions::with(['discount', 'promotion_bins' => function ($q) {
            $q->with(['promotion_items' => function ($q) {
                $q->with('discount');
            }]);
        }])
            ->whereIn('site_num', $this->data['site_num'])
            ->where('company_id', $this->data['company_id'])->get();

        $this->putInQueue($data);
    }

    public function plu_products()
    {
        $data = Products::with('groups')->where('company_id', $this->data['company_id'])
            ->whereIn('site_num', $this->data['site_num'])
            ->get();

        $this->putInQueue($data);
    }

    public function mod_groups()
    {
        $data = Commands::where('company_id', $this->data['company_id'])
            ->where('Command', 6)->get();

        $this->putInQueue($data);

        foreach ($data as $mod_group) {
            $buttons = PosButtons::with(['lnk_buttonlinks', 'plu_product_page', 'pos_button_style'])->where('plu_product_page_guid', $mod_group->CommandUID)->get();
            $this->putInQueue($buttons);
        }
    }

    public function plu_discount()
    {
        $data = Discounts::where('company_id', $this->data['company_id'])
            ->where('auto', 0)->where('isloyaltycard', 0)->get();

        $this->putInQueue($data);
    }

    public function plu_department()
    {
        $data = Departments::where('company_id', $this->data['company_id'])->get();
        $departments = new Collection();
        foreach ($data as $department) {
            $department->site_num = $this->data['site_num'];
            $departments->push($department);
        }

        $this->putInQueue($departments);
    }
}
