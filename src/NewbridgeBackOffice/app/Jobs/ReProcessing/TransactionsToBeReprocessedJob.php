<?php

namespace NewbridgeWeb\Jobs\ReProcessing;

use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\PosTransaction;
use NewbridgeWeb\Jobs\ReProcessing\ReProcessTransactionJob;
use Ramsey\Uuid\Uuid;
use Storage;

class TransactionsToBeReprocessedJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use Batchable;

    public int $tries = 1;

    public function __construct(public array $transactionIds, public array $dates = [], public int|null $companyId = null)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);
    }

    public function handle(): void
    {
        Log::info('[REPROCESS] TransactionsToBeReprocessedJob picked up by queue');

        if (!empty($this->dates)) {
            $transactions = PosTransaction::select('id')->whereBetween('finalised_date', $this->dates);
        } else {
            $transactions = PosTransaction::select('id')->whereIn('id', $this->transactionIds);
        }

        if($this->companyId !== null){
            $transactions = $transactions->where('company_id', $this->companyId);
        }

        $transactions = $transactions->get();

        $transactions = $transactions->chunk(5000);
        $batchJobs = [];
        foreach ($transactions as $transactionBatch) {
            $uuid = Uuid::uuid4()->toString();
            $batchJobs[] = new TransactionsToBeReprocessedBatchJob($transactionBatch->pluck('id')->toArray(), $uuid);
        }

        if(!empty($batchJobs)) {
            Bus::batch([$batchJobs])
                ->name('reprocess-transactions-' . Uuid::uuid4()->toString())
                ->dispatch();
        }

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}