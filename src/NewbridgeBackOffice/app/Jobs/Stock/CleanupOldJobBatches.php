<?php

namespace NewbridgeWeb\Jobs\Stock;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;

class CleanupOldJobBatches implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 2;

    public function __construct()
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);
    }

    public function handle()
    {
        DB::table('job_batches')
            ->where('finished_at', '<', now()->subHours(1))
            ->delete();

        DB::table('job_batches')
            ->where('created_at', '>', now()->subHours(2))
            ->delete();

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}