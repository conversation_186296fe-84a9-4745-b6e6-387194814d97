<?php

namespace NewbridgeWeb\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Redis;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Http\Controllers\Reports\ReportController;

class InstantRunReport implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    public $report;

    public function __construct($report)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);

        $this->report = json_decode(Redis::get('report-' . $report), true);
    }

    public function handle()
    {
        $this->runReport($this->report);

        MemorySpikeCheckAction::execute(self::class, 450, ['report' => $this->report]);
    }

    private function runReport($report)
    {
        \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($report): void {
            $scope->setTag('reports.id', $report['report_id']);
        });

        Redis::set('report-' . $this->report['guid'] . '-status', json_encode(['status' => 'success', 'text' => 'Queued Job Picked Up', 'percent' => '75']));

        $run = new ReportController();
        return $run->exportOrSave($report);
    }

    public function failed()
    {
        Redis::set('report-' . $this->report['guid'] . '-status', json_encode(['status' => 'error', 'text' => 'Error Processing report', 'percent' => '0']));
    }


}
