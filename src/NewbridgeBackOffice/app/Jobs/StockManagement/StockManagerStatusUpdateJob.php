<?php

namespace NewbridgeWeb\Jobs\StockManagement;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\StockSummary;
use NewbridgeWeb\Repositories\StockTransactions;

class StockManagerStatusUpdateJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(public int $id, public int $status,public Authenticatable $user)
    {
        $this->onQueue(HorizonQueues::LOW_PRIORITY);
    }

    public function handle()
    {
        $stockSummary = StockSummary::where('id', $this->id)
            ->first();

        if (empty($stockSummary)) {
            return response()->json(['message' => 'No stock summary found for this summary id'], 404);
        }

        $details = StockTransactions::where('summary_id', $this->id)->get();


        $stockSummary->status = $this->status;
        $stockSummary->save();

        foreach ($details as $detail) {
            $status = $this->status >= 2 ? 1 : 0;
            if($status != $detail->status){
                $detail->status = $status;
                $detail->save();
            }
        }

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}