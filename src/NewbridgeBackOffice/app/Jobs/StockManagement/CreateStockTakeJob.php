<?php

namespace NewbridgeWeb\Jobs\StockManagement;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Repositories\Products;
use NewbridgeWeb\Repositories\StockSummary;
use NewbridgeWeb\User;
use DB;

class CreateStockTakeJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(public StockSummary $summary, public User $user)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);
    }

    public function handle(): void
    {
        DB::transaction(function () {

            $products = Products::whereCompanyId($this->summary->company_id)
                ->whereIn('site_num', [0, $this->summary->site_num])
                ->orderBy('displayname', 'ASC')
                ->whereHas('sku')
                ->get()
                ->keyBy('id');

            $productList = implode(',', $products->keys()->toArray());
            $fromDate = $this->summary->stocktake_date;
            $productCurrentStock = DB::select("SELECT product_quantity, product_id
                FROM (
                    SELECT st.product_quantity, st.product_id, 
                           ROW_NUMBER() OVER (PARTITION BY st.product_id ORDER BY st.id DESC) AS rn
                    FROM plu_stock_transactions st
                    INNER JOIN (
                        SELECT product_id, MAX(created_at) as max_created_at
                        FROM plu_stock_transactions
                        WHERE status = 1
                            AND created_at <= '$fromDate'
                            AND product_id IN ($productList)
                        GROUP BY product_id
                    ) as latest ON st.product_id = latest.product_id 
                                 AND st.created_at = latest.max_created_at
                ) AS ranked
                WHERE rn = 1");

            $productCurrentStock = collect($productCurrentStock);
            $productCurrentStock = $productCurrentStock->keyBy('product_id');

            $siteNum = $this->summary->site_num;
            $userId =  $this->user->id;
            $createdAt = $this->summary->stocktake_date;
            $transactions = [];

            foreach ($products as $product) {
                $transactions[] = [
                    'company_id' => $userId,
                    'site_num' => $siteNum,
                    'product_id' => $product->id,
                    'quantity' => 0,
                    'units_consumed' => null,
                    'qty_entered' => 0,
                    'price' => $product->costprice,
                    'total' => 0,
                    'summary_type' => $this->summary->summary_type,
                    'summary_id' => $this->summary->id,
                    'status' => 0,
                    'supplier_id' => $product->supplier_guid,
                    'sku_guid' => $product->sku_guid,
                    'variance' => 0,
                    'variance_cost' => 0,
                    'variance_retail' => 0,
                    'variance_percentage' => 0,
                    'user_id' => $userId,
                    'previous_stock' => $productCurrentStock[$product->id]->product_quantity?? 0,
                    'reason' => null,
                    'costprice' => $product->costprice,
                    'costprice_total' => 0,
                    'created_at' => $createdAt,
                    'saleprice' => null
                ];
            }

            $chunk_data = array_chunk($transactions, 1000);
            if (!empty($chunk_data)) {
                foreach ($chunk_data as $chunk_data_val) {
                    DB::table('plu_stock_transactions')->insert($chunk_data_val);
                }
            }

            $this->summary->status = 0;
            $this->summary->save();
        });

        MemorySpikeCheckAction::execute(self::class, 300);
    }
}