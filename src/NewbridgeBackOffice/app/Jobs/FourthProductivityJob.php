<?php

namespace NewbridgeWeb\Jobs;

use Carbon\Carbon;
use GuzzleHttp\Middleware;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use NewbridgeWeb\Actions\MemorySpikeCheckAction;
use NewbridgeWeb\Enums\HorizonQueues;
use NewbridgeWeb\Exports\Fourth\LabProd\LabourProductivityExport;
use NewbridgeWeb\Http\Helpers\IntegrationsLoggingHelper;
use NewbridgeWeb\Repositories\Company;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationSites;
use Psr\Http\Message\RequestInterface;

class FourthProductivityJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    private Carbon $start;
    private Carbon $end;
    private SiteIntegrationSites $integrationSettings;
    private array $config;
    private string $path;
    private IntegrationsLoggingHelper $logger;

    public function __construct(SiteIntegrationSites $site, IntegrationsLoggingHelper $logger)
    {
        $this->onQueue(HorizonQueues::HIGH_MEMORY);

        $this->integrationSettings = $site;

        // TODO: set timezone based on company or site information
        $company = Company::find($this->integrationSettings->company_id);

        $this->start = Carbon::yesterday()->startOfDay()->addHours($company->time_offset);
        $this->end = Carbon::parse($this->start)->addHours(24);

        $this->config = $this->integrationSettings->configuration;

        $this->logger = $logger;
    }

    public function handle(): void
    {
        $this->logger->setDescription('FourthProductivityJob picked up by queue');
        $this->logger->write();

        try {
            $this->runReport();
            $this->upload();
        } catch (\Exception $e) {
            $this->logger->setException($e);
        } finally {
            $this->logger->setDescription('FourthProductivityJob ran');
            $this->logger->write();
        }

        MemorySpikeCheckAction::execute(self::class, 400);
    }

    private function setPath(): void
    {
        $root = 'Fourth/';
        $folder = 'Productivity/';
        $this->path = $root . $folder . Carbon::parse($this->start)->toDateString()
            . '-' . $this->integrationSettings->company_id
            . '-' . $this->integrationSettings->site_num . '.xml';
    }

    private function runReport(): void
    {
        $this->setPath();
        $export = new LabourProductivityExport([
            'company_id' => $this->integrationSettings->company_id,
            'site_num' => $this->integrationSettings->site_num,
            'group_guid' => $this->config['guid'],
            'location' => $this->config['locationId'],
            'start' => $this->start,
            'end' => $this->end,
            'now' => Carbon::now()
        ]);
        Storage::put($this->path, $export->run());
        $this->logger->setResource(Storage::url($this->path));
    }

    private function upload(): void
    {
        $username = $this->config['username'];
        $password = $this->config['password'];
        $url = 'https://api.fourth.com/teamhours/organisations/' . $this->config['clientId'] . '/tnasubmission';

        $content = Storage::get($this->path);

        $response = Http::withMiddleware(
            Middleware::mapRequest(function (RequestInterface $request) {
                $this->logger->setRequest($request);

                return $request;
            })
        )->withoutVerifying()
            ->withBody($content, 'application/xml')
            ->withBasicAuth($username, $password)
            ->send('POST', $url);

        if ($response->status() === 200) {
            $result = new \SimpleXMLElement($response->body());
            if ($result->ProcessedRecords !== $result->SubmittedRecords) {
                foreach ($result->Error as $error) {
                    Log::info($error);
                }
            }
        }

        $this->logger->setResponse($response);
    }
}
