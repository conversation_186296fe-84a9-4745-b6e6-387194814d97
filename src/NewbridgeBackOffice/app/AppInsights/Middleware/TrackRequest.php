<?php

namespace NewbridgeWeb\AppInsights\Middleware;

use Closure;
use Illuminate\Http\Request;
use NewbridgeWeb\AppInsights\ApplicationInsights;

class TrackRequest
{
    public function __construct(private ApplicationInsights $insights)
    {
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        if ($request->hasSession()) {
            $session = $request->session();
            // In this case, both the user id and session id are equal.
            $this->insights->setAnonymousUserId($request->session()->getId());
            $this->insights->setSessionId($request->session()->getId());

            $companyId = $session->get('user.company_id');
            $siteNum = $session->get('current_site');
            $userName = $session->get('user.username');

            if ($companyId) {
                $this->insights->addRequestProperty('company_id', $companyId);
            }
            if ($siteNum) {
                $this->insights->addRequestProperty('site_num', $siteNum);
            }
            if($userName){
                $this->insights->addRequestProperty('username', $userName);
            }
        }

        return $next($request);
    }

    public function terminate($request, $response)
    {
        foreach (config('appinsights.ignore_urls_ending_with') as $ending) {
            if (str_ends_with($request->getPathInfo(), $ending['url']) && $response->getStatusCode() == $ending['code']) {
                return; // Skip tracking
            }
        }

        $this->insights->trackRequest($request, $response);
        $this->insights->flush();
    }
}


