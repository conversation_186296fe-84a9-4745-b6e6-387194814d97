IS_DOCKER=FALSE
APP_ENV=local
APP_KEY=base64:2r7rdJapxXKeVlgs75pyQQw+ENrWnLCcZ4HlM2jn8ps=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_URL=https://newbridge-web.test
APP_PATH=https://newbridge-web.test
APP_NAME="Newbridge Backoffice (LOCAL)"
APP_VERSION="6.9.0"
CLOCKWORK_ENABLE=true

TRANSACTION_DATEFIELD=finalised_date
SESSION_SECURE_COOKIE=TRUE
DB_CONNECTION=mysql
DB_HOST=newbridge-web-database-1
DB_HOST_READ=newbridge-web-database-1
DB_PORT=3306
DB_USERNAME=newbridge
DB_PASSWORD=CHANGEME
DB_DATABASE=newbridge

WORKER_DELAY=2

BROADCAST_DRIVER=pusher
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_DRIVER=redis

ELASTICSEARCH_HOST="http://host.docker.internal:9200"
ELASTICSEARCH_SSL_VERIFICATION=false
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASSWORD=dth0ev9H
SCOUT_QUEUE=true

REDIS_SCHEME=tcp
REDIS_HOST=newbridge-web-redis-1
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DATABASE=0

REDIS_HOST_CACHE=newbridge-web-redis-1
REDIS_PASSWORD_CACHE=null
REDIS_PORT_CACHE=6379
REDIS_DATABASE_CACHE=1

REDIS_HOST_READER=newbridge-web-redis-1
REDIS_PASSWORD_READER=null
REDIS_PORT_READER=6379
REDIS_DATABASE_READER=1

PUSHER_APP_ID=NEWBRIDGE
PUSHER_APP_KEY=NEWBRIDGE
PUSHER_APP_SECRET=NEWBRIDGE
PUSHER_APP_CLUSTER=NEWBRIDGE

DEFAULT_FILESYSTEM=azure

AZURE_STORAGE_SAS_TOKEN=
AZURE_STORAGE_CONTAINER=
AZURE_STORAGE_CONTAINER_PUBLIC=
AZURE_STORAGE_URL=
AZURE_STORAGE_ENDPOINT=

AWS_SQS_KEY=
AWS_SQS_SECRET=
AWS_SQS_PREFIX=
AWS_SQS_QUEUE=development
AWS_SQS_REGION="eu-west-2"

AWS_SES_KEY=
AWS_SES_SECRET=
AWS_SES_REGION=eu-west-1

MAIL_DRIVER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Newbridge Software"

MAINTENANCE=false
MAINTENANCE_MESSAGE=""
XERO_API_KEY=""
XERO_API_SECRET=""

JWT_SECRET=Wko6ehSEmYzD2YZ1aOKtsSNZLPEl52ereOD95OpBxGRJTmvPxfvm665pV68i413g

QUICKBOOKS_CLIENT_ID=""
QUICKBOOKS_CLIENT_SECRET=""
QUICKBOOKS_API_URL=Development
QUICKBOOKS_DEBUG=true

LOG_CHANNEL=daily
CLOUDWATCH_LOG_LEVEL=info
CLOUDWATCH_LOG_GROUP_NAME=NewbridgeLocal
CLOUDWATCH_LOG_RETENTION=14
CLOUDWATCH_LOG_STREAM_NAME=NewbridgeLocal
CLOUDWATCH_LOG_NAME=NewbridgeLocalWeb
CLOUDWATCH_LOG_REGION=eu-west-2
CLOUDWATCH_LOG_VERSION=latest
CLOUDWATCH_LOG_KEY=
CLOUDWATCH_LOG_SECRET=
LOG_COMPANY_CONNECTIONS=125,186

APP_COLLECTION_TABLE_NUMBER=9999

MEWS_BASE_URL="https://api.mews-demo.com"
MEWS_CLIENT_ID=""
MEWS_CLIENT_NAME="NewbridgePOS"

LOYALTY_URL=nbloyalty.uk
LOYALTY_SCHEME=https://

EXPORT_EXCLUDED_COMMAND_TYPES=""

APPINSIGHTS_INSTRUMENTATIONKEY=

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=0.0
SENTRY_PROFILES_SAMPLE_RATE=0.0
