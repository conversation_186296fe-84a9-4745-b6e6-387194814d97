<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [
        'mysql' => [
            'read' => [
                'host' => env('DB_HOST_READ'),
            ],
            'write' => [
                'host' => env('DB_HOST'),
            ],
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'production'),
            'username' =>  env('DB_USERNAME', 'newbridge'),
            'password' =>  env('DB_PASSWORD', ''),
            'sticky'    => false,
            'driver'    => 'mysql',
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix'    => '',
            'strict' => false,
            'sslmode' => env('DB_SSL_MODE', 'require'),
            'options' => [
                PDO::MYSQL_ATTR_SSL_CA => '/var/www/ssl/BaltimoreCyberTrustRoot.crt.pem',
                PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
                PDO::MYSQL_ATTR_COMPRESS => true
            ],
        ],

        'testing' => [
            'host' => env('TEST_DB_HOST', '127.0.0.1'),
            'port' => env('TEST_DB_PORT', '3307'),
            'database' => env('TEST_DB_DATABASE', 'newbridge_test'),
            'username' =>  env('DB_USERNAME', 'newbridge'),
            'password' =>  env('DB_PASSWORD', 'secret'),
            'sticky'    => false,
            'driver'    => 'mysql',
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix'    => '',
            'strict' => false
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */
    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */
    'redis' => [
        'client' => env('REDIS_CLIENT', 'phpredis'),
        'options' => [
            'persistent' => true,
        ],
        'cache' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'port' => env('REDIS_PORT', 6379),
            'password' => env('REDIS_PASSWORD', null),
            'database' => env('REDIS_DATABASE', 0),
            'read_timeout' => 60,
            'scheme' => env('REDIS_SCHEME', 'tls')
        ],

        'default' => [
            'host' => env('REDIS_HOST_CACHE', '127.0.0.1'),
            'port' => env('REDIS_PORT_CACHE', 6379),
            'password' => env('REDIS_PASSWORD_CACHE', null),
            'database' => env('REDIS_DATABASE_CACHE', 1),
            'read_timeout' => 60,
            'scheme' => env('REDIS_SCHEME', 'tls')
        ]
    ]
];
