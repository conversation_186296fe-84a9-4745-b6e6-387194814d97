<?php

return [
    'host' => env('ELAST<PERSON><PERSON>ARCH_HOST'),
    'user' => env('ELASTICSEARCH_USER'),
    'password' => env('ELASTICSEARCH_PASSWORD'),
    'cloud_id' => env('<PERSON>LA<PERSON><PERSON><PERSON><PERSON><PERSON>_CLOUD_ID'),
    'api_key' => env('ELASTICSEARCH_API_KEY'),
    'ssl_verification' => env('ELASTICSEARCH_SSL_VERIFICATION', true),
    'queue' => [
        'timeout' => env('SCOUT_QUEUE_TIMEOUT'),
    ],
    'indices' => [
        'mappings' => [
            'default' => [
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'search_extensions' => [
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                    'type' => [
                        'type' => 'keyword',
                    ],
                    'title' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'info' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'url' => [
                        'type' => 'keyword',
                    ],
                    'keywords' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'products' => [
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                    'virtual_id' => [
                        'type' => 'keyword',
                    ],
                    'short_desc' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'virtual_short_desc' => [
                        'type' => 'keyword',
                    ],
                    'displayname' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'virtual_displayname' => [
                        'type' => 'keyword',
                    ],
                    'department' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'subdepartment' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'barcode' => [
                        'type' => 'keyword',
                    ],
                    'ean13' => [
                        'type' => 'keyword',
                    ],
                    'company_id' => [
                        'type' => 'keyword',
                    ],
                    'site_num' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'transactions' => [
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                    'virtual_id' => [
                        'type' => 'keyword',
                    ],
                    'reference' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'trans_datatime' => [
                        'type' => 'date',
                    ],
                    'order_number' => [
                        'type' => 'keyword',
                    ],
                    'virtual_order_number' => [
                        'type' => 'keyword',
                    ],
                    'table' => [
                        'type' => 'keyword',
                    ],
                    'virtual_table' => [
                        'type' => 'keyword',
                    ],
                    'concat_order_no' => [
                        'type' => 'keyword',
                    ],
                    'employee_no' => [
                        'type' => 'keyword',
                    ],
                    'virtual_employee_no' => [
                        'type' => 'keyword',
                    ],
                    'terminal_num' => [
                        'type' => 'keyword',
                    ],
                    'clerk' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'virtual_clerk' => [
                        'type' => 'keyword',
                    ],
                    'subtotal' => [
                        'type' => 'float',
                    ],
                    'total' => [
                        'type' => 'float',
                    ],
                    'method' => [
                        'type' => 'keyword',
                    ],
                    'room' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'customers' => [
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                    'virtual_id' => [
                        'type' => 'keyword',
                    ],
                    'membership_no' => [
                        'type' => 'keyword',
                    ],
                    'virtual_membership_no' => [
                        'type' => 'keyword',
                    ],
                    'first_name' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'last_name' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'full_name' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'virtual_full_name' => [
                        'type' => 'keyword',
                    ],
                    'email' => [
                        'type' => 'keyword',
                    ],
                    'virtual_email' => [
                        'type' => 'keyword',
                    ],
                    'telephone' => [
                        'type' => 'keyword',
                    ],
                    'virtual_telephone' => [
                        'type' => 'keyword',
                    ],
                    'virtual_telephone_2' => [
                        'type' => 'keyword',
                    ],
                    'group' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'virtual_group' => [
                        'type' => 'keyword',
                    ],
                    'type' => [
                        'type' => 'text',
                        'analyzer' => 'standard',
                    ],
                    'virtual_type' => [
                        'type' => 'keyword',
                    ],
                ],
            ]
        ],
        'settings' => [
            'default' => [
                'number_of_shards' => 2,
                'number_of_replicas' => env('APP_ENV') === 'local' ? 0 : 1,
            ],
            'transactions' => [
                'number_of_shards' => 4,
                'number_of_replicas' => env('APP_ENV') === 'local' ? 0 : 1,
            ],
        ],
    ],
];