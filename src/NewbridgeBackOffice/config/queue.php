<?php

use NewbridgeWeb\Enums\HorizonQueues;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Driver
    |--------------------------------------------------------------------------
    |
    | Laravel's queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same
    | syntax for each one. Here you may set the default queue driver.
    |
    | Supported: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'default' => env('QUEUE_DRIVER', 'sync'),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with Laravel. You are free to add more.
    |
    */

    'connections' => [

        'sync' => [
            'driver' => 'sync',
        ],

        'redis' => [
            'driver' => 'redis',
            'connection' => 'default',
            'retry_after' => 600,
            'queue' => HorizonQueues::LOW_PRIORITY,
            'http' => [
                'timeout' => 100000,
                'connect_timeout' => 60
            ]
        ],

        'redis-stock' => [
            'driver' => 'redis',
            'connection' => 'default',
            'retry_after' => 600,
            'queue' => HorizonQueues::STOCK,
            'http' => [
                'timeout' => 100000,
                'connect_timeout' => 60
            ]
        ],

        'redis-high-memory' => [
            'driver' => 'redis',
            'connection' => 'default',
            'retry_after' => 600,
            'queue' => HorizonQueues::HIGH_MEMORY,
            'http' => [
                'timeout' => 100000,
                'connect_timeout' => 60
            ]
        ],

        'redis-high-memory-background' => [
            'driver' => 'redis',
            'connection' => 'default',
            'retry_after' => 600,
            'queue' => HorizonQueues::HIGH_MEMORY_BACKGROUND,
            'http' => [
                'timeout' => 100000,
                'connect_timeout' => 60
            ]
        ],

        'redis-high-priority' => [
            'driver' => 'redis',
            'connection' => 'default',
            'retry_after' => 600,
            'queue' => HorizonQueues::HIGH_PRIORITY,
            'http' => [
                'timeout' => 100000,
                'connect_timeout' => 60
            ]
        ],

        'redis-low-priority' => [
            'driver' => 'redis',
            'connection' => 'default',
            'retry_after' => 600,
            'queue' => HorizonQueues::LOW_PRIORITY,
            'http' => [
                'timeout' => 100000,
                'connect_timeout' => 60
            ]
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed' => [
        'database' => env('DB_CONNECTION', 'mysql'),
        'table' => 'failed_jobs',
        'driver' => 'database-uuids',
    ],

];
