<?php

return [
    'service_bus_url' => env('SERVICE_BUS_URL', ''),

    'service_bus_sas_token' => env('SERVICE_BUS_SAS_TOKEN', ''),

    'send_to_event_grid' => env('SEND_TO_EVENT_GRID', false),

    'send_to_service_bus' => env('SEND_TO_SERVICE_BUS', false),

    'image_path' => env('AZURE_STORAGE_ENDPOINT').'/'.env('AZURE_STORAGE_CONTAINER_PUBLIC').'/',

    'xero_api_key' => env('XERO_API_KEY', 'VDB9TWC8RQ8QGREQNFSZ0UOGW1FEJD'),

    'xero_api_secret' => env('XERO_API_SECRET', '6SPVQBHSEMDMZPUX91J8LJ9LPLSWKI'),

    'maintenance' => env('MAINTENANCE'),

    'maintenance_message' => env('MAINTENANCE_MESSAGE', ''),

    'exclude_commands' => env('EXCLUDE_COMMANDS', []),

    'default_company_id' => env('DEFAULT_COMPANY_ID', 127),

    'report_expire_time' => env('REPORT_EXPIRE_TIME', 60),

    'event_grid_url' => env('EVENT_GRID_URL'),

    'event_grid_source' => env('EVENT_GRID_SOURCE'),

    'event_grid_key' => env('EVENT_GRID_KEY'),

    'internal_api_key' => env('INTERNAL_API_KEY'),

    'is_docker' => env('IS_DOCKER'),

    'is_worker' => env('IS_WORKER'),

    'maintenance_allowed_token' =>  env('MAINTENANCE_ALLOWED_TOKEN', 'b7wKdh6nM4IO'),

    'worker_delay' => env('WORKER_DELAY', 180),

    'log_company_connections' => env('LOG_COMPANY_CONNECTIONS', false),

    'button_update_limit' => env('BUTTON_UPDATE_LIMIT', 2000),

    'blob_url' => env('BLOB_URL'),

    'blob_token' => env('BLOB_TOKEN'),

    'max_event_size' => env('MAX_EVENT_SIZE', 1024),

    'max_sb_message_size' => env('MAX_SB_MESSAGE_SIZE', 200),

    'max_queue_reports' => env('MAX_QUEUE_REPORTS', 5),

    'uploader_maint' => env('uploader_maint', false),

    'app_collection_table_number' => env('APP_COLLECTION_TABLE_NUMBER', 9999),

    'product_stock_chunk_size' => env('PRODUCT_STOCK_CHUNK_SIZE', 10000),

    'stock_calculation_chunk_size' => env('STOCK_CALCULATION_CHUNK_SIZE', 10000),

    'cameron_email' => '<EMAIL>',

    'support_email' => '<EMAIL>',

    'external_stock_orders_key' => env('EXTERNAL_STOCK_ORDERS_KEY', 'cf26052f-9baf-41a7-bd6e-812768b0c52f'),

    'memory_speak_alert_level' => env('MEMORY_SPEAK_ALERT_LEVEL', 512), # In MB

    'replays_session_sample_rate' => env('SENTRY_REPLAYS_SESSION_SAMPLE_RATE') === null ? 0 : (float) env('SENTRY_REPLAYS_SESSION_SAMPLE_RATE'),

    'replays_on_error_sample_rate' => env('SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE') === null ? 0 : (float) env('SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE'),

    'sentry_sampler_patterns' => env('SENTRY_SAMPLER_PATTERNS', ''),

    'sentry_js_traces_sample_rate' => env('SENTRY_JS_TRACES_SAMPLE_RATE') === null ? 0 : (float) env('SENTRY_JS_TRACES_SAMPLE_RATE'),

    'sentry_js_profiles_sample_rate' => env('SENTRY_JS_PROFILES_SAMPLE_RATE') === null ? 0 : (float) env('SENTRY_JS_PROFILES_SAMPLE_RATE'),

    'sentry_js_ignore_transactions' => [
        '/',
        '/screens',
        '/transactions',
        '/reports'
    ],
];
