<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" bootstrap="bootstrap/autoload.php" colors="true" processIsolation="false" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.4/phpunit.xsd" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <testsuites>
    <testsuite name="Architecture Tests">
      <directory suffix="Test.php">./tests/Architecture</directory>
    </testsuite>
    <testsuite name="Feature Tests">
      <directory suffix="Test.php">./tests/Feature</directory>
    </testsuite>
    <testsuite name="Unit Tests">
      <directory suffix="Test.php">./tests/Unit</directory>
    </testsuite>
  </testsuites>
  <php>
    <env name="APP_ENV" value="testing"/>
    <env name="LOG_CHANNEL" value="stderr"/>
    <env name="DB_CONNECTION" value="testing" force="true"/>
    <env name="CACHE_DRIVER" value="redis"/>
    <env name="SESSION_DRIVER" value="redis"/>
    <env name="QUEUE_DRIVER" value="sync"/>
    <env name="TEST_DB_HOST" value="127.0.0.1"/>
    <env name="REDIS_HOST" value="127.0.0.1"/>
    <env name="REDIS_HOST_CACHE" value="127.0.0.1"/>
    <env name="REDIS_HOST_READER" value="127.0.0.1"/>
    <env name="REDIS_DATABASE" value="5"/>
    <env name="REDIS_DATABASE_CACHE" value="6"/>
  </php>
  <source>
    <include>
      <directory suffix=".php">./app</directory>
    </include>
  </source>
</phpunit>
