<?php

use Illuminate\Support\Facades\Route;

Route::get('/receipts', 'ReceiptController@table')->middleware(['ability:newbridge|reseller|owner,receipt_layouts']);
Route::get('/receipts/data/{site_num}', 'ReceiptController@data')->middleware(['ability:newbridge|reseller|owner,receipt_layouts']);
Route::get('/receipts/design/{id}/{site_num}', 'ReceiptController@design')->middleware(['ability:newbridge|reseller|owner,receipt_layouts']);
Route::post('/receipts/design/{id}/{site_num}', 'ReceiptController@postDesign')->middleware(['ability:newbridge|reseller|owner,receipt_layouts']);
