<?php

use Illuminate\Support\Facades\Route;
use NewbridgeWeb\Http\Controllers\Tax\TaxRateController;
use NewbridgeWeb\Http\Controllers\Tax\TaxRuleController;

/* TAX RATES */
Route::get('/taxrates', [TaxRateController::class, 'index'])->middleware(['ability:newbridge|reseller|owner,read_tax_rates']);
Route::get('/taxrate/data', [TaxRateController::class, 'getTaxRateData'])->middleware(['ability:newbridge|reseller|owner,read_tax_rates']);
Route::put('/taxrate/edit', [TaxRateController::class, 'edit'])->middleware(['ability:newbridge|reseller|owner,write_tax_rates']);
Route::put('/taxrate/edit-single', [TaxRateController::class, 'editById'])->middleware(['ability:newbridge|reseller|owner,write_tax_rates']);
Route::post('/taxrate/create', [TaxRateController::class, 'add'])->middleware(['ability:newbridge|reseller|owner,write_tax_rates']);
Route::delete('/taxrate/delete/{id}', [TaxRateController::class, 'delete'])->middleware(['ability:newbridge|reseller|owner,write_tax_rates']);
Route::get('/taxrate/edit-modal/{id}', [TaxRateController::class, 'editModal'])->middleware(['ability:newbridge|reseller|owner,write_tax_rates']);

Route::get('/tax/rules', [TaxRuleController::class, 'index']);
Route::get('/tax/rules/create', [TaxRuleController::class, 'getCreate']);
Route::get('/tax/rules/data', [TaxRuleController::class, 'getData']);
Route::post('/tax/rules/create', [TaxRuleController::class, 'postCreate']);
Route::get('/tax/rules/edit/{id}', [TaxRuleController::class, 'getEdit']);
Route::post('/tax/rules/edit/{id}', [TaxRuleController::class, 'postEdit']);
Route::delete('/tax/rules/delete/{id}', [TaxRuleController::class, 'delete']);

/** Day Parts */
Route::get('/day-parts', 'DayPartsController@index')->middleware(['ability:newbridge|reseller|owner,read_skus']);
Route::get('/day-parts/data', 'DayPartsController@data')->middleware(['ability:newbridge|reseller|owner,read_skus']);
Route::get('/day-parts/create', 'DayPartsController@getCreate')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::post('/day-parts/create', 'DayPartsController@create')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::get('/day-parts/edit/{id}', 'DayPartsController@getEdit')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::post('/day-parts/edit/{id}', 'DayPartsController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::get('/day-parts/edit-modal/{id}', 'DayPartsController@editModal')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::delete('/day-parts/delete/{id}', 'DayPartsController@delete')->middleware(['ability:newbridge|reseller|owner,write_skus']);

/** Revenue Centres */
Route::get('/company/revenue-centers', 'RevenueCentersController@index')->middleware(['ability:newbridge|reseller|owner,read_skus']);
Route::get('/company/revenue-centers/data', 'RevenueCentersController@data')->middleware(['ability:newbridge|reseller|owner,read_skus']);
Route::get('/company/revenue-centers/create', 'RevenueCentersController@getCreate')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::post('/company/revenue-centers/create', 'RevenueCentersController@postCreate')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::get('/company/revenue-centers/edit/{id}', 'RevenueCentersController@getEdit')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::post('/company/revenue-centers/edit/{id}', 'RevenueCentersController@postEdit')->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::delete('/company/revenue-centers/delete/{id}', 'RevenueCentersController@delete')->middleware(['ability:newbridge|reseller|owner,write_skus']);

/**
 * Sub Departments
 */

Route::get('/sub-departments', 'SubDepartmentController@index')->middleware(['ability:newbridge|reseller|owner,read_subdepartments']);
Route::get('/sub-departments/data', 'SubDepartmentController@data')->middleware(['ability:newbridge|reseller|owner,read_subdepartments']);
Route::post('/sub-departments/create', 'SubDepartmentController@create')->middleware(['ability:newbridge|reseller|owner,write_subdepartments']);
Route::put('/sub-departments/edit', 'SubDepartmentController@edit')->middleware(['ability:newbridge|reseller|owner,write_subdepartments']);
Route::put('/sub-departments/edit-single', 'SubDepartmentController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_subdepartments']);
Route::get('/sub-departments/edit-modal/{id}', 'SubDepartmentController@editModal')->middleware(['ability:newbridge|reseller|owner,write_subdepartments']);
Route::delete('/sub-departments/delete/{id}', 'SubDepartmentController@delete')->middleware(['ability:newbridge|reseller|owner,write_subdepartments']);
Route::get('/sub-departments/product-priority/{id}', 'SubDepartmentController@productOrder')->middleware(['ability:newbridge|reseller|owner,write_subdepartments']);
Route::post('/sub-departments/product-priority/{id}', 'SubDepartmentController@updateProductOrder')->middleware(['ability:newbridge|reseller|owner,write_subdepartments']);

/**
 * Course Separators
 */
Route::get('/course-separators', 'CourseSeparatorController@index')->middleware(['ability:newbridge|reseller|owner,read_course_separators']);
Route::get('/course-separators/data', 'CourseSeparatorController@data')->middleware(['ability:newbridge|reseller|owner,read_course_separators']);
Route::post('/course-separators/create', 'CourseSeparatorController@create')->middleware(['ability:newbridge|reseller|owner,write_course_separators']);
Route::put('/course-separators/edit', 'CourseSeparatorController@edit')->middleware(['ability:newbridge|reseller|owner,write_course_separators']);
Route::put('/course-separators/edit-single', 'CourseSeparatorController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_course_separators']);
Route::get('/course-separators/edit-modal/{id}', 'CourseSeparatorController@editModal')->middleware(['ability:newbridge|reseller|owner,write_course_separators']);
Route::delete('/course-separators/delete/{id}', 'CourseSeparatorController@delete')->middleware(['ability:newbridge|reseller|owner,write_course_separators']);


/** DEPARTMENTS **/
Route::get('/departments', 'DepartmentController@index')->middleware(['ability:newbridge|reseller|owner,read_departments']);
Route::get('/department/data', 'DepartmentController@getDepartmentData')->middleware(['ability:newbridge|reseller|owner,read_departments']);
Route::put('/department/edit', 'DepartmentController@edit')->middleware(['ability:newbridge|reseller|owner,write_departments']);
Route::put('/department/edit-single', 'DepartmentController@editById')->middleware(['ability:newbridge|reseller|owner,write_departments']);
Route::post('/department/create', 'DepartmentController@add')->middleware(['ability:newbridge|reseller|owner,write_departments']);
Route::delete('/department/delete', 'DepartmentController@delete')->middleware(['ability:newbridge|reseller|owner,write_departments']);
Route::get('/department/edit-modal/{id}', 'DepartmentController@editModal')->middleware(['ability:newbridge|reseller|owner,write_departments']);


/** Product Screens Here **/
Route::get('products', 'ProductController@index')->middleware(['ability:newbridge|reseller|owner,read_products']);
Route::get('products/stock-html/{id}', 'ProductController@getStockHtml')->middleware(['ability:newbridge|reseller|owner,read_products']);
Route::post('products/update-inline/{idx}', 'ProductController@updateInline')->middleware(['ability:newbridge|reseller|owner,write_products']);

Route::get('/products/create', 'ProductController@create')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::get('/products/data', 'ProductController@getProductData')->middleware(['ability:newbridge|reseller|owner,read_products']);
Route::put('/products/edit', 'ProductController@editProducts')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::post('/products/edit-single', 'ProductController@editProduct')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::put('/products/edit-mass', 'ProductController@massEditProducts')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::post('/products/create', 'ProductController@addProducts')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::delete('/products/delete/{id}', 'ProductController@deleteProduct')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::get('/products/edit/{id}', 'ProductController@editProductModal')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::post('/products/edit-ids', 'ProductController@storeIds')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::get('/products/mass-edit', 'ProductController@massEdit')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::get('/products/mass-edit/{string}', 'ProductController@massEdit')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::get('products/activity/{id}', 'ProductController@activity')->middleware(['ability:newbridge|reseller|owner,read_products']);
Route::get('products/replicate/{id}', 'ProductController@replicate')->middleware(['ability:newbridge|reseller|owner,read_products']);

Route::post('/products/barcodes/data', 'ProductController@barcodesToRedis')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::get('/products/barcodes/print', 'ProductController@printBarcodes')->middleware(['ability:newbridge|reseller|owner,write_products']);

Route::post('/products/print-amount/{ids}', 'ProductController@printAmountModal')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::get('/products/department/{id}', 'ProductController@getProductsByDepartment')->middleware(['ability:newbridge|reseller|owner,read_products']);
Route::get('/products/subdepartment/{id}', 'ProductController@getProductsBySubDepartment')->middleware(['ability:newbridge|reseller|owner,read_products']);
Route::get('/products/add-child-row', 'ProductController@addChildRow')->middleware(['ability:newbridge|reseller|owner,read_products']);

Route::get('/products/price-levels', 'PriceLevelController@index')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::post('/products/price-levels', 'PriceLevelController@update')->middleware(['ability:newbridge|reseller|owner,write_products']);

Route::get('/products/skus', 'SkuMassApplyController@index')->middleware(['ability:newbridge|reseller|owner,write_products']);
Route::post('/products/skus', 'SkuMassApplyController@update')->middleware(['ability:newbridge|reseller|owner,write_products']);

Route::post('/products/queue', 'ProductController@queue')->middleware(['ability:newbridge|reseller|owner,write_products']);

Route::get('/products/list-modifiers/{id}', 'ProductController@getListModifiers')->middleware(['ability:newbridge|reseller|owner,write_products']);
