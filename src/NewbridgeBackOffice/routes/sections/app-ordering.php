<?php

use Illuminate\Support\Facades\Route;
use NewbridgeWeb\Http\Controllers\Orders\OrdersController;

Route::group(['middleware' => ['auth']], function () {
    Route::get('/products/app-availability', 'Ordoo\OrdooUI@index');
    Route::post('/products/update-availability', 'Ordoo\OrdooUI@update');

    Route::get('/ordoo/multi-site-auth', 'Ordoo\Auth\MultiSiteAuth@index');
    Route::get('/ordoo/auth/{site_num?}', 'Ordoo\Auth\OrdooAuth@isAuthenticated');
    Route::get('/ordoo/callback/{site_num?}', 'Ordoo\Auth\OrdooAuth@callback');
});


Route::get('/web-orders', [OrdersController::class, 'indexBlocks'])->middleware(['ability:newbridge|reseller|owner,view_transactions']);
Route::get('/web-orders/table',  [OrdersController::class, 'index'])->middleware(['ability:newbridge|reseller|owner,view_transactions']);
Route::post('/web-orders/update-status',  [OrdersController::class, 'updateStatus'])->middleware(['ability:newbridge|reseller|owner,view_transactions']);
Route::get('/web-orders/poll',  [OrdersController::class, 'poll'])->middleware(['ability:newbridge|reseller|owner,view_transactions']);
Route::get('/web-orders/{id}',  [OrdersController::class, 'view'])->middleware(['ability:newbridge|reseller|owner,view_transactions']);
Route::get('/web-orders/complete/{id}',  [OrdersController::class, 'complete'])->middleware(['ability:newbridge|reseller|owner,view_transactions']);
