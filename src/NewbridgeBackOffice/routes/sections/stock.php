<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use NewbridgeWeb\Http\Controllers\ProductController;
use NewbridgeWeb\Http\Controllers\SkuController;
use NewbridgeWeb\Http\Controllers\Stock\OrderController;
use NewbridgeWeb\Http\Controllers\Stock\StartProductsStockCalculationController;
use NewbridgeWeb\Http\Controllers\Stock\StockController;
use NewbridgeWeb\Http\Controllers\SubDepartmentController;
use NewbridgeWeb\Http\Controllers\SupplierController;
use NewbridgeWeb\Http\Helpers\StockPeriodHelper;

Route::get('/stock', [StockController::class, 'index'])->middleware(['ability:newbridge|reseller|owner,read_stock']);
Route::post('/stock/current-stock', [ProductController::class, 'currentStockByIds']);
Route::post('/stock/products-calculations', [StartProductsStockCalculationController::class, '__invoke'])->middleware(['role:newbridge'])->name('stock.products.calculation.start');

Route::get('/stock/count-sheet', [StockController::class, 'countSheet'])->middleware(['ability:newbridge|reseller|owner,read_stock']);
Route::post('/stock/count-sheet/data', [StockController::class, 'loadCountSheet'])->middleware(['ability:newbridge|reseller|owner,read_stock']);
Route::get('/stock/count-sheet/print', [StockController::class, 'printCountSheet'])->middleware(['ability:newbridge|reseller|owner,read_stock']);

Route::get('/stock/sub-department-priority', [SubDepartmentController::class,'stockOrder'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::post('/subdepartments/update-priority', [SubDepartmentController::class,'updatePriority'])->middleware(['ability:newbridge|reseller|owner,write_stock']);

// pending stock records
Route::post('/stock/check-pending', [StockController::class, 'checkStockPending'])->middleware(['ability:newbridge|reseller|owner,read_orders']);

Route::get('/stock/current-stock/{product_id}/{datetime?}', [OrderController::class, 'getStockForProduct'])->middleware(['ability:newbridge|reseller|owner,read_orders']);

Route::get('/stock/orders', [OrderController::class, 'index'])->middleware(['ability:newbridge|reseller|owner,read_orders']);
Route::get('/stock/orders/data', [OrderController::class, 'data'])->middleware(['ability:newbridge|reseller|owner,read_orders']);
Route::get('/stock/orders/undeliver/{id}', [OrderController::class, 'undeliver'])->middleware(['ability:newbridge|reseller|owner,write_orders']);
Route::get('/stock/orders/create/{suggested?}', [OrderController::class, 'getCreate'])->middleware(['ability:newbridge|reseller|owner,write_orders']);
Route::get('/stock/orders/edit/{id}', [OrderController::class, 'getEdit'])->middleware(['ability:newbridge|reseller|owner,write_orders']);
Route::post('/stock/orders/update/{id}', [OrderController::class, 'postUpdate'])->middleware(['ability:newbridge|reseller|owner,write_orders']);
Route::delete('/stock/orders/delete/{id}', [OrderController::class, 'delete'])->middleware(['ability:newbridge|reseller|owner,write_orders']);
Route::post('/stock/orders/create', [OrderController::class, 'postCreate'])->middleware(['ability:newbridge|reseller|owner,write_orders']);
Route::post('/stock/orders/check-matches', [OrderController::class, 'postCheck'])->middleware(['ability:newbridge|reseller|owner,write_orders']);

Route::get('/stock/orders/view/{id}', [OrderController::class, 'view'])->middleware(['ability:newbridge|reseller|owner,read_orders']);

Route::get('/stock/orders/suggest-order/{supplier}', [OrderController::class, 'suggestOrder'])->middleware(['ability:newbridge|reseller|owner,write_orders']);
Route::get('/stock/orders/supplier-order/{supplier}', [OrderController::class, 'createSupplierOrder'])->middleware(['ability:newbridge|reseller|owner,write_orders']);

Route::get('/getstock', [StockController::class, 'getProducts'])->middleware(['ability:newbridge|reseller|owner,read_stock']);
Route::get('/stock/{module}', [StockController::class, 'index'])->middleware(['ability:newbridge|reseller|owner,read_stock'])->name('stocktake.index');
Route::get('/stock/export/{module}/{id}/{format?}', [StockController::class, 'export'])->middleware(['ability:newbridge|reseller|owner,read_stock']);

Route::get('/stock/{module}/products', [StockController::class, 'products'])->middleware(['ability:newbridge|reseller|owner,read_stock']);
Route::get('/stock/{module}/subdepartments', [StockController::class, 'subdepartments'])->middleware(['ability:newbridge|reseller|owner,read_stock']);

Route::get('/stock/{module}/data', [StockController::class, 'data'])->middleware(['ability:newbridge|reseller|owner,read_stock']);
Route::get('/stock/{module}/create/{date?}', [StockController::class, 'form'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::get('/stock/{module}/pre-create/{date?}', [StockController::class, 'stocktakeCreate'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::post('/stock/{module}/pre-create/{date?}', [StockController::class, 'stocktakePreCreate'])->middleware(['ability:newbridge|reseller|owner,write_stock'])->name('stocktake.preCreate.post');
Route::post('/stock/{module}/create', [StockController::class, 'create'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::get('/stock/stocktake/completion/{stockSummary}/{completed}', [StockController::class, 'setCompletionStatus'])->middleware(['ability:newbridge|reseller|owner,write_stock'])
    ->name('stocktake.completion.set');
Route::get('/stock/{module}/edit/{id}', [StockController::class, 'editForm'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::post('/stock/{module}/edit/{id}', [StockController::class, 'edit'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::get('/stock/{module}/view/{id}', [StockController::class, 'view'])->middleware(['ability:newbridge|reseller|owner,read_stock'])->name('stock.view');
Route::delete('/stock/{module}/delete/{id}', [StockController::class, 'delete'])->middleware(['ability:newbridge|reseller|owner,write_stock']);

Route::post('/stock/drafts', [StockController::class, 'saveRedisHashMap'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::post('/stock/drafts/check', [StockController::class, 'checkIfSaved'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::post('/stock/drafts/restore', [StockController::class, 'getDraft'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::put('/stock/drafts', [StockController::class, 'updateRedisHashMap'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::put('/stock/drafts/meta', [StockController::class, 'updateRedisHashMapMeta'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::delete('/stock/drafts/remove', [StockController::class, 'deleteFromRedisHashMap'])->middleware(['ability:newbridge|reseller|owner,write_stock']);
Route::delete('/stock/drafts/clear', [StockController::class, 'deleteRedisHashMap'])->middleware(['ability:newbridge|reseller|owner,write_stock']);

Route::get('/suppliers', [SupplierController::class, 'index'])->middleware(['ability:newbridge|reseller|owner,read_suppliers']);
Route::get('/suppliers/data', [SupplierController::class, 'data'])->middleware(['ability:newbridge|reseller|owner,read_suppliers']);
Route::post('/suppliers/create', [SupplierController::class, 'create'])->middleware(['ability:newbridge|reseller|owner,write_suppliers']);
Route::put('/suppliers/edit', [SupplierController::class, 'edit'])->middleware(['ability:newbridge|reseller|owner,write_suppliers']);
Route::put('/suppliers/edit-single', [SupplierController::class, 'editSingle'])->middleware(['ability:newbridge|reseller|owner,write_suppliers']);
Route::get('/suppliers/edit-modal/{id}', [SupplierController::class, 'editModal'])->middleware(['ability:newbridge|reseller|owner,write_suppliers']);
Route::delete('/suppliers/delete/{id}', [SupplierController::class, 'delete'])->middleware(['ability:newbridge|reseller|owner,write_suppliers']);
Route::get('/suppliers/get-email/{id}', [SupplierController::class, 'getEmail'])->middleware(['ability:newbridge|reseller|owner,read_suppliers']);

Route::get('/skus', [SkuController::class, 'index'])->middleware(['ability:newbridge|reseller|owner,read_skus']);
Route::get('/skus/data', [SkuController::class, 'data'])->middleware(['ability:newbridge|reseller|owner,read_skus']);
Route::post('/skus/create', [SkuController::class, 'create'])->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::put('/skus/edit', [SkuController::class, 'edit'])->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::put('/skus/edit-single', [SkuController::class, 'editSingle'])->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::get('/skus/edit-modal/{id}', [SkuController::class, 'editModal'])->middleware(['ability:newbridge|reseller|owner,write_skus']);
Route::delete('/skus/delete/{id}', [SkuController::class, 'delete'])->middleware(['ability:newbridge|reseller|owner,write_skus']);


Route::post('/stock/get-periods', static function (Request $request) {
    return StockPeriodHelper::get($request->input('site'));
})->middleware(['ability:newbridge|reseller|owner,report_stock_period']);
