<?php

use Illuminate\Support\Facades\Route;

/* Promotions */
Route::get('/promotions', 'PromotionController@index')->middleware(['ability:newbridge|reseller|owner,read_promotions']);
Route::get('/promotions/data', 'PromotionController@data')->middleware(['ability:newbridge|reseller|owner,read_promotions']);
Route::get('/promotions/create', 'PromotionController@createForm')->middleware(['ability:newbridge|reseller|owner,write_promotions']);
Route::post('/promotions/create', 'PromotionController@create')->middleware(['ability:newbridge|reseller|owner,write_promotions']);
Route::get('/promotions/edit/{id}', 'PromotionController@editForm')->middleware(['ability:newbridge|reseller|owner,write_promotions']);
Route::put('/promotions/edit', 'PromotionController@edit')->middleware(['ability:newbridge|reseller|owner,write_promotions']);
Route::put('/promotions/edit-single/{id}', 'PromotionController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_promotions']);
Route::post('/promotions/product-html/{promo?}', 'PromotionController@getProductLevelDiscountHTML')->middleware(['ability:newbridge|reseller|owner,write_promotions']);
Route::get('/promotions/edit-modal/{id}', 'PromotionController@editModal')->middleware(['ability:newbridge|reseller|owner,write_promotions']);
Route::delete('/promotions/delete/{id}', 'PromotionController@delete')->middleware(['ability:newbridge|reseller|owner,write_promotions']);

Route::get('/promotions/images/{id}', 'PromotionController@editImage')->middleware(['ability:newbridge|owner,write_promotions']);
Route::post('/promotions/save-image/{id}', 'PromotionController@saveImage')->middleware(['ability:newbridge|owner,write_promotions']);

/* Discounts */
Route::get('/discounts', 'DiscountController@index')->middleware(['ability:newbridge|reseller|owner,read_discounts']);
Route::get('/discounts/data', 'DiscountController@data')->middleware(['ability:newbridge|reseller|owner,read_discounts']);
Route::post('/discounts/create', 'DiscountController@create')->middleware(['ability:newbridge|reseller|owner,write_discounts']);
Route::put('/discounts/edit', 'DiscountController@edit')->middleware(['ability:newbridge|reseller|owner,write_discounts']);
Route::put('/discounts/edit-single/{id}', 'DiscountController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_discounts']);
Route::get('/discounts/edit-modal/{id}', 'DiscountController@editModal')->middleware(['ability:newbridge|reseller|owner,write_discounts']);
Route::delete('/discounts/delete/{id}', 'DiscountController@delete')->middleware(['ability:newbridge|reseller|owner,write_discounts']);
