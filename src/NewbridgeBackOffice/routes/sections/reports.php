<?php

use Illuminate\Support\Facades\Route;
use NewbridgeWeb\Http\Controllers\Reports\ReportController;

Route::get('/reports', 'Reports\ReportController@index');
Route::get('/reports/check-status/{guid}', [ReportController::class, 'checkStatus']);

/** REPORT SCHEDULES **/
Route::get('/reports/schedules', 'Reports\Scheduling@index')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::get('/reports/schedules/data', 'Reports\Scheduling@data')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::get('/reports/schedules/create', 'Reports\Scheduling@create')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::post('/reports/schedules/create', 'Reports\Scheduling@insert')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::post('/reports/schedules/update/{id}', 'Reports\Scheduling@update')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::get('/reports/schedules/edit/{id}', 'Reports\Scheduling@edit')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::get('/reports/schedules/results/{id}', 'Reports\Scheduling@results')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::post('/reports/schedules/filters/{id}/{type}/{scheduleid?}', 'Reports\Scheduling@getFilters')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::delete('/reports/schedules/delete/{id}', 'Reports\Scheduling@delete')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::post('/reports/schedules/filters/report/{id}', 'Reports\Scheduling@getReportDataById')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);
Route::get('/reports/schedules/{id}/schedule-filters', 'Reports\Scheduling@getScheduledReportFilters')->middleware(['ability:newbridge|reseller|owner,schedule_reports']);

Route::get('/reports/download', 'Reports\ReportController@download');
Route::get('/reports/{module}', 'Reports\ReportController@form');
Route::post('/reports/run/{save?}/{format?}', 'Reports\ReportController@run');
Route::post('/reports/export/{save}/{format}', 'Reports\ReportController@run');
