<?php

use Illuminate\Support\Facades\Route;

Route::post('/swap-site', 'SiteSelectController@swapSites')->middleware(['ability:newbridge|reseller|owner,company_management|switch_company']);

Route::get('/company/sites', 'Company\SitesController@index')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/data', 'Company\SitesController@data')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/sites/create', 'Company\SitesController@create')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/edit/{site_num}', 'Company\SitesController@getEdit')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/site/{id}/check-domain', 'Company\SitesController@checkDomain')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/site/{site}/add-opening-hours', 'Company\SitesController@addOpeningHours')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/update-opening-hours/{id}', 'Company\SitesController@updateOpeningHours')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/delete-opening-hours/{id}', 'Company\SitesController@deleteOpeningHours')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/default-opening-hours/{id}', 'Company\SitesController@defaultOpeningHours')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/update-opening-times-modal/{id}', 'Company\SitesController@updateOpeningHoursModal')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/site/{site}/delete-opening-hours/{id}', 'Company\SitesController@deleteOpeningHours')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::put('/company/sites/edit', 'Company\SitesController@edit')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/sites/edit-single', 'Company\SitesController@editSingle')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/edit-modal/{id}', 'Company\SitesController@editModal')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::delete('/company/sites/delete', 'Company\SitesController@delete')->middleware(['ability:newbridge|reseller|owner,company_management']);


Route::get('/company/sites/{site_num}/terminals', 'Company\TerminalController@index')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::put('/company/sites/{site_num}/terminals/edit', 'Company\TerminalController@edit')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/{site_num}/terminals/data', 'Company\TerminalController@data')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/{site_num}/colours', 'Company\SimpleColourSettings@index')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::post('/company/sites/{site_num}/colours/update', 'Company\SimpleColourSettings@update')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/{site_num}/colours/data', 'Company\SimpleColourSettings@data')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/{site_num}/terminals/send-functions', 'Company\TerminalController@sendCommandsToTerminals')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/{site_num}/terminals/{id}/settings', 'Settings\PosSettingsController@index')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/{site_num}/terminals/{id}/settings/data', 'Settings\PosSettingsController@getData')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/{site_num}/terminals/{id}/updates', 'Company\TerminalController@getPendingUpdatesView')->middleware(['ability:newbridge|reseller|owner,company_management']);
Route::get('/company/sites/table-qr-codes/{site_num}', 'Company\SitesController@printQrCodes')->middleware(['ability:newbridge|reseller|owner,company_management']);
