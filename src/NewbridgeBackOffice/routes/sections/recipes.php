<?php

use Illuminate\Support\Facades\Route;

Route::get('/recipes', 'RecipeController@index')->middleware(['ability:newbridge|reseller|owner,read_recipes']);
Route::get('/recipes/data', 'RecipeController@data')->middleware(['ability:newbridge|reseller|owner,read_recipes']);
Route::get('/recipes/create', 'RecipeController@getCreate')->middleware(['ability:newbridge|reseller|owner,write_recipes']);
Route::post('/recipes/create', 'RecipeController@create')->middleware(['ability:newbridge|reseller|owner,write_recipes']);
Route::get('/recipes/edit/{id}', 'RecipeController@getEdit')->middleware(['ability:newbridge|reseller|owner,write_recipes']);
Route::put('/recipes/edit', 'RecipeController@edit')->middleware(['ability:newbridge|reseller|owner,write_recipes']);
Route::put('/recipes/edit-single', 'RecipeController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_recipes']);
Route::get('/recipes/edit-modal/{id}', 'RecipeController@editModal')->middleware(['ability:newbridge|reseller|owner,read_recipes']);
Route::delete('/recipes/delete/{id}', 'RecipeController@delete')->middleware(['ability:newbridge|reseller|owner,write_recipes']);
