<?php

use Illuminate\Support\Facades\Route;

/* Users */
Route::get('/users', 'UserController@index')->middleware(['ability:newbridge|reseller|owner,read_users']);
Route::get('/users/data', 'UserController@data')->middleware(['ability:newbridge|reseller|owner,read_users']);
Route::post('/users/create', 'User<PERSON>ontroller@create')->middleware(['ability:newbridge|reseller|owner,write_users']);
Route::put('/users/edit', 'UserController@edit')->middleware(['ability:newbridge|reseller|owner,write_users']);
Route::put('/users/edit-single', 'UserController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_users']);
Route::get('/users/edit-modal/{id}', 'UserController@editModal')->middleware(['ability:newbridge|reseller|owner,write_users']);
Route::delete('/users/delete/{id}', 'UserC<PERSON>roller@delete')->middleware(['ability:newbridge|reseller|owner,write_users']);
Route::get('/users/check-username/{username}', 'UserController@checkUsername');

/* Roles */
Route::get('/roles', 'RolesAndPermissions\ManageRoles@index')->middleware(['ability:newbridge|reseller|owner,read_roles']);
Route::get('/roles/data', 'RolesAndPermissions\ManageRoles@data')->middleware(['ability:newbridge|reseller|owner,read_roles']);
Route::post('/roles/create', 'RolesAndPermissions\ManageRoles@create')->middleware(['ability:newbridge|reseller|owner,write_roles']);
Route::put('/roles/edit', 'RolesAndPermissions\ManageRoles@edit')->middleware(['ability:newbridge|reseller|owner,write_roles']);
Route::put('/roles/edit-single', 'RolesAndPermissions\ManageRoles@editSingle')->middleware(['ability:newbridge|reseller|owner,write_roles']);
Route::get('/roles/edit-modal/{id}', 'RolesAndPermissions\ManageRoles@editModal')->middleware(['ability:newbridge|reseller|owner,write_roles']);
Route::delete('/roles/delete/{id}', 'RolesAndPermissions\ManageRoles@delete')->middleware(['ability:newbridge|reseller|owner,write_roles']);
