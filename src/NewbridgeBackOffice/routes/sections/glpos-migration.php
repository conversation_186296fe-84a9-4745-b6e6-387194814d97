<?php

use Illuminate\Support\Facades\Route;

Route::get('glpos/{company}/{site_num}', 'PropertyManagement\GuestlineMigration\MigrationController@generateMigrationData');
Route::get('glpos/view/{company}/{site_num}', 'PropertyManagement\GuestlineMigration\MigrationController@viewCollectedData');
Route::get('glpos/convert/{company}/{site_num}', 'PropertyManagement\GuestlineMigration\MigrationController@convertData');
Route::get('glpos/view-converted/{company}/{site_num}', 'PropertyManagement\GuestlineMigration\MigrationController@viewConvertedData');
Route::get('glpos/insert/{company}/{site_num}', 'PropertyManagement\GuestlineMigration\MigrationController@insertData');
Route::get('guestline-migration', 'PropertyManagement\GuestlineMigration\MigrationController@index');
Route::post('guestline-migration/start', 'PropertyManagement\GuestlineMigration\MigrationController@start');
Route::get('guestline-migration/check-status/{guid}', 'PropertyManagement\GuestlineMigration\MigrationController@check');
