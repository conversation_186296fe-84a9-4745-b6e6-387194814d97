<?php

use Illuminate\Support\Facades\Route;
use NewbridgeWeb\Http\Controllers\Company\CompanyController;
use NewbridgeWeb\Http\Controllers\Company\CompanyCrudController;
use NewbridgeWeb\Http\Controllers\DepartmentController;
use NewbridgeWeb\Http\Controllers\Orders\OrdersController;

/* Protected Routes for Newbridge Staff Only */
Route::group(['middleware' => ['role:newbridge|reseller']], function () {

    /* Company Crud */
    Route::get('/companies', 'Company\CompanyCrudController@index');
    Route::get('/companies/recalculate/df29j239id239d29b239u8bd9u23bd2', [CompanyCrudController::class, 'recalculateAll']);
    Route::get('/companies/data', 'Company\CompanyCrudController@getData');
    Route::get('/companies/data-json', 'Company\CompanyCrudController@getDataAsJson');
    Route::get('/companies/edit/{id}', 'Company\CompanyCrudController@editForm');
    Route::post('/companies/edit/{id}', 'Company\CompanyCrudController@updateCompany');
    Route::put('/companies/edit-single', 'Company\CompanyCrudController@editSingle');
    Route::put('/companies/reseller-template', 'Company\CompanyCrudController@setTemplate');

    Route::post('/companies/push-live/{id}/{clearData?}', 'Company\CompanyController@pushLive');
    Route::get('/companies/change-status/{id}', 'Company\CompanyController@changeStatus');
    Route::get('/companies/add-terminal/{id}', 'Company\CompanyController@addTerminal');
    Route::get('/companies/add-terminal/company/{company_id}/site/{site_num}', 'Company\CompanyController@addTerminalWithSite');
    Route::delete('/companies/delete-terminal/{company_id}/{site_num}/{id}', 'Company\CompanyController@deleteTerminalWithSite');

    /* Create a new company account */
    Route::get('/company/register', [CompanyController::class, 'getRegisterCompany'])->middleware(['role:newbridge|reseller']);
    Route::post('/company/register', [CompanyController::class, 'postRegisterCompany'])->middleware(['role:newbridge|reseller']);
    Route::get('/departments/get-json', [DepartmentController::class, 'getAllByJson'])->middleware(['role:newbridge']);

    /* Push modules to the till */
    Route::get('/dev/re-sync/{site_num}', 'Newbridge\ModulesController@view');
    //Route::get('/re-sync', 'Newbridge\ModulesController@getStatus');
    Route::post('/dev/re-sync/completed', 'Newbridge\ModulesController@removeFromQueue');
    Route::get('/dev/re-sync/{update?}', 'Newbridge\ModulesController@sync');
    Route::post('/dev/re-sync', 'Newbridge\ModulesController@sync');
    Route::post('/dev/re-sync/check', 'Newbridge\ModulesController@getStatus');

    Route::get('/orders/re-send-till/{order_id}', [OrdersController::class, 'reSendOrdersToTills']);

    Route::get('/seed-settings/{company}/{terminal}/{site}', 'Company\CompanyController@seedSettings');
    Route::get('/default-settings', 'Company\TerminalController@defaultSettings');

});
