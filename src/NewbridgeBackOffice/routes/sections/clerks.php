<?php

use Illuminate\Support\Facades\Route;
use NewbridgeWeb\Http\Controllers\Clerks\Attendance\AttendanceController;
use NewbridgeWeb\Http\Controllers\Clerks\Attendance\RotaController;
use NewbridgeWeb\Http\Controllers\Clerks\Attendance\ShiftTypesController;
use NewbridgeWeb\Http\Controllers\Clerks\ClerkAreasController;
use NewbridgeWeb\Http\Controllers\Clerks\ClerkController;
use NewbridgeWeb\Http\Controllers\Clerks\ClerkPermissionGroupsController;
use NewbridgeWeb\Http\Controllers\Clerks\ClerkRolesController;

/**
 * Clerks
 */
Route::group(['middleware' => ['ability:newbridge|reseller|owner,read_clerks']], static function () {
    Route::get('/clerks', [ClerkController::class, 'index']);
    Route::get('/clerks/data', [ClerkController::class, 'data']);
});

Route::group(['middleware' => ['ability:newbridge|reseller|owner,write_clerks']], static function () {
    Route::get('/clerks/create', [ClerkController::class, 'getCreate']);
    Route::post('/clerks/create', [ClerkController::class, 'create']);
    Route::get('/clerks/edit/{id}', [ClerkController::class, 'getEdit']);
    Route::put('/clerks/edit', [ClerkController::class, 'edit']);
    Route::put('/clerks/edit-single', [ClerkController::class, 'editSingle']);
    Route::get('/clerks/edit-modal/{id}', [ClerkController::class, 'editModal']);
    Route::delete('/clerks/delete/{id}', [ClerkController::class, 'delete']);
    Route::post('/clerks/add-action', [ClerkController::class, 'addLink']);
    Route::delete('/clerks/delete-action/{id}', [ClerkController::class, 'removeLink']);
    Route::post('/clerks/reorder-actions', [ClerkController::class, 'reOrderLinks']);
    Route::get('/clerks/copy/{id}', [ClerkController::class, 'copyClerkWithoutRolesOrWages']);
});

/**
 * Attendance
 **/
Route::group(['middleware' => ['ability:newbridge|reseller|owner,read_attendance']], static function () {
    Route::get('/clerks/attendance', [AttendanceController::class, 'newIndex']);
    Route::post('/clerks/attendance/update', [AttendanceController::class, 'update']);
});

Route::group(['middleware' => ['ability:newbridge|reseller|owner,write_attendance']], static function () {
    Route::post('/clerks/attendance/update-calendar', [AttendanceController::class, 'updateCalendar']);
    Route::post('/clerks/attendance/save-times', [AttendanceController::class, 'save']);
    Route::post('/clerks/attendance/add', [AttendanceController::class, 'add']);
    Route::get('/clerks/attendance/get-modal/{id}/{type?}', [AttendanceController::class, 'getShift']);
    Route::post('/clerks/attendance/delete/{id}', [AttendanceController::class, 'delete']);
    Route::get('/clerks/attendance/activity/{id}', [AttendanceController::class, 'getActivity']);
    Route::post('/clerks/attendance/split', [AttendanceController::class, 'splitShift']);
    Route::get('/clerks/attendance/fields/{id}', [AttendanceController::class, 'shiftFields']);
});

/**
 * Rota
 */
Route::group(['middleware' => ['ability:newbridge|reseller|owner,read_attendance']], static function () {
    Route::get('/clerks/rota', 'Clerks\Attendance\RotaController@newIndex');
});
Route::group(['middleware' => ['ability:newbridge|reseller|owner,write_attendance']], static function () {
    Route::post('/clerks/rota/update', [RotaController::class, 'update']);
    Route::post('/clerks/rota/save-times', [RotaController::class, '@save']);
    Route::post('/clerks/rota/add', [RotaController::class, 'add']);
    Route::get('/clerks/rota/get-modal/{id}', [RotaController::class, 'getShift']);
    Route::post('/clerks/rota/delete/{id}', [RotaController::class, 'delete']);
    Route::get('/clerks/rota/activity/{id}', [RotaController::class, 'getActivity']);
    Route::get('/clerks/rota/get-data/{json?}/{false?}', [RotaController::class, 'newIndex']);
    Route::post('/clerks/rota/split', [RotaController::class, 'splitShift']);
    Route::get('/clerks/rota/weekly', [RotaController::class, 'indexWeekly']);
    Route::get('/clerk/rota/get-form/{clerk}', [RotaController::class, 'loadForm']);
    Route::post('/clerks/rota/weekly/create', [RotaController::class, 'weeklyCreate']);
});

/**
 * Clerk Shift Types
 */
Route::group(['middleware' => ['ability:newbridge|reseller|owner,read_attendance']], static function () {
    Route::get('/clerks/shift-types', [ShiftTypesController::class, 'index']);
    Route::get('/clerks/shift-types/data', [ShiftTypesController::class, 'getData']);
});
Route::group(['middleware' => ['ability:newbridge|reseller|owner,write_attendance']], static function () {
    Route::post('/clerks/shift-types/create', [ShiftTypesController::class, 'add']);
    Route::delete('/clerks/shift-types/delete/{id}', [ShiftTypesController::class, 'delete']);
    Route::get('/clerks/shift-types/edit-modal/{id}', [ShiftTypesController::class, 'editModal']);
    Route::put('/clerks/shift-types/edit', [ShiftTypesController::class, 'edit']);
    Route::put('/clerks/shift-types/edit-single', [ShiftTypesController::class, 'editById']);
});

/**
 * Clerk Areas
 */
Route::group(['middleware' => ['ability:newbridge|reseller|owner,read_clerks']], static function () {
    Route::get('/clerks/areas', [ClerkAreasController::class, 'index']);
    Route::get('/clerks/areas/data', [ClerkAreasController::class, 'getData']);
});
Route::group(['middleware' => ['ability:newbridge|reseller|owner,write_clerks']], static function () {
    Route::put('/clerks/areas/edit', [ClerkAreasController::class, 'edit']);
    Route::put('/clerks/areas/edit-single', [ClerkAreasController::class, 'editById']);
    Route::post('/clerks/areas/create', [ClerkAreasController::class, 'add']);
    Route::delete('/clerks/areas/delete/{id}', [ClerkAreasController::class, 'delete']);
    Route::get('/clerks/areas/edit-modal/{id}', [ClerkAreasController::class, 'editModal']);
});

/**
 * Clerk Job Roles
 */
Route::group(['middleware' => ['ability:newbridge|reseller|owner,read_clerks']], static function () {
    Route::get('/clerks/roles', [ClerkRolesController::class, 'index']);
    Route::get('/clerks/roles/data', [ClerkRolesController::class, 'getData']);
});
Route::group(['middleware' => ['ability:newbridge|reseller|owner,write_clerks']], static function () {
    Route::put('/clerks/roles/edit', [ClerkRolesController::class, 'edit']);
    Route::put('/clerks/roles/edit-single', [ClerkRolesController::class, 'editById']);
    Route::post('/clerks/roles/create', [ClerkRolesController::class, 'add']);
    Route::delete('/clerks/roles/delete/{id}', [ClerkRolesController::class, 'delete']);
    Route::get('/clerks/roles/edit-modal/{id}', [ClerkRolesController::class, 'editModal']);
});

/**
 * Clerk Permission Groups
 */
Route::group(['middleware' => ['ability:newbridge|reseller|owner,read_clerks']], static function () {
    Route::get('/clerks/permission-groups', [ClerkPermissionGroupsController::class, 'index']);
    Route::get('/clerks/permission-groups/data', [ClerkPermissionGroupsController::class, 'getData']);
});
Route::group(['middleware' => ['ability:newbridge|reseller|owner,write_clerks']], static function () {
    Route::get('/clerks/permission-groups/create', [ClerkPermissionGroupsController::class, 'getCreate']);
    Route::post('/clerks/permission-groups/add', [ClerkPermissionGroupsController::class, 'add']);
    Route::get('/clerks/permission-groups/edit/{id}', [ClerkPermissionGroupsController::class, 'getEdit']);
    Route::post('/clerks/permission-groups/edit/{id}', [ClerkPermissionGroupsController::class, 'editById']);
    Route::delete('/clerks/permission-groups/delete/{id}', [ClerkPermissionGroupsController::class, 'delete']);
});
