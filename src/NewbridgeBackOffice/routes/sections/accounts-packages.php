<?php

use Illuminate\Support\Facades\Route;

Route::group(['middleware' => ['auth.accounts', 'auth']], function () {

    Route::get('/accounts', 'AccountsPackages\Invoices\Invoices@selectDates')->middleware(['ability:newbridge|reseller|owner,accounts']);
    Route::post('/accounts/invoices', 'AccountsPackages\Invoices\Invoices@preview')->middleware(['ability:newbridge|reseller|owner,accounts']);

    Route::get('/accounts/configure', 'AccountsPackages\Configuration\Configuration@config')->middleware(['ability:newbridge|reseller|owner,accounts']);
    Route::post('/accounts/configure/save', 'AccountsPackages\Configuration\Configuration@save')->middleware(['ability:newbridge|reseller|owner,accounts']);

    Route::get('/accounts/configure-manual', 'AccountsPackages\Configuration\Configuration@manualConfig')->middleware(['ability:newbridge|reseller|owner,accounts']);
    Route::post('/accounts/configure/save-manual', 'AccountsPackages\Configuration\Configuration@saveManual')->middleware(['ability:newbridge|reseller|owner,accounts']);
    Route::post('/accounts/configure/add', 'AccountsPackages\Configuration\Configuration@addMapping')->middleware(['ability:newbridge|reseller|owner,accounts']);

    Route::post('/accounts/invoices/send', 'AccountsPackages\Invoices\Invoices@createInvoice')->middleware(['ability:newbridge|reseller|owner,accounts']);
    Route::post('/accounts/invoices/send/{key?}', 'AccountsPackages\Invoices\Invoices@createInvoice')->middleware(['ability:newbridge|reseller|owner,accounts']);
    Route::post('/accounts/invoices/check-status', 'AccountsPackages\Invoices\Invoices@checkStatus')->middleware(['ability:newbridge|reseller|owner,accounts']);

});

Route::get('/xero', 'XeroTest@createInvoice')->middleware(['role:newbridge|owner|accounts_xero']);
Route::get('/xero-auth', 'XeroTest@callback')->middleware(['role:newbridge|owner|accounts_xero']);
Route::get('/xero/setup', 'Xero\XeroSetup@view')->middleware(['role:newbridge|owner|accounts_xero']);

Route::get('/xero/checkToken', 'AccountsPackages\XeroAuth2@checkCurrentTokenOrStartAuthProcess')->middleware(['role:newbridge|owner|accounts_xero']);
Route::get('/xero/checkSession', 'AccountsPackages\XeroAuth2@checkSession')->middleware(['role:newbridge|owner|accounts_xero']);
Route::get('/xero/callback', 'AccountsPackages\XeroAuth2@processCallback')->middleware(['role:newbridge|owner|accounts_xero']);
Route::post('/xero/submit-summary', 'Xero\XeroCreateInvoice@createInvoice')->middleware(['role:newbridge|owner|accounts_xero']);

Route::get('/company/xero/setup', 'Xero\XeroSetup@view')->middleware(['role:newbridge|owner|accounts_xero']);
Route::get('/company/xero/callback', 'Xero\XeroSetup@callback')->middleware(['role:newbridge|owner|accounts_xero']);
Route::post('/company/xero/save', 'Xero\XeroSetup@save')->middleware(['role:newbridge|owner|accounts_xero']);
