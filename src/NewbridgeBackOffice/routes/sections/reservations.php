<?php

use Illuminate\Support\Facades\Route;

/**
 * No idea why this even exists anymore
 */

/* Asset groups */
Route::get('/asset-group', 'Reservations\Rooms\AssetGroupController@assetGroups')->middleware(['role:newbridge']);
Route::get('/asset-group/create', 'Reservations\Rooms\AssetGroupController@create')->middleware(['role:newbridge']);
Route::post('/asset-group/add', 'Reservations\Rooms\AssetGroupController@addAssetGroup')->middleware(['role:newbridge']);
Route::put('/asset-group/inline-edit/', 'Reservations\Rooms\AssetGroupController@inlineEdit')->middleware(['role:newbridge']);
Route::get('/asset-group/edit/{id}', 'Reservations\Rooms\AssetGroupController@edit')->middleware(['role:newbridge']);
Route::post('/asset-group/edit/{id}', 'Reservations\Rooms\AssetGroupController@updateAssetGroup')->middleware(['role:newbridge']);
Route::delete('/asset-group/delete/{id}', 'Reservations\Rooms\AssetGroupController@delete')->middleware(['role:newbridge']);
Route::get('/asset-group/data', 'Reservations\Rooms\AssetGroupController@getAssetGroups')->middleware(['role:newbridge']);

/* Assets */
Route::get('/asset-group/{id}/assets', 'Reservations\Rooms\AssetController@asset')->middleware(['role:newbridge']);
Route::get('/asset-group/{id}/assets/data', 'Reservations\Rooms\AssetController@getAsset')->middleware(['role:newbridge']);
Route::get('/asset-group/{id}/assets/create', 'Reservations\Rooms\AssetController@create')->middleware(['role:newbridge']);
Route::post('/asset-group/{id}/assets/add', 'Reservations\Rooms\AssetController@addAsset')->middleware(['role:newbridge']);
Route::delete('/asset-group/{id}/assets/delete/{asset_id}', 'Reservations\Rooms\AssetController@delete')->middleware(['role:newbridge']);
Route::get('/asset-group/{id}/assets/edit/{asset_id}', 'Reservations\Rooms\AssetController@edit')->middleware(['role:newbridge']);
Route::post('/asset-group/{id}/assets/edit/{asset_id}', 'Reservations\Rooms\AssetController@updateAsset')->middleware(['role:newbridge']);
Route::put('/asset-group/{id}/assets/inline-edit', 'Reservations\Rooms\AssetController@inlineEdit')->middleware(['role:newbridge']);

/* Bookings Calendar */
Route::get('/booking/calendar', 'Reservations\Rooms\BookingCalendarController@index')->middleware(['role:newbridge']);
Route::post('/booking/calendar/get-modal', 'Reservations\Rooms\BookingCalendarController@modalCheckIn')->middleware(['ability:newbridge|reseller|owner,write_attendance']);
Route::post('/booking/calendar/get-modal-booking', 'Reservations\Rooms\BookingCalendarController@modalBooking')->middleware(['ability:newbridge|reseller|owner,write_attendance']);
Route::post('/booking/calendar/get-modal/info', 'Reservations\Rooms\BookingCalendarController@modalBookingInfo')->middleware(['ability:newbridge|reseller|owner,write_attendance']);
Route::post('/booking/calendar/get-modal/check-in', 'Reservations\Rooms\BookingCalendarController@modalBookingCheckIn')->middleware(['ability:newbridge|reseller|owner,write_attendance']);
Route::post('/booking/emailCheck/modal', 'Reservations\Rooms\BookingCalendarController@emailCheckModal')->middleware(['role:newbridge']);
Route::post('/booking/calendar/update', 'Reservations\Rooms\BookingCalendarController@BookingUpdate')->middleware(['ability:newbridge|reseller|owner,write_attendance']);

/* Asset groups Booking */
Route::get('/booking', 'Reservations\Rooms\BookingController@index')->middleware(['role:newbridge']);
Route::post('/booking/data', 'Reservations\Rooms\BookingController@bookingData')->middleware(['role:newbridge']);
Route::post('/booking/emailCheck/{emailCheck}', 'Reservations\Rooms\BookingController@emailCheck')->middleware(['role:newbridge']);
Route::post('/booking/add/{id}', 'Reservations\Rooms\BookingController@addBooking')->middleware(['role:newbridge']);
Route::post('/booking/create', 'Reservations\Rooms\BookingController@createBooking')->middleware(['role:newbridge']);
Route::post('/booking/create/modal', 'Reservations\Rooms\BookingCalendarController@createBookingModal')->middleware(['role:newbridge']);
Route::post('/booking/calendar/is-available', 'Reservations\Rooms\BookingCalendarController@isAvailableBookingModal')->middleware(['role:newbridge']);
