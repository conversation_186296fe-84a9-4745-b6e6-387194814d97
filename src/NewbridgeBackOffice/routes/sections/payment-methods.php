<?php

use Illuminate\Support\Facades\Route;

Route::get('/payments', 'PaymentController@index')->middleware(['ability:newbridge|reseller|owner,read_payment_types']);
Route::get('/payments/data', 'PaymentController@data')->middleware(['ability:newbridge|reseller|owner,read_payment_types']);
Route::post('/payments/create', 'PaymentController@create')->middleware(['ability:newbridge|reseller|owner,write_payment_types']);
Route::put('/payments/edit', 'PaymentController@edit')->middleware(['ability:newbridge|reseller|owner,write_payment_types']);
Route::put('/payments/edit-single', 'PaymentController@editSingle')->middleware(['ability:newbridge|reseller|owner,write_payment_types']);
Route::get('/payments/edit-modal/{id}', 'PaymentController@editModal')->middleware(['ability:newbridge|reseller|owner,write_payment_types']);
Route::delete('/payments/delete/{id}', 'PaymentController@delete')->middleware(['ability:newbridge|reseller|owner,write_payment_types']);
