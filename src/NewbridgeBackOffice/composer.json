{"name": "newbridge/web", "description": "The management application for Newbridge POS", "keywords": ["EPOS", "Analytics", "EPOS Software"], "license": "PROP", "type": "project", "require": {"php": ">=8.1", "ext-redis": "*", "artisaninweb/laravel-soap": "*******", "aws/aws-sdk-php": "~3.0", "bacon/bacon-qr-code": "^2.0", "barryvdh/laravel-dompdf": "^2.0", "binarytorch/larecipe": "^2.4", "calcinai/xero-php": "^2.1", "codedredd/laravel-soap": "*", "consoletvs/charts": ">5", "doctrine/dbal": "^3.3", "dyrynda/laravel-cascade-soft-deletes": "^4.2", "fzaninotto/faker": "^1.7", "hashids/hashids": "^4.1", "laracraft-tech/laravel-useful-additions": "^3.4", "laravel/framework": "^10.0", "laravel/helpers": "^1.6", "laravel/horizon": "^5.17", "laravel/scout": "*", "laravel/ui": "^4.2", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-sftp-v3": "^3.0", "league/oauth2-client": "^2.4", "maatwebsite/excel": "^3.0", "matchish/laravel-scout-elasticsearch": "dev-fix-laravel-ide-helper", "matthewbdaly/laravel-azure-storage": "^2.0", "milon/barcode": "^10.0", "mtownsend/response-xml": "^2.0", "php-open-source-saver/jwt-auth": "^2.3", "pragmarx/google2fa-laravel": "^1.3", "predis/predis": "^1.1", "pusher/pusher-php-server": "^7.2", "responsilicious/laravel-quickbooks": "*******", "sentry/sentry-laravel": "^4.13", "spatie/laravel-activitylog": "^4.5", "spatie/laravel-data": "*", "spatie/laravel-permission": "^5.9", "spatie/opening-hours": "^2.8", "swisnl/json-api-client": "^2.0", "symfony/http-client": "^6.3", "tightenco/ziggy": "^1.8", "yajra/laravel-datatables": "^10.0", "yajra/laravel-datatables-oracle": "^10.4"}, "require-dev": {"barryvdh/laravel-ide-helper": "^v3.1.0", "driftingly/rector-laravel": "^0.15.0", "filp/whoops": "~2.0", "friendsofphp/php-cs-fixer": "v3.*", "laminas/laminas-code": "^4.6", "laravel/tinker": "^2.0", "mockery/mockery": "1.5.1", "nunomaduro/collision": "^v7.10.0", "larastan/larastan": "^v2.9.7", "nunomaduro/phpinsights": "^v2.8.0", "pestphp/pest": "^v2.24.1", "pestphp/pest-plugin-laravel": "^v2.2.0", "pestphp/pest-plugin-type-coverage": "^2.4", "phpunit/phpunit": "^10.4.2", "rector/rector": "^0.15.16", "staabm/annotate-pull-request-from-checkstyle": "^1.8.5", "wsdl2phpgenerator/wsdl2phpgenerator": "*"}, "autoload": {"classmap": ["database"], "psr-4": {"NewbridgeWeb\\": "app/", "Modules\\": "Mo<PERSON>les/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeds/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall", "@php artisan ide-helper:generate --ansi", "@php artisan ide-helper:meta --ansi", "@php artisan ide-helper:models --nowrite --ansi"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan ide-helper:generate --ansi", "@php artisan ide-helper:meta --ansi", "@php artisan ide-helper:models --nowrite --ansi"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover"]}, "config": {"platform-check": false, "preferred-install": "dist", "sort-packages": true, "github-oauth": {"github.com": "9c7150aca93ffccb9c828bc58f05880e53d49c69"}, "allow-plugins": {"php-http/discovery": true, "dealerdirect/phpcodesniffer-composer-installer": true, "pestphp/pest-plugin": true}}, "repositories": [{"type": "vcs", "url": "https://github.com/sync667/laravel-scout-elasticsearch"}], "extra": {"laravel": {}}, "minimum-stability": "dev", "prefer-stable": true}