<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use NewbridgeWeb\Repositories\Company;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\NewbridgeWeb\User>
 */
class UserFactory extends Factory
{
    protected static Company $company;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'password' => bcrypt('secret'),
            'remember_token' => Str::random(10),
        ];
    }

    public function withCompany(Company $company)
    {
        UserFactory::$company = $company;

        return $this->state(function (array $attributes) {
            return [
                'company_id' => UserFactory::$company->id,
            ];
        });
    }
}
