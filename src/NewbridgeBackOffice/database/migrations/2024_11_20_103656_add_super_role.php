<?php

use Illuminate\Database\Migrations\Migration;
use <PERSON><PERSON>\Permission\Models\Role;

return new class extends Migration
{
    public function up(): void
    {

         $role = [
             "company_id" => 0,
             "guard_name" => 'web',
             "name" => 'super-admin',
             "display_name" => 'Super Admin',
             "description" => 'That is total super admin role.'
         ];

         if (!Role::where('name', $role['name'])->exists()) {
             $role = Role::create($role);
         }

        if (App::environment('local')) {
            $user = \NewbridgeWeb\User::where('username', 'super@newbridge')->first();

            $user?->assignRole($role);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Role::where("name", "super-admin")->delete();
    }
};
