<?php

use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        \NewbridgeWeb\Repositories\Reports::where('class', 'NewbridgeWeb\Http\Controllers\Reports\Transactions\TransactionListReport')
            ->update(['class' => 'NewbridgeWeb\Http\Controllers\Reports\Transactions\CashierTransactionReport']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        \NewbridgeWeb\Repositories\Reports::where('class', 'NewbridgeWeb\Http\Controllers\Reports\Transactions\CashierTransactionReport')
            ->update(['class' => 'NewbridgeWeb\Http\Controllers\Reports\Transactions\TransactionListReport']);
    }
};
