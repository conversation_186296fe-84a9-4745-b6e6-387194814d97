<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $timezones = '[
          {
            "country_code": "CI",
            "timezone": "Africa/Abidjan",
            "gmt_offset": 0
          },
          {
            "country_code": "GH",
            "timezone": "Africa/Accra",
            "gmt_offset": 0
          },
          {
            "country_code": "ET",
            "timezone": "Africa/Addis_Ababa",
            "gmt_offset": 3
          },
          {
            "country_code": "DZ",
            "timezone": "Africa/Algiers",
            "gmt_offset": 1
          },
          {
            "country_code": "ER",
            "timezone": "Africa/Asmara",
            "gmt_offset": 3
          },
          {
            "country_code": "ML",
            "timezone": "Africa/Bamako",
            "gmt_offset": 0
          },
          {
            "country_code": "CF",
            "timezone": "Africa/Bangui",
            "gmt_offset": 1
          },
          {
            "country_code": "GM",
            "timezone": "Africa/Banjul",
            "gmt_offset": 0
          },
          {
            "country_code": "GW",
            "timezone": "Africa/Bissau",
            "gmt_offset": 0
          },
          {
            "country_code": "MW",
            "timezone": "Africa/Blantyre",
            "gmt_offset": 2
          },
          {
            "country_code": "CG",
            "timezone": "Africa/Brazzaville",
            "gmt_offset": 1
          },
          {
            "country_code": "BI",
            "timezone": "Africa/Bujumbura",
            "gmt_offset": 2
          },
          {
            "country_code": "EG",
            "timezone": "Africa/Cairo",
            "gmt_offset": 2
          },
          {
            "country_code": "MA",
            "timezone": "Africa/Casablanca",
            "gmt_offset": 0
          },
          {
            "country_code": "ES",
            "timezone": "Africa/Ceuta",
            "gmt_offset": 1
          },
          {
            "country_code": "GN",
            "timezone": "Africa/Conakry",
            "gmt_offset": 0
          },
          {
            "country_code": "SN",
            "timezone": "Africa/Dakar",
            "gmt_offset": 0
          },
          {
            "country_code": "TZ",
            "timezone": "Africa/Dar_es_Salaam",
            "gmt_offset": 3
          },
          {
            "country_code": "DJ",
            "timezone": "Africa/Djibouti",
            "gmt_offset": 3
          },
          {
            "country_code": "CM",
            "timezone": "Africa/Douala",
            "gmt_offset": 1
          },
          {
            "country_code": "EH",
            "timezone": "Africa/El_Aaiun",
            "gmt_offset": 0
          },
          {
            "country_code": "SL",
            "timezone": "Africa/Freetown",
            "gmt_offset": 0
          },
          {
            "country_code": "BW",
            "timezone": "Africa/Gaborone",
            "gmt_offset": 2
          },
          {
            "country_code": "ZW",
            "timezone": "Africa/Harare",
            "gmt_offset": 2
          },
          {
            "country_code": "ZA",
            "timezone": "Africa/Johannesburg",
            "gmt_offset": 2
          },
          {
            "country_code": "SS",
            "timezone": "Africa/Juba",
            "gmt_offset": 3
          },
          {
            "country_code": "UG",
            "timezone": "Africa/Kampala",
            "gmt_offset": 3
          },
          {
            "country_code": "SD",
            "timezone": "Africa/Khartoum",
            "gmt_offset": 3
          },
          {
            "country_code": "RW",
            "timezone": "Africa/Kigali",
            "gmt_offset": 2
          },
          {
            "country_code": "CD",
            "timezone": "Africa/Kinshasa",
            "gmt_offset": 1
          },
          {
            "country_code": "NG",
            "timezone": "Africa/Lagos",
            "gmt_offset": 1
          },
          {
            "country_code": "GA",
            "timezone": "Africa/Libreville",
            "gmt_offset": 1
          },
          {
            "country_code": "TG",
            "timezone": "Africa/Lome",
            "gmt_offset": 0
          },
          {
            "country_code": "AO",
            "timezone": "Africa/Luanda",
            "gmt_offset": 1
          },
          {
            "country_code": "CD",
            "timezone": "Africa/Lubumbashi",
            "gmt_offset": 2
          },
          {
            "country_code": "ZM",
            "timezone": "Africa/Lusaka",
            "gmt_offset": 2
          },
          {
            "country_code": "GQ",
            "timezone": "Africa/Malabo",
            "gmt_offset": 1
          },
          {
            "country_code": "MZ",
            "timezone": "Africa/Maputo",
            "gmt_offset": 2
          },
          {
            "country_code": "LS",
            "timezone": "Africa/Maseru",
            "gmt_offset": 2
          },
          {
            "country_code": "SZ",
            "timezone": "Africa/Mbabane",
            "gmt_offset": 2
          },
          {
            "country_code": "SO",
            "timezone": "Africa/Mogadishu",
            "gmt_offset": 3
          },
          {
            "country_code": "LR",
            "timezone": "Africa/Monrovia",
            "gmt_offset": 0
          },
          {
            "country_code": "KE",
            "timezone": "Africa/Nairobi",
            "gmt_offset": 3
          },
          {
            "country_code": "TD",
            "timezone": "Africa/Ndjamena",
            "gmt_offset": 1
          },
          {
            "country_code": "NE",
            "timezone": "Africa/Niamey",
            "gmt_offset": 1
          },
          {
            "country_code": "MR",
            "timezone": "Africa/Nouakchott",
            "gmt_offset": 0
          },
          {
            "country_code": "BF",
            "timezone": "Africa/Ouagadougou",
            "gmt_offset": 0
          },
          {
            "country_code": "BJ",
            "timezone": "Africa/Porto-Novo",
            "gmt_offset": 1
          },
          {
            "country_code": "ST",
            "timezone": "Africa/Sao_Tome",
            "gmt_offset": 0
          },
          {
            "country_code": "LY",
            "timezone": "Africa/Tripoli",
            "gmt_offset": 2
          },
          {
            "country_code": "TN",
            "timezone": "Africa/Tunis",
            "gmt_offset": 1
          },
          {
            "country_code": "NA",
            "timezone": "Africa/Windhoek",
            "gmt_offset": 2
          },
          {
            "country_code": "US",
            "timezone": "America/Adak",
            "gmt_offset": -10
          },
          {
            "country_code": "US",
            "timezone": "America/Anchorage",
            "gmt_offset": -9
          },
          {
            "country_code": "AI",
            "timezone": "America/Anguilla",
            "gmt_offset": -4
          },
          {
            "country_code": "AG",
            "timezone": "America/Antigua",
            "gmt_offset": -4
          },
          {
            "country_code": "BR",
            "timezone": "America/Araguaina",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Buenos_Aires",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Catamarca",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Cordoba",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Jujuy",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/La_Rioja",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Mendoza",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Rio_Gallegos",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Salta",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/San_Juan",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/San_Luis",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Tucuman",
            "gmt_offset": -3
          },
          {
            "country_code": "AR",
            "timezone": "America/Argentina/Ushuaia",
            "gmt_offset": -3
          },
          {
            "country_code": "AW",
            "timezone": "America/Aruba",
            "gmt_offset": -4
          },
          {
            "country_code": "PY",
            "timezone": "America/Asuncion",
            "gmt_offset": -3
          },
          {
            "country_code": "CA",
            "timezone": "America/Atikokan",
            "gmt_offset": -5
          },
          {
            "country_code": "BR",
            "timezone": "America/Bahia",
            "gmt_offset": -3
          },
          {
            "country_code": "MX",
            "timezone": "America/Bahia_Banderas",
            "gmt_offset": -6
          },
          {
            "country_code": "BB",
            "timezone": "America/Barbados",
            "gmt_offset": -4
          },
          {
            "country_code": "BR",
            "timezone": "America/Belem",
            "gmt_offset": -3
          },
          {
            "country_code": "BZ",
            "timezone": "America/Belize",
            "gmt_offset": -6
          },
          {
            "country_code": "CA",
            "timezone": "America/Blanc-Sablon",
            "gmt_offset": -4
          },
          {
            "country_code": "BR",
            "timezone": "America/Boa_Vista",
            "gmt_offset": -4
          },
          {
            "country_code": "CO",
            "timezone": "America/Bogota",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Boise",
            "gmt_offset": -7
          },
          {
            "country_code": "CA",
            "timezone": "America/Cambridge_Bay",
            "gmt_offset": -7
          },
          {
            "country_code": "BR",
            "timezone": "America/Campo_Grande",
            "gmt_offset": -3
          },
          {
            "country_code": "MX",
            "timezone": "America/Cancun",
            "gmt_offset": -6
          },
          {
            "country_code": "VE",
            "timezone": "America/Caracas",
            "gmt_offset": -4.5
          },
          {
            "country_code": "GF",
            "timezone": "America/Cayenne",
            "gmt_offset": -3
          },
          {
            "country_code": "KY",
            "timezone": "America/Cayman",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Chicago",
            "gmt_offset": -6
          },
          {
            "country_code": "MX",
            "timezone": "America/Chihuahua",
            "gmt_offset": -7
          },
          {
            "country_code": "CR",
            "timezone": "America/Costa_Rica",
            "gmt_offset": -6
          },
          {
            "country_code": "CA",
            "timezone": "America/Creston",
            "gmt_offset": -7
          },
          {
            "country_code": "BR",
            "timezone": "America/Cuiaba",
            "gmt_offset": -3
          },
          {
            "country_code": "CW",
            "timezone": "America/Curacao",
            "gmt_offset": -4
          },
          {
            "country_code": "GL",
            "timezone": "America/Danmarkshavn",
            "gmt_offset": 0
          },
          {
            "country_code": "CA",
            "timezone": "America/Dawson",
            "gmt_offset": -8
          },
          {
            "country_code": "CA",
            "timezone": "America/Dawson_Creek",
            "gmt_offset": -7
          },
          {
            "country_code": "US",
            "timezone": "America/Denver",
            "gmt_offset": -7
          },
          {
            "country_code": "US",
            "timezone": "America/Detroit",
            "gmt_offset": -5
          },
          {
            "country_code": "DM",
            "timezone": "America/Dominica",
            "gmt_offset": -4
          },
          {
            "country_code": "CA",
            "timezone": "America/Edmonton",
            "gmt_offset": -7
          },
          {
            "country_code": "BR",
            "timezone": "America/Eirunepe",
            "gmt_offset": -5
          },
          {
            "country_code": "SV",
            "timezone": "America/El_Salvador",
            "gmt_offset": -6
          },
          {
            "country_code": "BR",
            "timezone": "America/Fortaleza",
            "gmt_offset": -3
          },
          {
            "country_code": "CA",
            "timezone": "America/Glace_Bay",
            "gmt_offset": -4
          },
          {
            "country_code": "GL",
            "timezone": "America/Godthab",
            "gmt_offset": -3
          },
          {
            "country_code": "CA",
            "timezone": "America/Goose_Bay",
            "gmt_offset": -4
          },
          {
            "country_code": "TC",
            "timezone": "America/Grand_Turk",
            "gmt_offset": -5
          },
          {
            "country_code": "GD",
            "timezone": "America/Grenada",
            "gmt_offset": -4
          },
          {
            "country_code": "GP",
            "timezone": "America/Guadeloupe",
            "gmt_offset": -4
          },
          {
            "country_code": "GT",
            "timezone": "America/Guatemala",
            "gmt_offset": -6
          },
          {
            "country_code": "EC",
            "timezone": "America/Guayaquil",
            "gmt_offset": -5
          },
          {
            "country_code": "GY",
            "timezone": "America/Guyana",
            "gmt_offset": -4
          },
          {
            "country_code": "CA",
            "timezone": "America/Halifax",
            "gmt_offset": -4
          },
          {
            "country_code": "CU",
            "timezone": "America/Havana",
            "gmt_offset": -5
          },
          {
            "country_code": "MX",
            "timezone": "America/Hermosillo",
            "gmt_offset": -7
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Indianapolis",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Knox",
            "gmt_offset": -6
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Marengo",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Petersburg",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Tell_City",
            "gmt_offset": -6
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Vevay",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Vincennes",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Indiana/Winamac",
            "gmt_offset": -5
          },
          {
            "country_code": "CA",
            "timezone": "America/Inuvik",
            "gmt_offset": -7
          },
          {
            "country_code": "CA",
            "timezone": "America/Iqaluit",
            "gmt_offset": -5
          },
          {
            "country_code": "JM",
            "timezone": "America/Jamaica",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Juneau",
            "gmt_offset": -9
          },
          {
            "country_code": "US",
            "timezone": "America/Kentucky/Louisville",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Kentucky/Monticello",
            "gmt_offset": -5
          },
          {
            "country_code": "BQ",
            "timezone": "America/Kralendijk",
            "gmt_offset": -4
          },
          {
            "country_code": "BO",
            "timezone": "America/La_Paz",
            "gmt_offset": -4
          },
          {
            "country_code": "PE",
            "timezone": "America/Lima",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Los_Angeles",
            "gmt_offset": -8
          },
          {
            "country_code": "SX",
            "timezone": "America/Lower_Princes",
            "gmt_offset": -4
          },
          {
            "country_code": "BR",
            "timezone": "America/Maceio",
            "gmt_offset": -3
          },
          {
            "country_code": "NI",
            "timezone": "America/Managua",
            "gmt_offset": -6
          },
          {
            "country_code": "BR",
            "timezone": "America/Manaus",
            "gmt_offset": -4
          },
          {
            "country_code": "MF",
            "timezone": "America/Marigot",
            "gmt_offset": -4
          },
          {
            "country_code": "MQ",
            "timezone": "America/Martinique",
            "gmt_offset": -4
          },
          {
            "country_code": "MX",
            "timezone": "America/Matamoros",
            "gmt_offset": -6
          },
          {
            "country_code": "MX",
            "timezone": "America/Mazatlan",
            "gmt_offset": -7
          },
          {
            "country_code": "US",
            "timezone": "America/Menominee",
            "gmt_offset": -6
          },
          {
            "country_code": "MX",
            "timezone": "America/Merida",
            "gmt_offset": -6
          },
          {
            "country_code": "US",
            "timezone": "America/Metlakatla",
            "gmt_offset": -8
          },
          {
            "country_code": "MX",
            "timezone": "America/Mexico_City",
            "gmt_offset": -6
          },
          {
            "country_code": "PM",
            "timezone": "America/Miquelon",
            "gmt_offset": -3
          },
          {
            "country_code": "CA",
            "timezone": "America/Moncton",
            "gmt_offset": -4
          },
          {
            "country_code": "MX",
            "timezone": "America/Monterrey",
            "gmt_offset": -6
          },
          {
            "country_code": "UY",
            "timezone": "America/Montevideo",
            "gmt_offset": -2
          },
          {
            "country_code": "CA",
            "timezone": "America/Montreal",
            "gmt_offset": -5
          },
          {
            "country_code": "MS",
            "timezone": "America/Montserrat",
            "gmt_offset": -4
          },
          {
            "country_code": "BS",
            "timezone": "America/Nassau",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/New_York",
            "gmt_offset": -5
          },
          {
            "country_code": "CA",
            "timezone": "America/Nipigon",
            "gmt_offset": -5
          },
          {
            "country_code": "US",
            "timezone": "America/Nome",
            "gmt_offset": -9
          },
          {
            "country_code": "BR",
            "timezone": "America/Noronha",
            "gmt_offset": -2
          },
          {
            "country_code": "US",
            "timezone": "America/North_Dakota/Beulah",
            "gmt_offset": -6
          },
          {
            "country_code": "US",
            "timezone": "America/North_Dakota/Center",
            "gmt_offset": -6
          },
          {
            "country_code": "US",
            "timezone": "America/North_Dakota/New_Salem",
            "gmt_offset": -6
          },
          {
            "country_code": "MX",
            "timezone": "America/Ojinaga",
            "gmt_offset": -7
          },
          {
            "country_code": "PA",
            "timezone": "America/Panama",
            "gmt_offset": -5
          },
          {
            "country_code": "CA",
            "timezone": "America/Pangnirtung",
            "gmt_offset": -5
          },
          {
            "country_code": "SR",
            "timezone": "America/Paramaribo",
            "gmt_offset": -3
          },
          {
            "country_code": "US",
            "timezone": "America/Phoenix",
            "gmt_offset": -7
          },
          {
            "country_code": "HT",
            "timezone": "America/Port-au-Prince",
            "gmt_offset": -5
          },
          {
            "country_code": "BR",
            "timezone": "America/Porto_Velho",
            "gmt_offset": -4
          },
          {
            "country_code": "TT",
            "timezone": "America/Port_of_Spain",
            "gmt_offset": -4
          },
          {
            "country_code": "PR",
            "timezone": "America/Puerto_Rico",
            "gmt_offset": -4
          },
          {
            "country_code": "CA",
            "timezone": "America/Rainy_River",
            "gmt_offset": -6
          },
          {
            "country_code": "CA",
            "timezone": "America/Rankin_Inlet",
            "gmt_offset": -6
          },
          {
            "country_code": "BR",
            "timezone": "America/Recife",
            "gmt_offset": -3
          },
          {
            "country_code": "CA",
            "timezone": "America/Regina",
            "gmt_offset": -6
          },
          {
            "country_code": "CA",
            "timezone": "America/Resolute",
            "gmt_offset": -6
          },
          {
            "country_code": "BR",
            "timezone": "America/Rio_Branco",
            "gmt_offset": -5
          },
          {
            "country_code": "BR",
            "timezone": "America/Santarem",
            "gmt_offset": -3
          },
          {
            "country_code": "MX",
            "timezone": "America/Santa_Isabel",
            "gmt_offset": -8
          },
          {
            "country_code": "CL",
            "timezone": "America/Santiago",
            "gmt_offset": -3
          },
          {
            "country_code": "DO",
            "timezone": "America/Santo_Domingo",
            "gmt_offset": -4
          },
          {
            "country_code": "BR",
            "timezone": "America/Sao_Paulo",
            "gmt_offset": -2
          },
          {
            "country_code": "GL",
            "timezone": "America/Scoresbysund",
            "gmt_offset": -1
          },
          {
            "country_code": "US",
            "timezone": "America/Shiprock",
            "gmt_offset": -7
          },
          {
            "country_code": "US",
            "timezone": "America/Sitka",
            "gmt_offset": -9
          },
          {
            "country_code": "BL",
            "timezone": "America/St_Barthelemy",
            "gmt_offset": -4
          },
          {
            "country_code": "CA",
            "timezone": "America/St_Johns",
            "gmt_offset": -3.5
          },
          {
            "country_code": "KN",
            "timezone": "America/St_Kitts",
            "gmt_offset": -4
          },
          {
            "country_code": "LC",
            "timezone": "America/St_Lucia",
            "gmt_offset": -4
          },
          {
            "country_code": "VI",
            "timezone": "America/St_Thomas",
            "gmt_offset": -4
          },
          {
            "country_code": "VC",
            "timezone": "America/St_Vincent",
            "gmt_offset": -4
          },
          {
            "country_code": "CA",
            "timezone": "America/Swift_Current",
            "gmt_offset": -6
          },
          {
            "country_code": "HN",
            "timezone": "America/Tegucigalpa",
            "gmt_offset": -6
          },
          {
            "country_code": "GL",
            "timezone": "America/Thule",
            "gmt_offset": -4
          },
          {
            "country_code": "CA",
            "timezone": "America/Thunder_Bay",
            "gmt_offset": -5
          },
          {
            "country_code": "MX",
            "timezone": "America/Tijuana",
            "gmt_offset": -8
          },
          {
            "country_code": "CA",
            "timezone": "America/Toronto",
            "gmt_offset": -5
          },
          {
            "country_code": "VG",
            "timezone": "America/Tortola",
            "gmt_offset": -4
          },
          {
            "country_code": "CA",
            "timezone": "America/Vancouver",
            "gmt_offset": -8
          },
          {
            "country_code": "CA",
            "timezone": "America/Whitehorse",
            "gmt_offset": -8
          },
          {
            "country_code": "CA",
            "timezone": "America/Winnipeg",
            "gmt_offset": -6
          },
          {
            "country_code": "US",
            "timezone": "America/Yakutat",
            "gmt_offset": -9
          },
          {
            "country_code": "CA",
            "timezone": "America/Yellowknife",
            "gmt_offset": -7
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/Casey",
            "gmt_offset": 8
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/Davis",
            "gmt_offset": 7
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/DumontDUrville",
            "gmt_offset": 10
          },
          {
            "country_code": "AU",
            "timezone": "Antarctica/Macquarie",
            "gmt_offset": 11
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/Mawson",
            "gmt_offset": 5
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/McMurdo",
            "gmt_offset": 13
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/Palmer",
            "gmt_offset": -3
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/Rothera",
            "gmt_offset": -3
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/South_Pole",
            "gmt_offset": 13
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/Syowa",
            "gmt_offset": 3
          },
          {
            "country_code": "AQ",
            "timezone": "Antarctica/Vostok",
            "gmt_offset": 6
          },
          {
            "country_code": "SJ",
            "timezone": "Arctic/Longyearbyen",
            "gmt_offset": 1
          },
          {
            "country_code": "YE",
            "timezone": "Asia/Aden",
            "gmt_offset": 3
          },
          {
            "country_code": "KZ",
            "timezone": "Asia/Almaty",
            "gmt_offset": 6
          },
          {
            "country_code": "JO",
            "timezone": "Asia/Amman",
            "gmt_offset": 2
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Anadyr",
            "gmt_offset": 12
          },
          {
            "country_code": "KZ",
            "timezone": "Asia/Aqtau",
            "gmt_offset": 5
          },
          {
            "country_code": "KZ",
            "timezone": "Asia/Aqtobe",
            "gmt_offset": 5
          },
          {
            "country_code": "TM",
            "timezone": "Asia/Ashgabat",
            "gmt_offset": 5
          },
          {
            "country_code": "IQ",
            "timezone": "Asia/Baghdad",
            "gmt_offset": 3
          },
          {
            "country_code": "BH",
            "timezone": "Asia/Bahrain",
            "gmt_offset": 3
          },
          {
            "country_code": "AZ",
            "timezone": "Asia/Baku",
            "gmt_offset": 4
          },
          {
            "country_code": "TH",
            "timezone": "Asia/Bangkok",
            "gmt_offset": 7
          },
          {
            "country_code": "LB",
            "timezone": "Asia/Beirut",
            "gmt_offset": 2
          },
          {
            "country_code": "KG",
            "timezone": "Asia/Bishkek",
            "gmt_offset": 6
          },
          {
            "country_code": "BN",
            "timezone": "Asia/Brunei",
            "gmt_offset": 8
          },
          {
            "country_code": "MN",
            "timezone": "Asia/Choibalsan",
            "gmt_offset": 8
          },
          {
            "country_code": "CN",
            "timezone": "Asia/Chongqing",
            "gmt_offset": 8
          },
          {
            "country_code": "LK",
            "timezone": "Asia/Colombo",
            "gmt_offset": 5.5
          },
          {
            "country_code": "SY",
            "timezone": "Asia/Damascus",
            "gmt_offset": 2
          },
          {
            "country_code": "BD",
            "timezone": "Asia/Dhaka",
            "gmt_offset": 6
          },
          {
            "country_code": "TL",
            "timezone": "Asia/Dili",
            "gmt_offset": 9
          },
          {
            "country_code": "AE",
            "timezone": "Asia/Dubai",
            "gmt_offset": 4
          },
          {
            "country_code": "TJ",
            "timezone": "Asia/Dushanbe",
            "gmt_offset": 5
          },
          {
            "country_code": "PS",
            "timezone": "Asia/Gaza",
            "gmt_offset": 2
          },
          {
            "country_code": "CN",
            "timezone": "Asia/Harbin",
            "gmt_offset": 8
          },
          {
            "country_code": "PS",
            "timezone": "Asia/Hebron",
            "gmt_offset": 2
          },
          {
            "country_code": "HK",
            "timezone": "Asia/Hong_Kong",
            "gmt_offset": 8
          },
          {
            "country_code": "MN",
            "timezone": "Asia/Hovd",
            "gmt_offset": 7
          },
          {
            "country_code": "VN",
            "timezone": "Asia/Ho_Chi_Minh",
            "gmt_offset": 7
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Irkutsk",
            "gmt_offset": 9
          },
          {
            "country_code": "ID",
            "timezone": "Asia/Jakarta",
            "gmt_offset": 7
          },
          {
            "country_code": "ID",
            "timezone": "Asia/Jayapura",
            "gmt_offset": 9
          },
          {
            "country_code": "IL",
            "timezone": "Asia/Jerusalem",
            "gmt_offset": 2
          },
          {
            "country_code": "AF",
            "timezone": "Asia/Kabul",
            "gmt_offset": 4.5
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Kamchatka",
            "gmt_offset": 12
          },
          {
            "country_code": "PK",
            "timezone": "Asia/Karachi",
            "gmt_offset": 5
          },
          {
            "country_code": "CN",
            "timezone": "Asia/Kashgar",
            "gmt_offset": 8
          },
          {
            "country_code": "NP",
            "timezone": "Asia/Kathmandu",
            "gmt_offset": 5.75
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Khandyga",
            "gmt_offset": 10
          },
          {
            "country_code": "IN",
            "timezone": "Asia/Kolkata",
            "gmt_offset": 5.5
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Krasnoyarsk",
            "gmt_offset": 8
          },
          {
            "country_code": "MY",
            "timezone": "Asia/Kuala_Lumpur",
            "gmt_offset": 8
          },
          {
            "country_code": "MY",
            "timezone": "Asia/Kuching",
            "gmt_offset": 8
          },
          {
            "country_code": "KW",
            "timezone": "Asia/Kuwait",
            "gmt_offset": 3
          },
          {
            "country_code": "MO",
            "timezone": "Asia/Macau",
            "gmt_offset": 8
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Magadan",
            "gmt_offset": 12
          },
          {
            "country_code": "ID",
            "timezone": "Asia/Makassar",
            "gmt_offset": 8
          },
          {
            "country_code": "PH",
            "timezone": "Asia/Manila",
            "gmt_offset": 8
          },
          {
            "country_code": "OM",
            "timezone": "Asia/Muscat",
            "gmt_offset": 4
          },
          {
            "country_code": "CY",
            "timezone": "Asia/Nicosia",
            "gmt_offset": 2
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Novokuznetsk",
            "gmt_offset": 7
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Novosibirsk",
            "gmt_offset": 7
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Omsk",
            "gmt_offset": 7
          },
          {
            "country_code": "KZ",
            "timezone": "Asia/Oral",
            "gmt_offset": 5
          },
          {
            "country_code": "KH",
            "timezone": "Asia/Phnom_Penh",
            "gmt_offset": 7
          },
          {
            "country_code": "ID",
            "timezone": "Asia/Pontianak",
            "gmt_offset": 7
          },
          {
            "country_code": "KP",
            "timezone": "Asia/Pyongyang",
            "gmt_offset": 9
          },
          {
            "country_code": "QA",
            "timezone": "Asia/Qatar",
            "gmt_offset": 3
          },
          {
            "country_code": "KZ",
            "timezone": "Asia/Qyzylorda",
            "gmt_offset": 6
          },
          {
            "country_code": "MM",
            "timezone": "Asia/Rangoon",
            "gmt_offset": 6.5
          },
          {
            "country_code": "SA",
            "timezone": "Asia/Riyadh",
            "gmt_offset": 3
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Sakhalin",
            "gmt_offset": 11
          },
          {
            "country_code": "UZ",
            "timezone": "Asia/Samarkand",
            "gmt_offset": 5
          },
          {
            "country_code": "KR",
            "timezone": "Asia/Seoul",
            "gmt_offset": 9
          },
          {
            "country_code": "CN",
            "timezone": "Asia/Shanghai",
            "gmt_offset": 8
          },
          {
            "country_code": "SG",
            "timezone": "Asia/Singapore",
            "gmt_offset": 8
          },
          {
            "country_code": "TW",
            "timezone": "Asia/Taipei",
            "gmt_offset": 8
          },
          {
            "country_code": "UZ",
            "timezone": "Asia/Tashkent",
            "gmt_offset": 5
          },
          {
            "country_code": "GE",
            "timezone": "Asia/Tbilisi",
            "gmt_offset": 4
          },
          {
            "country_code": "IR",
            "timezone": "Asia/Tehran",
            "gmt_offset": 3.5
          },
          {
            "country_code": "BT",
            "timezone": "Asia/Thimphu",
            "gmt_offset": 6
          },
          {
            "country_code": "JP",
            "timezone": "Asia/Tokyo",
            "gmt_offset": 9
          },
          {
            "country_code": "MN",
            "timezone": "Asia/Ulaanbaatar",
            "gmt_offset": 8
          },
          {
            "country_code": "CN",
            "timezone": "Asia/Urumqi",
            "gmt_offset": 8
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Ust-Nera",
            "gmt_offset": 11
          },
          {
            "country_code": "LA",
            "timezone": "Asia/Vientiane",
            "gmt_offset": 7
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Vladivostok",
            "gmt_offset": 11
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Yakutsk",
            "gmt_offset": 10
          },
          {
            "country_code": "RU",
            "timezone": "Asia/Yekaterinburg",
            "gmt_offset": 6
          },
          {
            "country_code": "AM",
            "timezone": "Asia/Yerevan",
            "gmt_offset": 4
          },
          {
            "country_code": "PT",
            "timezone": "Atlantic/Azores",
            "gmt_offset": -1
          },
          {
            "country_code": "BM",
            "timezone": "Atlantic/Bermuda",
            "gmt_offset": -4
          },
          {
            "country_code": "ES",
            "timezone": "Atlantic/Canary",
            "gmt_offset": 0
          },
          {
            "country_code": "CV",
            "timezone": "Atlantic/Cape_Verde",
            "gmt_offset": -1
          },
          {
            "country_code": "FO",
            "timezone": "Atlantic/Faroe",
            "gmt_offset": 0
          },
          {
            "country_code": "PT",
            "timezone": "Atlantic/Madeira",
            "gmt_offset": 0
          },
          {
            "country_code": "IS",
            "timezone": "Atlantic/Reykjavik",
            "gmt_offset": 0
          },
          {
            "country_code": "GS",
            "timezone": "Atlantic/South_Georgia",
            "gmt_offset": -2
          },
          {
            "country_code": "FK",
            "timezone": "Atlantic/Stanley",
            "gmt_offset": -3
          },
          {
            "country_code": "SH",
            "timezone": "Atlantic/St_Helena",
            "gmt_offset": 0
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Adelaide",
            "gmt_offset": 10.5
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Brisbane",
            "gmt_offset": 10
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Broken_Hill",
            "gmt_offset": 10.5
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Currie",
            "gmt_offset": 11
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Darwin",
            "gmt_offset": 9.5
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Eucla",
            "gmt_offset": 8.75
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Hobart",
            "gmt_offset": 11
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Lindeman",
            "gmt_offset": 10
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Lord_Howe",
            "gmt_offset": 11
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Melbourne",
            "gmt_offset": 11
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Perth",
            "gmt_offset": 8
          },
          {
            "country_code": "AU",
            "timezone": "Australia/Sydney",
            "gmt_offset": 11
          },
          {
            "country_code": "NL",
            "timezone": "Europe/Amsterdam",
            "gmt_offset": 1
          },
          {
            "country_code": "AD",
            "timezone": "Europe/Andorra",
            "gmt_offset": 1
          },
          {
            "country_code": "GR",
            "timezone": "Europe/Athens",
            "gmt_offset": 2
          },
          {
            "country_code": "RS",
            "timezone": "Europe/Belgrade",
            "gmt_offset": 1
          },
          {
            "country_code": "DE",
            "timezone": "Europe/Berlin",
            "gmt_offset": 1
          },
          {
            "country_code": "SK",
            "timezone": "Europe/Bratislava",
            "gmt_offset": 1
          },
          {
            "country_code": "BE",
            "timezone": "Europe/Brussels",
            "gmt_offset": 1
          },
          {
            "country_code": "RO",
            "timezone": "Europe/Bucharest",
            "gmt_offset": 2
          },
          {
            "country_code": "HU",
            "timezone": "Europe/Budapest",
            "gmt_offset": 1
          },
          {
            "country_code": "DE",
            "timezone": "Europe/Busingen",
            "gmt_offset": 1
          },
          {
            "country_code": "MD",
            "timezone": "Europe/Chisinau",
            "gmt_offset": 2
          },
          {
            "country_code": "DK",
            "timezone": "Europe/Copenhagen",
            "gmt_offset": 1
          },
          {
            "country_code": "IE",
            "timezone": "Europe/Dublin",
            "gmt_offset": 0
          },
          {
            "country_code": "GI",
            "timezone": "Europe/Gibraltar",
            "gmt_offset": 1
          },
          {
            "country_code": "GG",
            "timezone": "Europe/Guernsey",
            "gmt_offset": 0
          },
          {
            "country_code": "FI",
            "timezone": "Europe/Helsinki",
            "gmt_offset": 2
          },
          {
            "country_code": "IM",
            "timezone": "Europe/Isle_of_Man",
            "gmt_offset": 0
          },
          {
            "country_code": "TR",
            "timezone": "Europe/Istanbul",
            "gmt_offset": 2
          },
          {
            "country_code": "JE",
            "timezone": "Europe/Jersey",
            "gmt_offset": 0
          },
          {
            "country_code": "RU",
            "timezone": "Europe/Kaliningrad",
            "gmt_offset": 3
          },
          {
            "country_code": "UA",
            "timezone": "Europe/Kiev",
            "gmt_offset": 2
          },
          {
            "country_code": "PT",
            "timezone": "Europe/Lisbon",
            "gmt_offset": 0
          },
          {
            "country_code": "SI",
            "timezone": "Europe/Ljubljana",
            "gmt_offset": 1
          },
          {
            "country_code": "GB",
            "timezone": "Europe/London",
            "gmt_offset": 0
          },
          {
            "country_code": "LU",
            "timezone": "Europe/Luxembourg",
            "gmt_offset": 1
          },
          {
            "country_code": "ES",
            "timezone": "Europe/Madrid",
            "gmt_offset": 1
          },
          {
            "country_code": "MT",
            "timezone": "Europe/Malta",
            "gmt_offset": 1
          },
          {
            "country_code": "AX",
            "timezone": "Europe/Mariehamn",
            "gmt_offset": 2
          },
          {
            "country_code": "BY",
            "timezone": "Europe/Minsk",
            "gmt_offset": 3
          },
          {
            "country_code": "MC",
            "timezone": "Europe/Monaco",
            "gmt_offset": 1
          },
          {
            "country_code": "RU",
            "timezone": "Europe/Moscow",
            "gmt_offset": 4
          },
          {
            "country_code": "NO",
            "timezone": "Europe/Oslo",
            "gmt_offset": 1
          },
          {
            "country_code": "FR",
            "timezone": "Europe/Paris",
            "gmt_offset": 1
          },
          {
            "country_code": "ME",
            "timezone": "Europe/Podgorica",
            "gmt_offset": 1
          },
          {
            "country_code": "CZ",
            "timezone": "Europe/Prague",
            "gmt_offset": 1
          },
          {
            "country_code": "LV",
            "timezone": "Europe/Riga",
            "gmt_offset": 2
          },
          {
            "country_code": "IT",
            "timezone": "Europe/Rome",
            "gmt_offset": 1
          },
          {
            "country_code": "RU",
            "timezone": "Europe/Samara",
            "gmt_offset": 4
          },
          {
            "country_code": "SM",
            "timezone": "Europe/San_Marino",
            "gmt_offset": 1
          },
          {
            "country_code": "BA",
            "timezone": "Europe/Sarajevo",
            "gmt_offset": 1
          },
          {
            "country_code": "UA",
            "timezone": "Europe/Simferopol",
            "gmt_offset": 2
          },
          {
            "country_code": "MK",
            "timezone": "Europe/Skopje",
            "gmt_offset": 1
          },
          {
            "country_code": "BG",
            "timezone": "Europe/Sofia",
            "gmt_offset": 2
          },
          {
            "country_code": "SE",
            "timezone": "Europe/Stockholm",
            "gmt_offset": 1
          },
          {
            "country_code": "EE",
            "timezone": "Europe/Tallinn",
            "gmt_offset": 2
          },
          {
            "country_code": "AL",
            "timezone": "Europe/Tirane",
            "gmt_offset": 1
          },
          {
            "country_code": "UA",
            "timezone": "Europe/Uzhgorod",
            "gmt_offset": 2
          },
          {
            "country_code": "LI",
            "timezone": "Europe/Vaduz",
            "gmt_offset": 1
          },
          {
            "country_code": "VA",
            "timezone": "Europe/Vatican",
            "gmt_offset": 1
          },
          {
            "country_code": "AT",
            "timezone": "Europe/Vienna",
            "gmt_offset": 1
          },
          {
            "country_code": "LT",
            "timezone": "Europe/Vilnius",
            "gmt_offset": 2
          },
          {
            "country_code": "RU",
            "timezone": "Europe/Volgograd",
            "gmt_offset": 4
          },
          {
            "country_code": "PL",
            "timezone": "Europe/Warsaw",
            "gmt_offset": 1
          },
          {
            "country_code": "HR",
            "timezone": "Europe/Zagreb",
            "gmt_offset": 1
          },
          {
            "country_code": "UA",
            "timezone": "Europe/Zaporozhye",
            "gmt_offset": 2
          },
          {
            "country_code": "CH",
            "timezone": "Europe/Zurich",
            "gmt_offset": 1
          },
          {
            "country_code": "MG",
            "timezone": "Indian/Antananarivo",
            "gmt_offset": 3
          },
          {
            "country_code": "IO",
            "timezone": "Indian/Chagos",
            "gmt_offset": 6
          },
          {
            "country_code": "CX",
            "timezone": "Indian/Christmas",
            "gmt_offset": 7
          },
          {
            "country_code": "CC",
            "timezone": "Indian/Cocos",
            "gmt_offset": 6.5
          },
          {
            "country_code": "KM",
            "timezone": "Indian/Comoro",
            "gmt_offset": 3
          },
          {
            "country_code": "TF",
            "timezone": "Indian/Kerguelen",
            "gmt_offset": 5
          },
          {
            "country_code": "SC",
            "timezone": "Indian/Mahe",
            "gmt_offset": 4
          },
          {
            "country_code": "MV",
            "timezone": "Indian/Maldives",
            "gmt_offset": 5
          },
          {
            "country_code": "MU",
            "timezone": "Indian/Mauritius",
            "gmt_offset": 4
          },
          {
            "country_code": "YT",
            "timezone": "Indian/Mayotte",
            "gmt_offset": 3
          },
          {
            "country_code": "RE",
            "timezone": "Indian/Reunion",
            "gmt_offset": 4
          },
          {
            "country_code": "WS",
            "timezone": "Pacific/Apia",
            "gmt_offset": 14
          },
          {
            "country_code": "NZ",
            "timezone": "Pacific/Auckland",
            "gmt_offset": 13
          },
          {
            "country_code": "NZ",
            "timezone": "Pacific/Chatham",
            "gmt_offset": 13.75
          },
          {
            "country_code": "FM",
            "timezone": "Pacific/Chuuk",
            "gmt_offset": 10
          },
          {
            "country_code": "CL",
            "timezone": "Pacific/Easter",
            "gmt_offset": -5
          },
          {
            "country_code": "VU",
            "timezone": "Pacific/Efate",
            "gmt_offset": 11
          },
          {
            "country_code": "KI",
            "timezone": "Pacific/Enderbury",
            "gmt_offset": 13
          },
          {
            "country_code": "TK",
            "timezone": "Pacific/Fakaofo",
            "gmt_offset": 13
          },
          {
            "country_code": "FJ",
            "timezone": "Pacific/Fiji",
            "gmt_offset": 13
          },
          {
            "country_code": "TV",
            "timezone": "Pacific/Funafuti",
            "gmt_offset": 12
          },
          {
            "country_code": "EC",
            "timezone": "Pacific/Galapagos",
            "gmt_offset": -6
          },
          {
            "country_code": "PF",
            "timezone": "Pacific/Gambier",
            "gmt_offset": -9
          },
          {
            "country_code": "SB",
            "timezone": "Pacific/Guadalcanal",
            "gmt_offset": 11
          },
          {
            "country_code": "GU",
            "timezone": "Pacific/Guam",
            "gmt_offset": 10
          },
          {
            "country_code": "US",
            "timezone": "Pacific/Honolulu",
            "gmt_offset": -10
          },
          {
            "country_code": "UM",
            "timezone": "Pacific/Johnston",
            "gmt_offset": -10
          },
          {
            "country_code": "KI",
            "timezone": "Pacific/Kiritimati",
            "gmt_offset": 14
          },
          {
            "country_code": "FM",
            "timezone": "Pacific/Kosrae",
            "gmt_offset": 11
          },
          {
            "country_code": "MH",
            "timezone": "Pacific/Kwajalein",
            "gmt_offset": 12
          },
          {
            "country_code": "MH",
            "timezone": "Pacific/Majuro",
            "gmt_offset": 12
          },
          {
            "country_code": "PF",
            "timezone": "Pacific/Marquesas",
            "gmt_offset": -9.5
          },
          {
            "country_code": "UM",
            "timezone": "Pacific/Midway",
            "gmt_offset": -11
          },
          {
            "country_code": "NR",
            "timezone": "Pacific/Nauru",
            "gmt_offset": 12
          },
          {
            "country_code": "NU",
            "timezone": "Pacific/Niue",
            "gmt_offset": -11
          },
          {
            "country_code": "NF",
            "timezone": "Pacific/Norfolk",
            "gmt_offset": 11.5
          },
          {
            "country_code": "NC",
            "timezone": "Pacific/Noumea",
            "gmt_offset": 11
          },
          {
            "country_code": "AS",
            "timezone": "Pacific/Pago_Pago",
            "gmt_offset": -11
          },
          {
            "country_code": "PW",
            "timezone": "Pacific/Palau",
            "gmt_offset": 9
          },
          {
            "country_code": "PN",
            "timezone": "Pacific/Pitcairn",
            "gmt_offset": -8
          },
          {
            "country_code": "FM",
            "timezone": "Pacific/Pohnpei",
            "gmt_offset": 11
          },
          {
            "country_code": "PG",
            "timezone": "Pacific/Port_Moresby",
            "gmt_offset": 10
          },
          {
            "country_code": "CK",
            "timezone": "Pacific/Rarotonga",
            "gmt_offset": -10
          },
          {
            "country_code": "MP",
            "timezone": "Pacific/Saipan",
            "gmt_offset": 10
          },
          {
            "country_code": "PF",
            "timezone": "Pacific/Tahiti",
            "gmt_offset": -10
          },
          {
            "country_code": "KI",
            "timezone": "Pacific/Tarawa",
            "gmt_offset": 12
          },
          {
            "country_code": "TO",
            "timezone": "Pacific/Tongatapu",
            "gmt_offset": 13
          },
          {
            "country_code": "UM",
            "timezone": "Pacific/Wake",
            "gmt_offset": 12
          },
          {
            "country_code": "WF",
            "timezone": "Pacific/Wallis",
            "gmt_offset": 12
          }
        ]';
        if(!Schema::hasTable('timezone')) {
            Schema::create('timezone', function (Blueprint $table) {
                $table->id();
                $table->string('country_code');
                $table->string('timezone');
                $table->integer('gmt_offset');
                $table->timestamps();
            });

            DB::table('timezone')->insert(json_decode($timezones, true));
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('timezone');
    }
};
