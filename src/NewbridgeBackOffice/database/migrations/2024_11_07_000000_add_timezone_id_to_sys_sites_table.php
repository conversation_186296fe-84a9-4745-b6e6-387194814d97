<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('sys_sites', function (Blueprint $table) {
            if(!Schema::hasColumn('sys_sites', 'timezone_id')) {
                $table->unsignedBigInteger('timezone_id')->nullable()->after('telephone');
                $table->foreign('timezone_id')->references('id')->on('timezone');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('sys_sites', function (Blueprint $table) {
            if(Schema::hasColumn('sys_sites', 'timezone_id')) {
                $table->dropForeign(['timezone_id']);
                $table->dropColumn('timezone_id');
            }
        });
    }
};
