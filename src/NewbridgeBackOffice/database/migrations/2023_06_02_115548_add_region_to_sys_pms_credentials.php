<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if(!Schema::hasTable('sys_pms_credentials')) {
            Schema::create('sys_pms_credentials', function (Blueprint $table) {
                $table->increments('id');
                $table->integer('company_id');
                $table->integer('site_num');
                $table->longText('value1')->nullable();
                $table->longText('value2')->nullable();
                $table->longText('value3')->nullable();
                $table->longText('value4')->nullable();
                $table->longText('value5')->nullable();
                $table->longText('value6')->nullable();
                $table->longText('value7')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }
        Schema::table('sys_pms_credentials', function (Blueprint $table) {
            $table->longText('value8')->after('value7');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sys_pms_credentials', function (Blueprint $table) {
            $table->removeColumn('value8');
        });
    }
};
