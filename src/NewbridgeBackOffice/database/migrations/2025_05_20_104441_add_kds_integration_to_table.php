<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrations;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $integrations = SiteIntegrations::create([
            'name' => 'Newbridge KDS',
            'bit' => 1024,
            'image' => 'kds',
            'description' => 'Kitchen Display System',
            'configuration' => '{
                  "type": "internal_settings",
                  "settings": [
                    {
                      "name": "Access Key",
                      "description": "The company terminal access key",
                      "default_value": "",
                      "setting_id": "AccessKey",
                      "type": "company_property",
                      "property_name": "terminal_access_key",
                      "hidden": true,
                      "required": true
                    },
                    {
                      "name": "Site Number",
                      "description": "The site number to active KDS for on.",
                      "type": "site_property",
                      "property_name": "site_num",
                      "setting_id": "SiteNumber",
                      "default_value": "",
                      "hidden": true,
                      "required": true
                    },
                    {
                      "name": "Company ID",
                      "description": "The Company ID",
                      "default_value": "",
                      "setting_id": "CompanyId",
                      "type": "company_property",
                      "property_name": "id",
                      "hidden": true,
                      "required": true
                    },
                    {
                      "name": "Site Name",
                      "description": "The Site Name",
                      "default_value": "",
                      "setting_id": "SiteName",
                      "type": "site_property",
                      "property_name": "site_name",
                      "hidden": true,
                      "required": true
                    },
                    {
                      "name": "Kitchen Display Screen Enabled",
                      "description": "",
                      "default_value": false,
                      "setting_id": "Enabled",
                      "type": "bool",
                      "required": true
                    }
                  ],
                  "api_events": [
                    {
                      "headers": [
                        {
                          "header": "X-Api-Key",
                          "value": "config::kds.api_key",
                          "encoding": false
                        }
                      ],
                      "auth": "headers",
                      "endpoints": {
                        "production": "https://kds-api.eu.guestline.app/Login/Sites/Create",
                        "development": "https://kds-api.ci.guestline.app/Login/Sites/Create",
                        "local": "https://kds-api.ci.guestline.app/Login/Sites/Create",
                        "staging": "https://kds-api.stage.guestline.app/Login/Sites/Create"
                      },
                      "method": "post"
                    }
                  ],
                  "select_master": false,
                  "can_override_path": false,
                  "updates_terminal_settings": false,
                  "company_integration": false,
                  "generate_api_tokens": true,
                  "process_api_events": true
                }'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SiteIntegrations::where('bit', 1024)->delete();
    }
};
