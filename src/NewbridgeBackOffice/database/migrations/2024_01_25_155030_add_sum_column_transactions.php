<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        /* if (!Schema::hasColumn('plu_stock_transactions', 'product_quantity')) {
             Schema::table('plu_stock_transactions', function (Blueprint $table) {
                 $table->float('product_quantity')->after('quantity')->default(0);
             });
         }*/
    }

    public function down(): void
    {
        /* Schema::table('plu_stock_transactions', function (Blueprint $table) {
             $table->dropColumn('product_quantity');
         });*/
    }
};
