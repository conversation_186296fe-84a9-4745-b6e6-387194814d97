<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('sys_reasons')) {
            Schema::create('sys_reasons', function (Blueprint $table) {
                $table->id();
                $table->integer('company_id');
                $table->string('guid', 64)->default('');
                $table->string('reason', 255)->nullable();
                $table->integer('reason_type')->default(1);
                $table->boolean('stock')->default(false);
                $table->integer('received_from')->nullable();
                $table->dateTime('received_at')->default(0);
                $table->boolean('hidden')->default(false);
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sys_reasons');
    }
};
