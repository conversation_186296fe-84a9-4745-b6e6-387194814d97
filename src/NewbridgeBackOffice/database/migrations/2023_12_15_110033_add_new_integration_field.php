<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

return new class () extends Migration {
    public function up(): void
    {
        Schema::table('new_app_integrations', function (Blueprint $table) {
            $table->string('api_test_url')->after('description')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('new_app_integrations', function (Blueprint $table) {
            $table->dropColumn('api_test_url');
        });
    }
};
