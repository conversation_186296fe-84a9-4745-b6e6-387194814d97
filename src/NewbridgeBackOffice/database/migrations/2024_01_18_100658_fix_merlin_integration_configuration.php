<?php

use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        \NewbridgeWeb\Repositories\Integrations\SiteIntegrations::where('name', 'Merlin')->update(['configuration' => '{"type":"internal_settings","settings":[{"name":"Site ID","description":"Enter your site ID","default_value":"","setting_id":"site_id","type":"string"},{"name":"Username","description":"Enter your username","default_value":"","setting_id":"username","type":"string"},{"name":"Password","description":"Enter your password","default_value":"","setting_id":"password","type":"string"},{"name":"URL","description":"Enter the API base url","default_value":"https://apps.quickmerlin.com","setting_id":"url","type":"string"}],"select_master":false,"can_override_path":false,"updates_terminal_settings":false,"company_integration":false,"generate_api_tokens":false,"process_api_events":false,"mappings":{"dayparts":true,"company_mapping":{"pos_setting_id":"PMSMaps","pos_setting_name":"PMSMaps","group_as":"hotels"},"types":{"departments":{"name":"displayname","guid":"guid","source_class":"MerlinAPI\\\MerlinEntities"},"discounts":{"name":"displayname","guid":"CommandUID","source_class":"MerlinAPI\\\MerlinEntities"},"functions":{"name":"displayname","guid":"CommandUID","source_class":"MerlinAPI\\\MerlinEntities"},"payments":{"name":"displayname","guid":"CommandUID","source_class":"MerlinAPI\\\MerlinPayments"}}}}']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
