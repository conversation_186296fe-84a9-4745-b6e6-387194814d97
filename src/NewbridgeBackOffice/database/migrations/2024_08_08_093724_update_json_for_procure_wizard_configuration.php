<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrations;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrationSites;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $config = [
            "type" => "internal_settings",
            "artisan_command" => "procurewizard:sales",
            "settings" => [
                [
                    "name" => "Username",
                    "description" => "Enter ProcureWizard username",
                    "default_value" => "",
                    "setting_id" => "username",
                    "type" => "string"
                ],
                [
                    "name" => "Password",
                    "description" => "Enter ProcureWizard password",
                    "default_value" => "",
                    "setting_id" => "password",
                    "type" => "string"
                ],
                [
                    "name" => "Export Format",
                    "description" => "Select which type of integration format you require.",
                    "type" => "select",
                    "setting_id" => "export_format",
                    "source" => "NewbridgeWeb\\Repositories\\Integrations\\SiteIntegrationOptions",
                    "filters" => [
                        "procurewizard_type"
                    ],
                    "value" => "value",
                    "display" => "name",
                    "default_value" => ""
                ]
            ],
            "select_master" => false,
            "can_override_path" => false,
            "updates_terminal_settings" => false,
            "company_integration" => false,
            "generate_api_tokens" => false,
            "process_api_events" => false
        ];

        SiteIntegrations::where('bit', '64')->update(['configuration' => $config]);
        $integration = DB::table('new_app_integrations')->where('bit', '64')->first();
        $configs = SiteIntegrationSites::where('integration_id', $integration->id)->get();
        foreach($configs as $config){
            $data = $config->configuration;
            $data['export_format'] = '2';
            $config->configuration = $data;
            $config->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
