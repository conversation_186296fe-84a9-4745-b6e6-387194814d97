<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('plu_promotion')) {
            if (!Schema::hasColumn('plu_promotion', 'isactive')) {
                Schema::table('plu_promotion', function (Blueprint $table) {
                    $table->boolean('isactive')->default(true);
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
