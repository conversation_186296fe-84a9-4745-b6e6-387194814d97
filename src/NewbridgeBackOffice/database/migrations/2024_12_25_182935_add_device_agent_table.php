<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('search_analytics', 'device_agent')) {
            return;
        }

        Schema::table('search_analytics', function (Blueprint $table) {
            $table->string('device_agent')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (!Schema::hasColumn('search_analytics', 'device_agent')) {
            return;
        }

        Schema::table('search_analytics', function (Blueprint $table) {
            $table->removeColumn('device_agent');
        });
    }
};
