<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use NewbridgeWeb\Repositories\Integrations\Integrations;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Integrations::where('id', 13)->delete();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
