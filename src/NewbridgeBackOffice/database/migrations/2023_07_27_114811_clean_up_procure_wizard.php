<?php

use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $existing = DB::table('new_app_integrations')->where('bit', 64)->get();
        foreach ($existing as $row) {
            $orphans = DB::table('new_app_integrations_sites')->where('integration_id', $row->id)->get();
            foreach ($orphans as $orphan) {
                DB::table('new_app_integrations_sites')->where('id', $orphan->id)->delete();
            }
            DB::table('new_app_integrations')->where('id', $row->id)->delete();
        }
        \NewbridgeWeb\Repositories\Integrations\SiteIntegrations::insert([
            "Name" => "ProcureWizard Stock Cost Sales",
            "bit" => 64,
            "image" => "https://www.theaccessgroup.com/media/yvdjebkj/purchasetopayhospitalityicon.png",
            "description" => "ProcureWizard Stock Cost Sales",
            "configuration" => '{"type": "internal_settings", "artisan_command":"procurewizard:sales", "settings":[{"name": "Username", "description": "Enter ProcureWizard username", "default_value": "", "setting_id": "username", "type": "string"}, {"name": "Password", "description": "Enter ProcureWizard password", "default_value": "", "setting_id": "password", "type": "string"}], "select_master":false, "can_override_path":false, "updates_terminal_settings":false, "company_integration":false, "generate_api_tokens":false, "process_api_events":false}'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
