<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plu_products', function ($table) {
            $table->string('tax_guid', 128)->default(null)->nullable()->change();
        });
        \NewbridgeWeb\Repositories\Products::where('tax_guid', '')->update(['tax_guid' => null]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
