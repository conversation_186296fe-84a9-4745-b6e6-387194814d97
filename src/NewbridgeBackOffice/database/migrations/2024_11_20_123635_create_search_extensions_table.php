<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        Schema::create('search_extensions', function (Blueprint $table) {
            $table->bigInteger('id')->autoIncrement();
            $table->uuid();
            $table->integer('type');
            $table->string('title');
            $table->text('info')->nullable();
            $table->string('url')->nullable();
            $table->json('keywords')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('search_extensions');
    }
};
