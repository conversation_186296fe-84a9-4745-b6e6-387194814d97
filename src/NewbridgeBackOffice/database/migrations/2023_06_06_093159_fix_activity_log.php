<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::table('activity_log', function (Blueprint $table) {
            if(!Schema::hasColumn('activity_log', 'batch_uuid')) {
                $table->string('batch_uuid', 255)->nullable();
            }
            if(!Schema::hasColumn('activity_log', 'event')) {
                $table->string('event', 255)->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
