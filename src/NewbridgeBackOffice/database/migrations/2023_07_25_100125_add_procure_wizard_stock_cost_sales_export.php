<?php

use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        \NewbridgeWeb\Repositories\Integrations\SiteIntegrations::insert([
            "Name" => "ProcureWizard Stock Cost Sales",
            "bit" => 64,
            "image" => "https://www.theaccessgroup.com/media/yvdjebkj/purchasetopayhospitalityicon.png",
            "description" => "ProcureWizard Stock Cost Sales",
            "configuration" => '{"type": "internal_settings", "settings":[{"name": "Username", "description": "Enter ProcureWizard username", "default_value": "", "setting_id": "username", "type": "string"}, {"name": "Password", "description": "Enter ProcureWizard password", "default_value": "", "setting_id": "password", "type": "string"}], "select_master":false, "can_override_path":false, "updates_terminal_settings":false, "company_integration":false, "generate_api_tokens":false, "process_api_events":false}'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
