<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('plu_stock_summary', function (Blueprint $table) {
            if(!Schema::hasColumn('plu_stock_summary', 'site_source')) {
                $table->integer('site_source')->nullable();
            }
            //            if(!Schema::hasColumn('plu_stock_summary', 'event')){
            //                $table->string('event', 255)->nullable();
            //            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
};
