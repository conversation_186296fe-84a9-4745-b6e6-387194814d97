<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if(!Schema::hasColumn('sys_company', 'notification_email')){
            Schema::table('sys_company', function (Blueprint $table) {
                $table->string('notification_email')->nullable()->after('email');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if(Schema::hasColumn('sys_company', 'notification_email')){
            Schema::table('sys_company', function (Blueprint $table) {
                $table->removeColumn('notification_email');
            });
        }
    }
};
