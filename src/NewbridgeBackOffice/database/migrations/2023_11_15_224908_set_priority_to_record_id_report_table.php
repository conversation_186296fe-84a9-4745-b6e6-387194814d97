<?php

use Illuminate\Database\Migrations\Migration;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $reports = \NewbridgeWeb\Repositories\Reports::all();

        foreach($reports as $report) {
            $report->priority = $report->id;
            $report->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
