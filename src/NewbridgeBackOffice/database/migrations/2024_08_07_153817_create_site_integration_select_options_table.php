<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('new_app_integrations_options', function (Blueprint $table) {
            $table->id();
            $table->string('filter', 255);
            $table->string('name', 255);
            $table->string('value', 255);
            $table->timestamps();
        });

        DB::table('new_app_integrations_options')->insert([
            [
                'filter' => 'procurewizard_type',
                'name' => 'EPoS Sales & Waste Flash',
                'value' => '2',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'filter' => 'procurewizard_type',
                'name' => 'EPoS Sales into Advanced Stock Module as Retail Sales',
                'value' => '4',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('new_app_integrations_options');
    }
};
