<?php

use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use NewbridgeWeb\Enums\SearchExtensionTypes;
use NewbridgeWeb\Models\Search\SearchExtensions;
use Ramsey\Uuid\Uuid;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $extensions = [
            [

                'title' => 'Dashboard',
                'info' => 'Go to the dashboard',
                'url' => '/',
                'keywords' => ['dashboard', 'home', 'main', 'start'],
            ],
            [

                'title' => 'Customers Management',
                'info' => 'Go to the customers page',
                'url' => '/customers',
                'keywords' => ['customers', 'clients', 'people'],
            ],
            [

                'title' => 'Products',
                'info' => 'Go to the products page',
                'url' => '/products',
                'keywords' => ['products', 'items', 'goods'],
            ],
            [

                'title' => 'Transactions',
                'info' => 'Go to the transactions list page',
                'url' => '/transactions',
                'keywords' => ['transactions', 'sales', 'purchases'],
            ],
            [

                'title' => 'App Orders',
                'info' => 'Go to the app orders page',
                'url' => '/web-orders/table',
                'keywords' => ['app', 'orders', 'web'],
            ],
            [

                'title' => 'Manage Sites',
                'info' => 'Go to the sites management page',
                'url' => '/company/sites',
                'keywords' => ['sites', 'manage', 'company'],
            ],
            [

                'title' => 'Logout',
                'info' => 'Logout from the system',
                'url' => '/logout',
                'keywords' => ['logout', 'exit', 'sign out'],
            ],
            [

                'title' => 'Loyalty Schemes',
                'info' => 'Go to the loyalty schemes page',
                'url' => '/customers/groups',
                'keywords' => ['loyalty', 'schemes', 'groups'],
            ],
            [

                'title' => 'Clerks',
                'info' => 'Go to the clerks page',
                'url' => '/clerks',
                'keywords' => ['clerks', 'staff', 'employees'],
            ],
            [

                'title' => 'Permission Groups',
                'info' => 'Go to the permission groups page',
                'url' => '/clerks/permission-groups',
                'keywords' => ['permission', 'groups', 'roles'],
            ],
            [

                'title' => 'Clerk Attendance',
                'info' => 'Go to the clerk attendance page',
                'url' => '/clerks/attendance',
                'keywords' => ['attendance', 'clerks', 'staff'],
            ],
            [

                'title' => 'Clerk Rota',
                'info' => 'Go to the clerk rota page',
                'url' => '/clerks/rota',
                'keywords' => ['rota', 'schedule', 'clerks'],
            ],
            [

                'title' => 'Clerk Weekly Rota',
                'info' => 'Go to the clerk weekly rota page',
                'url' => '/clerks/rota/weekly',
                'keywords' => ['weekly', 'rota', 'schedule'],
            ],
            [

                'title' => 'Clerk Shift Types',
                'info' => 'Go to the clerk shift types page',
                'url' => '/clerks/shift-types',
                'keywords' => ['shift', 'types', 'clerks'],
            ],
            [

                'title' => 'Clerk Roles',
                'info' => 'Go to the clerk roles page',
                'url' => '/clerks/roles',
                'keywords' => ['roles', 'clerks', 'staff'],
            ],
            [

                'title' => 'Clerk Areas',
                'info' => 'Go to the clerk areas page',
                'url' => '/clerks/areas',
                'keywords' => ['areas', 'clerks', 'staff'],
            ],
            [

                'title' => 'Promotions',
                'info' => 'Go to the promotions page',
                'url' => '/promotions',
                'keywords' => ['promotions', 'offers', 'deals'],
            ],
            [

                'title' => 'Discounts',
                'info' => 'Go to the discounts page',
                'url' => '/discounts',
                'keywords' => ['discounts', 'offers', 'deals'],
            ],
            [

                'title' => 'Screen Programming',
                'info' => 'Go to the screen programming page',
                'url' => '/screens',
                'keywords' => ['screen', 'programming', 'setup'],
            ],
            [

                'title' => 'Product App Availability',
                'info' => 'Go to the product app availability page',
                'url' => '/products/app-availability',
                'keywords' => ['product', 'app', 'availability'],
            ],
            [

                'title' => 'Product Price Levels',
                'info' => 'Go to the product price levels page',
                'url' => '/products/price-levels',
                'keywords' => ['product', 'price', 'levels'],
            ],
            [

                'title' => 'Product SKUs',
                'info' => 'Go to the product SKUs page',
                'url' => '/products/skus',
                'keywords' => ['product', 'skus', 'identifiers'],
            ],
            [

                'title' => 'Departments',
                'info' => 'Go to the departments page',
                'url' => '/departments',
                'keywords' => ['departments', 'sections', 'divisions'],
            ],
            [

                'title' => 'Sub Departments',
                'info' => 'Go to the sub departments page',
                'url' => '/sub-departments',
                'keywords' => ['sub', 'departments', 'sections'],
            ],
            [

                'title' => 'Revenue Centers',
                'info' => 'Go to the revenue centers page',
                'url' => '/company/revenue-centers',
                'keywords' => ['revenue', 'centers', 'income'],
            ],
            [

                'title' => 'Day Parts',
                'info' => 'Go to the day parts page',
                'url' => '/day-parts',
                'keywords' => ['day', 'parts', 'time'],
            ],
            [

                'title' => 'Modifier Groups',
                'info' => 'Go to the modifier groups page',
                'url' => '/modifier-groups',
                'keywords' => ['modifier', 'groups', 'options'],
            ],
            [

                'title' => 'Table Plans',
                'info' => 'Go to the table plans page',
                'url' => '/table-plans',
                'keywords' => ['table', 'plans', 'layout'],
            ],
            [

                'title' => 'Payment Types',
                'info' => 'Go to the payment types page',
                'url' => '/payments',
                'keywords' => ['payment', 'types', 'methods'],
            ],
            [

                'title' => 'Tax Rules',
                'info' => 'Go to the tax rules page',
                'url' => '/tax/rules',
                'keywords' => ['tax', 'rules', 'regulations'],
            ],
            [

                'title' => 'Tax Rates',
                'info' => 'Go to the tax rates page',
                'url' => '/taxrates',
                'keywords' => ['tax', 'rates', 'percentages'],
            ],
            [

                'title' => 'Receipt Layouts',
                'info' => 'Go to the receipt layouts page',
                'url' => '/receipts',
                'keywords' => ['receipt', 'layouts', 'design'],
            ],
            [

                'title' => 'Manage Reasons',
                'info' => 'Go to the manage reasons page',
                'url' => '/reasons',
                'keywords' => ['manage', 'reasons', 'causes'],
            ],
            [

                'title' => 'Kitchen Printer Categories',
                'info' => 'Go to the kitchen printer categories page',
                'url' => '/course-separators',
                'keywords' => ['kitchen', 'printer', 'categories'],
            ]
        ];

        collect($extensions)->each(function ($extension) {
            SearchExtensions::create([
                'uuid' => Uuid::uuid4(),
                'type' => SearchExtensionTypes::LINK->value,
                'title' => $extension['title'],
                'info' => $extension['info'],
                'url' => $extension['url'],
                'keywords' => $extension['keywords'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SearchExtensions::get()->each(function ($extension) {
            $extension->delete();
        });
    }
};
