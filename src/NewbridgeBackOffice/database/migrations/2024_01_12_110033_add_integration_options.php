<?php

use Illuminate\Database\Migrations\Migration;
use NewbridgeWeb\Repositories\Integrations\SiteIntegrations;

return new class () extends Migration {
    public function up(): void
    {
        $integration = SiteIntegrations::where('name', 'RotaReady')->first();

        if (!isset($integration->configuration['mappings'])) {
            $config = $integration->configuration;

            $config['mappings'] = [
                'settings' => [
                    [
                        "name" => "Entity ID",
                        "description" => "Select your site / entity",
                        "default_value" => "",
                        "setting_id" => "entity_id",
                        "type" => "select",
                        "source_class" => "RotaReadyAPI\\RotaReadyEntities"
                    ]
                ],
                'dayparts' => false,
                "company_mapping" => [
                    "pos_setting_id" => null,
                    "pos_setting_name" => null,
                    "group_as" => "hotels"
                ],
                'types' => [
                    'departments' => [
                        'name' => 'displayname',
                        'guid' => 'guid',
                        'source_class' => 'RotaReadyAPI\\RotaReadyEntitiesStream'
                    ],
                    'discounts' => [
                        'name' => 'displayname',
                        'guid' => 'CommandUID',
                        'source_class' => 'RotaReadyAPI\\RotaReadyEntitiesStream'
                    ]
                ]
            ];

            $integration->configuration = $config;
            $integration->save();
        }
    }

    public function down(): void
    {
        $integration = SiteIntegrations::where('name', 'RotaReady')->get();

        if (isset($integration->configuration['mappings'])) {
            $config = $integration->configuration;

            unset($config['mapping']);

            $integration->configuration = $config;
            $integration->save();
        }
    }
};
