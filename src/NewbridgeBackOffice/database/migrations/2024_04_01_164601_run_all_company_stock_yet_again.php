<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use NewbridgeWeb\Jobs\ProductsStocks\Calculate\ProductsStockForCompanyJob;
use NewbridgeWeb\Repositories\Company;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $companies = Company::all();

        foreach($companies as $company) {
            dispatch(new ProductsStockForCompanyJob($company->id))->onQueue(config('app.env'));
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
