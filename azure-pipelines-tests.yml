pool:
  vmImage: "ubuntu-latest"

variables:
  COMPOSER_CACHE_DIR: $(Pipeline.Workspace)/.composer

name: "Testing $(Date:yyyyMMdd).$(Rev:r)"

resources:
  containers:
    - container: builder
      image: 'sync667/awesome-laravel-base-builder:latest'
      command: 'tail -f /dev/null'
      options: '--name builder'
    - container: redis
      image: redis:7-alpine
      ports:
        - 6379:6379

stages:
  - stage: Tests
    jobs:
      - job: Tests_Run_And_Coverage
        services:
          builder: builder
          redis: redis
        steps:
          - task: DownloadPipelineArtifact@2
            inputs:
              artifact: 'newbridge-web-base-test-db'
              project: 'Newbridge'
              buildType: 'specific'
              buildVersionToDownload: 'latest'
              definition: 'Newbridge-Web-Base-Build'
              path: '$(Pipeline.Workspace)'
            displayName: 'Download Test Database Image Artifact'

          - script: |
              docker load -i $(Pipeline.Workspace)/newbridge-web-base-test-db.tar
            displayName: 'Load Test Database Image'

          - script: |
              docker run -d --name mysql -p 3307:3306 \
                -e MYSQL_DATABASE=newbridge_test \
                -e MYSQL_ROOT_PASSWORD=secret \
                -e MYSQL_PASSWORD=secret \
                -e MYSQL_USER=newbridge \
                newbridge-web-base-test-db:latest
            displayName: 'Start Test Database Container'

          - script: mkdir -p $(COMPOSER_CACHE_DIR)
            displayName: "Create Composer Cache Directory"

          - task: Cache@2
            inputs:
              key: "composer | src/NewbridgeBackOffice/composer.lock"
              restoreKeys: "composer"
              path: $(COMPOSER_CACHE_DIR)
            displayName: Cache Composer Packages
          - script: cp .env-testing .env
            displayName: "Copy .env-testing to .env"
            workingDirectory: "src/NewbridgeBackOffice"

          - script: |
              docker exec builder bash -c "cd /__w/1/s/src/NewbridgeBackOffice && composer install --prefer-dist --no-scripts"
            displayName: "Install dependencies"

          - script: php artisan migrate --force --isolated
            displayName: "Migrate Database"
            workingDirectory: "src/NewbridgeBackOffice"

          - script: php artisan db:seed --class=DevelopmentSeeder --database=testing
            displayName: "Seed database"
            workingDirectory: "src/NewbridgeBackOffice"

          - script: XDEBUG_MODE=coverage php -d memory_limit=2048M ./vendor/bin/pest --coverage-cobertura=COVERAGE-phpunit.xml --log-junit TEST-phpunit-junit.xml
            displayName: 'Run tests'
            workingDirectory: 'src/NewbridgeBackOffice'
            continueOnError: true

          - task: PublishTestResults@2
            displayName: "Publish test report"
            condition: always()
            inputs:
              testResultsFormat: "JUnit"
              testResultsFiles: "**/TEST-phpunit-*.xml"
              searchFolder: "$(System.DefaultWorkingDirectory)/src/NewbridgeBackOffice"
              failTaskOnFailedTests: true

          - task: PublishCodeCoverageResults@1
            displayName: "Publish coverage report"
            condition: always()
            inputs:
              codeCoverageTool: "Cobertura"
              summaryFileLocation: "$(System.DefaultWorkingDirectory)/src/NewbridgeBackOffice/COVERAGE-phpunit.xml"
              pathToSources: "$(System.DefaultWorkingDirectory)/src/NewbridgeBackOffice"
              failIfCoverageEmpty: true
