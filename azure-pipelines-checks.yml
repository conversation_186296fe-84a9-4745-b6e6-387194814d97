trigger:
  branches:
    include:
      - "*"
  paths:
    include:
      - "src/NewbridgeBackOffice/*"

pool:
  vmImage: "ubuntu-latest"

variables:
  COMPOSER_CACHE_DIR: $(Pipeline.Workspace)/.composer

name: "Validation $(Date:yyyyMMdd).$(Rev:r)"

resources:
  containers:
    - container: builder
      image: 'sync667/awesome-laravel-base-builder:latest'
      command: 'tail -f /dev/null'
      options: '--name builder'

stages:
  - stage: Validate
    jobs:
      - job: PHP_CS_Fixer
        services:
          builder: builder
        steps:
          - task: Cache@2
            inputs:
              key: "composer | src/NewbridgeBackOffice/composer.lock"
              restoreKeys: "composer"
              path: $(COMPOSER_CACHE_DIR)
            displayName: Cache Composer Packages

          - script: docker exec builder bash -c "cd /__w/1/s/src/NewbridgeBackOffice && composer install --prefer-dist --no-scripts"
            displayName: "Install dependencies"

          - script: ./vendor/bin/php-cs-fixer fix --dry-run --format=checkstyle | ./vendor/bin/cs2pr
            displayName: "Check PHP CS Fixes"
            workingDirectory: "src/NewbridgeBackOffice"

      - job: PHPStan
        services:
          builder: builder
        steps:
          - task: Cache@2
            inputs:
              key: "composer | src/NewbridgeBackOffice/composer.lock"
              restoreKeys: "composer"
              path: $(COMPOSER_CACHE_DIR)
            displayName: Cache Composer Packages

          - script: docker exec builder bash -c "cd /__w/1/s/src/NewbridgeBackOffice && composer install --prefer-dist --no-scripts"
            displayName: "Install dependencies"

          - script: ./vendor/bin/phpstan analyse --no-progress --no-interaction --memory-limit=1G --error-format=junit > larastan-results.xml
            displayName: "Check PHPStan"
            workingDirectory: "src/NewbridgeBackOffice"
            continueOnError: true

          - task: PublishTestResults@2
            inputs:
              testResultsFormat: "JUnit"
              testResultsFiles: "./src/NewbridgeBackOffice/larastan-results.xml"
            displayName: "Publish Larastan Results"

      - job: PHP_Insights
        services:
          builder: builder
        steps:
          - task: Cache@2
            inputs:
              key: "composer | src/NewbridgeBackOffice/composer.lock"
              restoreKeys: "composer"
              path: $(COMPOSER_CACHE_DIR)
            displayName: Cache Composer Packages

          - script: docker exec builder bash -c "cd /__w/1/s/src/NewbridgeBackOffice && composer install --prefer-dist --no-scripts"
            displayName: "Install dependencies"

          - script: ./vendor/bin/phpinsights --no-interaction --min-quality=80 --min-complexity=80 --min-architecture=80 --min-style=80 --format=codeclimate > codeclimate-report.json
            displayName: "Check PHP Insights"
            workingDirectory: "src/NewbridgeBackOffice"
            continueOnError: true

          - task: PublishBuildArtifacts@1
            inputs:
              pathtoPublish: "./src/NewbridgeBackOffice/codeclimate-report.json"
              artifactName: "PHP Insights Report"
            displayName: "Publish PHP Insights Report"
