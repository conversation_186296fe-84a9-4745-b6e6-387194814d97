## Newbridge Back Office

The cloud back office of the Newbridge EPOS System

### Development Environment Set Up

Newbridge development environment uses Docker. Once the repository has been
cloned you will need to create an environment file, an example file
is included in the repository.

### Initialise Project

In the root directory of the project is a local.sh file that needs
to be given execute permissions.

```bash
chmod 777 local.sh
```

When starting project for first time you will need to make composer install and initialise database.

````bash
./local.sh init-db
./local.sh composer install
./local.sh seed-db
./local.sh artisan migrate
````

After you init project it will be ready to use. For normal operations use this command to start/stop docker:

```bash
./local.sh start
./local.sh stop
```

To rebuild after any changes run:

```bash
./local.sh rebuild
```

Doing the build may take some time the first time around once complete
you can navigate to localhost on port 80 and the website will display.

Base user for super admin would be created by seeder.
Login is: `super@newbridge` and password is: `super`

Make sure that no other services are using the ports 80, 6379 or 3306
as the services will crash if the ports are in use on your local machine.

#### Create a Self Signed Certificate

Install mkcert for your platform:

```bash
Windows: choco install mkcert
Mac: brew install mkcert; brew install nss;
```

Create a certificate for localhost by running this command.

```bash
./local.sh init-ssl
```

#### Install Pre-Commit Hooks

To be sure that we follow our rules we use pre-commit hooks. To install them run use base on your OS:

Using pip:
```bash
pip install pre-commit
```
Using homebrew:
```bash
brew install pre-commit
```
Using conda (via conda-forge):
```bash
conda install -c conda-forge pre-commit
```

After that local.sh script will install hooks on project. You should see them in you IDE commit options. So you can turn them on/off there.

#### Mailhog service for testing mailing
We use Mailhog for testing mailing. You can access it on port 8025. Remember that you have to set up envs for mailhog in .env file.

#### local.sh script other commands

```bash
./local.sh help
```

### For Windows Users (Not accurate, needs updating)

For performance reasons it is highly recommended that you checkout the
repo from within WSL2.

[Install WSL2](https://learn.microsoft.com/en-us/windows/wsl/install)

If you have WSL2 installed, but when you try and run it you see a
command window open and then immediately close, then you need to set
Ubuntu as your default distro. See above link on how to do this.  
**NB**: use a standard command window.

#### Cloning the repo

When you have WSL2 set up with an Ubuntu distro, log into it from the
start menu or a cmd terminal.
Type:
```bash
  cd
```
Optionally type:
```bash
  mkdir projects
  cd projects  
```
Then type:
```bash
  code .  
```
This should start VS Code, possibly installing something as it does.  
Then scroll to the top of this readme in
[DevOps](https://dev.azure.com/guestlinelabs/Newbridge/_git/Newbridge-Web?version=GBmaster)  
Hit the *Clone* button, then the *Clone in VS Code*
After you let everything open VS Code should be asking you which folder
you want to clone the repo into, suggesting something like  
`/home/<USER>/`  
here I would select the previously created folder *projects*, but its up to you.
**NB** It won't create a non-existant folder at this point.

*If you see a standard windows file explorer dialog asking you to select
the destination folder, you did it wrong, go back to the start of
cloning the repo and **pay attention** to the part where you start VS Code from
**inside** WSL.*

After you have selected a folder VS Code will ask you for a password.  
If you are a SSH ninja you probably got it from here on the cloning part.  
If not go back to DevOps, hit the *Generate Git Credentials* button and
copy the password, then paste it into VS Code where it asks for it.

After the cloning is done, if you are running *IIS*, you will need to edit
docker-compose.yaml, at line 21, change
```yaml
      - '80:80'
```
to
```yaml
      - '8080:80'
```
The rest of the process should be mostly as above. You will need to copy the
`.env.example` file to `.env` Remember to set a DB password.

If you are running MySql as a service on Windows, you will need to stop it first.

If you prefer not to use the local.sh script because you are a Docker ninja,
remember to set the env file, i.e.
```bash
  docker-compose --env-file ./src/.env up -d
```

You should install mkcerts and create the certificates from windows and
then copy them into the linux filesystem. To open the windows file explorer from
WSL:
```bash
  explorer.exe .
```

With luck you should be able to [access the site](https://localhost:443)

If you see a database connection error, you may need to apply this hacky fix:
```bash
mysql -u root -p -h newbridge-web-database-1 
```
Then type the password you set earlier
```mysql
CREATE USER 'newbridge'@'192.168.%.2' IDENTIFIED BY '<password>';
GRANT ALL ON newbridge.* TO 'newbridge'@'192.168.%.2'
```