resource "azurerm_windows_function_app" "kds" {
  name                       = "kds-translation-function-${local.environment}"
  location                   = azurerm_resource_group.main.location
  resource_group_name        = azurerm_resource_group.main.name
  service_plan_id            = azurerm_service_plan.serviceplan.id
  storage_account_name       = azurerm_storage_account.main.name
  storage_account_access_key = azurerm_storage_account.main.primary_access_key
  tags                       = local.tags

  site_config {
    application_stack {
      dotnet_version = "v8.0"
    }
    cors {
      allowed_origins = ["*"]
    }
  }

  identity {
    type = "SystemAssigned"
  }

  app_settings = {
    "FUNCTIONS_WORKER_RUNTIME"        = "dotnet-isolated"
    "WEBSITE_RUN_FROM_PACKAGE"        = 1
    "WEBSITE_ENABLE_SYNC_UPDATE_SITE" = "true"

    # these variables are set when executing Azure Function Deployment task in DevOps
    "KeyVaultUri"                   = "SET_BY_FA_RELEASE_PIPELINE"
    "KdsServiceBusConnectionString" = "SET_BY_FA_RELEASE_PIPELINE"
  }
  lifecycle {
    ignore_changes = [
      app_settings["KeyVaultUri"],
      app_settings["KdsServiceBusConnectionString"],
      sticky_settings,
      site_config["application_insights_connection_string"],
      site_config["application_insights_key"]
    ]
  }
}



resource "azurerm_windows_function_app" "horizon" {
  name                       = "hmon-${local.environment}-fa"
  location                   = azurerm_resource_group.main.location
  resource_group_name        = azurerm_resource_group.main.name
  service_plan_id            = azurerm_service_plan.serviceplan.id
  storage_account_name       = azurerm_storage_account.main.name
  storage_account_access_key = azurerm_storage_account.main.primary_access_key
  tags                       = local.tags

  site_config {
    application_stack {
      dotnet_version = "v8.0"
    }
    cors {
      allowed_origins = ["*"]
    }
  }

  app_settings = {
    "FUNCTIONS_WORKER_RUNTIME"        = "dotnet-isolated"
    "WEBSITE_RUN_FROM_PACKAGE"        = 1
    "WEBSITE_ENABLE_SYNC_UPDATE_SITE" = "true"

    # these variables are set when executing Azure Function Deployment task in DevOps
    "AppInsightsKey"  = azurerm_application_insights.main.connection_string
    "NewbridgeApiUrl" = ""
  }

  lifecycle {
    ignore_changes = [
      app_settings["NewbridgeApiUrl"],
      app_settings["AppInsightsKey"]
    ]
  }
}

resource "azurerm_windows_function_app" "rotaready" {
  name                       = "rotaready-${local.environment}-fa"
  location                   = azurerm_resource_group.main.location
  resource_group_name        = azurerm_resource_group.main.name
  service_plan_id            = azurerm_service_plan.serviceplan.id
  storage_account_name       = azurerm_storage_account.main.name
  storage_account_access_key = azurerm_storage_account.main.primary_access_key
  tags                       = local.tags

  site_config {
    application_stack {
      dotnet_version = "v7.0"
    }
    cors {
      allowed_origins = ["*"]
    }
  }

  app_settings = {
    "FUNCTIONS_WORKER_RUNTIME"        = "dotnet-isolated"
    "WEBSITE_RUN_FROM_PACKAGE"        = 1
    "WEBSITE_ENABLE_SYNC_UPDATE_SITE" = "true"

    # these variables are set when executing Azure Function Deployment task in DevOps
    "StorageConnectionString"        = ""
    "APPINSIGHTS_INSTRUMENTATIONKEY" = azurerm_application_insights.main.instrumentation_key
    "AppInsightsInstrumentationKey"  = azurerm_application_insights.main.instrumentation_key
    "RotaReadyApiUrl"                = ""
    "RotaReadyApiKey"                = ""
  }

  lifecycle {
    ignore_changes = [
      site_config["application_insights_key"],
      app_settings["StorageConnectionString"],
      app_settings["AppInsightsInstrumentationKey"],
      app_settings["APPINSIGHTS_INSTRUMENTATIONKEY"],
      app_settings["RotaReadyApiUrl"],
      app_settings["RotaReadyApiKey"]
    ]
  }
}
