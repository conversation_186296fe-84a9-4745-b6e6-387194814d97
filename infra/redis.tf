resource "azurerm_redis_cache" "main" {
  name                 = "${local.prefix}-redis"
  location             = azurerm_resource_group.main.location
  resource_group_name  = azurerm_resource_group.main.name
  capacity             = var.redis_sku_capacity
  family               = var.redis_sku_family
  sku_name             = var.redis_sku_name
  non_ssl_port_enabled = false
  minimum_tls_version  = "1.2"
  redis_version        = 6
  tags                 = local.tags

  dynamic "redis_configuration" {
    for_each = var.redis_sku_name == "Premium" ? [1] : []

    content {
      rdb_backup_enabled            = true
      rdb_backup_frequency          = 30
      rdb_backup_max_snapshot_count = 1
      rdb_storage_connection_string = azurerm_storage_account.main.primary_connection_string
    }
  }

  patch_schedule {
    day_of_week        = "Tuesday"
    start_hour_utc     = 2
    maintenance_window = "PT5H"
  }

  lifecycle {
    ignore_changes = [redis_configuration.0.rdb_storage_connection_string]
  }
}

resource "azurerm_redis_firewall_rule" "main" {
  for_each = { for item in var.firewall_rules : item.name => item }

  name                = each.value.name
  redis_cache_name    = azurerm_redis_cache.main.name
  resource_group_name = azurerm_resource_group.main.name
  start_ip            = each.value.ip
  end_ip              = each.value.ip
}
