resource "azurerm_log_analytics_workspace" "main" {
  name                = "${local.prefix}-ai-workspace"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  sku                 = "Standalone"
  retention_in_days   = 90

  tags = local.tags
}

resource "azurerm_application_insights" "main" {
  name                = "${local.prefix}-ai"
  location            = local.location
  resource_group_name = azurerm_resource_group.main.name
  workspace_id        = azurerm_log_analytics_workspace.main.id
  application_type    = "web"
  disable_ip_masking  = true

  tags = local.tags
}
