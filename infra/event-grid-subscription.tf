locals {
  resource_group    = azurerm_resource_group.main.name
  event_grid_domain = azurerm_eventgrid_domain.main.name
}

resource "azurerm_eventgrid_event_subscription" "main" {
  name                  = "${local.prefix}-webhook"
  scope                 = "/subscriptions/${var.env_subscription_id}/resourceGroups/${local.resource_group}/providers/Microsoft.EventGrid/domains/${local.event_grid_domain}/topics/orders"
  event_delivery_schema = "CloudEventSchemaV1_0"

  azure_function_endpoint {
    function_id                       = "${azurerm_windows_function_app.kds.id}/functions/order-events-webhook"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }

  retry_policy {
    max_delivery_attempts = 30
    event_time_to_live    = 1440
  }
  storage_blob_dead_letter_destination {
    storage_account_id          = azurerm_storage_account.main.id
    storage_blob_container_name = azurerm_storage_container.nbwebblob.name
  }
}

resource "azurerm_eventgrid_event_subscription" "rota-ready-config" {
  name                  = "rota-ready-config-subscription"
  scope                 = "/subscriptions/${var.env_subscription_id}/resourceGroups/${local.resource_group}/providers/Microsoft.EventGrid/domains/${local.event_grid_domain}/topics/${var.web_events_topic_name}"
  event_delivery_schema = "CloudEventSchemaV1_0"
  included_event_types  = ["company_integration_mappings_rotaready"]

  azure_function_endpoint {
    function_id                       = "${azurerm_windows_function_app.rotaready.id}/functions/IngestConfig"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }

  retry_policy {
    max_delivery_attempts = 30
    event_time_to_live    = 1440
  }
  storage_blob_dead_letter_destination {
    storage_account_id          = azurerm_storage_account.main.id
    storage_blob_container_name = azurerm_storage_container.nbwebblob.name
  }
}

resource "azurerm_eventgrid_event_subscription" "rota-ready-transactions" {
  name                  = "rota-ready-transactions-subscription"
  scope                 = "/subscriptions/${var.env_subscription_id}/resourceGroups/${local.resource_group}/providers/Microsoft.EventGrid/domains/${local.event_grid_domain}/topics/${var.web_events_topic_name}"
  event_delivery_schema = "CloudEventSchemaV1_0"
  included_event_types  = ["transaction"]

  azure_function_endpoint {
    function_id                       = "${azurerm_windows_function_app.rotaready.id}/functions/IngestSales"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }

  retry_policy {
    max_delivery_attempts = 30
    event_time_to_live    = 1440
  }
  storage_blob_dead_letter_destination {
    storage_account_id          = azurerm_storage_account.main.id
    storage_blob_container_name = azurerm_storage_container.nbwebblob.name
  }
}

resource "azurerm_eventgrid_event_subscription" "kds-printers" {
  name                  = "kds-printers-subscription"
  scope                 = "/subscriptions/${var.env_subscription_id}/resourceGroups/${local.resource_group}/providers/Microsoft.EventGrid/domains/${local.event_grid_domain}/topics/${var.web_events_topic_name}"
  event_delivery_schema = "CloudEventSchemaV1_0"
  included_event_types  = ["pos_settings"]
  
  advanced_filter {
    string_begins_with {
      key = "data.setting_id"
      values = [ "KITCHEN_PRINTER" ]
    }
  }

  azure_function_endpoint {
    function_id                       = "${azurerm_windows_function_app.kds.id}/functions/kds-printers-webhook"
    max_events_per_batch              = 1
    preferred_batch_size_in_kilobytes = 64
  }

  retry_policy {
    max_delivery_attempts = 30
    event_time_to_live    = 1440
  }
  storage_blob_dead_letter_destination {
    storage_account_id          = azurerm_storage_account.main.id
    storage_blob_container_name = azurerm_storage_container.nbwebblob.name
  }
}
