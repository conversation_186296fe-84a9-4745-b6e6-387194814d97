resource "azurerm_servicebus_namespace" "main" {
  name                = "${local.prefix}-service-bus"
  location            = azurerm_resource_group.main.location
  resource_group_name = azurerm_resource_group.main.name
  sku                 = "Standard"
  minimum_tls_version = "1.2"

  tags = local.tags
}

resource "azurerm_servicebus_topic" "client-topics" {
  for_each = {
    "kitchenjobs"   = {}
    "orders"        = {}
    "sites"         = {}
    "company"       = {}
    "printer-names" = {}
  }

  name                 = each.key
  namespace_id         = azurerm_servicebus_namespace.main.id
  partitioning_enabled = true
}

resource "azurerm_servicebus_subscription" "kds-kitchenjobs-sub" {
  name               = "kds-kitchenjobs-sub"
  topic_id           = azurerm_servicebus_topic.client-topics["kitchenjobs"].id
  max_delivery_count = 10
}

resource "azurerm_servicebus_subscription" "kds-printernames-sub" {
  name               = "kds-printer-names-sub"
  topic_id           = azurerm_servicebus_topic.client-topics["printer-names"].id
  max_delivery_count = 10
}

resource "azurerm_servicebus_subscription" "checkout-orders-sub" {
  name               = "checkout-orders-sub"
  topic_id           = azurerm_servicebus_topic.client-topics["orders"].id
  max_delivery_count = 10
}
